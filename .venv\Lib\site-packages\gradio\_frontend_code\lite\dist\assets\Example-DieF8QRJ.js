import{a as z,i as B,s as D,f as g,E as G,l as u,w as k,o as _,p as h,q as m,r,F as y,P as H,ao as b,x as F,v,aq as P,k as S,n as j}from"../lite.js";function p(c,e,l){const t=c.slice();return t[8]=e[l],t[10]=l,t}function q(c,e,l){const t=c.slice();return t[11]=e[l],t[13]=l,t}function I(c){let e,l,t;function i(a,s){return typeof a[0]=="string"?K:J}let f=i(c),n=f(c);return{c(){e=h("div"),n.c(),m(e,"class","svelte-1cib1xd"),r(e,"table",c[1]==="table"),r(e,"gallery",c[1]==="gallery"),r(e,"selected",c[2])},m(a,s){u(a,e,s),n.m(e,null),l||(t=[y(e,"mouseenter",c[6]),y(e,"mouseleave",c[7])],l=!0)},p(a,s){f===(f=i(a))&&n?n.p(a,s):(n.d(1),n=f(a),n&&(n.c(),n.m(e,null))),s&2&&r(e,"table",a[1]==="table"),s&2&&r(e,"gallery",a[1]==="gallery"),s&4&&r(e,"selected",a[2])},d(a){a&&_(e),n.d(),l=!1,H(t)}}}function J(c){let e,l,t=b(c[0].slice(0,3)),i=[];for(let n=0;n<t.length;n+=1)i[n]=E(p(c,t,n));let f=c[0].length>3&&w(c);return{c(){e=h("table");for(let n=0;n<i.length;n+=1)i[n].c();l=F(),f&&f.c(),m(e,"class"," svelte-1cib1xd")},m(n,a){u(n,e,a);for(let s=0;s<i.length;s+=1)i[s]&&i[s].m(e,null);v(e,l),f&&f.m(e,null)},p(n,a){if(a&1){t=b(n[0].slice(0,3));let s;for(s=0;s<t.length;s+=1){const d=p(n,t,s);i[s]?i[s].p(d,a):(i[s]=E(d),i[s].c(),i[s].m(e,l))}for(;s<i.length;s+=1)i[s].d(1);i.length=t.length}n[0].length>3?f?f.p(n,a):(f=w(n),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(n){n&&_(e),P(i,n),f&&f.d()}}}function K(c){let e;return{c(){e=S(c[0])},m(l,t){u(l,e,t)},p(l,t){t&1&&j(e,l[0])},d(l){l&&_(e)}}}function A(c){let e,l=c[11]+"",t;return{c(){e=h("td"),t=S(l),m(e,"class","svelte-1cib1xd")},m(i,f){u(i,e,f),v(e,t)},p(i,f){f&1&&l!==(l=i[11]+"")&&j(t,l)},d(i){i&&_(e)}}}function C(c){let e;return{c(){e=h("td"),e.textContent="…",m(e,"class","svelte-1cib1xd")},m(l,t){u(l,e,t)},d(l){l&&_(e)}}}function E(c){let e,l,t=b(c[8].slice(0,3)),i=[];for(let n=0;n<t.length;n+=1)i[n]=A(q(c,t,n));let f=c[8].length>3&&C();return{c(){e=h("tr");for(let n=0;n<i.length;n+=1)i[n].c();l=F(),f&&f.c()},m(n,a){u(n,e,a);for(let s=0;s<i.length;s+=1)i[s]&&i[s].m(e,null);v(e,l),f&&f.m(e,null)},p(n,a){if(a&1){t=b(n[8].slice(0,3));let s;for(s=0;s<t.length;s+=1){const d=q(n,t,s);i[s]?i[s].p(d,a):(i[s]=A(d),i[s].c(),i[s].m(e,l))}for(;s<i.length;s+=1)i[s].d(1);i.length=t.length}n[8].length>3?f||(f=C(),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(n){n&&_(e),P(i,n),f&&f.d()}}}function w(c){let e;return{c(){e=h("div"),m(e,"class","overlay svelte-1cib1xd"),r(e,"odd",c[3]%2!=0),r(e,"even",c[3]%2==0),r(e,"button",c[1]==="gallery")},m(l,t){u(l,e,t)},p(l,t){t&8&&r(e,"odd",l[3]%2!=0),t&8&&r(e,"even",l[3]%2==0),t&2&&r(e,"button",l[1]==="gallery")},d(l){l&&_(e)}}}function L(c){let e,l=c[5]&&I(c);return{c(){l&&l.c(),e=G()},m(t,i){l&&l.m(t,i),u(t,e,i)},p(t,[i]){t[5]&&l.p(t,i)},i:k,o:k,d(t){t&&_(e),l&&l.d(t)}}}function M(c,e,l){let{value:t}=e,{type:i}=e,{selected:f=!1}=e,{index:n}=e,a=!1,s=Array.isArray(t);const d=()=>l(4,a=!0),x=()=>l(4,a=!1);return c.$$set=o=>{"value"in o&&l(0,t=o.value),"type"in o&&l(1,i=o.type),"selected"in o&&l(2,f=o.selected),"index"in o&&l(3,n=o.index)},[t,i,f,n,a,s,d,x]}class O extends z{constructor(e){super(),B(this,e,M,L,D,{value:0,type:1,selected:2,index:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),g()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),g()}get index(){return this.$$.ctx[3]}set index(e){this.$$set({index:e}),g()}}export{O as default};
//# sourceMappingURL=Example-DieF8QRJ.js.map
