import{a as p,i as _,s as f,f as c,e as m,p as d,q as r,R as u,l as h,u as v,h as $,j as b,t as g,b as k,o as q}from"../lite.js";function w(l){let s,n,a;const o=l[2].default,e=m(o,l,l[1],null);return{c(){s=d("div"),e&&e.c(),r(s,"class",n=u(`icon-button-wrapper ${l[0]?"top-panel":""}`)+" svelte-sr71km")},m(t,i){h(t,s,i),e&&e.m(s,null),a=!0},p(t,[i]){e&&e.p&&(!a||i&2)&&v(e,o,t,t[1],a?b(o,t[1],i,null):$(t[1]),null),(!a||i&1&&n!==(n=u(`icon-button-wrapper ${t[0]?"top-panel":""}`)+" svelte-sr71km"))&&r(s,"class",n)},i(t){a||(g(e,t),a=!0)},o(t){k(e,t),a=!1},d(t){t&&q(s),e&&e.d(t)}}}function I(l,s,n){let{$$slots:a={},$$scope:o}=s,{top_panel:e=!0}=s;return l.$$set=t=>{"top_panel"in t&&n(0,e=t.top_panel),"$$scope"in t&&n(1,o=t.$$scope)},[e,o,a]}class B extends p{constructor(s){super(),_(this,s,I,w,f,{top_panel:0})}get top_panel(){return this.$$.ctx[0]}set top_panel(s){this.$$set({top_panel:s}),c()}}export{B as I};
//# sourceMappingURL=IconButtonWrapper-Ck50MZwX.js.map
