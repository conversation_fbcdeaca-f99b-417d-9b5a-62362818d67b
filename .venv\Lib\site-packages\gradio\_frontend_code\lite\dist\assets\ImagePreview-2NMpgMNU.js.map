{"version": 3, "file": "ImagePreview-2NMpgMNU.js", "sources": ["../../../image/shared/ImagePreview.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport {\n\t\tBlockLabel,\n\t\tEmpty,\n\t\tIconButton,\n\t\tShareButton,\n\t\tIconButtonWrapper\n\t} from \"@gradio/atoms\";\n\timport { Download } from \"@gradio/icons\";\n\timport { get_coordinates_of_clicked_image } from \"./utils\";\n\timport Image from \"./Image.svelte\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport { Maximize, Minimize } from \"@gradio/icons\";\n\n\timport { Image as ImageIcon } from \"@gradio/icons\";\n\timport { type FileData } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\texport let value: null | FileData;\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\texport let show_download_button = true;\n\texport let selectable = false;\n\texport let show_share_button = false;\n\texport let i18n: I18nFormatter;\n\texport let show_fullscreen_button = true;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tselect: SelectData;\n\t}>();\n\n\tconst handle_click = (evt: MouseEvent): void => {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t};\n\n\tlet is_full_screen = false;\n\tlet image_container: HTMLElement;\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"fullscreenchange\", () => {\n\t\t\tis_full_screen = !!document.fullscreenElement;\n\t\t});\n\t});\n\n\tconst toggle_full_screen = async (): Promise<void> => {\n\t\tif (!is_full_screen) {\n\t\t\tawait image_container.requestFullscreen();\n\t\t} else {\n\t\t\tawait document.exitFullscreen();\n\t\t\tis_full_screen = !is_full_screen;\n\t\t}\n\t};\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={ImageIcon}\n\tlabel={!show_label ? \"\" : label || i18n(\"image.image\")}\n/>\n{#if value === null || !value.url}\n\t<Empty unpadded_box={true} size=\"large\"><ImageIcon /></Empty>\n{:else}\n\t<div class=\"image-container\" bind:this={image_container}>\n\t\t<IconButtonWrapper>\n\t\t\t{#if !is_full_screen && show_fullscreen_button}\n\t\t\t\t<IconButton\n\t\t\t\t\tIcon={Maximize}\n\t\t\t\t\tlabel={is_full_screen ? \"Exit full screen\" : \"View in full screen\"}\n\t\t\t\t\ton:click={toggle_full_screen}\n\t\t\t\t/>\n\t\t\t{/if}\n\n\t\t\t{#if is_full_screen && show_fullscreen_button}\n\t\t\t\t<IconButton\n\t\t\t\t\tIcon={Minimize}\n\t\t\t\t\tlabel={is_full_screen ? \"Exit full screen\" : \"View in full screen\"}\n\t\t\t\t\ton:click={toggle_full_screen}\n\t\t\t\t/>\n\t\t\t{/if}\n\n\t\t\t{#if show_download_button}\n\t\t\t\t<DownloadLink href={value.url} download={value.orig_name || \"image\"}>\n\t\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t\t</DownloadLink>\n\t\t\t{/if}\n\t\t\t{#if show_share_button}\n\t\t\t\t<ShareButton\n\t\t\t\t\t{i18n}\n\t\t\t\t\ton:share\n\t\t\t\t\ton:error\n\t\t\t\t\tformatter={async (value) => {\n\t\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\t\tlet url = await uploadToHuggingFace(value, \"url\");\n\t\t\t\t\t\treturn `<img src=\"${url}\" />`;\n\t\t\t\t\t}}\n\t\t\t\t\t{value}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</IconButtonWrapper>\n\t\t<button on:click={handle_click}>\n\t\t\t<div class:selectable class=\"image-frame\">\n\t\t\t\t<Image src={value.url} alt=\"\" loading=\"lazy\" on:load />\n\t\t\t</div>\n\t\t</button>\n\t</div>\n{/if}\n\n<style>\n\t.image-container {\n\t\theight: 100%;\n\t\tposition: relative;\n\t}\n\n\t.image-container button {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tborder-radius: var(--radius-lg);\n\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.image-frame {\n\t\twidth: auto;\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t.image-frame :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: scale-down;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t:global(.fullscreen-controls svg) {\n\t\tposition: relative;\n\t\ttop: 0px;\n\t}\n\n\t:global(.image-container:fullscreen) {\n\t\tbackground-color: black;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t:global(.image-container:fullscreen img) {\n\t\tmax-width: 90vw;\n\t\tmax-height: 90vh;\n\t\tobject-fit: scale-down;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div1", "anchor", "append", "button", "div0", "dirty", "image_changes", "Maximize", "Minimize", "downloadlink_changes", "Download", "iconbutton_changes", "if_block0", "create_if_block_4", "if_block1", "create_if_block_3", "create_if_block_2", "create_if_block_1", "ImageIcon", "value", "$$props", "label", "show_label", "show_download_button", "selectable", "show_share_button", "i18n", "show_fullscreen_button", "dispatch", "createEventDispatcher", "handle_click", "evt", "coordinates", "get_coordinates_of_clicked_image", "is_full_screen", "image_container", "onMount", "$$invalidate", "toggle_full_screen", "uploadToHuggingFace", "$$value"], "mappings": "m6BA4GgB,IAAAA,KAAM,uRAvCrBC,EA0CKC,EAAAC,EAAAC,CAAA,qBALJC,EAIQF,EAAAG,CAAA,EAHPD,EAEKC,EAAAC,CAAA,8CAHYP,EAAY,EAAA,CAAA,sFAEhBQ,EAAA,IAAAC,EAAA,IAAAT,KAAM,oQAzCA,2SAMXU,SACCV,EAAc,CAAA,EAAG,mBAAqB,uCACnCA,EAAkB,EAAA,CAAA,iFADrBA,EAAc,CAAA,EAAG,mBAAqB,mKAOvCW,SACCX,EAAc,CAAA,EAAG,mBAAqB,uCACnCA,EAAkB,EAAA,CAAA,iFADrBA,EAAc,CAAA,EAAG,mBAAqB,+JAM1B,KAAAA,KAAM,aAAeA,EAAK,CAAA,EAAC,WAAa,iHAAxCQ,EAAA,IAAAI,EAAA,KAAAZ,KAAM,sBAAeA,EAAK,CAAA,EAAC,WAAa,4LACzCa,GAAiB,MAAAb,KAAK,iBAAiB,oEAAtBQ,EAAA,KAAAM,EAAA,MAAAd,KAAK,iBAAiB,2bAlBrDe,EAAA,CAAAf,MAAkBA,EAAsB,CAAA,GAAAgB,EAAAhB,CAAA,EAQzCiB,EAAAjB,MAAkBA,EAAsB,CAAA,GAAAkB,EAAAlB,CAAA,IAQxCA,EAAoB,CAAA,GAAAmB,EAAAnB,CAAA,IAKpBA,EAAiB,CAAA,GAAAoB,EAAApB,CAAA,kLArBhB,CAAAA,MAAkBA,EAAsB,CAAA,0GAQzCA,MAAkBA,EAAsB,CAAA,0GAQxCA,EAAoB,CAAA,wGAKpBA,EAAiB,CAAA,geA7BlBqB,QACErB,EAAU,CAAA,EAAQA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,EAAhC,2CAEjB,OAAAA,EAAU,CAAA,IAAA,MAAS,CAAAA,KAAM,IAAG,0LAFxBA,EAAU,CAAA,EAAQA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,EAAhC,+RA3CV,GAAA,CAAA,MAAAsB,CAAA,EAAAC,GACA,MAAAC,EAA4B,MAAA,EAAAD,EAC5B,CAAA,WAAAE,CAAA,EAAAF,GACA,qBAAAG,EAAuB,EAAA,EAAAH,GACvB,WAAAI,EAAa,EAAA,EAAAJ,GACb,kBAAAK,EAAoB,EAAA,EAAAL,EACpB,CAAA,KAAAM,CAAA,EAAAN,GACA,uBAAAO,EAAyB,EAAA,EAAAP,QAE9BQ,EAAWC,IAKXC,EAAgBC,GAAA,CACjB,IAAAC,EAAcC,GAAiCF,CAAG,EAClDC,GACHJ,EAAS,SAAA,CAAY,MAAOI,EAAa,MAAO,IAAA,CAAA,OAI9CE,EAAiB,GACjBC,EAEJC,EAAA,IAAA,CACC,SAAS,iBAAiB,mBAAA,IAAA,CACzBC,EAAA,EAAAH,EAAA,CAAA,CAAmB,SAAS,iBAAA,MAIxB,MAAAI,EAAA,SAAA,CACAJ,SAGE,SAAS,qBACfA,EAAkB,CAAAA,CAAA,SAHZC,EAAgB,6BA4CFhB,GACZA,eACW,MAAAoB,EAAoBpB,CAAY,CACzB,OAFJ,yIA7BgBgB,EAAeK"}