const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./error.svelte-B8wCU0c1.js')).default;
const imports = ["_app/immutable/nodes/1.BJyLLxOT.js","_app/immutable/chunks/stores.DcWgXC6T.js","_app/immutable/chunks/client.DB6ownDU.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-Dc5_PyOe.js.map
