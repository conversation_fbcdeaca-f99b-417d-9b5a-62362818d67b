import{a as d,i as v,s as y,f,p as r,k as h,q as o,r as u,l as m,v as c,n as _,w as g,o as b}from"../lite.js";function p(s){let e,a,n=JSON.stringify(s[0],null,2)+"",i;return{c(){e=r("div"),a=r("pre"),i=h(n),o(e,"class","svelte-1ayixqk"),u(e,"table",s[1]==="table"),u(e,"gallery",s[1]==="gallery"),u(e,"selected",s[2])},m(l,t){m(l,e,t),c(e,a),c(a,i)},p(l,[t]){t&1&&n!==(n=JSON.stringify(l[0],null,2)+"")&&_(i,n),t&2&&u(e,"table",l[1]==="table"),t&2&&u(e,"gallery",l[1]==="gallery"),t&4&&u(e,"selected",l[2])},i:g,o:g,d(l){l&&b(e)}}}function q(s,e,a){let{value:n}=e,{type:i}=e,{selected:l=!1}=e;return s.$$set=t=>{"value"in t&&a(0,n=t.value),"type"in t&&a(1,i=t.type),"selected"in t&&a(2,l=t.selected)},[n,i,l]}class k extends d{constructor(e){super(),v(this,e,q,p,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),f()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),f()}}export{k as default};
//# sourceMappingURL=Example-C2tF73Zd.js.map
