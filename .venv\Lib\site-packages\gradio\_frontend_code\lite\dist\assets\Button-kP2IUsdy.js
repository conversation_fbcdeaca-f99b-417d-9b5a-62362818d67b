import{a as K,i as L,s as M,f as h,E as O,l as g,y as P,b as k,z as Q,t as w,o as v,H as R,e as N,p as j,x as S,q as r,r as b,$ as d,v as A,F as T,u as D,h as G,j as I,ap as z}from"../lite.js";function U(i){let e,n,_,s,a,m,u=i[7]&&F(i);const l=i[12].default,t=N(l,i,i[11],null);return{c(){e=j("button"),u&&u.c(),n=S(),t&&t.c(),r(e,"class",_=i[4]+" "+i[3]+" "+i[1].join(" ")+" svelte-1137axg"),r(e,"id",i[0]),e.disabled=i[8],b(e,"hidden",!i[2]),d(e,"flex-grow",i[9]),d(e,"width",i[9]===0?"fit-content":null),d(e,"min-width",typeof i[10]=="number"?`calc(min(${i[10]}px, 100%))`:null)},m(f,c){g(f,e,c),u&&u.m(e,null),A(e,n),t&&t.m(e,null),s=!0,a||(m=T(e,"click",i[13]),a=!0)},p(f,c){f[7]?u?u.p(f,c):(u=F(f),u.c(),u.m(e,n)):u&&(u.d(1),u=null),t&&t.p&&(!s||c&2048)&&D(t,l,f,f[11],s?I(l,f[11],c,null):G(f[11]),null),(!s||c&26&&_!==(_=f[4]+" "+f[3]+" "+f[1].join(" ")+" svelte-1137axg"))&&r(e,"class",_),(!s||c&1)&&r(e,"id",f[0]),(!s||c&256)&&(e.disabled=f[8]),(!s||c&30)&&b(e,"hidden",!f[2]),c&512&&d(e,"flex-grow",f[9]),c&512&&d(e,"width",f[9]===0?"fit-content":null),c&1024&&d(e,"min-width",typeof f[10]=="number"?`calc(min(${f[10]}px, 100%))`:null)},i(f){s||(w(t,f),s=!0)},o(f){k(t,f),s=!1},d(f){f&&v(e),u&&u.d(),t&&t.d(f),a=!1,m()}}}function V(i){let e,n,_,s,a=i[7]&&H(i);const m=i[12].default,u=N(m,i,i[11],null);return{c(){e=j("a"),a&&a.c(),n=S(),u&&u.c(),r(e,"href",i[6]),r(e,"rel","noopener noreferrer"),r(e,"aria-disabled",i[8]),r(e,"class",_=i[4]+" "+i[3]+" "+i[1].join(" ")+" svelte-1137axg"),r(e,"id",i[0]),b(e,"hidden",!i[2]),b(e,"disabled",i[8]),d(e,"flex-grow",i[9]),d(e,"pointer-events",i[8]?"none":null),d(e,"width",i[9]===0?"fit-content":null),d(e,"min-width",typeof i[10]=="number"?`calc(min(${i[10]}px, 100%))`:null)},m(l,t){g(l,e,t),a&&a.m(e,null),A(e,n),u&&u.m(e,null),s=!0},p(l,t){l[7]?a?a.p(l,t):(a=H(l),a.c(),a.m(e,n)):a&&(a.d(1),a=null),u&&u.p&&(!s||t&2048)&&D(u,m,l,l[11],s?I(m,l[11],t,null):G(l[11]),null),(!s||t&64)&&r(e,"href",l[6]),(!s||t&256)&&r(e,"aria-disabled",l[8]),(!s||t&26&&_!==(_=l[4]+" "+l[3]+" "+l[1].join(" ")+" svelte-1137axg"))&&r(e,"class",_),(!s||t&1)&&r(e,"id",l[0]),(!s||t&30)&&b(e,"hidden",!l[2]),(!s||t&282)&&b(e,"disabled",l[8]),t&512&&d(e,"flex-grow",l[9]),t&256&&d(e,"pointer-events",l[8]?"none":null),t&512&&d(e,"width",l[9]===0?"fit-content":null),t&1024&&d(e,"min-width",typeof l[10]=="number"?`calc(min(${l[10]}px, 100%))`:null)},i(l){s||(w(u,l),s=!0)},o(l){k(u,l),s=!1},d(l){l&&v(e),a&&a.d(),u&&u.d(l)}}}function F(i){let e,n,_;return{c(){e=j("img"),r(e,"class","button-icon svelte-1137axg"),z(e.src,n=i[7].url)||r(e,"src",n),r(e,"alt",_=`${i[5]} icon`),b(e,"right-padded",i[5])},m(s,a){g(s,e,a)},p(s,a){a&128&&!z(e.src,n=s[7].url)&&r(e,"src",n),a&32&&_!==(_=`${s[5]} icon`)&&r(e,"alt",_),a&32&&b(e,"right-padded",s[5])},d(s){s&&v(e)}}}function H(i){let e,n,_;return{c(){e=j("img"),r(e,"class","button-icon svelte-1137axg"),z(e.src,n=i[7].url)||r(e,"src",n),r(e,"alt",_=`${i[5]} icon`)},m(s,a){g(s,e,a)},p(s,a){a&128&&!z(e.src,n=s[7].url)&&r(e,"src",n),a&32&&_!==(_=`${s[5]} icon`)&&r(e,"alt",_)},d(s){s&&v(e)}}}function W(i){let e,n,_,s;const a=[V,U],m=[];function u(l,t){return l[6]&&l[6].length>0?0:1}return e=u(i),n=m[e]=a[e](i),{c(){n.c(),_=O()},m(l,t){m[e].m(l,t),g(l,_,t),s=!0},p(l,[t]){let f=e;e=u(l),e===f?m[e].p(l,t):(P(),k(m[f],1,1,()=>{m[f]=null}),Q(),n=m[e],n?n.p(l,t):(n=m[e]=a[e](l),n.c()),w(n,1),n.m(_.parentNode,_))},i(l){s||(w(n),s=!0)},o(l){k(n),s=!1},d(l){l&&v(_),m[e].d(l)}}}function X(i,e,n){let{$$slots:_={},$$scope:s}=e,{elem_id:a=""}=e,{elem_classes:m=[]}=e,{visible:u=!0}=e,{variant:l="secondary"}=e,{size:t="lg"}=e,{value:f=null}=e,{link:c=null}=e,{icon:q=null}=e,{disabled:B=!1}=e,{scale:C=null}=e,{min_width:E=void 0}=e;function J(o){R.call(this,i,o)}return i.$$set=o=>{"elem_id"in o&&n(0,a=o.elem_id),"elem_classes"in o&&n(1,m=o.elem_classes),"visible"in o&&n(2,u=o.visible),"variant"in o&&n(3,l=o.variant),"size"in o&&n(4,t=o.size),"value"in o&&n(5,f=o.value),"link"in o&&n(6,c=o.link),"icon"in o&&n(7,q=o.icon),"disabled"in o&&n(8,B=o.disabled),"scale"in o&&n(9,C=o.scale),"min_width"in o&&n(10,E=o.min_width),"$$scope"in o&&n(11,s=o.$$scope)},[a,m,u,l,t,f,c,q,B,C,E,s,_,J]}class Z extends K{constructor(e){super(),L(this,e,X,W,M,{elem_id:0,elem_classes:1,visible:2,variant:3,size:4,value:5,link:6,icon:7,disabled:8,scale:9,min_width:10})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),h()}get variant(){return this.$$.ctx[3]}set variant(e){this.$$set({variant:e}),h()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),h()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),h()}get link(){return this.$$.ctx[6]}set link(e){this.$$set({link:e}),h()}get icon(){return this.$$.ctx[7]}set icon(e){this.$$set({icon:e}),h()}get disabled(){return this.$$.ctx[8]}set disabled(e){this.$$set({disabled:e}),h()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),h()}}export{Z as B};
//# sourceMappingURL=Button-kP2IUsdy.js.map
