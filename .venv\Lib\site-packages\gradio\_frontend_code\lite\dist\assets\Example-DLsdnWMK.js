import{a as w,i as q,s as z,f as d,E as h,l as m,t as _,y as E,b as p,z as I,o as b,p as k,k as V,v as A,n as C,w as g,I as N,a5 as O,O as y,c as S,q as j,r as c,m as B,a6 as D,d as F}from"../lite.js";import{V as G}from"./Video-B2DSnbGg.js";import"./file-url-Co2ROWca.js";import"./hls-CnVhpNcu.js";function v(s){let e,t,l,n;const a=[J,H],f=[];function o(i,r){return 0}return e=o(),t=f[e]=a[e](s),{c(){t.c(),l=h()},m(i,r){f[e].m(i,r),m(i,l,r),n=!0},p(i,r){t.p(i,r)},i(i){n||(_(t),n=!0)},o(i){p(t),n=!1},d(i){i&&b(l),f[e].d(i)}}}function H(s){let e,t;return{c(){e=k("div"),t=V(s[2])},m(l,n){m(l,e,n),A(e,t)},p(l,n){n&4&&C(t,l[2])},i:g,o:g,d(l){l&&b(e)}}}function J(s){let e,t,l,n;function a(o){s[6](o)}let f={muted:!0,playsinline:!0,src:s[2]?.video.url,is_stream:!1,loop:s[3]};return s[4]!==void 0&&(f.node=s[4]),t=new G({props:f}),N.push(()=>O(t,"node",a)),t.$on("loadeddata",s[5]),t.$on("mouseover",function(){y(s[4].play.bind(s[4]))&&s[4].play.bind(s[4]).apply(this,arguments)}),t.$on("mouseout",function(){y(s[4].pause.bind(s[4]))&&s[4].pause.bind(s[4]).apply(this,arguments)}),{c(){e=k("div"),S(t.$$.fragment),j(e,"class","container svelte-1de9zxs"),c(e,"table",s[0]==="table"),c(e,"gallery",s[0]==="gallery"),c(e,"selected",s[1])},m(o,i){m(o,e,i),B(t,e,null),n=!0},p(o,i){s=o;const r={};i&4&&(r.src=s[2]?.video.url),i&8&&(r.loop=s[3]),!l&&i&16&&(l=!0,r.node=s[4],D(()=>l=!1)),t.$set(r),(!n||i&1)&&c(e,"table",s[0]==="table"),(!n||i&1)&&c(e,"gallery",s[0]==="gallery"),(!n||i&2)&&c(e,"selected",s[1])},i(o){n||(_(t.$$.fragment,o),n=!0)},o(o){p(t.$$.fragment,o),n=!1},d(o){o&&b(e),F(t)}}}function K(s){let e,t,l=s[2]&&v(s);return{c(){l&&l.c(),e=h()},m(n,a){l&&l.m(n,a),m(n,e,a),t=!0},p(n,[a]){n[2]?l?(l.p(n,a),a&4&&_(l,1)):(l=v(n),l.c(),_(l,1),l.m(e.parentNode,e)):l&&(E(),p(l,1,1,()=>{l=null}),I())},i(n){t||(_(l),t=!0)},o(n){p(l),t=!1},d(n){n&&b(e),l&&l.d(n)}}}function L(s,e,t){let{type:l}=e,{selected:n=!1}=e,{value:a}=e,{loop:f}=e,o;async function i(){t(4,o.muted=!0,o),t(4,o.playsInline=!0,o),t(4,o.controls=!1,o),o.setAttribute("muted",""),await o.play(),o.pause()}function r(u){o=u,t(4,o)}return s.$$set=u=>{"type"in u&&t(0,l=u.type),"selected"in u&&t(1,n=u.selected),"value"in u&&t(2,a=u.value),"loop"in u&&t(3,f=u.loop)},[l,n,a,f,o,i,r]}class T extends w{constructor(e){super(),q(this,e,L,K,z,{type:0,selected:1,value:2,loop:3})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),d()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),d()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),d()}get loop(){return this.$$.ctx[3]}set loop(e){this.$$set({loop:e}),d()}}export{T as default};
//# sourceMappingURL=Example-DLsdnWMK.js.map
