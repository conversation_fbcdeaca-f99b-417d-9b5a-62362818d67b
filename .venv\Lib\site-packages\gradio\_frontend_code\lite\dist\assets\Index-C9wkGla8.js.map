{"version": 3, "file": "Index-C9wkGla8.js", "sources": ["../../../dataframe/shared/EditableCell.svelte", "../../../dataframe/shared/VirtualTable.svelte", "../../../dataframe/shared/Arrow.svelte", "../../../dataframe/shared/CellMenu.svelte", "../../../dataframe/shared/Table.svelte", "../../../dataframe/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { MarkdownCode } from \"@gradio/markdown-code\";\n\n\texport let edit: boolean;\n\texport let value: string | number = \"\";\n\texport let display_value: string | null = null;\n\texport let styling = \"\";\n\texport let header = false;\n\texport let datatype:\n\t\t| \"str\"\n\t\t| \"markdown\"\n\t\t| \"html\"\n\t\t| \"number\"\n\t\t| \"bool\"\n\t\t| \"date\" = \"str\";\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let clear_on_focus = false;\n\texport let select_on_focus = false;\n\texport let line_breaks = true;\n\texport let editable = true;\n\texport let root: string;\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let el: HTMLInputElement | null;\n\t$: _value = value;\n\n\tfunction use_focus(node: HTMLInputElement): any {\n\t\tif (clear_on_focus) {\n\t\t\t_value = \"\";\n\t\t}\n\t\tif (select_on_focus) {\n\t\t\tnode.select();\n\t\t}\n\n\t\tnode.focus();\n\n\t\treturn {};\n\t}\n\n\tfunction handle_blur({\n\t\tcurrentTarget\n\t}: Event & {\n\t\tcurrentTarget: HTMLInputElement;\n\t}): void {\n\t\tvalue = currentTarget.value;\n\t\tdispatch(\"blur\");\n\t}\n</script>\n\n{#if edit}\n\t<input\n\t\trole=\"textbox\"\n\t\tbind:this={el}\n\t\tbind:value={_value}\n\t\tclass:header\n\t\ttabindex=\"-1\"\n\t\ton:blur={handle_blur}\n\t\tuse:use_focus\n\t\ton:keydown\n\t/>\n{/if}\n\n<span\n\ton:dblclick\n\ttabindex=\"-1\"\n\trole=\"button\"\n\tclass:edit\n\ton:focus|preventDefault\n\tstyle={styling}\n>\n\t{#if datatype === \"html\"}\n\t\t{@html value}\n\t{:else if datatype === \"markdown\"}\n\t\t<MarkdownCode\n\t\t\tmessage={value.toLocaleString()}\n\t\t\t{latex_delimiters}\n\t\t\t{line_breaks}\n\t\t\tchatbot={false}\n\t\t\t{root}\n\t\t/>\n\t{:else}\n\t\t{editable ? value : display_value || value}\n\t{/if}\n</span>\n\n<style>\n\tinput {\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tbottom: var(--size-2);\n\t\tleft: var(--size-2);\n\t\tflex: 1 1 0%;\n\t\ttransform: translateX(-0.1px);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t}\n\n\tspan {\n\t\tflex: 1 1 0%;\n\t\toutline: none;\n\t\tpadding: var(--size-2);\n\t\t-webkit-user-select: text;\n\t\t-moz-user-select: text;\n\t\t-ms-user-select: text;\n\t\tuser-select: text;\n\t}\n\n\t.header {\n\t\ttransform: translateX(0);\n\t\tfont: var(--weight-bold);\n\t}\n\n\t.edit {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount, tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let items: any[][] = [];\n\n\texport let max_height: number;\n\texport let actual_height: number;\n\texport let table_scrollbar_width: number;\n\texport let start = 0;\n\texport let end = 20;\n\texport let selected: number | false;\n\tlet height = \"100%\";\n\n\tlet average_height = 30;\n\tlet bottom = 0;\n\tlet contents: HTMLTableSectionElement;\n\tlet head_height = 0;\n\tlet foot_height = 0;\n\tlet height_map: number[] = [];\n\tlet mounted: boolean;\n\tlet rows: HTMLCollectionOf<HTMLTableRowElement>;\n\tlet top = 0;\n\tlet viewport: HTMLTableElement;\n\tlet viewport_height = 200;\n\tlet visible: { index: number; data: any[] }[] = [];\n\tlet viewport_box: DOMRectReadOnly;\n\n\t$: viewport_height = viewport_box?.height || 200;\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tconst raf = is_browser\n\t\t? window.requestAnimationFrame\n\t\t: (cb: (...args: any[]) => void) => cb();\n\n\t$: mounted && raf(() => refresh_height_map(sortedItems));\n\n\tlet content_height = 0;\n\tasync function refresh_height_map(_items: typeof items): Promise<void> {\n\t\tif (viewport_height === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst { scrollTop } = viewport;\n\t\ttable_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\n\n\t\tcontent_height = top - (scrollTop - head_height);\n\t\tlet i = start;\n\n\t\twhile (content_height < max_height && i < _items.length) {\n\t\t\tlet row = rows[i - start];\n\t\t\tif (!row) {\n\t\t\t\tend = i + 1;\n\t\t\t\tawait tick(); // render the newly visible row\n\t\t\t\trow = rows[i - start];\n\t\t\t}\n\t\t\tlet _h = row?.getBoundingClientRect().height;\n\t\t\tif (!_h) {\n\t\t\t\t_h = average_height;\n\t\t\t}\n\t\t\tconst row_height = (height_map[i] = _h);\n\t\t\tcontent_height += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tend = i;\n\t\tconst remaining = _items.length - end;\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tcontent_height += scrollbar_height;\n\t\t}\n\n\t\tlet filtered_height_map = height_map.filter((v) => typeof v === \"number\");\n\t\taverage_height =\n\t\t\tfiltered_height_map.reduce((a, b) => a + b, 0) /\n\t\t\tfiltered_height_map.length;\n\n\t\tbottom = remaining * average_height;\n\t\theight_map.length = _items.length;\n\t\tawait tick();\n\t\tif (!max_height) {\n\t\t\tactual_height = content_height + 1;\n\t\t} else if (content_height < max_height) {\n\t\t\tactual_height = content_height + 2;\n\t\t} else {\n\t\t\tactual_height = max_height;\n\t\t}\n\n\t\tawait tick();\n\t}\n\n\t$: scroll_and_render(selected);\n\n\tasync function scroll_and_render(n: number | false): Promise<void> {\n\t\traf(async () => {\n\t\t\tif (typeof n !== \"number\") return;\n\t\t\tconst direction = typeof n !== \"number\" ? false : is_in_view(n);\n\t\t\tif (direction === true) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (direction === \"back\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" });\n\t\t\t}\n\n\t\t\tif (direction === \"forwards\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" }, true);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction is_in_view(n: number): \"back\" | \"forwards\" | true {\n\t\tconst current = rows && rows[n - start];\n\t\tif (!current && n < start) {\n\t\t\treturn \"back\";\n\t\t}\n\t\tif (!current && n >= end - 1) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\tconst { top: viewport_top } = viewport.getBoundingClientRect();\n\t\tconst { top, bottom } = current.getBoundingClientRect();\n\n\t\tif (top - viewport_top < 37) {\n\t\t\treturn \"back\";\n\t\t}\n\n\t\tif (bottom - viewport_top > viewport_height) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\treturn true;\n\t}\n\n\tfunction get_computed_px_amount(elem: HTMLElement, property: string): number {\n\t\tif (!elem) {\n\t\t\treturn 0;\n\t\t}\n\t\tconst compStyle = getComputedStyle(elem);\n\n\t\tlet x = parseInt(compStyle.getPropertyValue(property));\n\t\treturn x;\n\t}\n\n\tasync function handle_scroll(e: Event): Promise<void> {\n\t\tconst scroll_top = viewport.scrollTop;\n\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tconst is_start_overflow = sortedItems.length < start;\n\n\t\tconst row_top_border = get_computed_px_amount(rows[1], \"border-top-width\");\n\n\t\tconst actual_border_collapsed_width = 0;\n\n\t\tif (is_start_overflow) {\n\t\t\tawait scroll_to_index(sortedItems.length - 1, { behavior: \"auto\" });\n\t\t}\n\n\t\tlet new_start = 0;\n\t\t// acquire height map for currently visible rows\n\t\tfor (let v = 0; v < rows.length; v += 1) {\n\t\t\theight_map[start + v] = rows[v].getBoundingClientRect().height;\n\t\t}\n\t\tlet i = 0;\n\t\t// start from top: thead, with its borders, plus the first border to afterwards neglect\n\t\tlet y = head_height + row_top_border / 2;\n\t\tlet row_heights = [];\n\t\t// loop items to find new start\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\trow_heights[i] = row_height;\n\t\t\t// we only want to jump if the full (incl. border) row is away\n\t\t\tif (y + row_height + actual_border_collapsed_width > scroll_top) {\n\t\t\t\t// this is the last index still inside the viewport\n\t\t\t\tnew_start = i;\n\t\t\t\ttop = y - (head_height + row_top_border / 2);\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tnew_start = Math.max(0, new_start);\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t\tif (y > scroll_top + viewport_height) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tstart = new_start;\n\t\tend = i;\n\t\tconst remaining = sortedItems.length - end;\n\t\tif (end === 0) {\n\t\t\tend = 10;\n\t\t}\n\t\taverage_height = (y - head_height) / end;\n\t\tlet remaining_height = remaining * average_height; // 0\n\t\t// compute height map for remaining items\n\t\twhile (i < sortedItems.length) {\n\t\t\ti += 1;\n\t\t\theight_map[i] = average_height;\n\t\t}\n\t\tbottom = remaining_height;\n\t\tif (!isFinite(bottom)) {\n\t\t\tbottom = 200000;\n\t\t}\n\t}\n\n\texport async function scroll_to_index(\n\t\tindex: number,\n\t\topts: ScrollToOptions,\n\t\talign_end = false\n\t): Promise<void> {\n\t\tawait tick();\n\n\t\tconst _itemHeight = average_height;\n\n\t\tlet distance = index * _itemHeight;\n\t\tif (align_end) {\n\t\t\tdistance = distance - viewport_height + _itemHeight + head_height;\n\t\t}\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tdistance += scrollbar_height;\n\t\t}\n\n\t\tconst _opts = {\n\t\t\ttop: distance,\n\t\t\tbehavior: \"smooth\" as ScrollBehavior,\n\t\t\t...opts\n\t\t};\n\n\t\tviewport.scrollTo(_opts);\n\t}\n\n\t$: sortedItems = items;\n\n\t$: visible = is_browser\n\t\t? sortedItems.slice(start, end).map((data, i) => {\n\t\t\t\treturn { index: i + start, data };\n\t\t\t})\n\t\t: sortedItems\n\t\t\t\t.slice(0, (max_height / sortedItems.length) * average_height + 1)\n\t\t\t\t.map((data, i) => {\n\t\t\t\t\treturn { index: i + start, data };\n\t\t\t\t});\n\n\tonMount(() => {\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tmounted = true;\n\t\trefresh_height_map(items);\n\t});\n</script>\n\n<svelte-virtual-table-viewport>\n\t<table\n\t\tclass=\"table\"\n\t\tbind:this={viewport}\n\t\tbind:contentRect={viewport_box}\n\t\ton:scroll={handle_scroll}\n\t\tstyle=\"height: {height}; --bw-svt-p-top: {top}px; --bw-svt-p-bottom: {bottom}px; --bw-svt-head-height: {head_height}px; --bw-svt-foot-height: {foot_height}px; --bw-svt-avg-row-height: {average_height}px\"\n\t>\n\t\t<thead class=\"thead\" bind:offsetHeight={head_height}>\n\t\t\t<slot name=\"thead\" />\n\t\t</thead>\n\t\t<tbody bind:this={contents} class=\"tbody\">\n\t\t\t{#if visible.length && visible[0].data.length}\n\t\t\t\t{#each visible as item (item.data[0].id)}\n\t\t\t\t\t<slot name=\"tbody\" item={item.data} index={item.index}>\n\t\t\t\t\t\tMissing Table Row\n\t\t\t\t\t</slot>\n\t\t\t\t{/each}\n\t\t\t{/if}\n\t\t</tbody>\n\t\t<tfoot class=\"tfoot\" bind:offsetHeight={foot_height}>\n\t\t\t<slot name=\"tfoot\" />\n\t\t</tfoot>\n\t</table>\n</svelte-virtual-table-viewport>\n\n<style type=\"text/css\">\n\ttable {\n\t\tposition: relative;\n\t\toverflow-y: scroll;\n\t\toverflow-x: scroll;\n\t\t-webkit-overflow-scrolling: touch;\n\t\tmax-height: 100vh;\n\t\tbox-sizing: border-box;\n\t\tdisplay: block;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\twidth: 100%;\n\t\tscroll-snap-type: x proximity;\n\t\tborder-collapse: separate;\n\t}\n\ttable :is(thead, tfoot, tbody) {\n\t\tdisplay: table;\n\t\ttable-layout: fixed;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\ttbody {\n\t\toverflow-x: scroll;\n\t\toverflow-y: hidden;\n\t}\n\n\ttable tbody {\n\t\tpadding-top: var(--bw-svt-p-top);\n\t\tpadding-bottom: var(--bw-svt-p-bottom);\n\t}\n\ttbody {\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tborder: 0px solid currentColor;\n\t}\n\n\ttbody > :global(tr:last-child) {\n\t\tborder: none;\n\t}\n\n\ttable :global(td) {\n\t\tscroll-snap-align: start;\n\t}\n\n\ttbody > :global(tr:nth-child(even)) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: var(--layer-1);\n\t\toverflow: hidden;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let transform: string;\n</script>\n\n<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t<path\n\t\td=\"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z\"\n\t\t{transform}\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport Arrow from \"./Arrow.svelte\";\n\timport type { I18nFormatter } from \"js/utils/src\";\n\n\texport let x: number;\n\texport let y: number;\n\texport let on_add_row_above: () => void;\n\texport let on_add_row_below: () => void;\n\texport let on_add_column_left: () => void;\n\texport let on_add_column_right: () => void;\n\texport let row: number;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\n\texport let i18n: I18nFormatter;\n\tlet menu_element: HTMLDivElement;\n\n\t$: is_header = row === -1;\n\t$: can_add_rows = row_count[1] === \"dynamic\";\n\t$: can_add_columns = col_count[1] === \"dynamic\";\n\n\tonMount(() => {\n\t\tposition_menu();\n\t});\n\n\tfunction position_menu(): void {\n\t\tif (!menu_element) return;\n\n\t\tconst viewport_width = window.innerWidth;\n\t\tconst viewport_height = window.innerHeight;\n\t\tconst menu_rect = menu_element.getBoundingClientRect();\n\n\t\tlet new_x = x - 30;\n\t\tlet new_y = y - 20;\n\n\t\tif (new_x + menu_rect.width > viewport_width) {\n\t\t\tnew_x = x - menu_rect.width + 10;\n\t\t}\n\n\t\tif (new_y + menu_rect.height > viewport_height) {\n\t\t\tnew_y = y - menu_rect.height + 10;\n\t\t}\n\n\t\tmenu_element.style.left = `${new_x}px`;\n\t\tmenu_element.style.top = `${new_y}px`;\n\t}\n</script>\n\n<div bind:this={menu_element} class=\"cell-menu\">\n\t{#if !is_header && can_add_rows}\n\t\t<button on:click={() => on_add_row_above()}>\n\t\t\t<Arrow transform=\"rotate(-90 12 12)\" />\n\t\t\t{i18n(\"dataframe.add_row_above\")}\n\t\t</button>\n\t\t<button on:click={() => on_add_row_below()}>\n\t\t\t<Arrow transform=\"rotate(90 12 12)\" />\n\t\t\t{i18n(\"dataframe.add_row_below\")}\n\t\t</button>\n\t{/if}\n\t{#if can_add_columns}\n\t\t<button on:click={() => on_add_column_left()}>\n\t\t\t<Arrow transform=\"rotate(180 12 12)\" />\n\t\t\t{i18n(\"dataframe.add_column_left\")}\n\t\t</button>\n\t\t<button on:click={() => on_add_column_right()}>\n\t\t\t<Arrow transform=\"rotate(0 12 12)\" />\n\t\t\t{i18n(\"dataframe.add_column_right\")}\n\t\t</button>\n\t{/if}\n</div>\n\n<style>\n\t.cell-menu {\n\t\tposition: fixed;\n\t\tz-index: var(--layer-2);\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-1);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-1);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tmin-width: 150px;\n\t}\n\n\t.cell-menu button {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\ttext-align: left;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\ttransition:\n\t\t\tbackground-color 0.2s,\n\t\t\tcolor 0.2s;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t}\n\n\t.cell-menu button:hover {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.cell-menu button :global(svg) {\n\t\tfill: currentColor;\n\t\ttransition: fill 0.2s;\n\t}\n\n\t.cell-menu button:hover :global(svg) {\n\t\tfill: var(--color-accent);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { dsvFormat } from \"d3-dsv\";\n\timport { dequal } from \"dequal/lite\";\n\timport { copy } from \"@gradio/utils\";\n\timport { Upload } from \"@gradio/upload\";\n\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport { type Client } from \"@gradio/client\";\n\timport VirtualTable from \"./VirtualTable.svelte\";\n\timport type { Headers, HeadersWithIDs, Metadata, Datatype } from \"./utils\";\n\timport CellMenu from \"./CellMenu.svelte\";\n\n\texport let datatype: Datatype | Datatype[];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let headers: Headers = [];\n\texport let values: (string | number)[][] = [];\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\n\texport let editable = true;\n\texport let wrap = false;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\n\texport let max_height = 500;\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\n\tlet selected: false | [number, number] = false;\n\texport let display_value: string[][] | null = null;\n\texport let styling: string[][] | null = null;\n\tlet t_rect: DOMRectReadOnly;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: {\n\t\t\tdata: (string | number)[][];\n\t\t\theaders: string[];\n\t\t\tmetadata: Metadata;\n\t\t};\n\t\tselect: SelectData;\n\t}>();\n\n\tlet editing: false | [number, number] = false;\n\n\tconst get_data_at = (row: number, col: number): string | number =>\n\t\tdata?.[row]?.[col]?.value;\n\n\tlet last_selected: [number, number] | null = null;\n\n\t$: {\n\t\tif (selected !== false && !dequal(selected, last_selected)) {\n\t\t\tconst [row, col] = selected;\n\t\t\tif (!isNaN(row) && !isNaN(col) && data[row]) {\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: [row, col],\n\t\t\t\t\tvalue: get_data_at(row, col),\n\t\t\t\t\trow_value: data[row].map((d) => d.value)\n\t\t\t\t});\n\t\t\t\tlast_selected = selected;\n\t\t\t}\n\t\t}\n\t}\n\n\tlet els: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLInputElement }\n\t> = {};\n\n\tlet data_binding: Record<string, (typeof data)[0][0]> = {};\n\n\tfunction make_id(): string {\n\t\treturn Math.random().toString(36).substring(2, 15);\n\t}\n\n\tfunction make_headers(_head: Headers): HeadersWithIDs {\n\t\tlet _h = _head || [];\n\t\tif (col_count[1] === \"fixed\" && _h.length < col_count[0]) {\n\t\t\tconst fill = Array(col_count[0] - _h.length)\n\t\t\t\t.fill(\"\")\n\t\t\t\t.map((_, i) => `${i + _h.length}`);\n\t\t\t_h = _h.concat(fill);\n\t\t}\n\n\t\tif (!_h || _h.length === 0) {\n\t\t\treturn Array(col_count[0])\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, i) => {\n\t\t\t\t\tconst _id = make_id();\n\t\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\t\treturn { id: _id, value: JSON.stringify(i + 1) };\n\t\t\t\t});\n\t\t}\n\n\t\treturn _h.map((h, i) => {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\treturn { id: _id, value: h ?? \"\" };\n\t\t});\n\t}\n\n\tfunction process_data(_values: (string | number)[][]): {\n\t\tvalue: string | number;\n\t\tid: string;\n\t}[][] {\n\t\tconst data_row_length = _values.length;\n\t\treturn Array(\n\t\t\trow_count[1] === \"fixed\"\n\t\t\t\t? row_count[0]\n\t\t\t\t: data_row_length < row_count[0]\n\t\t\t\t\t? row_count[0]\n\t\t\t\t\t: data_row_length\n\t\t)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) =>\n\t\t\t\tArray(\n\t\t\t\t\tcol_count[1] === \"fixed\"\n\t\t\t\t\t\t? col_count[0]\n\t\t\t\t\t\t: data_row_length > 0\n\t\t\t\t\t\t\t? _values[0].length\n\t\t\t\t\t\t\t: headers.length\n\t\t\t\t)\n\t\t\t\t\t.fill(0)\n\t\t\t\t\t.map((_, j) => {\n\t\t\t\t\t\tconst id = make_id();\n\t\t\t\t\t\tels[id] = els[id] || { input: null, cell: null };\n\t\t\t\t\t\tconst obj = { value: _values?.[i]?.[j] ?? \"\", id };\n\t\t\t\t\t\tdata_binding[id] = obj;\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t})\n\t\t\t);\n\t}\n\n\tlet _headers = make_headers(headers);\n\tlet old_headers: string[] | undefined;\n\n\t$: {\n\t\tif (!dequal(headers, old_headers)) {\n\t\t\ttrigger_headers();\n\t\t}\n\t}\n\n\tfunction trigger_headers(): void {\n\t\t_headers = make_headers(headers);\n\n\t\told_headers = headers.slice();\n\t\ttrigger_change();\n\t}\n\n\t$: if (!dequal(values, old_val)) {\n\t\tdata = process_data(values as (string | number)[][]);\n\t\told_val = values as (string | number)[][];\n\t}\n\n\tlet data: { id: string; value: string | number }[][] = [[]];\n\n\tlet old_val: undefined | (string | number)[][] = undefined;\n\n\tasync function trigger_change(): Promise<void> {\n\t\tdispatch(\"change\", {\n\t\t\tdata: data.map((r) => r.map(({ value }) => value)),\n\t\t\theaders: _headers.map((h) => h.value),\n\t\t\tmetadata: editable\n\t\t\t\t? null\n\t\t\t\t: { display_value: display_value, styling: styling }\n\t\t});\n\t}\n\n\tfunction get_sort_status(\n\t\tname: string,\n\t\t_sort?: number,\n\t\tdirection?: SortDirection\n\t): \"none\" | \"ascending\" | \"descending\" {\n\t\tif (!_sort) return \"none\";\n\t\tif (headers[_sort] === name) {\n\t\t\tif (direction === \"asc\") return \"ascending\";\n\t\t\tif (direction === \"des\") return \"descending\";\n\t\t}\n\n\t\treturn \"none\";\n\t}\n\n\tfunction get_current_indices(id: string): [number, number] {\n\t\treturn data.reduce(\n\t\t\t(acc, arr, i) => {\n\t\t\t\tconst j = arr.reduce(\n\t\t\t\t\t(_acc, _data, k) => (id === _data.id ? k : _acc),\n\t\t\t\t\t-1\n\t\t\t\t);\n\n\t\t\t\treturn j === -1 ? acc : [i, j];\n\t\t\t},\n\t\t\t[-1, -1]\n\t\t);\n\t}\n\n\tasync function start_edit(i: number, j: number): Promise<void> {\n\t\tif (!editable || dequal(editing, [i, j])) return;\n\n\t\tediting = [i, j];\n\t}\n\n\tfunction move_cursor(\n\t\tkey: \"ArrowRight\" | \"ArrowLeft\" | \"ArrowDown\" | \"ArrowUp\",\n\t\tcurrent_coords: [number, number]\n\t): void {\n\t\tconst dir = {\n\t\t\tArrowRight: [0, 1],\n\t\t\tArrowLeft: [0, -1],\n\t\t\tArrowDown: [1, 0],\n\t\t\tArrowUp: [-1, 0]\n\t\t}[key];\n\n\t\tconst i = current_coords[0] + dir[0];\n\t\tconst j = current_coords[1] + dir[1];\n\n\t\tif (i < 0 && j <= 0) {\n\t\t\tselected_header = j;\n\t\t\tselected = false;\n\t\t} else {\n\t\t\tconst is_data = data[i]?.[j];\n\t\t\tselected = is_data ? [i, j] : selected;\n\t\t}\n\t}\n\n\tlet clear_on_focus = false;\n\t// eslint-disable-next-line complexity\n\tasync function handle_keydown(event: KeyboardEvent): Promise<void> {\n\t\tif (selected_header !== false && header_edit === false) {\n\t\t\tswitch (event.key) {\n\t\t\t\tcase \"ArrowDown\":\n\t\t\t\t\tselected = [0, selected_header];\n\t\t\t\t\tselected_header = false;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"ArrowLeft\":\n\t\t\t\t\tselected_header =\n\t\t\t\t\t\tselected_header > 0 ? selected_header - 1 : selected_header;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"ArrowRight\":\n\t\t\t\t\tselected_header =\n\t\t\t\t\t\tselected_header < _headers.length - 1\n\t\t\t\t\t\t\t? selected_header + 1\n\t\t\t\t\t\t\t: selected_header;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"Escape\":\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tselected_header = false;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"Enter\":\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (!selected) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst [i, j] = selected;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"ArrowRight\":\n\t\t\tcase \"ArrowLeft\":\n\t\t\tcase \"ArrowDown\":\n\t\t\tcase \"ArrowUp\":\n\t\t\t\tif (editing) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tmove_cursor(event.key, [i, j]);\n\t\t\t\tbreak;\n\n\t\t\tcase \"Escape\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tediting = false;\n\t\t\t\tbreak;\n\t\t\tcase \"Enter\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\n\t\t\t\tif (event.shiftKey) {\n\t\t\t\t\tadd_row(i);\n\t\t\t\t\tawait tick();\n\n\t\t\t\t\tselected = [i + 1, j];\n\t\t\t\t} else {\n\t\t\t\t\tif (dequal(editing, [i, j])) {\n\t\t\t\t\t\tediting = false;\n\t\t\t\t\t\tawait tick();\n\t\t\t\t\t\tselected = [i, j];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tediting = [i, j];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\tcase \"Backspace\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (!editing) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tdata[i][j].value = \"\";\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Delete\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (!editing) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tdata[i][j].value = \"\";\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Tab\":\n\t\t\t\tlet direction = event.shiftKey ? -1 : 1;\n\n\t\t\t\tlet is_data_x = data[i][j + direction];\n\t\t\t\tlet is_data_y =\n\t\t\t\t\tdata?.[i + direction]?.[direction > 0 ? 0 : _headers.length - 1];\n\n\t\t\t\tif (is_data_x || is_data_y) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tselected = is_data_x\n\t\t\t\t\t\t? [i, j + direction]\n\t\t\t\t\t\t: [i + direction, direction > 0 ? 0 : _headers.length - 1];\n\t\t\t\t}\n\t\t\t\tediting = false;\n\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (\n\t\t\t\t\t(!editing || (editing && dequal(editing, [i, j]))) &&\n\t\t\t\t\tevent.key.length === 1\n\t\t\t\t) {\n\t\t\t\t\tclear_on_focus = true;\n\t\t\t\t\tediting = [i, j];\n\t\t\t\t}\n\t\t}\n\t}\n\n\tlet active_cell: { row: number; col: number } | null = null;\n\n\tasync function handle_cell_click(i: number, j: number): Promise<void> {\n\t\tif (active_cell && active_cell.row === i && active_cell.col === j) {\n\t\t\tactive_cell = null;\n\t\t} else {\n\t\t\tactive_cell = { row: i, col: j };\n\t\t}\n\t\tif (dequal(editing, [i, j])) return;\n\t\theader_edit = false;\n\t\tselected_header = false;\n\t\tediting = false;\n\t\tif (!dequal(selected, [i, j])) {\n\t\t\tselected = [i, j];\n\t\t\tawait tick();\n\t\t\tparent.focus();\n\t\t}\n\t}\n\n\ttype SortDirection = \"asc\" | \"des\";\n\tlet sort_direction: SortDirection | undefined;\n\tlet sort_by: number | undefined;\n\n\tfunction handle_sort(col: number): void {\n\t\tif (typeof sort_by !== \"number\" || sort_by !== col) {\n\t\t\tsort_direction = \"asc\";\n\t\t\tsort_by = col;\n\t\t} else {\n\t\t\tif (sort_direction === \"asc\") {\n\t\t\t\tsort_direction = \"des\";\n\t\t\t} else if (sort_direction === \"des\") {\n\t\t\t\tsort_direction = \"asc\";\n\t\t\t}\n\t\t}\n\t}\n\n\tlet header_edit: number | false;\n\n\tlet select_on_focus = false;\n\tlet selected_header: number | false = false;\n\tasync function edit_header(i: number, _select = false): Promise<void> {\n\t\tif (!editable || col_count[1] !== \"dynamic\" || header_edit === i) return;\n\t\tselected = false;\n\t\tselected_header = i;\n\t\theader_edit = i;\n\t\tselect_on_focus = _select;\n\t}\n\n\tfunction end_header_edit(event: KeyboardEvent): void {\n\t\tif (!editable) return;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"Escape\":\n\t\t\tcase \"Enter\":\n\t\t\tcase \"Tab\":\n\t\t\t\tevent.preventDefault();\n\t\t\t\tselected = false;\n\t\t\t\tselected_header = header_edit;\n\t\t\t\theader_edit = false;\n\t\t\t\tparent.focus();\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tasync function add_row(index?: number): Promise<void> {\n\t\tparent.focus();\n\n\t\tif (row_count[1] !== \"dynamic\") return;\n\t\tif (data.length === 0) {\n\t\t\tvalues = [Array(headers.length).fill(\"\")];\n\t\t\treturn;\n\t\t}\n\n\t\tconst new_row = Array(data[0].length)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) => {\n\t\t\t\tconst _id = make_id();\n\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\treturn { id: _id, value: \"\" };\n\t\t\t});\n\n\t\tif (index !== undefined && index >= 0 && index <= data.length) {\n\t\t\tdata.splice(index, 0, new_row);\n\t\t} else {\n\t\t\tdata.push(new_row);\n\t\t}\n\n\t\tdata = data;\n\t\tselected = [index !== undefined ? index : data.length - 1, 0];\n\t}\n\n\t$: (data || selected_header) && trigger_change();\n\n\tasync function add_col(index?: number): Promise<void> {\n\t\tparent.focus();\n\t\tif (col_count[1] !== \"dynamic\") return;\n\n\t\tconst insert_index = index !== undefined ? index : data[0].length;\n\n\t\tfor (let i = 0; i < data.length; i++) {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\tdata[i].splice(insert_index, 0, { id: _id, value: \"\" });\n\t\t}\n\n\t\theaders.splice(insert_index, 0, `Header ${headers.length + 1}`);\n\n\t\tdata = data;\n\t\theaders = headers;\n\n\t\tawait tick();\n\n\t\trequestAnimationFrame(() => {\n\t\t\tedit_header(insert_index, true);\n\t\t\tconst new_w = parent.querySelectorAll(\"tbody\")[1].offsetWidth;\n\t\t\tparent.querySelectorAll(\"table\")[1].scrollTo({ left: new_w });\n\t\t});\n\t}\n\n\tfunction handle_click_outside(event: Event): void {\n\t\tif (\n\t\t\t(active_cell_menu &&\n\t\t\t\t!(event.target as HTMLElement).closest(\".cell-menu\")) ||\n\t\t\t(active_header_menu &&\n\t\t\t\t!(event.target as HTMLElement).closest(\".cell-menu\"))\n\t\t) {\n\t\t\tactive_cell_menu = null;\n\t\t\tactive_header_menu = null;\n\t\t}\n\n\t\tevent.stopImmediatePropagation();\n\t\tconst [trigger] = event.composedPath() as HTMLElement[];\n\t\tif (parent.contains(trigger)) {\n\t\t\treturn;\n\t\t}\n\n\t\tediting = false;\n\t\theader_edit = false;\n\t\tselected_header = false;\n\t\treset_selection();\n\t\tactive_cell = null;\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tfunction guess_delimitaor(\n\t\ttext: string,\n\t\tpossibleDelimiters: string[]\n\t): string[] {\n\t\treturn possibleDelimiters.filter(weedOut);\n\n\t\tfunction weedOut(delimiter: string): boolean {\n\t\t\tvar cache = -1;\n\t\t\treturn text.split(\"\\n\").every(checkLength);\n\n\t\t\tfunction checkLength(line: string): boolean {\n\t\t\t\tif (!line) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tvar length = line.split(delimiter).length;\n\t\t\t\tif (cache < 0) {\n\t\t\t\t\tcache = length;\n\t\t\t\t}\n\t\t\t\treturn cache === length && length > 1;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction data_uri_to_blob(data_uri: string): Blob {\n\t\tconst byte_str = atob(data_uri.split(\",\")[1]);\n\t\tconst mime_str = data_uri.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\n\t\tconst ab = new ArrayBuffer(byte_str.length);\n\t\tconst ia = new Uint8Array(ab);\n\n\t\tfor (let i = 0; i < byte_str.length; i++) {\n\t\t\tia[i] = byte_str.charCodeAt(i);\n\t\t}\n\n\t\treturn new Blob([ab], { type: mime_str });\n\t}\n\n\tfunction blob_to_string(blob: Blob): void {\n\t\tconst reader = new FileReader();\n\n\t\tfunction handle_read(e: ProgressEvent<FileReader>): void {\n\t\t\tif (!e?.target?.result || typeof e.target.result !== \"string\") return;\n\n\t\t\tconst [delimiter] = guess_delimitaor(e.target.result, [\",\", \"\\t\"]);\n\n\t\t\tconst [head, ...rest] = dsvFormat(delimiter).parseRows(e.target.result);\n\n\t\t\t_headers = make_headers(\n\t\t\t\tcol_count[1] === \"fixed\" ? head.slice(0, col_count[0]) : head\n\t\t\t);\n\n\t\t\tvalues = rest;\n\t\t\treader.removeEventListener(\"loadend\", handle_read);\n\t\t}\n\n\t\treader.addEventListener(\"loadend\", handle_read);\n\n\t\treader.readAsText(blob);\n\t}\n\n\tlet dragging = false;\n\n\tfunction get_max(\n\t\t_d: { value: any; id: string }[][]\n\t): { value: any; id: string }[] {\n\t\tlet max = _d[0].slice();\n\t\tfor (let i = 0; i < _d.length; i++) {\n\t\t\tfor (let j = 0; j < _d[i].length; j++) {\n\t\t\t\tif (`${max[j].value}`.length < `${_d[i][j].value}`.length) {\n\t\t\t\t\tmax[j] = _d[i][j];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn max;\n\t}\n\n\t$: max = get_max(data);\n\n\t$: cells[0] && set_cell_widths();\n\tlet cells: HTMLTableCellElement[] = [];\n\tlet parent: HTMLDivElement;\n\tlet table: HTMLTableElement;\n\n\tfunction set_cell_widths(): void {\n\t\tconst widths = cells.map((el, i) => {\n\t\t\treturn el?.clientWidth || 0;\n\t\t});\n\t\tif (widths.length === 0) return;\n\t\tfor (let i = 0; i < widths.length; i++) {\n\t\t\tparent.style.setProperty(\n\t\t\t\t`--cell-width-${i}`,\n\t\t\t\t`${widths[i] - scrollbar_width / widths.length}px`\n\t\t\t);\n\t\t}\n\t}\n\n\tlet table_height: number =\n\t\tvalues.slice(0, (max_height / values.length) * 37).length * 37 + 37;\n\tlet scrollbar_width = 0;\n\n\tfunction sort_data(\n\t\t_data: typeof data,\n\t\t_display_value: string[][] | null,\n\t\t_styling: string[][] | null,\n\t\tcol?: number,\n\t\tdir?: SortDirection\n\t): void {\n\t\tlet id = null;\n\t\t//Checks if the selected cell is still in the data\n\t\tif (selected && selected[0] in data && selected[1] in data[selected[0]]) {\n\t\t\tid = data[selected[0]][selected[1]].id;\n\t\t}\n\t\tif (typeof col !== \"number\" || !dir) {\n\t\t\treturn;\n\t\t}\n\t\tconst indices = [...Array(_data.length).keys()];\n\n\t\tif (dir === \"asc\") {\n\t\t\tindices.sort((i, j) =>\n\t\t\t\t_data[i][col].value < _data[j][col].value ? -1 : 1\n\t\t\t);\n\t\t} else if (dir === \"des\") {\n\t\t\tindices.sort((i, j) =>\n\t\t\t\t_data[i][col].value > _data[j][col].value ? -1 : 1\n\t\t\t);\n\t\t} else {\n\t\t\treturn;\n\t\t}\n\n\t\t// sort all the data and metadata based on the values in the data\n\t\tconst temp_data = [..._data];\n\t\tconst temp_display_value = _display_value ? [..._display_value] : null;\n\t\tconst temp_styling = _styling ? [..._styling] : null;\n\t\tindices.forEach((originalIndex, sortedIndex) => {\n\t\t\t_data[sortedIndex] = temp_data[originalIndex];\n\t\t\tif (_display_value && temp_display_value)\n\t\t\t\t_display_value[sortedIndex] = temp_display_value[originalIndex];\n\t\t\tif (_styling && temp_styling)\n\t\t\t\t_styling[sortedIndex] = temp_styling[originalIndex];\n\t\t});\n\n\t\tdata = data;\n\n\t\tif (id) {\n\t\t\tconst [i, j] = get_current_indices(id);\n\t\t\tselected = [i, j];\n\t\t}\n\t}\n\n\t$: sort_data(data, display_value, styling, sort_by, sort_direction);\n\n\t$: selected_index = !!selected && selected[0];\n\n\tlet is_visible = false;\n\n\tonMount(() => {\n\t\tconst observer = new IntersectionObserver((entries, observer) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting && !is_visible) {\n\t\t\t\t\tset_cell_widths();\n\t\t\t\t\tdata = data;\n\t\t\t\t}\n\n\t\t\t\tis_visible = entry.isIntersecting;\n\t\t\t});\n\t\t});\n\n\t\tobserver.observe(parent);\n\n\t\treturn () => {\n\t\t\tobserver.disconnect();\n\t\t};\n\t});\n\n\tlet highlighted_column: number | null = null;\n\n\tlet active_cell_menu: {\n\t\trow: number;\n\t\tcol: number;\n\t\tx: number;\n\t\ty: number;\n\t} | null = null;\n\n\tfunction toggle_cell_menu(event: MouseEvent, row: number, col: number): void {\n\t\tevent.stopPropagation();\n\t\tif (\n\t\t\tactive_cell_menu &&\n\t\t\tactive_cell_menu.row === row &&\n\t\t\tactive_cell_menu.col === col\n\t\t) {\n\t\t\tactive_cell_menu = null;\n\t\t} else {\n\t\t\tconst cell = (event.target as HTMLElement).closest(\"td\");\n\t\t\tif (cell) {\n\t\t\t\tconst rect = cell.getBoundingClientRect();\n\t\t\t\tactive_cell_menu = {\n\t\t\t\t\trow,\n\t\t\t\t\tcol,\n\t\t\t\t\tx: rect.right,\n\t\t\t\t\ty: rect.bottom\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction add_row_at(index: number, position: \"above\" | \"below\"): void {\n\t\tconst row_index = position === \"above\" ? index : index + 1;\n\t\tadd_row(row_index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tfunction add_col_at(index: number, position: \"left\" | \"right\"): void {\n\t\tconst col_index = position === \"left\" ? index : index + 1;\n\t\tadd_col(col_index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tfunction handle_resize(): void {\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t\tset_cell_widths();\n\t}\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"click\", handle_click_outside);\n\t\twindow.addEventListener(\"resize\", handle_resize);\n\t\treturn () => {\n\t\t\tdocument.removeEventListener(\"click\", handle_click_outside);\n\t\t\twindow.removeEventListener(\"resize\", handle_resize);\n\t\t};\n\t});\n\n\tlet active_button: {\n\t\ttype: \"header\" | \"cell\";\n\t\trow?: number;\n\t\tcol: number;\n\t} | null = null;\n\n\tfunction toggle_header_button(col: number): void {\n\t\tif (active_button?.type === \"header\" && active_button.col === col) {\n\t\t\tactive_button = null;\n\t\t} else {\n\t\t\tactive_button = { type: \"header\", col };\n\t\t}\n\t}\n\n\tfunction toggle_cell_button(row: number, col: number): void {\n\t\tif (\n\t\t\tactive_button?.type === \"cell\" &&\n\t\t\tactive_button.row === row &&\n\t\t\tactive_button.col === col\n\t\t) {\n\t\t\tactive_button = null;\n\t\t} else {\n\t\t\tactive_button = { type: \"cell\", row, col };\n\t\t}\n\t}\n\n\tlet active_header_menu: {\n\t\tcol: number;\n\t\tx: number;\n\t\ty: number;\n\t} | null = null;\n\n\tfunction toggle_header_menu(event: MouseEvent, col: number): void {\n\t\tevent.stopPropagation();\n\t\tif (active_header_menu && active_header_menu.col === col) {\n\t\t\tactive_header_menu = null;\n\t\t} else {\n\t\t\tconst header = (event.target as HTMLElement).closest(\"th\");\n\t\t\tif (header) {\n\t\t\t\tconst rect = header.getBoundingClientRect();\n\t\t\t\tactive_header_menu = {\n\t\t\t\t\tcol,\n\t\t\t\t\tx: rect.right,\n\t\t\t\t\ty: rect.bottom\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction reset_selection(): void {\n\t\tselected = false;\n\t\tlast_selected = null;\n\t}\n</script>\n\n<svelte:window\n\ton:click={handle_click_outside}\n\ton:touchstart={handle_click_outside}\n\ton:resize={() => set_cell_widths()}\n/>\n\n<div class:label={label && label.length !== 0} use:copy>\n\t{#if label && label.length !== 0 && show_label}\n\t\t<p>\n\t\t\t{label}\n\t\t</p>\n\t{/if}\n\t<div\n\t\tbind:this={parent}\n\t\tclass=\"table-wrap\"\n\t\tclass:dragging\n\t\tclass:no-wrap={!wrap}\n\t\tstyle=\"height:{table_height}px\"\n\t\ton:keydown={(e) => handle_keydown(e)}\n\t\trole=\"grid\"\n\t\ttabindex=\"0\"\n\t>\n\t\t<table\n\t\t\tbind:contentRect={t_rect}\n\t\t\tbind:this={table}\n\t\t\tclass:fixed-layout={column_widths.length != 0}\n\t\t>\n\t\t\t{#if label && label.length !== 0}\n\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t{/if}\n\t\t\t<thead>\n\t\t\t\t<tr>\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<th\n\t\t\t\t\t\t\tclass:editing={header_edit === i}\n\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\tstyle:width={column_widths.length ? column_widths[i] : undefined}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass:sorted={sort_by === i}\n\t\t\t\t\t\t\t\t\tclass:des={sort_by === i && sort_direction === \"des\"}\n\t\t\t\t\t\t\t\t\tclass=\"sort-button {sort_direction} \"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 9 7\"\n\t\t\t\t\t\t\t\t\t\tfill=\"none\"\n\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</th>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</thead>\n\t\t\t<tbody>\n\t\t\t\t<tr>\n\t\t\t\t\t{#each max as { value, id }, j (id)}\n\t\t\t\t\t\t<td tabindex=\"-1\" bind:this={cells[j]}>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</tbody>\n\t\t</table>\n\t\t<Upload\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t\tflex={false}\n\t\t\tcenter={false}\n\t\t\tboundedheight={false}\n\t\t\tdisable_click={true}\n\t\t\t{root}\n\t\t\ton:load={(e) => blob_to_string(data_uri_to_blob(e.detail.data))}\n\t\t\tbind:dragging\n\t\t>\n\t\t\t<VirtualTable\n\t\t\t\tbind:items={data}\n\t\t\t\t{max_height}\n\t\t\t\tbind:actual_height={table_height}\n\t\t\t\tbind:table_scrollbar_width={scrollbar_width}\n\t\t\t\tselected={selected_index}\n\t\t\t>\n\t\t\t\t{#if label && label.length !== 0}\n\t\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t\t{/if}\n\t\t\t\t<tr slot=\"thead\">\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<th\n\t\t\t\t\t\t\tclass:focus={header_edit === i || selected_header === i}\n\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\tstyle=\"width: var(--cell-width-{i});\"\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\ttoggle_header_button(i);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<div class=\"header-content\">\n\t\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\tedit={header_edit === i}\n\t\t\t\t\t\t\t\t\t\ton:keydown={end_header_edit}\n\t\t\t\t\t\t\t\t\t\ton:dblclick={() => edit_header(i)}\n\t\t\t\t\t\t\t\t\t\t{select_on_focus}\n\t\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\tclass:sorted={sort_by === i}\n\t\t\t\t\t\t\t\t\t\tclass:des={sort_by === i && sort_direction === \"des\"}\n\t\t\t\t\t\t\t\t\t\tclass=\"sort-button {sort_direction}\"\n\t\t\t\t\t\t\t\t\t\ton:click={(event) => {\n\t\t\t\t\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t\t\t\t\t\thandle_sort(i);\n\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<svg\n\t\t\t\t\t\t\t\t\t\t\twidth=\"1em\"\n\t\t\t\t\t\t\t\t\t\t\theight=\"1em\"\n\t\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 9 7\"\n\t\t\t\t\t\t\t\t\t\t\tfill=\"none\"\n\t\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\" />\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t{#if editable}\n\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\tclass=\"cell-menu-button\"\n\t\t\t\t\t\t\t\t\t\ton:click={(event) => toggle_header_menu(event, i)}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t⋮\n\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</th>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\n\t\t\t\t<tr slot=\"tbody\" let:item let:index class:row_odd={index % 2 === 0}>\n\t\t\t\t\t{#each item as { value, id }, j (id)}\n\t\t\t\t\t\t<td\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ton:touchstart={() => start_edit(index, j)}\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\thandle_cell_click(index, j);\n\t\t\t\t\t\t\t\ttoggle_cell_button(index, j);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\ton:dblclick={() => start_edit(index, j)}\n\t\t\t\t\t\t\tstyle:width=\"var(--cell-width-{j})\"\n\t\t\t\t\t\t\tstyle={styling?.[index]?.[j] || \"\"}\n\t\t\t\t\t\t\tclass:focus={dequal(selected, [index, j])}\n\t\t\t\t\t\t\tclass:menu-active={active_cell_menu &&\n\t\t\t\t\t\t\t\tactive_cell_menu.row === index &&\n\t\t\t\t\t\t\t\tactive_cell_menu.col === j}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\tbind:value={data[index][j].value}\n\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\tdisplay_value={display_value?.[index]?.[j]}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t\tedit={dequal(editing, [index, j])}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\ton:blur={() => ((clear_on_focus = false), parent.focus())}\n\t\t\t\t\t\t\t\t\t{clear_on_focus}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{#if editable}\n\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\tclass=\"cell-menu-button\"\n\t\t\t\t\t\t\t\t\t\ton:click={(event) => toggle_cell_menu(event, index, j)}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t⋮\n\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</VirtualTable>\n\t\t</Upload>\n\t</div>\n</div>\n\n{#if active_cell_menu !== null}\n\t<CellMenu\n\t\t{i18n}\n\t\tx={active_cell_menu.x}\n\t\ty={active_cell_menu.y}\n\t\trow={active_cell_menu?.row ?? -1}\n\t\t{col_count}\n\t\t{row_count}\n\t\ton_add_row_above={() => add_row_at(active_cell_menu?.row ?? -1, \"above\")}\n\t\ton_add_row_below={() => add_row_at(active_cell_menu?.row ?? -1, \"below\")}\n\t\ton_add_column_left={() => add_col_at(active_cell_menu?.col ?? -1, \"left\")}\n\t\ton_add_column_right={() => add_col_at(active_cell_menu?.col ?? -1, \"right\")}\n\t/>\n{/if}\n\n{#if active_header_menu !== null}\n\t<CellMenu\n\t\t{i18n}\n\t\tx={active_header_menu.x}\n\t\ty={active_header_menu.y}\n\t\trow={-1}\n\t\t{col_count}\n\t\t{row_count}\n\t\ton_add_row_above={() => add_row_at(active_cell_menu?.row ?? -1, \"above\")}\n\t\ton_add_row_below={() => add_row_at(active_cell_menu?.row ?? -1, \"below\")}\n\t\ton_add_column_left={() => add_col_at(active_header_menu?.col ?? -1, \"left\")}\n\t\ton_add_column_right={() =>\n\t\t\tadd_col_at(active_header_menu?.col ?? -1, \"right\")}\n\t/>\n{/if}\n\n<style>\n\t.button-wrap:hover svg {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.button-wrap svg {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: -5px;\n\t}\n\n\t.label p {\n\t\tposition: relative;\n\t\tz-index: var(--layer-4);\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--block-label-text-size);\n\t}\n\n\t.table-wrap {\n\t\tposition: relative;\n\t\ttransition: 150ms;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\toverflow: hidden;\n\t}\n\n\t.table-wrap:focus-within {\n\t\toutline: none;\n\t\tbackground-color: none;\n\t}\n\n\t.dragging {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.no-wrap {\n\t\twhite-space: nowrap;\n\t}\n\n\ttable {\n\t\tposition: absolute;\n\t\topacity: 0;\n\t\ttransition: 150ms;\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t}\n\n\tdiv:not(.no-wrap) td {\n\t\toverflow-wrap: anywhere;\n\t}\n\n\tdiv.no-wrap td {\n\t\toverflow-x: hidden;\n\t}\n\n\ttable.fixed-layout {\n\t\ttable-layout: fixed;\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: var(--layer-1);\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n\n\ttr {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t}\n\n\ttr > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth,\n\ttd {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t}\n\n\tth:first-child {\n\t\tborder-top-left-radius: var(--table-radius);\n\t}\n\n\tth:last-child {\n\t\tborder-top-right-radius: var(--table-radius);\n\t}\n\n\tth.focus,\n\ttd.focus {\n\t\t--ring-color: var(--color-accent);\n\t}\n\n\ttr:last-child td:first-child {\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t}\n\n\ttr:last-child td:last-child {\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\ttr th {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\tth svg {\n\t\tfill: currentColor;\n\t\tfont-size: 10px;\n\t}\n\n\t.sort-button {\n\t\tdisplay: flex;\n\t\tflex: none;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransition: 150ms;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tline-height: var(--text-sm);\n\t}\n\n\t.sort-button:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.des {\n\t\ttransform: scaleY(-1);\n\t}\n\n\t.sort-button.sorted {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.editing {\n\t\tbackground: var(--table-editing);\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\toutline: none;\n\t\theight: var(--size-full);\n\t\tmin-height: var(--size-9);\n\t\toverflow: hidden;\n\t}\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\toverflow: hidden;\n\t\tflex-grow: 1;\n\t\tmin-width: 0;\n\t}\n\n\t.controls-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tpadding-top: var(--size-2);\n\t}\n\n\t.row_odd {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.row_odd.focus {\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\ttable {\n\t\tborder-collapse: separate;\n\t}\n\n\t.cell-menu-button {\n\t\tflex-shrink: 0;\n\t\tdisplay: none;\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-radius);\n\t\twidth: var(--size-5);\n\t\theight: var(--size-5);\n\t\tmin-width: var(--size-5);\n\t\tpadding: 0;\n\t\tmargin-right: var(--spacing-sm);\n\t\tz-index: var(--layer-2);\n\t}\n\n\t.cell-menu-button:hover {\n\t\tbackground-color: var(--color-bg-hover);\n\t}\n\n\ttd.focus .cell-menu-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\tth .header-content {\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseDataFrame } from \"./shared/Table.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport { afterUpdate, tick } from \"svelte\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Table from \"./shared/Table.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Headers, Data, Metadata, Datatype } from \"./shared/utils\";\n\texport let headers: Headers = [];\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { data: Data; headers: Headers; metadata: Metadata } = {\n\t\tdata: [[\"\", \"\", \"\"]],\n\t\theaders: [\"1\", \"2\", \"3\"],\n\t\tmetadata: null\n\t};\n\tlet old_value = \"\";\n\texport let value_is_output = false;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let wrap: boolean;\n\texport let datatype: Datatype | Datatype[];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let root: string;\n\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let max_height: number | undefined = undefined;\n\n\texport let loading_status: LoadingStatus;\n\texport let interactive: boolean;\n\n\tlet _headers: Headers;\n\tlet display_value: string[][] | null;\n\tlet styling: string[][] | null;\n\tlet values: (string | number)[][];\n\tasync function handle_change(data?: {\n\t\tdata: Data;\n\t\theaders: Headers;\n\t\tmetadata: Metadata;\n\t}): Promise<void> {\n\t\tlet _data = data || value;\n\n\t\t_headers = [...(_data.headers || headers)];\n\t\tvalues = _data.data ? [..._data.data] : [];\n\t\tdisplay_value = _data?.metadata?.display_value\n\t\t\t? [..._data?.metadata?.display_value]\n\t\t\t: null;\n\t\tstyling =\n\t\t\t!interactive && _data?.metadata?.styling\n\t\t\t\t? [..._data?.metadata?.styling]\n\t\t\t\t: null;\n\t\tawait tick();\n\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\n\thandle_change();\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\t$: {\n\t\tif (old_value && JSON.stringify(value) !== old_value) {\n\t\t\told_value = JSON.stringify(value);\n\t\t\thandle_change();\n\t\t}\n\t}\n\n\tif (\n\t\t(Array.isArray(value) && value?.[0]?.length === 0) ||\n\t\tvalue.data?.[0]?.length === 0\n\t) {\n\t\tvalue = {\n\t\t\tdata: [Array(col_count?.[0] || 3).fill(\"\")],\n\t\t\theaders: Array(col_count?.[0] || 3)\n\t\t\t\t.fill(\"\")\n\t\t\t\t.map((_, i) => `${i + 1}`),\n\t\t\tmetadata: null\n\t\t};\n\t}\n\n\tasync function handle_value_change(data: {\n\t\tdata: Data;\n\t\theaders: Headers;\n\t\tmetadata: Metadata;\n\t}): Promise<void> {\n\t\tif (JSON.stringify(data) !== old_value) {\n\t\t\tvalue = { ...data };\n\t\t\told_value = JSON.stringify(value);\n\t\t\thandle_change(data);\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<Table\n\t\t{root}\n\t\t{label}\n\t\t{show_label}\n\t\t{row_count}\n\t\t{col_count}\n\t\t{values}\n\t\t{display_value}\n\t\t{styling}\n\t\theaders={_headers}\n\t\ton:change={(e) => handle_value_change(e.detail)}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t{wrap}\n\t\t{datatype}\n\t\t{latex_delimiters}\n\t\teditable={interactive}\n\t\t{max_height}\n\t\ti18n={gradio.i18n}\n\t\t{line_breaks}\n\t\t{column_widths}\n\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t/>\n</Block>\n"], "names": ["insert", "target", "input", "anchor", "ctx", "set_data", "t_value", "dirty", "markdowncode_changes", "create_if_block_2", "span", "edit", "$$props", "value", "display_value", "styling", "header", "datatype", "latex_delimiters", "clear_on_focus", "select_on_focus", "line_breaks", "editable", "root", "dispatch", "createEventDispatcher", "el", "use_focus", "node", "_value", "handle_blur", "currentTarget", "$$invalidate", "$$value", "get_key", "i", "create_if_block", "height", "svelte_virtual_table_viewport", "append", "table", "thead", "tbody", "tfoot", "get_computed_px_amount", "elem", "property", "compStyle", "items", "max_height", "actual_height", "table_scrollbar_width", "start", "end", "selected", "average_height", "bottom", "contents", "head_height", "foot_height", "height_map", "mounted", "rows", "top", "viewport", "viewport_height", "visible", "viewport_box", "is_browser", "raf", "cb", "content_height", "refresh_height_map", "_items", "scrollTop", "row", "tick", "_h", "row_height", "remaining", "scrollbar_height", "filtered_height_map", "v", "a", "b", "scroll_and_render", "n", "direction", "is_in_view", "scroll_to_index", "current", "viewport_top", "handle_scroll", "e", "scroll_top", "is_start_overflow", "sortedItems", "row_top_border", "actual_border_collapsed_width", "new_start", "y", "row_heights", "remaining_height", "index", "opts", "align_end", "_itemHeight", "distance", "_opts", "onMount", "ResizeObserverSingleton", "data", "svg", "path", "transform", "t1_value", "t4_value", "button0", "button1", "t1", "t4", "if_block0", "create_if_block_1", "div", "x", "on_add_row_above", "on_add_row_below", "on_add_column_left", "on_add_column_right", "col_count", "row_count", "i18n", "menu_element", "position_menu", "viewport_width", "menu_rect", "new_x", "new_y", "is_header", "can_add_rows", "can_add_columns", "p", "caption", "toggle_class", "div0", "attr", "th", "th_aria_sort_value", "set_style", "div1", "td", "if_block", "create_if_block_4", "button", "create_if_block_3", "div2", "editablecell_changes", "tr", "dequal", "editablecell_props", "td_style_value", "style_changed", "cellmenu_changes", "create_if_block_6", "if_block1", "create_if_block_5", "if_block2", "if_block3", "table_1", "tr0", "tr1", "make_id", "guess_delimitaor", "text", "possibleDelimiters", "weedOut", "delimiter", "cache", "checkLength", "line", "length", "data_uri_to_blob", "data_uri", "byte_str", "mime_str", "ab", "ia", "get_max", "_d", "max", "j", "label", "show_label", "headers", "values", "wrap", "column_widths", "upload", "stream_handler", "t_rect", "editing", "get_data_at", "col", "last_selected", "els", "make_headers", "_head", "fill", "_", "_id", "h", "process_data", "_values", "data_row_length", "id", "_headers", "old_headers", "trigger_headers", "trigger_change", "old_val", "get_sort_status", "name", "_sort", "get_current_indices", "acc", "arr", "_acc", "_data", "k", "start_edit", "move_cursor", "key", "current_coords", "dir", "selected_header", "is_data", "handle_keydown", "event", "header_edit", "add_row", "is_data_x", "is_data_y", "active_cell", "handle_cell_click", "parent", "sort_direction", "sort_by", "handle_sort", "edit_header", "_select", "end_header_edit", "new_row", "add_col", "insert_index", "new_w", "handle_click_outside", "active_cell_menu", "active_header_menu", "trigger", "reset_selection", "blob_to_string", "blob", "reader", "handle_read", "head", "rest", "dsvFormat", "dragging", "cells", "set_cell_widths", "widths", "scrollbar_width", "table_height", "sort_data", "_display_value", "_styling", "indices", "temp_data", "temp_display_value", "temp_styling", "originalIndex", "sortedIndex", "is_visible", "observer", "entries", "entry", "toggle_cell_menu", "cell", "rect", "add_row_at", "position", "row_index", "add_col_at", "col_index", "handle_resize", "active_button", "toggle_header_button", "toggle_cell_button", "toggle_header_menu", "$$self", "blur_handler", "click_handler", "dblclick_handler_1", "click_handler_3", "func", "func_1", "func_2", "func_3", "func_4", "func_5", "func_6", "func_7", "d", "selected_index", "table_changes", "elem_id", "elem_classes", "old_value", "value_is_output", "scale", "min_width", "gradio", "loading_status", "interactive", "handle_change", "afterUpdate", "handle_value_change", "clear_status_handler", "args", "change_handler"], "mappings": "s6BAwDCA,EASCC,EAAAC,EAAAC,CAAA,gBANYC,EAAM,EAAA,CAAA,uCAGTA,EAAW,EAAA,CAAA,+EAHRA,EAAM,EAAA,QAANA,EAAM,EAAA,CAAA,wFA4BjBA,EAAQ,CAAA,EAAGA,EAAK,CAAA,EAAGA,MAAiBA,EAAK,CAAA,GAAA,gEAAzCA,EAAQ,CAAA,EAAGA,EAAK,CAAA,EAAGA,MAAiBA,EAAK,CAAA,GAAA,KAAAC,GAAA,EAAAC,CAAA,2EAPhC,QAAAF,KAAM,eAAc,iDAGpB,kFAHAG,EAAA,IAAAC,EAAA,QAAAJ,KAAM,yQAHTA,EAAK,CAAA,EAAAH,EAAAE,CAAA,4BAALC,EAAK,CAAA,CAAA,uEAtBTA,EAAI,CAAA,GAAAK,GAAAL,CAAA,0CAqBH,OAAAA,OAAa,OAAM,EAEdA,OAAa,WAAU,6HAJ1BA,EAAO,CAAA,CAAA,6EANfJ,EAqBMC,EAAAS,EAAAP,CAAA,2FAlCDC,EAAI,CAAA,iOAmBDA,EAAO,CAAA,CAAA,iJAtEH,CAAA,KAAAO,CAAA,EAAAC,GACA,MAAAC,EAAyB,EAAA,EAAAD,GACzB,cAAAE,EAA+B,IAAA,EAAAF,GAC/B,QAAAG,EAAU,EAAA,EAAAH,GACV,OAAAI,EAAS,EAAA,EAAAJ,GACT,SAAAK,EAMC,KAAA,EAAAL,EACD,CAAA,iBAAAM,CAAA,EAAAN,GAKA,eAAAO,EAAiB,EAAA,EAAAP,GACjB,gBAAAQ,EAAkB,EAAA,EAAAR,GAClB,YAAAS,EAAc,EAAA,EAAAT,GACd,SAAAU,EAAW,EAAA,EAAAV,EACX,CAAA,KAAAW,CAAA,EAAAX,QAELY,EAAWC,KAEN,GAAA,CAAA,GAAAC,CAAA,EAAAd,WAGFe,EAAUC,EAAA,CACd,OAAAT,QACHU,EAAS,EAAA,EAENT,GACHQ,EAAK,OAAA,EAGNA,EAAK,MAAA,cAKGE,EACR,CAAA,cAAAC,GAAA,CAIAC,EAAA,EAAAnB,EAAQkB,EAAc,KAAA,EACtBP,EAAS,MAAM,4IAOJE,EAAEO,wBACDJ,EAAM,KAAA,uhBA7BnBG,EAAA,GAAGH,EAAShB,CAAA,89CCiPiB,KAAAT,MAAK,KAAa,MAAAA,MAAK,yEAD1CA,EAAO,CAAA,CAAA,EAAU,MAAA8B,EAAA9B,GAAAA,EAAK,EAAA,EAAA,KAAK,CAAC,EAAE,mBAAnC,OAAI,GAAA,EAAA,qMAACA,EAAO,CAAA,CAAA,sFAAZ,OAAI+B,GAAA,0JACiD;AAAA,MAEtD,6bAJG/B,EAAO,CAAA,EAAC,QAAUA,KAAQ,CAAC,EAAE,KAAK,QAAMgC,GAAAhC,CAAA,8XAN9BiC,EAAM,wBAAoBjC,EAAG,CAAA,EAAA,IAAA,2BAAyBA,EAAM,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,iCAA+BA,EAAc,CAAA,EAAA,IAAA,UANzMJ,EAwB+BC,EAAAqC,EAAAnC,CAAA,EAvB9BoC,EAsBOD,EAAAE,CAAA,EAfND,EAEOC,EAAAC,CAAA,8CACPF,EAQOC,EAAAE,CAAA,iCACPH,EAEOC,EAAAG,CAAA,sGAjBIvC,EAAa,CAAA,CAAA,4FAOlBA,EAAO,CAAA,EAAC,QAAUA,KAAQ,CAAC,EAAE,KAAK,8NANEA,EAAG,CAAA,EAAA,IAAA,yCAAyBA,EAAM,CAAA,EAAA,IAAA,6CAA4BA,EAAW,CAAA,EAAA,IAAA,6CAA4BA,EAAW,CAAA,EAAA,IAAA,+CAA+BA,EAAc,CAAA,EAAA,IAAA,iKA3PpMiC,GAAS,OA0HJ,SAAAO,GAAuBC,EAAmBC,EAAA,CAC7C,GAAA,CAAAD,EACG,MAAA,GAEF,MAAAE,EAAY,iBAAiBF,CAAI,EAGhC,OADC,SAASE,EAAU,iBAAiBD,CAAQ,CAAA,sDAxI1C,CAAA,MAAAE,EAAA,EAAA,EAAApC,EAEA,CAAA,WAAAqC,CAAA,EAAArC,EACA,CAAA,cAAAsC,CAAA,EAAAtC,EACA,CAAA,sBAAAuC,CAAA,EAAAvC,GACA,MAAAwC,EAAQ,CAAA,EAAAxC,GACR,IAAAyC,EAAM,EAAA,EAAAzC,EACN,CAAA,SAAA0C,CAAA,EAAA1C,EAGP2C,EAAiB,GACjBC,EAAS,EACTC,EACAC,EAAc,EACdC,EAAc,EACdC,EAAA,CAAA,EACAC,EACAC,EACAC,EAAM,EACNC,EACAC,EAAkB,IAClBC,EAAA,CAAA,EACAC,EAIE,MAAAC,EAAA,OAAoB,OAAW,IAC/BC,EAAMD,EACT,OAAO,sBACNE,GAAiCA,QAIjCC,EAAiB,iBACNC,GAAmBC,EAAA,IAC7BR,IAAoB,eAIhB,UAAAS,CAAc,EAAAV,OACtBb,EAAwBa,EAAS,YAAcA,EAAS,WAAA,EAExDO,EAAiBR,GAAOW,EAAYhB,OAChCvB,GAAIiB,OAEDmB,EAAiBtB,GAAcd,GAAIsC,EAAO,QAAA,KAC5CE,EAAMb,EAAK3B,GAAIiB,CAAK,EACnBuB,IACJ3C,EAAA,GAAAqB,EAAMlB,GAAI,CAAA,EACJ,MAAAyC,GAAA,EACND,EAAMb,EAAK3B,GAAIiB,CAAK,GAEjB,IAAAyB,GAAKF,GAAK,sBAAwB,EAAA,OACjCE,KACJA,GAAKtB,SAEAuB,GAAclB,EAAWzB,EAAC,EAAI0C,GACpCN,GAAkBO,GAClB3C,IAAK,OAGNkB,EAAMlB,EAAA,EACA,MAAA4C,GAAYN,EAAO,OAASpB,EAE5B2B,GAAmBhB,EAAS,aAAeA,EAAS,aACtDgB,GAAmB,IACtBT,GAAkBS,IAGf,IAAAC,GAAsBrB,EAAW,OAAQsB,GAAa,OAAAA,GAAM,QAAQ,MACxE3B,EACC0B,GAAoB,QAAQE,EAAGC,KAAMD,EAAIC,GAAG,CAAC,EAC7CH,GAAoB,MAAA,EAErBjD,EAAA,EAAAwB,EAASuB,GAAYxB,CAAA,EACrBK,EAAW,OAASa,EAAO,OACrB,MAAAG,GAAA,EACD3B,EAEMsB,EAAiBtB,EAC3BjB,EAAA,GAAAkB,EAAgBqB,EAAiB,CAAA,OAEjCrB,EAAgBD,CAAA,EAJhBjB,EAAA,GAAAkB,EAAgBqB,EAAiB,CAAA,EAO5B,MAAAK,GAAA,iBAKQS,EAAkBC,EAAA,CAChCjB,EAAA,SAAA,WACYiB,GAAM,SAAA,OACX,MAAAC,EAAA,OAAmBD,GAAM,SAAW,GAAQE,GAAWF,CAAC,EAC1DC,IAAc,KAGdA,IAAc,QACX,MAAAE,EAAgBH,GAAK,SAAU,SAAA,CAAA,EAGlCC,IAAc,kBACXE,EAAgBH,EAAA,CAAK,SAAU,SAAA,EAAa,EAAI,cAKhDE,GAAWF,EAAA,OACbI,EAAU5B,GAAQA,EAAKwB,EAAIlC,CAAK,EACjC,GAAA,CAAAsC,GAAWJ,EAAIlC,EACZ,MAAA,OAEH,GAAA,CAAAsC,GAAWJ,GAAKjC,EAAM,EACnB,MAAA,WAGA,KAAA,CAAA,IAAKsC,IAAiB3B,EAAS,sBAAA,GAC/B,IAAAD,GAAK,OAAAP,IAAWkC,EAAQ,wBAE5B3B,OAAAA,GAAM4B,GAAe,GACjB,OAGJnC,GAASmC,GAAe1B,EACpB,WAGD,kBAaO2B,GAAcC,EAAA,CACtB,MAAAC,EAAa9B,EAAS,UAE5BF,EAAOL,EAAS,SACV,MAAAsC,GAAoBC,EAAY,OAAS5C,EAEzC6C,GAAiBrD,GAAuBkB,EAAK,CAAC,EAAG,kBAAkB,EAEnEoC,GAAgC,EAElCH,UACGN,EAAgBO,EAAY,OAAS,EAAA,CAAK,SAAU,MAAA,CAAA,MAGvDG,GAAY,EAEP,QAAAjB,GAAI,EAAGA,GAAIpB,EAAK,OAAQoB,IAAK,EACrCtB,EAAWR,EAAQ8B,EAAC,EAAIpB,EAAKoB,EAAC,EAAE,sBAAwB,EAAA,WAErD/C,EAAI,EAEJiE,GAAI1C,EAAcuC,GAAiB,EACnCI,GAAA,CAAA,EAEG,KAAAlE,EAAI6D,EAAY,QAAA,OAChBlB,GAAalB,EAAWzB,CAAC,GAAKoB,EAGhC,GAFJ8C,GAAYlE,CAAC,EAAI2C,GAEbsB,GAAItB,GAAaoB,GAAgCJ,EAAA,CAEpDK,GAAYhE,MACZ4B,EAAMqC,IAAK1C,EAAcuC,GAAiB,EAAA,QAG3CG,IAAKtB,GACL3C,GAAK,EAIC,IADPgE,GAAY,KAAK,IAAI,EAAGA,EAAS,EAC1BhE,EAAI6D,EAAY,QAAA,OAChBlB,GAAalB,EAAWzB,CAAC,GAAKoB,EAGhC,GAFJ6C,IAAKtB,GACL3C,GAAK,EACDiE,GAAIN,EAAa7B,aAItBb,EAAQ+C,EAAA,OACR9C,EAAMlB,CAAA,EACA,MAAA4C,GAAYiB,EAAY,OAAS3C,EACnCA,IAAQ,QACXA,EAAM,EAAA,EAEPrB,EAAA,EAAAuB,GAAkB6C,GAAI1C,GAAeL,CAAA,EACjC,IAAAiD,GAAmBvB,GAAYxB,EAE5B,KAAApB,EAAI6D,EAAY,QACtB7D,GAAK,EACLyB,EAAWzB,CAAC,EAAIoB,MAEjBC,EAAS8C,EAAA,EACJ,SAAS9C,CAAM,OACnBA,EAAS,GAAA,iBAIWiC,EACrBc,EACAC,EACAC,GAAY,GAAA,CAEN,MAAA7B,GAAA,QAEA8B,GAAcnD,EAEhB,IAAAoD,GAAWJ,EAAQG,GACnBD,KACHE,GAAWA,GAAW1C,EAAkByC,GAAchD,SAGjDsB,GAAmBhB,EAAS,aAAeA,EAAS,aACtDgB,GAAmB,IACtB2B,IAAY3B,IAGP,MAAA4B,EAAA,CACL,IAAKD,GACL,SAAU,SACP,GAAAH,GAGJxC,EAAS,SAAS4C,CAAK,EAexBC,GAAA,IAAA,CACC/C,EAAOL,EAAS,cAChBI,EAAU,EAAA,EACVW,GAAmBxB,CAAK,iBAYgBU,EAAW,KAAA,8DAGjCD,EAAQxB,wBASc0B,EAAW,KAAA,8DAjBxCK,EAAQ/B,yBACDkC,EAAY2C,GAAA,QAAA,IAAA,IAAA,GAAA,kXAzO5B7C,EAAkBE,GAAc,QAAU,0BAkN7CnC,EAAA,GAAGgE,EAAchD,CAAA,wBA3Mda,GAAWQ,EAAU,IAAAG,GAAmBwB,CAAW,CAAA,uBAyDnDX,EAAkB/B,CAAQ,wBAoJ7BtB,EAAA,EAAGkC,EAAUE,EACV4B,EAAY,MAAM5C,EAAOC,CAAG,EAAE,KAAK0D,EAAM5E,KAChC,CAAA,MAAOA,EAAIiB,EAAO,KAAA2D,CAAA,IAE3Bf,EACC,MAAM,EAAI/C,EAAa+C,EAAY,OAAUzC,EAAiB,CAAC,EAC/D,IAAA,CAAKwD,EAAM5E,KACF,CAAA,MAAOA,EAAIiB,EAAO,KAAA2D,CAAA,0mCCnPhC/G,EAKKC,EAAA+G,EAAA7G,CAAA,EAJJoC,EAGCyE,EAAAC,CAAA,oFAPU,GAAA,CAAA,UAAAC,CAAA,EAAAtG,0PCoDRuG,EAAA/G,KAAK,yBAAyB,EAAA,aAI9BgH,EAAAhH,KAAK,yBAAyB,EAAA,gSANhCJ,EAGQC,EAAAoH,EAAAlH,CAAA,sCACRH,EAGQC,EAAAqH,EAAAnH,CAAA,+FALN,CAAAuF,GAAAnF,EAAA,KAAA4G,KAAAA,EAAA/G,KAAK,yBAAyB,EAAA,KAAAC,GAAAkH,EAAAJ,CAAA,GAI9B,CAAAzB,GAAAnF,EAAA,KAAA6G,KAAAA,EAAAhH,KAAK,yBAAyB,EAAA,KAAAC,GAAAmH,EAAAJ,CAAA,qLAM9BD,EAAA/G,KAAK,2BAA2B,EAAA,aAIhCgH,EAAAhH,KAAK,4BAA4B,EAAA,+RANnCJ,EAGQC,EAAAoH,EAAAlH,CAAA,sCACRH,EAGQC,EAAAqH,EAAAnH,CAAA,+FALN,CAAAuF,GAAAnF,EAAA,KAAA4G,KAAAA,EAAA/G,KAAK,2BAA2B,EAAA,KAAAC,GAAAkH,EAAAJ,CAAA,GAIhC,CAAAzB,GAAAnF,EAAA,KAAA6G,KAAAA,EAAAhH,KAAK,4BAA4B,EAAA,KAAAC,GAAAmH,EAAAJ,CAAA,qLAjB9BK,EAAA,CAAArH,MAAaA,EAAY,CAAA,GAAAsH,GAAAtH,CAAA,IAU1BA,EAAe,CAAA,GAAAgC,GAAAhC,CAAA,gGAXrBJ,EAqBKC,EAAA0H,EAAAxH,CAAA,+DApBE,CAAAC,MAAaA,EAAY,CAAA,kGAU1BA,EAAe,CAAA,8NAvDT,CAAA,EAAAwH,CAAA,EAAAhH,EACA,CAAA,EAAAwF,CAAA,EAAAxF,EACA,CAAA,iBAAAiH,CAAA,EAAAjH,EACA,CAAA,iBAAAkH,CAAA,EAAAlH,EACA,CAAA,mBAAAmH,CAAA,EAAAnH,EACA,CAAA,oBAAAoH,CAAA,EAAApH,EACA,CAAA,IAAA+D,CAAA,EAAA/D,EACA,CAAA,UAAAqH,CAAA,EAAArH,EACA,CAAA,UAAAsH,CAAA,EAAAtH,EAEA,CAAA,KAAAuH,CAAA,EAAAvH,EACPwH,EAMJvB,GAAA,IAAA,CACCwB,MAGQ,SAAAA,GAAA,CACH,GAAA,CAAAD,EAAA,OAEC,MAAAE,EAAiB,OAAO,WACxBrE,EAAkB,OAAO,YACzBsE,EAAYH,EAAa,wBAE3B,IAAAI,EAAQZ,EAAI,GACZa,EAAQrC,EAAI,GAEZoC,EAAQD,EAAU,MAAQD,IAC7BE,EAAQZ,EAAIW,EAAU,MAAQ,IAG3BE,EAAQF,EAAU,OAAStE,IAC9BwE,EAAQrC,EAAImC,EAAU,OAAS,QAGhCH,EAAa,MAAM,QAAUI,CAAK,KAAAJ,CAAA,MAClCA,EAAa,MAAM,OAASK,CAAK,KAAAL,CAAA,cAMTP,UAIAC,UAMAC,UAIAC,8CAhBVI,EAAYnG,ybA/B3BD,EAAA,EAAG0G,EAAY/D,IAAQ,EAAA,mBACpB3C,EAAA,EAAA2G,EAAeT,EAAU,CAAC,IAAM,SAAA,mBAChClG,EAAA,EAAA4G,EAAkBX,EAAU,CAAC,IAAM,SAAA,shDCmwBnC7H,EAAK,CAAA,CAAA,wCADPJ,EAEGC,EAAA4I,EAAA1I,CAAA,8BADDC,EAAK,CAAA,CAAA,wEAmBqBA,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAA6I,EAAA3I,CAAA,8BAAfC,EAAK,CAAA,CAAA,yJAgBpB,MACF,6WAOgBA,EAAc,EAAA,EAAA,iBAAA,EAFpB2I,EAAAC,EAAA,SAAA5I,QAAYA,EAAC,GAAA,CAAA,EAChB2I,EAAAC,EAAA,MAAA5I,EAAY,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAmB,KAAK,0CAhB3C6I,EAAAC,EAAA,YAAAC,EAAA/I,EAAgB,EAAA,EAAAA,EAAO,GAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,gCAD1C2I,EAAAG,EAAA,UAAA9I,QAAgBA,EAAC,GAAA,CAAA,EAEnBgJ,GAAAF,EAAA,QAAA9I,MAAc,OAASA,MAAcA,EAAC,GAAA,CAAA,EAAI,MAAS,uBAHjEJ,EAgCIC,EAAAiJ,EAAA/I,CAAA,EA3BHoC,EA0BK2G,EAAAG,CAAA,sBAfJ9G,EAcK8G,EAAAL,CAAA,EATJzG,EAQKyG,EAAAhC,CAAA,EADJzE,EAAsDyE,EAAAC,CAAA,mNATnC7G,EAAc,EAAA,EAAA,yDAFpB2I,EAAAC,EAAA,SAAA5I,QAAYA,EAAC,GAAA,CAAA,uBAChB2I,EAAAC,EAAA,MAAA5I,EAAY,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAmB,KAAK,GAhB3C,CAAAsF,GAAAnF,EAAA,CAAA,EAAA,UAAA4I,KAAAA,EAAA/I,EAAgB,EAAA,EAAAA,EAAO,GAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,8CAD1C2I,EAAAG,EAAA,UAAA9I,QAAgBA,EAAC,GAAA,CAAA,iBAEnBgJ,GAAAF,EAAA,QAAA9I,MAAc,OAASA,MAAcA,EAAC,GAAA,CAAA,EAAI,MAAS,gNA0CpD,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,OACpD,MACF,sPARPJ,EAYIC,EAAAqJ,EAAAnJ,CAAA,EAXHoC,EAUK+G,EAAA3B,CAAA,kLALO,MAAM,QAAQvH,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,qNA8BpCA,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAA6I,EAAA3I,CAAA,8BAAfC,EAAK,CAAA,CAAA,uCAD3BmJ,EAAAnJ,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACoJ,GAAApJ,CAAA,kEAA3BA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,oQAoD1BJ,EAKQC,EAAAwJ,EAAAtJ,CAAA,sQArCD,KAAAC,QAAgBA,EAAC,GAAA,6CAJXA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,OACfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,kGAILA,EAAe,EAAA,CAAA,uEA8BxBA,EAAQ,CAAA,GAAAsJ,GAAAtJ,CAAA,+ZAlBSA,EAAc,EAAA,EAAA,iBAAA,EAFpB2I,EAAAC,EAAA,SAAA5I,QAAYA,EAAC,GAAA,CAAA,EAChB2I,EAAAC,EAAA,MAAA5I,EAAY,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAmB,KAAK,uFAzB5C6I,EAAAC,EAAA,YAAAC,EAAA/I,EAAgB,EAAA,EAAAA,EAAO,GAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,mCACzBA,EAAC,GAAA,EAAA,GAAA,gCAFpB2I,EAAAG,EAAA,QAAA9I,EAAgB,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAoBA,EAAC,GAAA,CAAA,uBADxDJ,EAuDIC,EAAAiJ,EAAA/I,CAAA,EA/CHoC,EA8CK2G,EAAAS,CAAA,EA7CJpH,EAmCKoH,EAAAN,CAAA,sBAnBJ9G,EAkBK8G,EAAAL,CAAA,EATJzG,EAQKyG,EAAAhC,CAAA,EADJzE,EAAsDyE,EAAAC,CAAA,2KA1BjD1G,EAAA,CAAA,EAAA,YAAAqJ,EAAA,KAAAxJ,QAAgBA,EAAC,GAAA,sGAJXA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,mDACfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,sEAgBGA,EAAc,EAAA,EAAA,yDAFpB2I,EAAAC,EAAA,SAAA5I,QAAYA,EAAC,GAAA,CAAA,uBAChB2I,EAAAC,EAAA,MAAA5I,EAAY,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAmB,KAAK,EAmBjDA,EAAQ,CAAA,6DA5CH,CAAAsF,GAAAnF,EAAA,CAAA,EAAA,UAAA4I,KAAAA,EAAA/I,EAAgB,EAAA,EAAAA,EAAO,GAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,8EACzBA,EAAC,GAAA,EAAA,GAAA,wBAFpB2I,EAAAG,EAAA,QAAA9I,EAAgB,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAoBA,EAAC,GAAA,CAAA,wJAFlDA,EAAQ,EAAA,CAAA,aAAsBA,EAAE,GAAA,kBAArC,OAAI,GAAA,EAAA,kKADPJ,EA2DIC,EAAA4J,EAAA1J,CAAA,oGA1DIC,EAAQ,EAAA,CAAA,8EAAb,OAAI+B,GAAA,iSA4FFnC,EAKQC,EAAAwJ,EAAAtJ,CAAA,6HA3BqBC,EAAC,GAAA,CAAA,kGAWfA,EAAa,EAAA,IAAGA,EAAK,GAAA,CAAA,IAAIA,EAAC,GAAA,CAAA,wDAInC,KAAA0J,GAAO1J,EAAU,EAAA,EAAA,CAAAA,OAAOA,EAAC,GAAA,CAAA,CAAA,WACrB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,kCAP9CA,EAAK,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,QAAK,SAApB2J,EAAA,MAAA3J,EAAK,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,OAClBA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,4GAWbA,EAAQ,CAAA,GAAAK,GAAAL,CAAA,iRApBP6I,EAAAK,EAAA,QAAAU,EAAA5J,EAAU,EAAA,IAAAA,EAAS,GAAA,CAAA,IAAAA,SAAM,EAAE,gCACrB2I,EAAAO,EAAA,QAAAQ,GAAO1J,EAAW,EAAA,EAAA,CAAAA,OAAOA,EAAC,GAAA,CAAA,CAAA,CAAA,oBACpBA,EAAgB,EAAA,GAClCA,EAAgB,EAAA,EAAC,MAAQA,EAAK,GAAA,GAC9BA,EAAgB,EAAA,EAAC,MAAQA,EAAC,GAAA,CAAA,uCAb5BJ,EAsCIC,EAAAqJ,EAAAnJ,CAAA,EAvBHoC,EAsBK+G,EAAA3B,CAAA,iMAlBYvH,EAAa,EAAA,IAAGA,EAAK,GAAA,CAAA,IAAIA,EAAC,GAAA,CAAA,kGAInCG,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,SAAAqJ,EAAA,KAAAE,GAAO1J,EAAU,EAAA,EAAA,CAAAA,OAAOA,EAAC,GAAA,CAAA,CAAA,mCACrB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,uGAP9CwJ,EAAA,MAAAxJ,EAAK,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,8DAClBA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,8BAWbA,EAAQ,CAAA,6DApBP,CAAAsF,GAAAnF,EAAA,CAAA,EAAA,MAAAA,EAAA,CAAA,EAAA,QAAAyJ,KAAAA,EAAA5J,EAAU,EAAA,IAAAA,EAAS,GAAA,CAAA,IAAAA,SAAM,oDACnB2I,EAAAO,EAAA,QAAAQ,GAAO1J,EAAW,EAAA,EAAA,CAAAA,OAAOA,EAAC,GAAA,CAAA,CAAA,CAAA,+CACpBA,EAAgB,EAAA,GAClCA,EAAgB,EAAA,EAAC,MAAQA,EAAK,GAAA,GAC9BA,EAAgB,EAAA,EAAC,MAAQA,EAAC,GAAA,CAAA,qFALIA,EAAC,GAAA,CAAA,MAAA6J,0KAT3B7J,EAAI,GAAA,CAAA,aAAsBA,EAAE,GAAA,kBAAjC,OAAI,GAAA,EAAA,wKAD4CA,EAAK,GAAA,EAAG,IAAM,CAAC,UAAlEJ,EA0CIC,EAAA4J,EAAA1J,CAAA,+GAzCIC,EAAI,GAAA,CAAA,iFADuCA,EAAK,GAAA,EAAG,IAAM,CAAC,+BAC/D,OAAI+B,GAAA,uPAnEG/B,EAAc,EAAA,gKAJZA,EAAI,EAAA,IAAA,iBAAJA,EAAI,EAAA,GAEIA,EAAY,EAAA,IAAA,yBAAZA,EAAY,EAAA,GACJA,EAAe,EAAA,IAAA,iCAAfA,EAAe,EAAA,4PACjCA,EAAc,EAAA,qGAJZA,EAAI,EAAA,kDAEIA,EAAY,EAAA,0DACJA,EAAe,EAAA,oKAsH1C,EAAAA,MAAiB,EACjB,EAAAA,MAAiB,MACfA,EAAgB,EAAA,GAAE,KAAG,8NAFvBG,EAAA,CAAA,EAAA,KAAA2J,EAAA,EAAA9J,MAAiB,GACjBG,EAAA,CAAA,EAAA,KAAA2J,EAAA,EAAA9J,MAAiB,mBACfA,EAAgB,EAAA,GAAE,KAAG,oWAavB,EAAAA,MAAmB,EACnB,EAAAA,MAAmB,oOADnBG,EAAA,CAAA,EAAA,KAAA2J,EAAA,EAAA9J,MAAmB,GACnBG,EAAA,CAAA,EAAA,KAAA2J,EAAA,EAAA9J,MAAmB,mYAvOlBqH,EAAArH,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,GAAA+J,GAAA/J,CAAA,EAoBvCgK,EAAAhK,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACiK,GAAAjK,CAAA,OAKvBA,EAAQ,EAAA,CAAA,aAAsBA,EAAE,GAAA,kBAArC,OAAI+B,GAAA,EAAA,wDAuCC/B,EAAG,EAAA,CAAA,aAAsBA,EAAE,GAAA,mBAAhC,OAAI+B,GAAA,EAAA,sHAqBF,UACE,iBACO,iBACA,mKA4Hb,IAAAmI,EAAAlK,QAAqB,MAAIsH,GAAAtH,CAAA,EAezBmK,EAAAnK,QAAuB,MAAIgC,GAAAhC,CAAA,oZAjNTA,EAAa,EAAA,EAAC,QAAU,CAAC,yDAR/BA,EAAY,EAAA,EAAA,IAAA,8EADXA,EAAI,CAAA,CAAA,gCAVJ2I,EAAAM,EAAA,QAAAjJ,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,CAAC,UAA7CJ,EAmNKC,EAAAoJ,EAAAlJ,CAAA,wBA7MJoC,EA4MK8G,EAAAL,CAAA,EAlMJzG,EAkEOyG,EAAAwB,CAAA,wBA1DNjI,EAsCOiI,EAAA/H,CAAA,EArCNF,EAoCIE,EAAAgI,CAAA,0DAELlI,EAkBOiI,EAAA9H,CAAA,EAjBNH,EAgBIG,EAAAgI,CAAA,mMArFGtK,EAAoB,EAAA,CAAA,oBACfA,EAAoB,EAAA,CAAA,+EAK9BA,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,yDAoBvCA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,wFAKtBA,EAAQ,EAAA,CAAA,+EAuCRA,EAAG,EAAA,CAAA,sFA9CQA,EAAa,EAAA,EAAC,QAAU,CAAC,yQAR/BA,EAAY,EAAA,EAAA,IAAA,8EADXA,EAAI,CAAA,CAAA,gBAVJ2I,EAAAM,EAAA,QAAAjJ,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,CAAC,EAqNxCA,QAAqB,kHAerBA,QAAuB,+IA1MrB,OAAI+B,GAAA,2BAuCJ,OAAIA,GAAA,2VApwBD,SAAAwI,IAAA,CACD,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,EAyZzC,SAAAC,GACRC,EACAC,EAAA,CAEO,OAAAA,EAAmB,OAAOC,CAAO,WAE/BA,EAAQC,EAAA,KACZC,EAAQ,GACL,OAAAJ,EAAK,MAAM;AAAA,CAAI,EAAE,MAAMK,CAAW,WAEhCA,EAAYC,EAAA,CACf,GAAA,CAAAA,EACG,MAAA,OAGJC,EAASD,EAAK,MAAMH,CAAS,EAAE,cAC/BC,EAAQ,IACXA,EAAQG,GAEFH,IAAUG,GAAUA,EAAS,aAK9BC,GAAiBC,EAAA,CACnB,MAAAC,EAAW,KAAKD,EAAS,MAAM,GAAG,EAAE,CAAC,CAAA,EACrCE,EAAWF,EAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAE5DG,EAAS,IAAA,YAAYF,EAAS,MAAM,EACpCG,EAAA,IAAS,WAAWD,CAAE,UAEnBtJ,EAAI,EAAGA,EAAIoJ,EAAS,OAAQpJ,IACpCuJ,EAAGvJ,CAAC,EAAIoJ,EAAS,WAAWpJ,CAAC,aAGnB,KAAM,CAAAsJ,CAAE,GAAK,KAAMD,CAAA,CAAA,WA4BtBG,GACRC,EAAA,KAEIC,EAAMD,EAAG,CAAC,EAAE,MAAA,UACPzJ,EAAI,EAAGA,EAAIyJ,EAAG,OAAQzJ,IACrB,QAAA2J,EAAI,EAAGA,EAAIF,EAAGzJ,CAAC,EAAE,OAAQ2J,OAC1BD,EAAIC,CAAC,EAAE,KAAK,GAAG,OAAA,GAAYF,EAAGzJ,CAAC,EAAE2J,CAAC,EAAE,KAAK,GAAG,SAClDD,EAAIC,CAAC,EAAIF,EAAGzJ,CAAC,EAAE2J,CAAC,GAKZD,OAAAA,6BAviBG,CAAA,SAAA5K,CAAA,EAAAL,GACA,MAAAmL,EAAuB,IAAA,EAAAnL,GACvB,WAAAoL,EAAa,EAAA,EAAApL,EACb,CAAA,QAAAqL,EAAA,EAAA,EAAArL,EACA,CAAA,OAAAsL,EAAA,EAAA,EAAAtL,EACA,CAAA,UAAAqH,CAAA,EAAArH,EACA,CAAA,UAAAsH,CAAA,EAAAtH,EACA,CAAA,iBAAAM,CAAA,EAAAN,GAMA,SAAAU,EAAW,EAAA,EAAAV,GACX,KAAAuL,EAAO,EAAA,EAAAvL,EACP,CAAA,KAAAW,CAAA,EAAAX,EACA,CAAA,KAAAuH,CAAA,EAAAvH,GAEA,WAAAqC,EAAa,GAAA,EAAArC,GACb,YAAAS,EAAc,EAAA,EAAAT,EACd,CAAA,cAAAwL,EAAA,EAAA,EAAAxL,EACA,CAAA,OAAAyL,CAAA,EAAAzL,EACA,CAAA,eAAA0L,CAAA,EAAA1L,EAEP0C,EAAqC,IAC9B,cAAAxC,EAAmC,IAAA,EAAAF,GACnC,QAAAG,EAA6B,IAAA,EAAAH,EACpC2L,QAEE/K,EAAWC,SASb+K,EAAoC,GAElC,MAAAC,EAAA,CAAe9H,EAAa+H,IACjC3F,IAAOpC,CAAG,IAAI+H,CAAG,GAAG,UAEjBC,GAAyC,KAgBzCC,EAAA,CAAA,WAWKC,GAAaC,EAAA,KACjBjI,EAAKiI,GAAA,GACL,GAAA7E,EAAU,CAAC,IAAM,SAAWpD,EAAG,OAASoD,EAAU,CAAC,EAAA,CAChD,MAAA8E,EAAO,MAAM9E,EAAU,CAAC,EAAIpD,EAAG,MAAM,EACzC,KAAK,EAAE,EACP,IAAK,CAAAmI,EAAG7K,IAAS,GAAAA,EAAI0C,EAAG,MAAM,EAAA,EAChCA,EAAKA,EAAG,OAAOkI,CAAI,EAGf,MAAA,CAAAlI,GAAMA,EAAG,SAAW,EACjB,MAAMoD,EAAU,CAAC,CAAA,EACtB,KAAK,CAAC,EACN,KAAK+E,EAAG7K,IAAA,OACF8K,EAAMtC,KACZ,OAAA3I,EAAA,GAAA4K,EAAIK,CAAG,EAAA,CAAM,KAAM,KAAM,MAAO,IAAA,EAAAL,CAAA,EACvB,CAAA,GAAIK,EAAK,MAAO,KAAK,UAAU9K,EAAI,CAAC,KAIzC0C,EAAG,KAAKqI,EAAG/K,IAAA,OACX8K,EAAMtC,KACZ,OAAA3I,EAAA,GAAA4K,EAAIK,CAAG,EAAA,CAAM,KAAM,KAAM,MAAO,IAAA,EAAAL,CAAA,GACvB,GAAIK,EAAK,MAAOC,GAAK,EAAA,aAIvBC,GAAaC,EAAA,CAIf,MAAAC,EAAkBD,EAAQ,cACzB,MACNlF,EAAU,CAAC,IAAM,SAEdmF,EAAkBnF,EAAU,CAAC,EAD7BA,EAAU,CAAC,EAGVmF,CAEH,EAAA,KAAK,CAAC,EACN,IAAA,CAAKL,EAAG7K,IACR,MACC8F,EAAU,CAAC,IAAM,QACdA,EAAU,CAAC,EACXoF,EAAkB,EACjBD,EAAQ,CAAC,EAAE,OACXnB,EAAQ,MAAA,EAEX,KAAK,CAAC,EACN,IAAA,CAAKe,EAAGlB,KAAA,OACFwB,GAAK3C,KACX,OAAA3I,EAAA,GAAA4K,EAAIU,EAAE,EAAIV,EAAIU,EAAE,GAAO,CAAA,MAAO,KAAM,KAAM,IAAA,EAAAV,CAAA,EACpC,CAAQ,MAAOQ,IAAUjL,CAAC,IAAI2J,EAAC,GAAK,GAAI,GAAAwB,OAO/C,IAAAC,EAAWV,GAAaZ,CAAO,EAC/BuB,EAQK,SAAAC,GAAA,CACRzL,EAAA,GAAAuL,EAAWV,GAAaZ,CAAO,CAAA,EAE/BjK,EAAA,GAAAwL,EAAcvB,EAAQ,MAAA,CAAA,EACtByB,KAQG,IAAA3G,EAAA,CAAA,CAAA,CAAA,EAEA4G,EAEW,eAAAD,IAAA,CACdlM,EAAS,SAAA,CACR,KAAMuF,EAAK,IAAK,GAAM,EAAE,IAAA,CAAA,CAAO,MAAAlG,KAAYA,CAAK,CAAA,EAChD,QAAS0M,EAAS,IAAKL,GAAMA,EAAE,KAAK,EACpC,SAAU5L,EACP,MACE,cAAAR,EAA8B,QAAAC,CAAA,IAI5B,SAAA6M,EACRC,EACAC,EACAvI,EAAA,KAEKuI,EAAc,MAAA,OACf,GAAA7B,EAAQ6B,CAAK,IAAMD,EAAA,CAClB,GAAAtI,IAAc,MAAc,MAAA,YAC5B,GAAAA,IAAc,MAAc,MAAA,aAG1B,MAAA,gBAGCwI,EAAoBT,EAAA,QACrBvG,EAAK,OACV,CAAAiH,EAAKC,EAAK9L,IAAA,OACJ2J,EAAImC,EAAI,QACZC,GAAMC,GAAOC,KAAOd,IAAOa,GAAM,GAAKC,GAAIF,GAC3C,EAAA,SAGMpC,IAAM,GAAKkC,EAAO,CAAA7L,EAAG2J,CAAC,GAE7B,CAAA,GAAA,EAAM,GAIM,eAAAuC,GAAWlM,EAAW2J,EAAA,EAC/BxK,GAAYwI,GAAO0C,EAAU,CAAArK,EAAG2J,CAAC,CAAA,GAEtC9J,EAAA,GAAAwK,EAAA,CAAWrK,EAAG2J,CAAC,CAAA,EAGP,SAAAwC,GACRC,EACAC,EAAA,CAEM,MAAAC,EAAA,CACL,WAAA,CAAa,EAAG,CAAC,EACjB,UAAA,CAAY,IAAK,EACjB,UAAA,CAAY,EAAG,CAAC,EAChB,QAAA,CAAA,GAAc,CAAC,GACdF,CAAG,EAECpM,EAAIqM,EAAe,CAAC,EAAIC,EAAI,CAAC,EAC7B3C,EAAI0C,EAAe,CAAC,EAAIC,EAAI,CAAC,EAE/B,GAAAtM,EAAI,GAAK2J,GAAK,OACjB4C,GAAkB5C,CAAA,OAClBxI,EAAW,EAAA,aAELqL,GAAU5H,EAAK5E,CAAC,IAAI2J,CAAC,OAC3BxI,EAAWqL,GAAA,CAAWxM,EAAG2J,CAAC,EAAIxI,CAAA,OAI5BnC,GAAiB,kBAENyN,GAAeC,EAAA,CACzB,GAAAH,KAAoB,IAASI,KAAgB,UACxCD,EAAM,IAAA,CACR,IAAA,YACJ7M,EAAA,GAAAsB,EAAA,CAAY,EAAGoL,EAAe,CAAA,OAC9BA,GAAkB,EAAA,SAEd,IAAA,YACJ1M,EAAA,GAAA0M,GACCA,GAAkB,EAAIA,GAAkB,EAAIA,EAAA,SAEzC,IAAA,kBACJA,GACCA,GAAkBnB,EAAS,OAAS,EACjCmB,GAAkB,EAClBA,EAAA,SAEA,IAAA,SACJG,EAAM,eAAA,OACNH,GAAkB,EAAA,QAEd,IAAA,QACJG,EAAM,eAAA,QAIJ,GAAA,CAAAvL,SAIE,KAAA,CAAAnB,EAAG2J,CAAC,EAAIxI,SAEPuL,EAAM,IAAA,CACR,IAAA,aACA,IAAA,YACA,IAAA,YACA,IAAA,UACA,GAAArC,EAAA,MACJqC,EAAM,eAAA,EACNP,GAAYO,EAAM,IAAM,CAAA1M,EAAG2J,CAAC,CAAA,QAGxB,IAAA,SACC,GAAA,CAAAxK,EAAA,MACLuN,EAAM,eAAA,OACNrC,EAAU,EAAA,QAEN,IAAA,QACC,GAAA,CAAAlL,EAAA,MACLuN,EAAM,eAAA,EAEFA,EAAM,UACTE,GAAQ5M,CAAC,EACH,MAAAyC,GAAA,OAENtB,EAAY,CAAAnB,EAAI,EAAG2J,CAAC,CAAA,GAEhBhC,GAAO0C,EAAA,CAAUrK,EAAG2J,CAAC,CAAA,QACxBU,EAAU,EAAA,EACJ,MAAA5H,GAAA,EACN5C,EAAA,GAAAsB,EAAA,CAAYnB,EAAG2J,CAAC,CAAA,GAEhB9J,EAAA,GAAAwK,EAAA,CAAWrK,EAAG2J,CAAC,CAAA,QAKb,IAAA,YACC,GAAA,CAAAxK,EAAA,MACAkL,IACJqC,EAAM,eAAA,EACN7M,EAAA,GAAA+E,EAAK5E,CAAC,EAAE2J,CAAC,EAAE,MAAQ,GAAA/E,CAAA,SAGhB,IAAA,SACC,GAAA,CAAAzF,EAAA,MACAkL,IACJqC,EAAM,eAAA,EACN7M,EAAA,GAAA+E,EAAK5E,CAAC,EAAE2J,CAAC,EAAE,MAAQ,GAAA/E,CAAA,SAGhB,IAAA,UACAxB,EAAYsJ,EAAM,YAAgB,EAElCG,EAAYjI,EAAK5E,CAAC,EAAE2J,EAAIvG,CAAS,EACjC0J,GACHlI,IAAO5E,EAAIoD,CAAS,IAAIA,EAAY,EAAI,EAAIgI,EAAS,OAAS,CAAC,GAE5DyB,GAAaC,MAChBJ,EAAM,eAAA,OACNvL,EAAW0L,EACP,CAAA7M,EAAG2J,EAAIvG,CAAS,EAChB,CAAApD,EAAIoD,EAAWA,EAAY,EAAI,EAAIgI,EAAS,OAAS,CAAC,CAAA,QAE3Df,EAAU,EAAA,gBAIL,GAAA,CAAAlL,EAAA,QAEFkL,GAAYA,GAAW1C,GAAO0C,EAAU,CAAArK,EAAG2J,CAAC,CAC9C,IAAA+C,EAAM,IAAI,SAAW,SAErB1N,GAAiB,EAAA,EACjBa,EAAA,GAAAwK,EAAA,CAAWrK,EAAG2J,CAAC,CAAA,QAKfoD,EAAmD,KAExC,eAAAC,GAAkBhN,EAAW2J,EAAA,CACvCoD,GAAeA,EAAY,MAAQ/M,GAAK+M,EAAY,MAAQpD,EAC/DoD,EAAc,KAEdA,EAAgB,CAAA,IAAK/M,EAAG,IAAK2J,CAAA,EAE1B,CAAAhC,GAAO0C,EAAA,CAAUrK,EAAG2J,CAAC,CAAA,SACzBgD,GAAc,EAAA,OACdJ,GAAkB,EAAA,OAClBlC,EAAU,EAAA,EACL1C,GAAOxG,EAAA,CAAWnB,EAAG2J,CAAC,CAAA,IAC1B9J,EAAA,GAAAsB,EAAA,CAAYnB,EAAG2J,CAAC,CAAA,EACV,MAAAlH,GAAA,EACNwK,GAAO,MAAA,IAKL,IAAAC,GACAC,YAEKC,GAAY7C,EAAA,CACT,OAAA4C,IAAY,UAAYA,KAAY5C,QAC9C2C,GAAiB,KAAA,OACjBC,GAAU5C,CAAA,GAEN2C,KAAmB,WACtBA,GAAiB,KAAA,EACPA,KAAmB,YAC7BA,GAAiB,KAAA,EAKhB,IAAAP,GAEA1N,GAAkB,GAClBsN,GAAkC,GACvB,eAAAc,GAAYrN,EAAWsN,EAAU,GAAA,CAC1C,CAAAnO,GAAY2G,EAAU,CAAC,IAAM,WAAa6G,KAAgB3M,SAC/DmB,EAAW,EAAA,OACXoL,GAAkBvM,CAAA,OAClB2M,GAAc3M,CAAA,OACdf,GAAkBqO,CAAA,YAGVC,GAAgBb,EAAA,CACnB,GAAAvN,SAEGuN,EAAM,IAAA,CACR,IAAA,SACA,IAAA,QACA,IAAA,MACJA,EAAM,eAAA,OACNvL,EAAW,EAAA,OACXoL,GAAkBI,EAAA,OAClBA,GAAc,EAAA,EACdM,GAAO,MAAA,wBAKKL,GAAQxI,EAAA,CAGlB,GAFJ6I,GAAO,MAAA,EAEHlH,EAAU,CAAC,IAAM,UAAA,OACjB,GAAAnB,EAAK,SAAW,EAAA,CACnB/E,EAAA,GAAAkK,EAAA,CAAU,MAAMD,EAAQ,MAAM,EAAE,KAAK,EAAE,CAAA,CAAA,eAIlC0D,EAAU,MAAM5I,EAAK,CAAC,EAAE,MAAM,EAClC,KAAK,CAAC,EACN,IAAA,CAAKiG,EAAG7K,IAAA,OACF8K,EAAMtC,KACZ,OAAA3I,EAAA,GAAA4K,EAAIK,CAAG,EAAA,CAAM,KAAM,KAAM,MAAO,IAAA,EAAAL,CAAA,EACvB,CAAA,GAAIK,EAAK,MAAO,EAAA,IAGvB1G,IAAA,QAAuBA,GAAS,GAAKA,GAASQ,EAAK,OACtDA,EAAK,OAAOR,EAAO,EAAGoJ,CAAO,EAE7B5I,EAAK,KAAK4I,CAAO,0BAIlB3N,EAAA,GAAAsB,EAAA,CAAYiD,WAAsBA,EAAQQ,EAAK,OAAS,EAAG,CAAC,CAAA,iBAK9C6I,GAAQrJ,EAAA,CAElB,GADJ6I,GAAO,MAAA,EACHnH,EAAU,CAAC,IAAM,UAAA,OAEf,MAAA4H,EAAetJ,IAAU,OAAYA,EAAQQ,EAAK,CAAC,EAAE,eAElD5E,EAAI,EAAGA,EAAI4E,EAAK,OAAQ5E,IAAA,OAC1B8K,EAAMtC,KACZ3I,EAAA,GAAA4K,EAAIK,CAAG,EAAA,CAAM,KAAM,KAAM,MAAO,IAAA,EAAAL,CAAA,EAChC7F,EAAK5E,CAAC,EAAE,OAAO0N,EAAc,EAAK,CAAA,GAAI5C,EAAK,MAAO,EAAA,CAAA,EAGnDhB,EAAQ,OAAO4D,EAAc,EAAa,UAAA5D,EAAQ,OAAS,CAAC,EAAA,kCAKtD,MAAArH,GAAA,EAEN,sBAAA,IAAA,CACC4K,GAAYK,EAAc,EAAI,EACxB,MAAAC,EAAQV,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,YAClDA,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,UAAW,KAAMU,CAAA,CAAA,aAI9CC,GAAqBlB,EAAA,EAE3BmB,IACE,CAAAnB,EAAM,OAAuB,QAAQ,YAAY,GACnDoB,IACE,CAAApB,EAAM,OAAuB,QAAQ,YAAY,UAEpDmB,GAAmB,IAAA,OACnBC,GAAqB,IAAA,GAGtBpB,EAAM,yBAAA,EACC,KAAA,CAAAqB,CAAO,EAAIrB,EAAM,aAAA,EACpBO,GAAO,SAASc,CAAO,SAI3B1D,EAAU,EAAA,OACVsC,GAAc,EAAA,OACdJ,GAAkB,EAAA,EAClByB,KACAjB,EAAc,UACdc,GAAmB,IAAA,OACnBC,GAAqB,IAAA,YAyCbG,GAAeC,EAAA,OACjBC,EAAa,IAAA,oBAEVC,EAAY1K,EAAA,CACf,GAAA,CAAAA,GAAG,QAAQ,QAAA,OAAiBA,EAAE,OAAO,QAAW,SAAA,OAE9C,KAAA,CAAAmF,CAAS,EAAIJ,GAAiB/E,EAAE,OAAO,OAAA,CAAS,IAAK,GAAI,CAAA,GAEzD2K,GAAS,GAAAC,EAAI,EAAIC,GAAU1F,CAAS,EAAE,UAAUnF,EAAE,OAAO,MAAM,OAEtE0H,EAAWV,GACV5E,EAAU,CAAC,IAAM,QAAUuI,GAAK,MAAM,EAAGvI,EAAU,CAAC,CAAA,EAAKuI,EAAA,CAAA,OAG1DtE,EAASuE,EAAA,EACTH,EAAO,oBAAoB,UAAWC,CAAW,EAGlDD,EAAO,iBAAiB,UAAWC,CAAW,EAE9CD,EAAO,WAAWD,CAAI,MAGnBM,GAAW,GAoBXC,GAAA,CAAA,EACAxB,GACA5M,GAEK,SAAAqO,IAAA,OACFC,EAASF,GAAM,KAAKlP,EAAIS,IACtBT,GAAI,aAAe,GAEvB,GAAAoP,EAAO,SAAW,UACb3O,EAAI,EAAGA,EAAI2O,EAAO,OAAQ3O,IAClCiN,GAAO,MAAM,4BACIjN,CAAC,GAAA,GACd2O,EAAO3O,CAAC,EAAI4O,GAAkBD,EAAO,MAAM,IAAA,MAK7CE,GACH9E,EAAO,MAAM,EAAIjJ,EAAaiJ,EAAO,OAAU,EAAE,EAAE,OAAS,GAAK,GAC9D6E,GAAkB,WAEbE,GACR9C,EACA+C,EACAC,EACAzE,EACA+B,EAAA,KAEInB,GAAK,KAKE,GAHPhK,GAAYA,EAAS,CAAC,IAAKyD,GAAQzD,EAAS,CAAC,IAAKyD,EAAKzD,EAAS,CAAC,CAAA,IACpEgK,GAAKvG,EAAKzD,EAAS,CAAC,CAAG,EAAAA,EAAS,CAAC,CAAG,EAAA,IAE1B,OAAAoJ,GAAQ,UAAa,CAAA+B,eAG1B2C,GAAc,CAAA,GAAA,MAAMjD,EAAM,MAAM,EAAE,KAAA,CAAA,KAEpCM,IAAQ,MACX2C,GAAQ,KAAM,CAAAjP,GAAG2J,KAChBqC,EAAMhM,EAAC,EAAEuK,CAAG,EAAE,MAAQyB,EAAMrC,EAAC,EAAEY,CAAG,EAAE,SAAa,CAAA,UAExC+B,IAAQ,MAClB2C,GAAQ,KAAM,CAAAjP,GAAG2J,KAChBqC,EAAMhM,EAAC,EAAEuK,CAAG,EAAE,MAAQyB,EAAMrC,EAAC,EAAEY,CAAG,EAAE,SAAa,CAAA,cAO7C,MAAA2E,GAAA,CAAA,GAAgBlD,CAAK,EACrBmD,GAAqBJ,EAAA,CAAA,GAAqBA,CAAc,EAAI,KAC5DK,GAAeJ,EAAA,CAAA,GAAeA,CAAQ,EAAI,KAW5C,GAVJC,GAAQ,SAASI,GAAeC,KAAA,CAC/BtD,EAAMsD,EAAW,EAAIJ,GAAUG,EAAa,EACxCN,GAAkBI,KACrBJ,EAAeO,EAAW,EAAIH,GAAmBE,EAAa,GAC3DL,GAAYI,KACfJ,EAASM,EAAW,EAAIF,GAAaC,EAAa,6BAKhDlE,GAAA,OACInL,GAAG2J,EAAC,EAAIiC,EAAoBT,EAAE,EACrCtL,EAAA,GAAAsB,EAAA,CAAYnB,GAAG2J,EAAC,CAAA,OAQd4F,GAAa,GAEjB7K,GAAA,IAAA,CACO,MAAA8K,EAAA,IAAe,sBAAsBC,EAASD,IAAAA,CACnDC,EAAQ,QAASC,GAAA,CACZA,EAAM,gBAAmB,CAAAH,KAC5Bb,8BAIDa,GAAaG,EAAM,mBAIrB,OAAAF,EAAS,QAAQvC,EAAM,OAGtBuC,EAAS,WAAA,SAMP3B,GAKO,KAEF,SAAA8B,GAAiBjD,EAAmBlK,EAAa+H,EAAA,CAGxD,GAFDmC,EAAM,gBAAA,EAELmB,IACAA,GAAiB,MAAQrL,GACzBqL,GAAiB,MAAQtD,OAEzBsD,GAAmB,IAAA,aAEb+B,EAAQlD,EAAM,OAAuB,QAAQ,IAAI,EACnD,GAAAkD,EAAA,CACG,MAAAC,EAAOD,EAAK,wBAClB/P,EAAA,GAAAgO,GAAA,CACC,IAAArL,EACA,IAAA+H,EACA,EAAGsF,EAAK,MACR,EAAGA,EAAK,MAAA,CAAA,IAMH,SAAAC,GAAW1L,EAAe2L,EAAA,OAC5BC,EAAYD,IAAa,QAAU3L,EAAQA,EAAQ,EACzDwI,GAAQoD,CAAS,OACjBnC,GAAmB,IAAA,OACnBC,GAAqB,IAAA,EAGb,SAAAmC,GAAW7L,EAAe2L,EAAA,OAC5BG,EAAYH,IAAa,OAAS3L,EAAQA,EAAQ,EACxDqJ,GAAQyC,CAAS,OACjBrC,GAAmB,IAAA,OACnBC,GAAqB,IAAA,EAGb,SAAAqC,IAAA,MACRtC,GAAmB,IAAA,OACnBC,GAAqB,IAAA,EACrBY,KAGDhK,GAAA,KACC,SAAS,iBAAiB,QAASkJ,EAAoB,EACvD,OAAO,iBAAiB,SAAUuC,EAAa,OAE9C,SAAS,oBAAoB,QAASvC,EAAoB,EAC1D,OAAO,oBAAoB,SAAUuC,EAAa,SAIhDC,GAIO,cAEFC,GAAqB9F,EAAA,CACzB6F,IAAe,OAAS,UAAYA,GAAc,MAAQ7F,EAC7D6F,GAAgB,KAEhBA,GAAA,CAAkB,KAAM,SAAU,IAAA7F,CAAA,EAI3B,SAAA+F,GAAmB9N,EAAa+H,EAAA,CAEvC6F,IAAe,OAAS,QACxBA,GAAc,MAAQ5N,GACtB4N,GAAc,MAAQ7F,EAEtB6F,GAAgB,KAEhBA,GAAkB,CAAA,KAAM,OAAQ,IAAA5N,EAAK,IAAA+H,CAAA,MAInCuD,GAIO,KAEF,SAAAyC,GAAmB7D,EAAmBnC,EAAA,CAE1C,GADJmC,EAAM,gBAAA,EACFoB,IAAsBA,GAAmB,MAAQvD,OACpDuD,GAAqB,IAAA,aAEfjP,EAAU6N,EAAM,OAAuB,QAAQ,IAAI,EACrD,GAAA7N,EAAA,CACG,MAAAgR,EAAOhR,EAAO,wBACpBgB,EAAA,GAAAiO,GAAA,CACC,IAAAvD,EACA,EAAGsF,EAAK,MACR,EAAGA,EAAK,MAAA,CAAA,IAMH,SAAA7B,IAAA,MACR7M,EAAW,EAAA,OACXqJ,GAAgB,IAAA,eAOAkE,kDAqEiBD,GAAM9E,CAAC,EAAA7J,2BAjDrBsK,EAAMzF,GAAA,QAAA,IAAA,IAAA,GAAA,+DACbtE,GAAKP,gCAsKE0Q,EAAA,GAAA,UAAA5L,EAAKR,CAAK,EAAEuF,CAAC,EAAE,MAAKjL,CAAA,IAApBkG,EAAKR,CAAK,EAAEuF,CAAC,EAAE,MAAKjL,2DACvB+L,EAAIU,CAAE,EAAE,MAAKzM,CAAA,IAAb+L,EAAIU,CAAE,EAAE,MAAKzM,WAOL,MAAA+R,GAAA,KAAA5Q,EAAA,GAAAb,GAAiB,EAAK,EAAGiO,GAAO,MAAK,GAO1CyD,GAAA,CAAAtM,EAAAuF,EAAA+C,IAAUiD,GAAiBjD,EAAOtI,EAAOuF,CAAC,YA9BnCuC,GAAW9H,EAAOuF,CAAC,aAEvCqD,GAAkB5I,EAAOuF,CAAC,EAC1B2G,GAAmBlM,EAAOuF,CAAC,aAETuC,GAAW9H,EAAOuF,CAAC,kCAzDvByB,EAASpL,CAAC,EAAE,MAAKtB,CAAA,IAAjB0M,EAASpL,CAAC,EAAE,MAAKtB,2CACpB+L,EAAIU,CAAE,EAAE,MAAKzM,CAAA,IAAb+L,EAAIU,CAAE,EAAE,MAAKzM,WAKH,MAAAiS,GAAA3Q,GAAAqN,GAAYrN,CAAC,QAYrB0M,IAAK,CACfA,EAAM,gBAAe,EACrBU,GAAYpN,CAAC,GAkBH4Q,GAAA,CAAA5Q,EAAA0M,IAAU6D,GAAmB7D,EAAO1M,CAAC,SA5ClDqQ,GAAqBrQ,CAAC,kBAhBd4E,EAAIlG,yCAEImQ,GAAYnQ,0BACJkQ,GAAelQ,iDAPlCgF,GAAMuK,GAAe/E,GAAiBxF,EAAE,OAAO,IAAI,CAAA,6CApFnDuJ,GAAMnN,sBAKJ4D,GAAM+I,GAAe/I,CAAC,EAiNXmN,GAAA,IAAAf,GAAWjC,IAAkB,KAAQ,GAAG,OAAO,EAC/CiD,GAAA,IAAAhB,GAAWjC,IAAkB,KAAQ,GAAG,OAAO,EAC7CkD,GAAA,IAAAd,GAAWpC,IAAkB,KAAQ,GAAG,MAAM,EAC7CmD,GAAA,IAAAf,GAAWpC,IAAkB,KAAQ,GAAG,OAAO,EAYlDoD,GAAA,IAAAnB,GAAWjC,IAAkB,KAAQ,GAAG,OAAO,EAC/CqD,GAAA,IAAApB,GAAWjC,IAAkB,KAAQ,GAAG,OAAO,EAC7CsD,GAAA,IAAAlB,GAAWnC,IAAoB,KAAQ,GAAG,MAAM,EAEzEsD,GAAA,IAAAnB,GAAWnC,IAAoB,KAAQ,GAAG,OAAO,2uBAr2B3CnG,GAAOoC,EAAQyB,CAAO,IAC7B3L,EAAA,GAAA+E,EAAOoG,GAAajB,CAA+B,CAAA,OACnDyB,EAAUzB,CAAA,iDApGN5I,IAAa,IAAA,CAAUwG,GAAOxG,EAAUqJ,EAAa,EAAA,CACjD,KAAA,CAAAhI,EAAK+H,CAAG,EAAIpJ,EACd,CAAA,MAAMqB,CAAG,GAAM,CAAA,MAAM+H,CAAG,GAAK3F,EAAKpC,CAAG,IACzCnD,EAAS,SAAA,CACR,MAAA,CAAQmD,EAAK+H,CAAG,EAChB,MAAOD,EAAY9H,EAAK+H,CAAG,EAC3B,UAAW3F,EAAKpC,CAAG,EAAE,IAAK6O,GAAMA,EAAE,KAAK,SAExC7G,GAAgBrJ,CAAA,6BA8EbwG,GAAOmC,EAASuB,CAAW,GAC/BC,6BAiSE1G,GAAQ2H,KAAoBhB,2BAoI7B1L,EAAA,GAAA6J,EAAMF,GAAQ5E,CAAI,CAAA,yBAElB6J,GAAM,CAAC,GAAKC,4BAuEZI,GAAUlK,EAAMjG,EAAeC,EAASuO,GAASD,EAAc,wBAE/DrN,EAAA,GAAAyR,EAAA,CAAA,CAAmBnQ,GAAYA,EAAS,CAAC,CAAA,0qEClgB/B,WAAAlD,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,wPAYTA,EAAQ,EAAA,0DAMPA,EAAW,EAAA,mBAEf,KAAAA,MAAO,oQAtBD,WAAAA,MAAO,YACbG,EAAA,CAAA,EAAA,OAAA,CAAA,KAAAH,MAAO,IAAI,kBACbA,EAAc,EAAA,CAAA,+RAYTA,EAAQ,EAAA,uHAMPA,EAAW,EAAA,oCAEfG,EAAA,CAAA,EAAA,QAAAmT,EAAA,KAAAtT,MAAO,iWA/BL,4CAGE,6CAGK,qZAlHL,GAAA,CAAA,QAAA6L,EAAA,EAAA,EAAArL,GACA,QAAA+S,EAAU,EAAA,EAAA/S,EACV,CAAA,aAAAgT,EAAA,EAAA,EAAAhT,GACA,QAAAsD,EAAU,EAAA,EAAAtD,EACV,CAAA,MAAAC,EAAA,CACV,KAAQ,CAAA,CAAA,GAAI,GAAI,EAAE,CAAA,EAClB,QAAU,CAAA,IAAK,IAAK,GAAG,EACvB,SAAU,SAEPgT,EAAY,IACL,gBAAAC,EAAkB,EAAA,EAAAlT,EAClB,CAAA,UAAAqH,CAAA,EAAArH,EACA,CAAA,UAAAsH,CAAA,EAAAtH,GACA,MAAAmL,EAAuB,IAAA,EAAAnL,GACvB,WAAAoL,EAAa,EAAA,EAAApL,EACb,CAAA,KAAAuL,CAAA,EAAAvL,EACA,CAAA,SAAAK,CAAA,EAAAL,GACA,MAAAmT,EAAuB,IAAA,EAAAnT,GACvB,UAAAoT,EAAgC,MAAA,EAAApT,EAChC,CAAA,KAAAW,CAAA,EAAAX,GAEA,YAAAS,EAAc,EAAA,EAAAT,EACd,CAAA,cAAAwL,EAAA,EAAA,EAAAxL,EACA,CAAA,OAAAqT,CAAA,EAAArT,EAMA,CAAA,iBAAAM,CAAA,EAAAN,GAKA,WAAAqC,EAAiC,MAAA,EAAArC,EAEjC,CAAA,eAAAsT,CAAA,EAAAtT,EACA,CAAA,YAAAuT,CAAA,EAAAvT,EAEP2M,EACAzM,EACAC,EACAmL,kBACWkI,EAAcrN,EAAA,CAKxB,IAAAoH,GAAQpH,GAAQlG,OAEpB0M,EAAgB,CAAA,GAAAY,GAAM,SAAWlC,CAAQ,CAAA,OACzCC,GAASiC,GAAM,KAAW,CAAA,GAAAA,GAAM,IAAI,EAAA,CAAA,CAAA,EACpCnM,EAAA,GAAAlB,EAAgBqN,IAAO,UAAU,cAC1B,CAAA,GAAAA,IAAO,UAAU,aAAa,EAClC,IAAA,OACHpN,EACE,CAAAoT,GAAehG,IAAO,UAAU,QAC1B,CAAA,GAAAA,IAAO,UAAU,OAAO,EAC5B,IAAA,EACE,MAAAvJ,GAAA,EAENqP,EAAO,SAAS,QAAQ,EACnBH,GACJG,EAAO,SAAS,OAAO,EAIzBG,IAEAC,GAAA,IAAA,MACCP,EAAkB,EAAA,KAWjB,MAAM,QAAQjT,CAAK,GAAKA,IAAQ,CAAC,GAAG,SAAW,GAChDA,EAAM,OAAO,CAAC,GAAG,SAAW,KAE5BA,EAAA,CACC,KAAA,CAAO,MAAMoH,IAAY,CAAC,GAAK,CAAC,EAAE,KAAK,EAAE,CAAA,EACzC,QAAS,MAAMA,IAAY,CAAC,GAAK,CAAC,EAChC,KAAK,EAAE,EACP,IAAA,CAAK+E,EAAG7K,KAAA,GAASA,GAAI,CAAC,EAAA,EACxB,SAAU,sBAIGmS,GAAoBvN,EAAA,CAK9B,KAAK,UAAUA,CAAI,IAAM8M,SAC5BhT,EAAa,CAAA,GAAAkG,CAAA,CAAA,OACb8M,EAAY,KAAK,UAAUhT,CAAK,CAAA,EAChCuT,EAAcrN,CAAI,GAmBI,MAAAwN,GAAA,IAAAN,EAAO,SAAS,eAAgBC,CAAc,EAsBzDlB,EAAA,IAAAwB,IAASP,EAAO,OAAO,UAAUO,CAAI,EAC7BvB,EAAA,IAAAuB,IAASP,EAAO,OAAO,UAAUO,CAAI,EAX7CC,EAAA5O,GAAMyO,GAAoBzO,EAAE,MAAM,IAClCA,GAAMoO,EAAO,SAAS,SAAUpO,EAAE,MAAM,k2BA3DhDgO,GAAa,KAAK,UAAUhT,CAAK,IAAMgT,SAC1CA,EAAY,KAAK,UAAUhT,CAAK,CAAA,EAChCuT"}