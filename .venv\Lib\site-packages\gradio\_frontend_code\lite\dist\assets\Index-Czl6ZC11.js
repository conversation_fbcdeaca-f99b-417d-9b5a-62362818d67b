import{a as ce,i as fe,s as _e,f as w,c as U,x as I,p as E,E as De,q as H,$ as R,m as J,l as N,F as T,t as G,y as he,b as Y,z as de,o as j,d as K,P as me,A as Xe,a3 as qe,D as Pe,aA as Te,k as Ge,n as Le,ao as x,v as M,M as $,N as Ye,aq as Ae,r as ee,G as Ie,H as te,I as W,B as Ne,Y as je,S as Fe,a5 as se,a0 as Ue,a4 as Je,a6 as le}from"../lite.js";import{t as V,E as Ke}from"./tinycolor-DQkyKtGc.js";import{B as Oe}from"./BlockTitle-BlPSRItZ.js";import{default as ft}from"./Example-DBvB9epk.js";import"./Info-2h-pcw07.js";import"./MarkdownCode-C6RHM0m6.js";function Qe(t,e){const s=l=>{t&&!t.contains(l.target)&&!l.defaultPrevented&&e(l)};return document.addEventListener("mousedown",s,!0),{destroy(){document.removeEventListener("mousedown",s,!0)}}}function ne(t){const e=t.s,s=t.v;let l=e*s;const u=t.h/60;let a=l*(1-Math.abs(u%2-1));const f=s-l;l=l+f,a=a+f;const b=Math.floor(u)%6,v=[l,a,f,f,a,l][b],_=[a,l,l,a,f,f][b],r=[f,f,a,l,l,a][b];return`rgba(${v*255}, ${_*255}, ${r*255}, ${t.a})`}function Ve(t,e){return e==="hex"?V(t).toHexString():e==="rgb"?V(t).toRgbString():V(t).toHslString()}const{window:ie}=Te;function oe(t,e,s){const l=t.slice();return l[7]=e[s][0],l[1]=e[s][1],l}function We(t){let e;return{c(){e=Ge(t[7])},m(s,l){N(s,e,l)},p(s,l){l[0]&128&&Le(e,s[7])},d(s){s&&j(e)}}}function ue(t){let e,s,l,u=`translate(${t[12][0]}px,${t[12][1]}px)`,a,f,b,v=`translateX(${t[14]}px)`,_,r,c,i,m,p,g,C,S,k,q,P,L,A,d=t[9]&&ae(),D=x(t[21]),z=[];for(let h=0;h<D.length;h+=1)z[h]=re(oe(t,D,h));return{c(){e=E("div"),s=E("div"),l=E("div"),a=I(),f=E("div"),b=E("div"),_=I(),r=E("div"),c=E("button"),i=I(),m=E("div"),p=E("div"),g=E("input"),C=I(),S=E("button"),d&&d.c(),k=I(),q=E("div");for(let h=0;h<z.length;h+=1)z[h].c();H(l,"class","marker svelte-1oxhzww"),R(l,"transform",u),R(l,"background",t[1]),H(s,"class","color-gradient svelte-1oxhzww"),R(s,"--hue",t[13]),H(b,"class","marker svelte-1oxhzww"),R(b,"background","hsl("+t[13]+", 100%, 50%)"),R(b,"transform",v),H(f,"class","hue-slider svelte-1oxhzww"),H(c,"class","swatch svelte-1oxhzww"),R(c,"background",t[1]),H(g,"type","text"),H(g,"class","svelte-1oxhzww"),H(S,"class","eyedropper svelte-1oxhzww"),H(p,"class","input-wrap svelte-1oxhzww"),H(q,"class","buttons svelte-1oxhzww"),H(r,"class","input svelte-1oxhzww"),H(e,"class","color-picker svelte-1oxhzww")},m(h,n){N(h,e,n),M(e,s),M(s,l),t[28](s),M(e,a),M(e,f),M(f,b),t[29](f),M(e,_),M(e,r),M(r,c),M(r,i),M(r,m),M(m,p),M(p,g),$(g,t[8]),M(p,C),M(p,S),d&&d.m(S,null),M(m,k),M(m,q);for(let B=0;B<z.length;B+=1)z[B]&&z[B].m(q,null);P=!0,L||(A=[T(s,"mousedown",t[16]),T(f,"mousedown",t[15]),T(c,"click",t[23]),T(g,"input",t[30]),T(g,"change",t[31]),T(S,"click",t[20]),T(e,"focus",t[25]),T(e,"blur",t[26]),Ye(Qe.call(null,e,t[22]))],L=!0)},p(h,n){if(n[0]&4096&&u!==(u=`translate(${h[12][0]}px,${h[12][1]}px)`)&&R(l,"transform",u),n[0]&2&&R(l,"background",h[1]),(!P||n[0]&8192)&&R(s,"--hue",h[13]),n[0]&8192&&R(b,"background","hsl("+h[13]+", 100%, 50%)"),n[0]&16384&&v!==(v=`translateX(${h[14]}px)`)&&R(b,"transform",v),n[0]&2&&R(c,"background",h[1]),n[0]&256&&g.value!==h[8]&&$(g,h[8]),h[9]?d?n[0]&512&&G(d,1):(d=ae(),d.c(),G(d,1),d.m(S,null)):d&&(he(),Y(d,1,1,()=>{d=null}),de()),n[0]&2097153){D=x(h[21]);let B;for(B=0;B<D.length;B+=1){const O=oe(h,D,B);z[B]?z[B].p(O,n):(z[B]=re(O),z[B].c(),z[B].m(q,null))}for(;B<z.length;B+=1)z[B].d(1);z.length=D.length}},i(h){P||(G(d),P=!0)},o(h){Y(d),P=!1},d(h){h&&j(e),t[28](null),t[29](null),d&&d.d(),Ae(z,h),L=!1,me(A)}}}function ae(t){let e,s;return e=new Ke({}),{c(){U(e.$$.fragment)},m(l,u){J(e,l,u),s=!0},i(l){s||(G(e.$$.fragment,l),s=!0)},o(l){Y(e.$$.fragment,l),s=!1},d(l){K(e,l)}}}function re(t){let e,s,l;function u(){return t[32](t[1])}return{c(){e=E("button"),e.textContent=`${t[7]}`,H(e,"class","button svelte-1oxhzww"),ee(e,"active",t[0]===t[1])},m(a,f){N(a,e,f),s||(l=T(e,"click",u),s=!0)},p(a,f){t=a,f[0]&2097153&&ee(e,"active",t[0]===t[1])},d(a){a&&j(e),s=!1,l()}}}function Ze(t){let e,s,l,u,a,f,b,v;e=new Oe({props:{root:t[6],show_label:t[5],info:t[3],$$slots:{default:[We]},$$scope:{ctx:t}}});let _=t[2]&&ue(t);return{c(){U(e.$$.fragment),s=I(),l=E("button"),u=I(),_&&_.c(),a=De(),H(l,"class","dialog-button svelte-1oxhzww"),l.disabled=t[4],R(l,"background",t[1])},m(r,c){J(e,r,c),N(r,s,c),N(r,l,c),N(r,u,c),_&&_.m(r,c),N(r,a,c),f=!0,b||(v=[T(ie,"mousemove",t[17]),T(ie,"mouseup",t[18]),T(l,"click",t[27])],b=!0)},p(r,c){const i={};c[0]&64&&(i.root=r[6]),c[0]&32&&(i.show_label=r[5]),c[0]&8&&(i.info=r[3]),c[0]&128|c[1]&8192&&(i.$$scope={dirty:c,ctx:r}),e.$set(i),(!f||c[0]&16)&&(l.disabled=r[4]),c[0]&2&&R(l,"background",r[1]),r[2]?_?(_.p(r,c),c[0]&4&&G(_,1)):(_=ue(r),_.c(),G(_,1),_.m(a.parentNode,a)):_&&(he(),Y(_,1,1,()=>{_=null}),de())},i(r){f||(G(e.$$.fragment,r),G(_),f=!0)},o(r){Y(e.$$.fragment,r),Y(_),f=!1},d(r){r&&(j(s),j(l),j(u),j(a)),K(e,r),_&&_.d(r),b=!1,me(v)}}}function ye(t,e,s){let l,{value:u="#000000"}=e,{value_is_output:a=!1}=e,{label:f}=e,{info:b=void 0}=e,{disabled:v=!1}=e,{show_label:_=!0}=e,{root:r}=e,{current_mode:c="hex"}=e,{dialog_open:i=!1}=e,m=!1,p,g;const C=Xe();let S=[0,0],k=null,q=!1,P=[0,0],L=0,A=0,d=null,D=!1;function z(o){d=o.currentTarget.getBoundingClientRect(),D=!0,h(o.clientX)}function h(o){if(!d)return;const X=Math.max(0,Math.min(o-d.left,d.width));s(14,A=X);const F=X/d.width*360;s(13,L=F),s(1,u=ne({h:F,s:P[0],v:P[1],a:1}))}function n(o,X){if(!k)return;const F=Math.max(0,Math.min(o-k.left,k.width)),Q=Math.max(0,Math.min(X-k.top,k.height));s(12,S=[F,Q]);const y={h:L*1,s:F/k.width,v:1-Q/k.height,a:1};P=[y.s,y.v],s(1,u=ne(y))}function B(o){q=!0,k=o.currentTarget.getBoundingClientRect(),n(o.clientX,o.clientY)}function O(o){q&&n(o.clientX,o.clientY),D&&h(o.clientX)}function ge(){q=!1,D=!1}async function Z(o){if(q||D||(await Ie(),!o)||(!k&&p&&(k=p.getBoundingClientRect()),!d&&g&&(d=g.getBoundingClientRect()),!k||!d))return;const X=V(o).toHsv(),F=X.s*k.width,Q=(1-X.v)*k.height;s(12,S=[F,Q]),P=[X.s,X.v],s(13,L=X.h),s(14,A=X.h/360*d.width)}function be(){new EyeDropper().open().then(X=>{s(1,u=X.sRGBHex)})}const we=[["Hex","hex"],["RGB","rgb"],["HSL","hsl"]];qe(async()=>{s(9,m=window!==void 0&&!!window.EyeDropper)});function ve(){s(2,i=!1)}function ke(){C("change",u),a||C("input")}Pe(()=>{s(24,a=!1)});function ze(){C("selected",l),C("close")}function Be(o){te.call(this,t,o)}function pe(o){te.call(this,t,o)}const Ce=()=>{Z(u),s(2,i=!i)};function Me(o){W[o?"unshift":"push"](()=>{p=o,s(10,p)})}function Ee(o){W[o?"unshift":"push"](()=>{g=o,s(11,g)})}function He(){l=this.value,s(8,l),s(1,u),s(0,c)}const Re=o=>s(1,u=o.currentTarget.value),Se=o=>s(0,c=o);return t.$$set=o=>{"value"in o&&s(1,u=o.value),"value_is_output"in o&&s(24,a=o.value_is_output),"label"in o&&s(7,f=o.label),"info"in o&&s(3,b=o.info),"disabled"in o&&s(4,v=o.disabled),"show_label"in o&&s(5,_=o.show_label),"root"in o&&s(6,r=o.root),"current_mode"in o&&s(0,c=o.current_mode),"dialog_open"in o&&s(2,i=o.dialog_open)},t.$$.update=()=>{t.$$.dirty[0]&3&&s(8,l=Ve(u,c)),t.$$.dirty[0]&256&&l&&C("selected",l),t.$$.dirty[0]&2&&Z(u),t.$$.dirty[0]&2&&ke()},[c,u,i,b,v,_,r,f,l,m,p,g,S,L,A,z,B,O,ge,Z,be,we,ve,ze,a,Be,pe,Ce,Me,Ee,He,Re,Se]}class xe extends ce{constructor(e){super(),fe(this,e,ye,Ze,_e,{value:1,value_is_output:24,label:7,info:3,disabled:4,show_label:5,root:6,current_mode:0,dialog_open:2},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),w()}get value_is_output(){return this.$$.ctx[24]}set value_is_output(e){this.$$set({value_is_output:e}),w()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),w()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),w()}get disabled(){return this.$$.ctx[4]}set disabled(e){this.$$set({disabled:e}),w()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),w()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),w()}get current_mode(){return this.$$.ctx[0]}set current_mode(e){this.$$set({current_mode:e}),w()}get dialog_open(){return this.$$.ctx[2]}set dialog_open(e){this.$$set({dialog_open:e}),w()}}const $e=xe;function et(t){let e,s,l,u,a,f;const b=[{autoscroll:t[13].autoscroll},{i18n:t[13].i18n},t[11]];let v={};for(let i=0;i<b.length;i+=1)v=je(v,b[i]);e=new Fe({props:v}),e.$on("clear_status",t[16]);function _(i){t[17](i)}function r(i){t[18](i)}let c={root:t[12],label:t[2],info:t[3],show_label:t[7],disabled:!t[14]||t[15]};return t[0]!==void 0&&(c.value=t[0]),t[1]!==void 0&&(c.value_is_output=t[1]),l=new $e({props:c}),W.push(()=>se(l,"value",_)),W.push(()=>se(l,"value_is_output",r)),l.$on("change",t[19]),l.$on("input",t[20]),l.$on("submit",t[21]),l.$on("blur",t[22]),l.$on("focus",t[23]),{c(){U(e.$$.fragment),s=I(),U(l.$$.fragment)},m(i,m){J(e,i,m),N(i,s,m),J(l,i,m),f=!0},p(i,m){const p=m&10240?Ue(b,[m&8192&&{autoscroll:i[13].autoscroll},m&8192&&{i18n:i[13].i18n},m&2048&&Je(i[11])]):{};e.$set(p);const g={};m&4096&&(g.root=i[12]),m&4&&(g.label=i[2]),m&8&&(g.info=i[3]),m&128&&(g.show_label=i[7]),m&49152&&(g.disabled=!i[14]||i[15]),!u&&m&1&&(u=!0,g.value=i[0],le(()=>u=!1)),!a&&m&2&&(a=!0,g.value_is_output=i[1],le(()=>a=!1)),l.$set(g)},i(i){f||(G(e.$$.fragment,i),G(l.$$.fragment,i),f=!0)},o(i){Y(e.$$.fragment,i),Y(l.$$.fragment,i),f=!1},d(i){i&&j(s),K(e,i),K(l,i)}}}function tt(t){let e,s;return e=new Ne({props:{visible:t[6],elem_id:t[4],elem_classes:t[5],container:t[8],scale:t[9],min_width:t[10],$$slots:{default:[et]},$$scope:{ctx:t}}}),{c(){U(e.$$.fragment)},m(l,u){J(e,l,u),s=!0},p(l,[u]){const a={};u&64&&(a.visible=l[6]),u&16&&(a.elem_id=l[4]),u&32&&(a.elem_classes=l[5]),u&256&&(a.container=l[8]),u&512&&(a.scale=l[9]),u&1024&&(a.min_width=l[10]),u&16840847&&(a.$$scope={dirty:u,ctx:l}),e.$set(a)},i(l){s||(G(e.$$.fragment,l),s=!0)},o(l){Y(e.$$.fragment,l),s=!1},d(l){K(e,l)}}}function st(t,e,s){let{label:l="ColorPicker"}=e,{info:u=void 0}=e,{elem_id:a=""}=e,{elem_classes:f=[]}=e,{visible:b=!0}=e,{value:v}=e,{value_is_output:_=!1}=e,{show_label:r}=e,{container:c=!0}=e,{scale:i=null}=e,{min_width:m=void 0}=e,{loading_status:p}=e,{root:g}=e,{gradio:C}=e,{interactive:S}=e,{disabled:k=!1}=e;const q=()=>C.dispatch("clear_status",p);function P(n){v=n,s(0,v)}function L(n){_=n,s(1,_)}const A=()=>C.dispatch("change"),d=()=>C.dispatch("input"),D=()=>C.dispatch("submit"),z=()=>C.dispatch("blur"),h=()=>C.dispatch("focus");return t.$$set=n=>{"label"in n&&s(2,l=n.label),"info"in n&&s(3,u=n.info),"elem_id"in n&&s(4,a=n.elem_id),"elem_classes"in n&&s(5,f=n.elem_classes),"visible"in n&&s(6,b=n.visible),"value"in n&&s(0,v=n.value),"value_is_output"in n&&s(1,_=n.value_is_output),"show_label"in n&&s(7,r=n.show_label),"container"in n&&s(8,c=n.container),"scale"in n&&s(9,i=n.scale),"min_width"in n&&s(10,m=n.min_width),"loading_status"in n&&s(11,p=n.loading_status),"root"in n&&s(12,g=n.root),"gradio"in n&&s(13,C=n.gradio),"interactive"in n&&s(14,S=n.interactive),"disabled"in n&&s(15,k=n.disabled)},[v,_,l,u,a,f,b,r,c,i,m,p,g,C,S,k,q,P,L,A,d,D,z,h]}class at extends ce{constructor(e){super(),fe(this,e,st,tt,_e,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,value:0,value_is_output:1,show_label:7,container:8,scale:9,min_width:10,loading_status:11,root:12,gradio:13,interactive:14,disabled:15})}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),w()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),w()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),w()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),w()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),w()}get container(){return this.$$.ctx[8]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),w()}get loading_status(){return this.$$.ctx[11]}set loading_status(e){this.$$set({loading_status:e}),w()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),w()}get gradio(){return this.$$.ctx[13]}set gradio(e){this.$$set({gradio:e}),w()}get interactive(){return this.$$.ctx[14]}set interactive(e){this.$$set({interactive:e}),w()}get disabled(){return this.$$.ctx[15]}set disabled(e){this.$$set({disabled:e}),w()}}export{$e as BaseColorPicker,ft as BaseExample,at as default};
//# sourceMappingURL=Index-Czl6ZC11.js.map
