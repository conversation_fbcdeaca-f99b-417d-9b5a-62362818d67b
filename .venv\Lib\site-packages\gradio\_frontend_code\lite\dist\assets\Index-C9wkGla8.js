import{a as Pe,i as Fe,s as Je,f as z,x as Y,p as F,q as A,r as V,l as I,F as x,a1 as Ol,y as ve,b as U,z as pe,t as O,o as j,P as Te,A as Yt,M as At,N as Xt,H as ot,I as _e,k as Me,n as Ne,w as qe,c as oe,m as re,d as ae,E as Ge,bb as Pl,e as at,J as zt,$ as le,v as S,a2 as Et,bl as xt,u as ut,h as ft,j as _t,a3 as xe,ao as ze,b4 as Ve,b9 as Ze,G as De,bm as $t,Q as Oe,a5 as Se,a6 as Ce,aA as Fl,B as Jl,D as Ul,Y as Wl,S as Il,a0 as jl,a4 as Kl}from"../lite.js";import{d as be}from"./index-CnqicUFC.js";import{a as Vl}from"./utils-BsGrhMNe.js";import{U as Zl}from"./Upload-TGDabKXH.js";import{M as Gl}from"./MarkdownCode-C6RHM0m6.js";import{d as Ql}from"./dsv-DB8NKgIY.js";import{default as Fn}from"./Example-DieF8QRJ.js";/* empty css                                             */function Lt(n){let e,t,l;return{c(){e=F("input"),A(e,"role","textbox"),A(e,"tabindex","-1"),A(e,"class","svelte-z9gpua"),V(e,"header",n[5])},m(s,a){I(s,e,a),n[19](e),At(e,n[11]),t||(l=[x(e,"input",n[20]),x(e,"blur",n[13]),Xt(n[12].call(null,e)),x(e,"keydown",n[18])],t=!0)},p(s,a){a&2048&&e.value!==s[11]&&At(e,s[11]),a&32&&V(e,"header",s[5])},d(s){s&&j(e),n[19](null),t=!1,Te(l)}}}function Yl(n){let e=(n[9]?n[0]:n[3]||n[0])+"",t;return{c(){t=Me(e)},m(l,s){I(l,t,s)},p(l,s){s&521&&e!==(e=(l[9]?l[0]:l[3]||l[0])+"")&&Ne(t,e)},i:qe,o:qe,d(l){l&&j(t)}}}function Xl(n){let e,t;return e=new Gl({props:{message:n[0].toLocaleString(),latex_delimiters:n[7],line_breaks:n[8],chatbot:!1,root:n[10]}}),{c(){oe(e.$$.fragment)},m(l,s){re(e,l,s),t=!0},p(l,s){const a={};s&1&&(a.message=l[0].toLocaleString()),s&128&&(a.latex_delimiters=l[7]),s&256&&(a.line_breaks=l[8]),s&1024&&(a.root=l[10]),e.$set(a)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function xl(n){let e,t;return{c(){e=new Pl(!1),t=Ge(),e.a=t},m(l,s){e.m(n[0],l,s),I(l,t,s)},p(l,s){s&1&&e.p(l[0])},i:qe,o:qe,d(l){l&&(j(t),e.d())}}}function $l(n){let e,t,l,s,a,_,i,o=n[2]&&Lt(n);const d=[xl,Xl,Yl],u=[];function c(g,w){return g[6]==="html"?0:g[6]==="markdown"?1:2}return l=c(n),s=u[l]=d[l](n),{c(){o&&o.c(),e=Y(),t=F("span"),s.c(),A(t,"tabindex","-1"),A(t,"role","button"),A(t,"style",n[4]),A(t,"class","svelte-z9gpua"),V(t,"edit",n[2])},m(g,w){o&&o.m(g,w),I(g,e,w),I(g,t,w),u[l].m(t,null),a=!0,_||(i=[x(t,"dblclick",n[16]),x(t,"focus",Ol(n[17]))],_=!0)},p(g,[w]){g[2]?o?o.p(g,w):(o=Lt(g),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null);let B=l;l=c(g),l===B?u[l].p(g,w):(ve(),U(u[B],1,1,()=>{u[B]=null}),pe(),s=u[l],s?s.p(g,w):(s=u[l]=d[l](g),s.c()),O(s,1),s.m(t,null)),(!a||w&16)&&A(t,"style",g[4]),(!a||w&4)&&V(t,"edit",g[2])},i(g){a||(O(s),a=!0)},o(g){U(s),a=!1},d(g){g&&(j(e),j(t)),o&&o.d(g),u[l].d(),_=!1,Te(i)}}}function en(n,e,t){let l,{edit:s}=e,{value:a=""}=e,{display_value:_=null}=e,{styling:i=""}=e,{header:o=!1}=e,{datatype:d="str"}=e,{latex_delimiters:u}=e,{clear_on_focus:c=!1}=e,{select_on_focus:g=!1}=e,{line_breaks:w=!0}=e,{editable:B=!0}=e,{root:y}=e;const k=Yt();let{el:R}=e;function P(v){return c&&t(11,l=""),g&&v.select(),v.focus(),{}}function D({currentTarget:v}){t(0,a=v.value),k("blur")}function N(v){ot.call(this,n,v)}function q(v){ot.call(this,n,v)}function b(v){ot.call(this,n,v)}function E(v){_e[v?"unshift":"push"](()=>{R=v,t(1,R)})}function W(){l=this.value,t(11,l),t(0,a)}return n.$$set=v=>{"edit"in v&&t(2,s=v.edit),"value"in v&&t(0,a=v.value),"display_value"in v&&t(3,_=v.display_value),"styling"in v&&t(4,i=v.styling),"header"in v&&t(5,o=v.header),"datatype"in v&&t(6,d=v.datatype),"latex_delimiters"in v&&t(7,u=v.latex_delimiters),"clear_on_focus"in v&&t(14,c=v.clear_on_focus),"select_on_focus"in v&&t(15,g=v.select_on_focus),"line_breaks"in v&&t(8,w=v.line_breaks),"editable"in v&&t(9,B=v.editable),"root"in v&&t(10,y=v.root),"el"in v&&t(1,R=v.el)},n.$$.update=()=>{n.$$.dirty&1&&t(11,l=a)},[a,R,s,_,i,o,d,u,w,B,y,l,P,D,c,g,N,q,b,E,W]}class et extends Pe{constructor(e){super(),Fe(this,e,en,$l,Je,{edit:2,value:0,display_value:3,styling:4,header:5,datatype:6,latex_delimiters:7,clear_on_focus:14,select_on_focus:15,line_breaks:8,editable:9,root:10,el:1})}get edit(){return this.$$.ctx[2]}set edit(e){this.$$set({edit:e}),z()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),z()}get display_value(){return this.$$.ctx[3]}set display_value(e){this.$$set({display_value:e}),z()}get styling(){return this.$$.ctx[4]}set styling(e){this.$$set({styling:e}),z()}get header(){return this.$$.ctx[5]}set header(e){this.$$set({header:e}),z()}get datatype(){return this.$$.ctx[6]}set datatype(e){this.$$set({datatype:e}),z()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),z()}get clear_on_focus(){return this.$$.ctx[14]}set clear_on_focus(e){this.$$set({clear_on_focus:e}),z()}get select_on_focus(){return this.$$.ctx[15]}set select_on_focus(e){this.$$set({select_on_focus:e}),z()}get line_breaks(){return this.$$.ctx[8]}set line_breaks(e){this.$$set({line_breaks:e}),z()}get editable(){return this.$$.ctx[9]}set editable(e){this.$$set({editable:e}),z()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),z()}get el(){return this.$$.ctx[1]}set el(e){this.$$set({el:e}),z()}}const tn=n=>({}),Bt=n=>({});function Dt(n,e,t){const l=n.slice();return l[36]=e[t],l}const ln=n=>({item:n[0]&256,index:n[0]&256}),Mt=n=>({item:n[36].data,index:n[36].index}),nn=n=>({}),Rt=n=>({});function St(n){let e=[],t=new Map,l,s,a=ze(n[8]);const _=i=>i[36].data[0].id;for(let i=0;i<a.length;i+=1){let o=Dt(n,a,i),d=_(o);t.set(d,e[i]=Ct(d,o))}return{c(){for(let i=0;i<e.length;i+=1)e[i].c();l=Ge()},m(i,o){for(let d=0;d<e.length;d+=1)e[d]&&e[d].m(i,o);I(i,l,o),s=!0},p(i,o){o[0]&1048832&&(a=ze(i[8]),ve(),e=Ve(e,o,_,1,i,a,t,l.parentNode,Ze,Ct,l,Dt),pe())},i(i){if(!s){for(let o=0;o<a.length;o+=1)O(e[o]);s=!0}},o(i){for(let o=0;o<e.length;o+=1)U(e[o]);s=!1},d(i){i&&j(l);for(let o=0;o<e.length;o+=1)e[o].d(i)}}}function sn(n){let e;return{c(){e=Me(`Missing Table Row
					`)},m(t,l){I(t,e,l)},d(t){t&&j(e)}}}function Ct(n,e){let t,l;const s=e[21].tbody,a=at(s,e,e[20],Mt),_=a||sn();return{key:n,first:null,c(){t=Ge(),_&&_.c(),this.first=t},m(i,o){I(i,t,o),_&&_.m(i,o),l=!0},p(i,o){e=i,a&&a.p&&(!l||o[0]&1048832)&&ut(a,s,e,e[20],l?_t(s,e[20],o,ln):ft(e[20]),Mt)},i(i){l||(O(_,i),l=!0)},o(i){U(_,i),l=!1},d(i){i&&j(t),_&&_.d(i)}}}function on(n){let e,t,l,s,a,_,i,o,d,u,c,g,w;const B=n[21].thead,y=at(B,n,n[20],Rt);let k=n[8].length&&n[8][0].data.length&&St(n);const R=n[21].tfoot,P=at(R,n,n[20],Bt);return{c(){e=F("svelte-virtual-table-viewport"),t=F("table"),l=F("thead"),y&&y.c(),a=Y(),_=F("tbody"),k&&k.c(),i=Y(),o=F("tfoot"),P&&P.c(),A(l,"class","thead svelte-1xyl3gk"),zt(()=>n[22].call(l)),A(_,"class","tbody svelte-1xyl3gk"),A(o,"class","tfoot svelte-1xyl3gk"),zt(()=>n[24].call(o)),A(t,"class","table svelte-1xyl3gk"),le(t,"height",rn),le(t,"--bw-svt-p-top",n[6]+"px"),le(t,"--bw-svt-p-bottom",n[2]+"px"),le(t,"--bw-svt-head-height",n[4]+"px"),le(t,"--bw-svt-foot-height",n[5]+"px"),le(t,"--bw-svt-avg-row-height",n[0]+"px")},m(D,N){I(D,e,N),S(e,t),S(t,l),y&&y.m(l,null),s=Et(l,n[22].bind(l)),S(t,a),S(t,_),k&&k.m(_,null),n[23](_),S(t,i),S(t,o),P&&P.m(o,null),d=Et(o,n[24].bind(o)),n[25](t),u=xt.observe(t,n[26].bind(t)),c=!0,g||(w=x(t,"scroll",n[9]),g=!0)},p(D,N){y&&y.p&&(!c||N[0]&1048576)&&ut(y,B,D,D[20],c?_t(B,D[20],N,nn):ft(D[20]),Rt),D[8].length&&D[8][0].data.length?k?(k.p(D,N),N[0]&256&&O(k,1)):(k=St(D),k.c(),O(k,1),k.m(_,null)):k&&(ve(),U(k,1,1,()=>{k=null}),pe()),P&&P.p&&(!c||N[0]&1048576)&&ut(P,R,D,D[20],c?_t(R,D[20],N,tn):ft(D[20]),Bt),(!c||N[0]&64)&&le(t,"--bw-svt-p-top",D[6]+"px"),(!c||N[0]&4)&&le(t,"--bw-svt-p-bottom",D[2]+"px"),(!c||N[0]&16)&&le(t,"--bw-svt-head-height",D[4]+"px"),(!c||N[0]&32)&&le(t,"--bw-svt-foot-height",D[5]+"px"),(!c||N[0]&1)&&le(t,"--bw-svt-avg-row-height",D[0]+"px")},i(D){c||(O(y,D),O(k),O(P,D),c=!0)},o(D){U(y,D),U(k),U(P,D),c=!1},d(D){D&&j(e),y&&y.d(D),s(),k&&k.d(),n[23](null),P&&P.d(D),d(),n[25](null),u(),g=!1,w()}}}let rn="100%";function an(n,e){if(!n)return 0;const t=getComputedStyle(n);return parseInt(t.getPropertyValue(e))}function un(n,e,t){let l,{$$slots:s={},$$scope:a}=e,{items:_=[]}=e,{max_height:i}=e,{actual_height:o}=e,{table_scrollbar_width:d}=e,{start:u=0}=e,{end:c=20}=e,{selected:g}=e,w=30,B=0,y,k=0,R=0,P=[],D,N,q=0,b,E=200,W=[],v;const H=typeof window<"u",M=H?window.requestAnimationFrame:C=>C();let X=0;async function ie(C){if(E===0)return;const{scrollTop:$}=b;t(13,d=b.offsetWidth-b.clientWidth),X=q-($-k);let ne=u;for(;X<i&&ne<C.length;){let Z=N[ne-u];Z||(t(11,c=ne+1),await De(),Z=N[ne-u]);let fe=Z?.getBoundingClientRect().height;fe||(fe=w);const ke=P[ne]=fe;X+=ke,ne+=1}t(11,c=ne);const we=C.length-c,ue=b.offsetHeight-b.clientHeight;ue>0&&(X+=ue);let de=P.filter(Z=>typeof Z=="number");t(0,w=de.reduce((Z,fe)=>Z+fe,0)/de.length),t(2,B=we*w),P.length=C.length,await De(),i?X<i?t(12,o=X+2):t(12,o=i):t(12,o=X+1),await De()}async function G(C){M(async()=>{if(typeof C!="number")return;const $=typeof C!="number"?!1:Ae(C);$!==!0&&($==="back"&&await J(C,{behavior:"instant"}),$==="forwards"&&await J(C,{behavior:"instant"},!0))})}function Ae(C){const $=N&&N[C-u];if(!$&&C<u)return"back";if(!$&&C>=c-1)return"forwards";const{top:ne}=b.getBoundingClientRect(),{top:we,bottom:ue}=$.getBoundingClientRect();return we-ne<37?"back":ue-ne>E?"forwards":!0}async function Re(C){const $=b.scrollTop;N=y.children;const ne=l.length<u,we=an(N[1],"border-top-width"),ue=0;ne&&await J(l.length-1,{behavior:"auto"});let de=0;for(let ee=0;ee<N.length;ee+=1)P[u+ee]=N[ee].getBoundingClientRect().height;let Z=0,fe=k+we/2,ke=[];for(;Z<l.length;){const ee=P[Z]||w;if(ke[Z]=ee,fe+ee+ue>$){de=Z,t(6,q=fe-(k+we/2));break}fe+=ee,Z+=1}for(de=Math.max(0,de);Z<l.length;){const ee=P[Z]||w;if(fe+=ee,Z+=1,fe>$+E)break}t(10,u=de),t(11,c=Z);const He=l.length-c;c===0&&t(11,c=10),t(0,w=(fe-k)/c);let Qe=He*w;for(;Z<l.length;)Z+=1,P[Z]=w;t(2,B=Qe),isFinite(B)||t(2,B=2e5)}async function J(C,$,ne=!1){await De();const we=w;let ue=C*we;ne&&(ue=ue-E+we+k);const de=b.offsetHeight-b.clientHeight;de>0&&(ue+=de);const Z={top:ue,behavior:"smooth",...$};b.scrollTo(Z)}xe(()=>{N=y.children,t(18,D=!0),ie(_)});function Q(){k=this.offsetHeight,t(4,k)}function p(C){_e[C?"unshift":"push"](()=>{y=C,t(3,y)})}function h(){R=this.offsetHeight,t(5,R)}function m(C){_e[C?"unshift":"push"](()=>{b=C,t(7,b)})}function he(){v=$t.entries.get(this)?.contentRect,t(1,v)}return n.$$set=C=>{"items"in C&&t(14,_=C.items),"max_height"in C&&t(15,i=C.max_height),"actual_height"in C&&t(12,o=C.actual_height),"table_scrollbar_width"in C&&t(13,d=C.table_scrollbar_width),"start"in C&&t(10,u=C.start),"end"in C&&t(11,c=C.end),"selected"in C&&t(16,g=C.selected),"$$scope"in C&&t(20,a=C.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&2&&(E=v?.height||200),n.$$.dirty[0]&16384&&t(19,l=_),n.$$.dirty[0]&786432&&D&&M(()=>ie(l)),n.$$.dirty[0]&65536&&G(g),n.$$.dirty[0]&560129&&t(8,W=H?l.slice(u,c).map((C,$)=>({index:$+u,data:C})):l.slice(0,i/l.length*w+1).map((C,$)=>({index:$+u,data:C})))},[w,v,B,y,k,R,q,b,W,Re,u,c,o,d,_,i,g,J,D,l,a,s,Q,p,h,m,he]}class fn extends Pe{constructor(e){super(),Fe(this,e,un,on,Je,{items:14,max_height:15,actual_height:12,table_scrollbar_width:13,start:10,end:11,selected:16,scroll_to_index:17},null,[-1,-1])}get items(){return this.$$.ctx[14]}set items(e){this.$$set({items:e}),z()}get max_height(){return this.$$.ctx[15]}set max_height(e){this.$$set({max_height:e}),z()}get actual_height(){return this.$$.ctx[12]}set actual_height(e){this.$$set({actual_height:e}),z()}get table_scrollbar_width(){return this.$$.ctx[13]}set table_scrollbar_width(e){this.$$set({table_scrollbar_width:e}),z()}get start(){return this.$$.ctx[10]}set start(e){this.$$set({start:e}),z()}get end(){return this.$$.ctx[11]}set end(e){this.$$set({end:e}),z()}get selected(){return this.$$.ctx[16]}set selected(e){this.$$set({selected:e}),z()}get scroll_to_index(){return this.$$.ctx[17]}}function _n(n){let e,t;return{c(){e=Oe("svg"),t=Oe("path"),A(t,"d","M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"),A(t,"transform",n[0]),A(e,"viewBox","0 0 24 24"),A(e,"width","16"),A(e,"height","16")},m(l,s){I(l,e,s),S(e,t)},p(l,[s]){s&1&&A(t,"transform",l[0])},i:qe,o:qe,d(l){l&&j(e)}}}function hn(n,e,t){let{transform:l}=e;return n.$$set=s=>{"transform"in s&&t(0,l=s.transform)},[l]}class $e extends Pe{constructor(e){super(),Fe(this,e,hn,_n,Je,{transform:0})}get transform(){return this.$$.ctx[0]}set transform(e){this.$$set({transform:e}),z()}}function Nt(n){let e,t,l,s=n[4]("dataframe.add_row_above")+"",a,_,i,o,d,u=n[4]("dataframe.add_row_below")+"",c,g,w,B;return t=new $e({props:{transform:"rotate(-90 12 12)"}}),o=new $e({props:{transform:"rotate(90 12 12)"}}),{c(){e=F("button"),oe(t.$$.fragment),l=Y(),a=Me(s),_=Y(),i=F("button"),oe(o.$$.fragment),d=Y(),c=Me(u),A(e,"class","svelte-1ygaf0d"),A(i,"class","svelte-1ygaf0d")},m(y,k){I(y,e,k),re(t,e,null),S(e,l),S(e,a),I(y,_,k),I(y,i,k),re(o,i,null),S(i,d),S(i,c),g=!0,w||(B=[x(e,"click",n[14]),x(i,"click",n[15])],w=!0)},p(y,k){(!g||k&16)&&s!==(s=y[4]("dataframe.add_row_above")+"")&&Ne(a,s),(!g||k&16)&&u!==(u=y[4]("dataframe.add_row_below")+"")&&Ne(c,u)},i(y){g||(O(t.$$.fragment,y),O(o.$$.fragment,y),g=!0)},o(y){U(t.$$.fragment,y),U(o.$$.fragment,y),g=!1},d(y){y&&(j(e),j(_),j(i)),ae(t),ae(o),w=!1,Te(B)}}}function Ht(n){let e,t,l,s=n[4]("dataframe.add_column_left")+"",a,_,i,o,d,u=n[4]("dataframe.add_column_right")+"",c,g,w,B;return t=new $e({props:{transform:"rotate(180 12 12)"}}),o=new $e({props:{transform:"rotate(0 12 12)"}}),{c(){e=F("button"),oe(t.$$.fragment),l=Y(),a=Me(s),_=Y(),i=F("button"),oe(o.$$.fragment),d=Y(),c=Me(u),A(e,"class","svelte-1ygaf0d"),A(i,"class","svelte-1ygaf0d")},m(y,k){I(y,e,k),re(t,e,null),S(e,l),S(e,a),I(y,_,k),I(y,i,k),re(o,i,null),S(i,d),S(i,c),g=!0,w||(B=[x(e,"click",n[16]),x(i,"click",n[17])],w=!0)},p(y,k){(!g||k&16)&&s!==(s=y[4]("dataframe.add_column_left")+"")&&Ne(a,s),(!g||k&16)&&u!==(u=y[4]("dataframe.add_column_right")+"")&&Ne(c,u)},i(y){g||(O(t.$$.fragment,y),O(o.$$.fragment,y),g=!0)},o(y){U(t.$$.fragment,y),U(o.$$.fragment,y),g=!1},d(y){y&&(j(e),j(_),j(i)),ae(t),ae(o),w=!1,Te(B)}}}function cn(n){let e,t,l,s=!n[8]&&n[7]&&Nt(n),a=n[6]&&Ht(n);return{c(){e=F("div"),s&&s.c(),t=Y(),a&&a.c(),A(e,"class","cell-menu svelte-1ygaf0d")},m(_,i){I(_,e,i),s&&s.m(e,null),S(e,t),a&&a.m(e,null),n[18](e),l=!0},p(_,[i]){!_[8]&&_[7]?s?(s.p(_,i),i&384&&O(s,1)):(s=Nt(_),s.c(),O(s,1),s.m(e,t)):s&&(ve(),U(s,1,1,()=>{s=null}),pe()),_[6]?a?(a.p(_,i),i&64&&O(a,1)):(a=Ht(_),a.c(),O(a,1),a.m(e,null)):a&&(ve(),U(a,1,1,()=>{a=null}),pe())},i(_){l||(O(s),O(a),l=!0)},o(_){U(s),U(a),l=!1},d(_){_&&j(e),s&&s.d(),a&&a.d(),n[18](null)}}}function dn(n,e,t){let l,s,a,{x:_}=e,{y:i}=e,{on_add_row_above:o}=e,{on_add_row_below:d}=e,{on_add_column_left:u}=e,{on_add_column_right:c}=e,{row:g}=e,{col_count:w}=e,{row_count:B}=e,{i18n:y}=e,k;xe(()=>{R()});function R(){if(!k)return;const E=window.innerWidth,W=window.innerHeight,v=k.getBoundingClientRect();let H=_-30,M=i-20;H+v.width>E&&(H=_-v.width+10),M+v.height>W&&(M=i-v.height+10),t(5,k.style.left=`${H}px`,k),t(5,k.style.top=`${M}px`,k)}const P=()=>o(),D=()=>d(),N=()=>u(),q=()=>c();function b(E){_e[E?"unshift":"push"](()=>{k=E,t(5,k)})}return n.$$set=E=>{"x"in E&&t(9,_=E.x),"y"in E&&t(10,i=E.y),"on_add_row_above"in E&&t(0,o=E.on_add_row_above),"on_add_row_below"in E&&t(1,d=E.on_add_row_below),"on_add_column_left"in E&&t(2,u=E.on_add_column_left),"on_add_column_right"in E&&t(3,c=E.on_add_column_right),"row"in E&&t(11,g=E.row),"col_count"in E&&t(12,w=E.col_count),"row_count"in E&&t(13,B=E.row_count),"i18n"in E&&t(4,y=E.i18n)},n.$$.update=()=>{n.$$.dirty&2048&&t(8,l=g===-1),n.$$.dirty&8192&&t(7,s=B[1]==="dynamic"),n.$$.dirty&4096&&t(6,a=w[1]==="dynamic")},[o,d,u,c,y,k,a,s,l,_,i,g,w,B,P,D,N,q,b]}class el extends Pe{constructor(e){super(),Fe(this,e,dn,cn,Je,{x:9,y:10,on_add_row_above:0,on_add_row_below:1,on_add_column_left:2,on_add_column_right:3,row:11,col_count:12,row_count:13,i18n:4})}get x(){return this.$$.ctx[9]}set x(e){this.$$set({x:e}),z()}get y(){return this.$$.ctx[10]}set y(e){this.$$set({y:e}),z()}get on_add_row_above(){return this.$$.ctx[0]}set on_add_row_above(e){this.$$set({on_add_row_above:e}),z()}get on_add_row_below(){return this.$$.ctx[1]}set on_add_row_below(e){this.$$set({on_add_row_below:e}),z()}get on_add_column_left(){return this.$$.ctx[2]}set on_add_column_left(e){this.$$set({on_add_column_left:e}),z()}get on_add_column_right(){return this.$$.ctx[3]}set on_add_column_right(e){this.$$set({on_add_column_right:e}),z()}get row(){return this.$$.ctx[11]}set row(e){this.$$set({row:e}),z()}get col_count(){return this.$$.ctx[12]}set col_count(e){this.$$set({col_count:e}),z()}get row_count(){return this.$$.ctx[13]}set row_count(e){this.$$set({row_count:e}),z()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),z()}}const{window:rt}=Fl;function Tt(n,e,t){const l=n.slice();return l[111]=e[t].value,l[112]=e[t].id,l[115]=e,l[116]=t,l}function qt(n,e,t){const l=n.slice();return l[111]=e[t].value,l[112]=e[t].id,l[113]=e,l[114]=t,l}function Ot(n,e,t){const l=n.slice();return l[111]=e[t].value,l[112]=e[t].id,l[117]=e,l[114]=t,l}function Pt(n,e,t){const l=n.slice();return l[111]=e[t].value,l[112]=e[t].id,l[116]=t,l}function Ft(n){let e,t;return{c(){e=F("p"),t=Me(n[1]),A(e,"class","svelte-1o4y3py")},m(l,s){I(l,e,s),S(e,t)},p(l,s){s[0]&2&&Ne(t,l[1])},d(l){l&&j(e)}}}function Jt(n){let e,t;return{c(){e=F("caption"),t=Me(n[1]),A(e,"class","sr-only")},m(l,s){I(l,e,s),S(e,t)},p(l,s){s[0]&2&&Ne(t,l[1])},d(l){l&&j(e)}}}function Ut(n,e){let t,l,s,a,_,i,o,d,u,c,g;return s=new et({props:{value:e[111],latex_delimiters:e[5],line_breaks:e[11],header:!0,edit:!1,el:null,root:e[8]}}),{key:n,first:null,c(){t=F("th"),l=F("div"),oe(s.$$.fragment),a=Y(),_=F("div"),i=Oe("svg"),o=Oe("path"),u=Y(),A(o,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),A(i,"width","1em"),A(i,"height","1em"),A(i,"viewBox","0 0 9 7"),A(i,"fill","none"),A(i,"xmlns","http://www.w3.org/2000/svg"),A(i,"class","svelte-1o4y3py"),A(_,"class",d="sort-button "+e[19]+" svelte-1o4y3py"),V(_,"sorted",e[20]===e[116]),V(_,"des",e[20]===e[116]&&e[19]==="des"),A(l,"class","cell-wrap svelte-1o4y3py"),A(t,"aria-sort",c=e[39](e[111],e[20],e[19])),A(t,"class","svelte-1o4y3py"),V(t,"editing",e[28]===e[116]),le(t,"width",e[12].length?e[12][e[116]]:void 0),this.first=t},m(w,B){I(w,t,B),S(t,l),re(s,l,null),S(l,a),S(l,_),S(_,i),S(i,o),S(t,u),g=!0},p(w,B){e=w;const y={};B[0]&67108864&&(y.value=e[111]),B[0]&32&&(y.latex_delimiters=e[5]),B[0]&2048&&(y.line_breaks=e[11]),B[0]&256&&(y.root=e[8]),s.$set(y),(!g||B[0]&524288&&d!==(d="sort-button "+e[19]+" svelte-1o4y3py"))&&A(_,"class",d),(!g||B[0]&68681728)&&V(_,"sorted",e[20]===e[116]),(!g||B[0]&68681728)&&V(_,"des",e[20]===e[116]&&e[19]==="des"),(!g||B[0]&68681728&&c!==(c=e[39](e[111],e[20],e[19])))&&A(t,"aria-sort",c),(!g||B[0]&335544320)&&V(t,"editing",e[28]===e[116]),B[0]&67112960&&le(t,"width",e[12].length?e[12][e[116]]:void 0)},i(w){g||(O(s.$$.fragment,w),g=!0)},o(w){U(s.$$.fragment,w),g=!1},d(w){w&&j(t),ae(s)}}}function Wt(n,e){let t,l,s,a,_=e[114],i;s=new et({props:{value:e[111],latex_delimiters:e[5],line_breaks:e[11],datatype:Array.isArray(e[0])?e[0][e[114]]:e[0],edit:!1,el:null,root:e[8]}});const o=()=>e[61](t,_),d=()=>e[61](null,_);return{key:n,first:null,c(){t=F("td"),l=F("div"),oe(s.$$.fragment),a=Y(),A(l,"class","cell-wrap svelte-1o4y3py"),A(t,"tabindex","-1"),A(t,"class","svelte-1o4y3py"),this.first=t},m(u,c){I(u,t,c),S(t,l),re(s,l,null),S(t,a),o(),i=!0},p(u,c){e=u;const g={};c[1]&128&&(g.value=e[111]),c[0]&32&&(g.latex_delimiters=e[5]),c[0]&2048&&(g.line_breaks=e[11]),c[0]&1|c[1]&128&&(g.datatype=Array.isArray(e[0])?e[0][e[114]]:e[0]),c[0]&256&&(g.root=e[8]),s.$set(g),_!==e[114]&&(d(),_=e[114],o())},i(u){i||(O(s.$$.fragment,u),i=!0)},o(u){U(s.$$.fragment,u),i=!1},d(u){u&&j(t),ae(s),d()}}}function It(n){let e,t;return{c(){e=F("caption"),t=Me(n[1]),A(e,"class","sr-only")},m(l,s){I(l,e,s),S(e,t)},p(l,s){s[0]&2&&Ne(t,l[1])},d(l){l&&j(e)}}}function gn(n){let e,t=n[1]&&n[1].length!==0&&It(n);return{c(){t&&t.c(),e=Ge()},m(l,s){t&&t.m(l,s),I(l,e,s)},p(l,s){l[1]&&l[1].length!==0?t?t.p(l,s):(t=It(l),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(l){l&&j(e),t&&t.d(l)}}}function jt(n){let e,t,l;function s(...a){return n[75](n[116],...a)}return{c(){e=F("button"),e.textContent="⋮",A(e,"class","cell-menu-button svelte-1o4y3py")},m(a,_){I(a,e,_),t||(l=x(e,"click",s),t=!0)},p(a,_){n=a},d(a){a&&j(e),t=!1,l()}}}function Kt(n,e){let t,l,s,a,_,i,o,d,u,c,g,w,B,y,k,R,P;function D(H){e[71](H,e[116])}function N(H){e[72](H,e[112])}function q(){return e[73](e[116])}let b={latex_delimiters:e[5],line_breaks:e[11],edit:e[28]===e[116],select_on_focus:e[29],header:!0,root:e[8]};e[26][e[116]].value!==void 0&&(b.value=e[26][e[116]].value),e[25][e[112]].input!==void 0&&(b.el=e[25][e[112]].input),a=new et({props:b}),_e.push(()=>Se(a,"value",D)),_e.push(()=>Se(a,"el",N)),a.$on("keydown",e[45]),a.$on("dblclick",q);function E(...H){return e[74](e[116],...H)}let W=e[6]&&jt(e);function v(){return e[76](e[116])}return{key:n,first:null,c(){t=F("th"),l=F("div"),s=F("div"),oe(a.$$.fragment),o=Y(),d=F("div"),u=Oe("svg"),c=Oe("path"),w=Y(),W&&W.c(),B=Y(),A(c,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),A(u,"width","1em"),A(u,"height","1em"),A(u,"viewBox","0 0 9 7"),A(u,"fill","none"),A(u,"xmlns","http://www.w3.org/2000/svg"),A(u,"class","svelte-1o4y3py"),A(d,"class",g="sort-button "+e[19]+" svelte-1o4y3py"),V(d,"sorted",e[20]===e[116]),V(d,"des",e[20]===e[116]&&e[19]==="des"),A(s,"class","header-content svelte-1o4y3py"),A(l,"class","cell-wrap svelte-1o4y3py"),A(t,"aria-sort",y=e[39](e[111],e[20],e[19])),le(t,"width","var(--cell-width-"+e[116]+")"),A(t,"class","svelte-1o4y3py"),V(t,"focus",e[28]===e[116]||e[21]===e[116]),this.first=t},m(H,M){I(H,t,M),S(t,l),S(l,s),re(a,s,null),S(s,o),S(s,d),S(d,u),S(u,c),S(l,w),W&&W.m(l,null),S(t,B),k=!0,R||(P=[x(d,"click",E),x(t,"click",v)],R=!0)},p(H,M){e=H;const X={};M[0]&32&&(X.latex_delimiters=e[5]),M[0]&2048&&(X.line_breaks=e[11]),M[0]&335544320&&(X.edit=e[28]===e[116]),M[0]&536870912&&(X.select_on_focus=e[29]),M[0]&256&&(X.root=e[8]),!_&&M[0]&67108864&&(_=!0,X.value=e[26][e[116]].value,Ce(()=>_=!1)),!i&&M[0]&100663296&&(i=!0,X.el=e[25][e[112]].input,Ce(()=>i=!1)),a.$set(X),(!k||M[0]&524288&&g!==(g="sort-button "+e[19]+" svelte-1o4y3py"))&&A(d,"class",g),(!k||M[0]&68681728)&&V(d,"sorted",e[20]===e[116]),(!k||M[0]&68681728)&&V(d,"des",e[20]===e[116]&&e[19]==="des"),e[6]?W?W.p(e,M):(W=jt(e),W.c(),W.m(l,null)):W&&(W.d(1),W=null),(!k||M[0]&68681728&&y!==(y=e[39](e[111],e[20],e[19])))&&A(t,"aria-sort",y),(!k||M[0]&67108864)&&le(t,"width","var(--cell-width-"+e[116]+")"),(!k||M[0]&337641472)&&V(t,"focus",e[28]===e[116]||e[21]===e[116])},i(H){k||(O(a.$$.fragment,H),k=!0)},o(H){U(a.$$.fragment,H),k=!1},d(H){H&&j(t),ae(a),W&&W.d(),R=!1,Te(P)}}}function mn(n){let e,t=[],l=new Map,s,a=ze(n[26]);const _=i=>i[112];for(let i=0;i<a.length;i+=1){let o=Tt(n,a,i),d=_(o);l.set(d,t[i]=Kt(d,o))}return{c(){e=F("tr");for(let i=0;i<t.length;i+=1)t[i].c();A(e,"slot","thead"),A(e,"class","svelte-1o4y3py")},m(i,o){I(i,e,o);for(let d=0;d<t.length;d+=1)t[d]&&t[d].m(e,null);s=!0},p(i,o){o[0]&909642080|o[1]&10514688&&(a=ze(i[26]),ve(),t=Ve(t,o,_,1,i,a,l,e,Ze,Kt,null,Tt),pe())},i(i){if(!s){for(let o=0;o<a.length;o+=1)O(t[o]);s=!0}},o(i){for(let o=0;o<t.length;o+=1)U(t[o]);s=!1},d(i){i&&j(e);for(let o=0;o<t.length;o+=1)t[o].d()}}}function Vt(n){let e,t,l;function s(...a){return n[67](n[109],n[114],...a)}return{c(){e=F("button"),e.textContent="⋮",A(e,"class","cell-menu-button svelte-1o4y3py")},m(a,_){I(a,e,_),t||(l=x(e,"click",s),t=!0)},p(a,_){n=a},d(a){a&&j(e),t=!1,l()}}}function Zt(n,e){let t,l,s,a,_,i,o,d,u=`var(--cell-width-${e[114]})`,c,g,w;function B(q){e[64](q,e[109],e[114])}function y(q){e[65](q,e[112])}let k={display_value:e[15]?.[e[109]]?.[e[114]],latex_delimiters:e[5],line_breaks:e[11],editable:e[6],edit:be(e[24],[e[109],e[114]]),datatype:Array.isArray(e[0])?e[0][e[114]]:e[0],clear_on_focus:e[27],root:e[8]};e[18][e[109]][e[114]].value!==void 0&&(k.value=e[18][e[109]][e[114]].value),e[25][e[112]].input!==void 0&&(k.el=e[25][e[112]].input),s=new et({props:k}),_e.push(()=>Se(s,"value",B)),_e.push(()=>Se(s,"el",y)),s.$on("blur",e[66]);let R=e[6]&&Vt(e);function P(){return e[68](e[109],e[114])}function D(){return e[69](e[109],e[114])}function N(){return e[70](e[109],e[114])}return{key:n,first:null,c(){t=F("td"),l=F("div"),oe(s.$$.fragment),i=Y(),R&&R.c(),o=Y(),A(l,"class","cell-wrap svelte-1o4y3py"),A(t,"tabindex","0"),A(t,"style",d=e[16]?.[e[109]]?.[e[114]]||""),A(t,"class","svelte-1o4y3py"),V(t,"focus",be(e[17],[e[109],e[114]])),V(t,"menu-active",e[35]&&e[35].row===e[109]&&e[35].col===e[114]),le(t,"width",u),this.first=t},m(q,b){I(q,t,b),S(t,l),re(s,l,null),S(l,i),R&&R.m(l,null),S(t,o),c=!0,g||(w=[x(t,"touchstart",P,{passive:!0}),x(t,"click",D),x(t,"dblclick",N)],g=!0)},p(q,b){e=q;const E={};b[0]&32768|b[3]&196608&&(E.display_value=e[15]?.[e[109]]?.[e[114]]),b[0]&32&&(E.latex_delimiters=e[5]),b[0]&2048&&(E.line_breaks=e[11]),b[0]&64&&(E.editable=e[6]),b[0]&16777216|b[3]&196608&&(E.edit=be(e[24],[e[109],e[114]])),b[0]&1|b[3]&131072&&(E.datatype=Array.isArray(e[0])?e[0][e[114]]:e[0]),b[0]&134217728&&(E.clear_on_focus=e[27]),b[0]&256&&(E.root=e[8]),!a&&b[0]&262144|b[3]&196608&&(a=!0,E.value=e[18][e[109]][e[114]].value,Ce(()=>a=!1)),!_&&b[0]&33554432|b[3]&131072&&(_=!0,E.el=e[25][e[112]].input,Ce(()=>_=!1)),s.$set(E),e[6]?R?R.p(e,b):(R=Vt(e),R.c(),R.m(l,null)):R&&(R.d(1),R=null),(!c||b[0]&65536|b[3]&196608&&d!==(d=e[16]?.[e[109]]?.[e[114]]||""))&&A(t,"style",d),(!c||b[0]&131072|b[3]&196608)&&V(t,"focus",be(e[17],[e[109],e[114]])),(!c||b[1]&16|b[3]&196608)&&V(t,"menu-active",e[35]&&e[35].row===e[109]&&e[35].col===e[114]);const W=b[0]&65536|b[3]&196608;(b[0]&65536|b[3]&196608&&u!==(u=`var(--cell-width-${e[114]})`)||W)&&le(t,"width",u)},i(q){c||(O(s.$$.fragment,q),c=!0)},o(q){U(s.$$.fragment,q),c=!1},d(q){q&&j(t),ae(s),R&&R.d(),g=!1,Te(w)}}}function bn(n){let e,t=[],l=new Map,s,a=ze(n[110]);const _=i=>i[112];for(let i=0;i<a.length;i+=1){let o=qt(n,a,i),d=_(o);l.set(d,t[i]=Zt(d,o))}return{c(){e=F("tr");for(let i=0;i<t.length;i+=1)t[i].c();A(e,"slot","tbody"),A(e,"class","svelte-1o4y3py"),V(e,"row_odd",n[109]%2===0)},m(i,o){I(i,e,o);for(let d=0;d<t.length;d+=1)t[d]&&t[d].m(e,null);s=!0},p(i,o){o[0]&185043297|o[1]&4459025|o[3]&196608&&(a=ze(i[110]),ve(),t=Ve(t,o,_,1,i,a,l,e,Ze,Zt,null,qt),pe()),(!s||o[3]&65536)&&V(e,"row_odd",i[109]%2===0)},i(i){if(!s){for(let o=0;o<a.length;o+=1)O(t[o]);s=!0}},o(i){for(let o=0;o<t.length;o+=1)U(t[o]);s=!1},d(i){i&&j(e);for(let o=0;o<t.length;o+=1)t[o].d()}}}function wn(n){let e,t,l,s,a;function _(u){n[77](u)}function i(u){n[78](u)}function o(u){n[79](u)}let d={max_height:n[10],selected:n[37],$$slots:{tbody:[bn,({index:u,item:c})=>({109:u,110:c}),({index:u,item:c})=>[0,0,0,(u?65536:0)|(c?131072:0)]],thead:[mn],default:[gn]},$$scope:{ctx:n}};return n[18]!==void 0&&(d.items=n[18]),n[33]!==void 0&&(d.actual_height=n[33]),n[34]!==void 0&&(d.table_scrollbar_width=n[34]),e=new fn({props:d}),_e.push(()=>Se(e,"items",_)),_e.push(()=>Se(e,"actual_height",i)),_e.push(()=>Se(e,"table_scrollbar_width",o)),{c(){oe(e.$$.fragment)},m(u,c){re(e,u,c),a=!0},p(u,c){const g={};c[0]&1024&&(g.max_height=u[10]),c[1]&64&&(g.selected=u[37]),c[0]&1061128547|c[1]&17|c[3]&67305472&&(g.$$scope={dirty:c,ctx:u}),!t&&c[0]&262144&&(t=!0,g.items=u[18],Ce(()=>t=!1)),!l&&c[1]&4&&(l=!0,g.actual_height=u[33],Ce(()=>l=!1)),!s&&c[1]&8&&(s=!0,g.table_scrollbar_width=u[34],Ce(()=>s=!1)),e.$set(g)},i(u){a||(O(e.$$.fragment,u),a=!0)},o(u){U(e.$$.fragment,u),a=!1},d(u){ae(e,u)}}}function Gt(n){let e,t;return e=new el({props:{i18n:n[9],x:n[35].x,y:n[35].y,row:n[35]?.row??-1,col_count:n[3],row_count:n[4],on_add_row_above:n[84],on_add_row_below:n[85],on_add_column_left:n[86],on_add_column_right:n[87]}}),{c(){oe(e.$$.fragment)},m(l,s){re(e,l,s),t=!0},p(l,s){const a={};s[0]&512&&(a.i18n=l[9]),s[1]&16&&(a.x=l[35].x),s[1]&16&&(a.y=l[35].y),s[1]&16&&(a.row=l[35]?.row??-1),s[0]&8&&(a.col_count=l[3]),s[0]&16&&(a.row_count=l[4]),s[1]&16&&(a.on_add_row_above=l[84]),s[1]&16&&(a.on_add_row_below=l[85]),s[1]&16&&(a.on_add_column_left=l[86]),s[1]&16&&(a.on_add_column_right=l[87]),e.$set(a)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Qt(n){let e,t;return e=new el({props:{i18n:n[9],x:n[36].x,y:n[36].y,row:-1,col_count:n[3],row_count:n[4],on_add_row_above:n[88],on_add_row_below:n[89],on_add_column_left:n[90],on_add_column_right:n[91]}}),{c(){oe(e.$$.fragment)},m(l,s){re(e,l,s),t=!0},p(l,s){const a={};s[0]&512&&(a.i18n=l[9]),s[1]&32&&(a.x=l[36].x),s[1]&32&&(a.y=l[36].y),s[0]&8&&(a.col_count=l[3]),s[0]&16&&(a.row_count=l[4]),s[1]&16&&(a.on_add_row_above=l[88]),s[1]&16&&(a.on_add_row_below=l[89]),s[1]&32&&(a.on_add_column_left=l[90]),s[1]&32&&(a.on_add_column_right=l[91]),e.$set(a)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function kn(n){let e,t,l,s,a,_,i,o=[],d=new Map,u,c,g,w=[],B=new Map,y,k,R,P,D,N,q,b,E,W,v=n[1]&&n[1].length!==0&&n[2]&&Ft(n),H=n[1]&&n[1].length!==0&&Jt(n),M=ze(n[26]);const X=p=>p[112];for(let p=0;p<M.length;p+=1){let h=Pt(n,M,p),m=X(h);d.set(m,o[p]=Ut(m,h))}let ie=ze(n[38]);const G=p=>p[112];for(let p=0;p<ie.length;p+=1){let h=Ot(n,ie,p),m=G(h);B.set(m,w[p]=Wt(m,h))}function Ae(p){n[80](p)}let Re={upload:n[13],stream_handler:n[14],flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:n[8],$$slots:{default:[wn]},$$scope:{ctx:n}};n[30]!==void 0&&(Re.dragging=n[30]),R=new Zl({props:Re}),_e.push(()=>Se(R,"dragging",Ae)),R.$on("load",n[81]);let J=n[35]!==null&&Gt(n),Q=n[36]!==null&&Qt(n);return{c(){e=F("div"),v&&v.c(),t=Y(),l=F("div"),s=F("table"),H&&H.c(),a=Y(),_=F("thead"),i=F("tr");for(let p=0;p<o.length;p+=1)o[p].c();u=Y(),c=F("tbody"),g=F("tr");for(let p=0;p<w.length;p+=1)w[p].c();k=Y(),oe(R.$$.fragment),D=Y(),J&&J.c(),N=Y(),Q&&Q.c(),q=Ge(),A(i,"class","svelte-1o4y3py"),A(_,"class","svelte-1o4y3py"),A(g,"class","svelte-1o4y3py"),A(s,"class","svelte-1o4y3py"),V(s,"fixed-layout",n[12].length!=0),A(l,"class","table-wrap svelte-1o4y3py"),le(l,"height",n[33]+"px"),A(l,"role","grid"),A(l,"tabindex","0"),V(l,"dragging",n[30]),V(l,"no-wrap",!n[7]),A(e,"class","svelte-1o4y3py"),V(e,"label",n[1]&&n[1].length!==0)},m(p,h){I(p,e,h),v&&v.m(e,null),S(e,t),S(e,l),S(l,s),H&&H.m(s,null),S(s,a),S(s,_),S(_,i);for(let m=0;m<o.length;m+=1)o[m]&&o[m].m(i,null);S(s,u),S(s,c),S(c,g);for(let m=0;m<w.length;m+=1)w[m]&&w[m].m(g,null);y=xt.observe(s,n[62].bind(s)),n[63](s),S(l,k),re(R,l,null),n[82](l),I(p,D,h),J&&J.m(p,h),I(p,N,h),Q&&Q.m(p,h),I(p,q,h),b=!0,E||(W=[x(rt,"click",n[46]),x(rt,"touchstart",n[46]),x(rt,"resize",n[60]),x(l,"keydown",n[83]),Xt(Vl.call(null,e))],E=!0)},p(p,h){p[1]&&p[1].length!==0&&p[2]?v?v.p(p,h):(v=Ft(p),v.c(),v.m(e,t)):v&&(v.d(1),v=null),p[1]&&p[1].length!==0?H?H.p(p,h):(H=Jt(p),H.c(),H.m(s,a)):H&&(H.d(1),H=null),h[0]&337123616|h[1]&256&&(M=ze(p[26]),ve(),o=Ve(o,h,X,1,p,M,d,i,Ze,Ut,null,Pt),pe()),h[0]&4196641|h[1]&128&&(ie=ze(p[38]),ve(),w=Ve(w,h,G,1,p,ie,B,g,Ze,Wt,null,Ot),pe()),(!b||h[0]&4096)&&V(s,"fixed-layout",p[12].length!=0);const m={};h[0]&8192&&(m.upload=p[13]),h[0]&16384&&(m.stream_handler=p[14]),h[0]&256&&(m.root=p[8]),h[0]&1061129571|h[1]&93|h[3]&67108864&&(m.$$scope={dirty:h,ctx:p}),!P&&h[0]&1073741824&&(P=!0,m.dragging=p[30],Ce(()=>P=!1)),R.$set(m),(!b||h[1]&4)&&le(l,"height",p[33]+"px"),(!b||h[0]&1073741824)&&V(l,"dragging",p[30]),(!b||h[0]&128)&&V(l,"no-wrap",!p[7]),(!b||h[0]&2)&&V(e,"label",p[1]&&p[1].length!==0),p[35]!==null?J?(J.p(p,h),h[1]&16&&O(J,1)):(J=Gt(p),J.c(),O(J,1),J.m(N.parentNode,N)):J&&(ve(),U(J,1,1,()=>{J=null}),pe()),p[36]!==null?Q?(Q.p(p,h),h[1]&32&&O(Q,1)):(Q=Qt(p),Q.c(),O(Q,1),Q.m(q.parentNode,q)):Q&&(ve(),U(Q,1,1,()=>{Q=null}),pe())},i(p){if(!b){for(let h=0;h<M.length;h+=1)O(o[h]);for(let h=0;h<ie.length;h+=1)O(w[h]);O(R.$$.fragment,p),O(J),O(Q),b=!0}},o(p){for(let h=0;h<o.length;h+=1)U(o[h]);for(let h=0;h<w.length;h+=1)U(w[h]);U(R.$$.fragment,p),U(J),U(Q),b=!1},d(p){p&&(j(e),j(D),j(N),j(q)),v&&v.d(),H&&H.d();for(let h=0;h<o.length;h+=1)o[h].d();for(let h=0;h<w.length;h+=1)w[h].d();y(),n[63](null),ae(R),n[82](null),J&&J.d(p),Q&&Q.d(p),E=!1,Te(W)}}}function Ke(){return Math.random().toString(36).substring(2,15)}function yn(n,e){return e.filter(t);function t(l){var s=-1;return n.split(`
`).every(a);function a(_){if(!_)return!0;var i=_.split(l).length;return s<0&&(s=i),s===i&&i>1}}}function vn(n){const e=atob(n.split(",")[1]),t=n.split(",")[0].split(":")[1].split(";")[0],l=new ArrayBuffer(e.length),s=new Uint8Array(l);for(let a=0;a<e.length;a++)s[a]=e.charCodeAt(a);return new Blob([l],{type:t})}function pn(n){let e=n[0].slice();for(let t=0;t<n.length;t++)for(let l=0;l<n[t].length;l++)`${e[l].value}`.length<`${n[t][l].value}`.length&&(e[l]=n[t][l]);return e}function An(n,e,t){let l,s,{datatype:a}=e,{label:_=null}=e,{show_label:i=!0}=e,{headers:o=[]}=e,{values:d=[]}=e,{col_count:u}=e,{row_count:c}=e,{latex_delimiters:g}=e,{editable:w=!0}=e,{wrap:B=!1}=e,{root:y}=e,{i18n:k}=e,{max_height:R=500}=e,{line_breaks:P=!0}=e,{column_widths:D=[]}=e,{upload:N}=e,{stream_handler:q}=e,b=!1,{display_value:E=null}=e,{styling:W=null}=e,v;const H=Yt();let M=!1;const X=(r,f)=>h?.[r]?.[f]?.value;let ie=null,G={};function Ae(r){let f=r||[];if(u[1]==="fixed"&&f.length<u[0]){const L=Array(u[0]-f.length).fill("").map((T,K)=>`${K+f.length}`);f=f.concat(L)}return!f||f.length===0?Array(u[0]).fill(0).map((L,T)=>{const K=Ke();return t(25,G[K]={cell:null,input:null},G),{id:K,value:JSON.stringify(T+1)}}):f.map((L,T)=>{const K=Ke();return t(25,G[K]={cell:null,input:null},G),{id:K,value:L??""}})}function Re(r){const f=r.length;return Array(c[1]==="fixed"||f<c[0]?c[0]:f).fill(0).map((L,T)=>Array(u[1]==="fixed"?u[0]:f>0?r[0].length:o.length).fill(0).map((K,ce)=>{const ye=Ke();return t(25,G[ye]=G[ye]||{input:null,cell:null},G),{value:r?.[T]?.[ce]??"",id:ye}}))}let J=Ae(o),Q;function p(){t(26,J=Ae(o)),t(58,Q=o.slice()),he()}let h=[[]],m;async function he(){H("change",{data:h.map(r=>r.map(({value:f})=>f)),headers:J.map(r=>r.value),metadata:w?null:{display_value:E,styling:W}})}function C(r,f,L){if(!f)return"none";if(o[f]===r){if(L==="asc")return"ascending";if(L==="des")return"descending"}return"none"}function $(r){return h.reduce((f,L,T)=>{const K=L.reduce((ce,ye,Xe)=>r===ye.id?Xe:ce,-1);return K===-1?f:[T,K]},[-1,-1])}async function ne(r,f){!w||be(M,[r,f])||t(24,M=[r,f])}function we(r,f){const L={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[r],T=f[0]+L[0],K=f[1]+L[1];if(T<0&&K<=0)t(21,te=K),t(17,b=!1);else{const ce=h[T]?.[K];t(17,b=ce?[T,K]:b)}}let ue=!1;async function de(r){if(te!==!1&&ee===!1)switch(r.key){case"ArrowDown":t(17,b=[0,te]),t(21,te=!1);return;case"ArrowLeft":t(21,te=te>0?te-1:te);return;case"ArrowRight":t(21,te=te<J.length-1?te+1:te);return;case"Escape":r.preventDefault(),t(21,te=!1);break;case"Enter":r.preventDefault();break}if(!b)return;const[f,L]=b;switch(r.key){case"ArrowRight":case"ArrowLeft":case"ArrowDown":case"ArrowUp":if(M)break;r.preventDefault(),we(r.key,[f,L]);break;case"Escape":if(!w)break;r.preventDefault(),t(24,M=!1);break;case"Enter":if(!w)break;r.preventDefault(),r.shiftKey?(ct(f),await De(),t(17,b=[f+1,L])):be(M,[f,L])?(t(24,M=!1),await De(),t(17,b=[f,L])):t(24,M=[f,L]);break;case"Backspace":if(!w)break;M||(r.preventDefault(),t(18,h[f][L].value="",h));break;case"Delete":if(!w)break;M||(r.preventDefault(),t(18,h[f][L].value="",h));break;case"Tab":let T=r.shiftKey?-1:1,K=h[f][L+T],ce=h?.[f+T]?.[T>0?0:J.length-1];(K||ce)&&(r.preventDefault(),t(17,b=K?[f,L+T]:[f+T,T>0?0:J.length-1])),t(24,M=!1);break;default:if(!w)break;(!M||M&&be(M,[f,L]))&&r.key.length===1&&(t(27,ue=!0),t(24,M=[f,L]))}}let Z=null;async function fe(r,f){Z&&Z.row===r&&Z.col===f?Z=null:Z={row:r,col:f},!be(M,[r,f])&&(t(28,ee=!1),t(21,te=!1),t(24,M=!1),be(b,[r,f])||(t(17,b=[r,f]),await De(),ge.focus()))}let ke,He;function Qe(r){typeof He!="number"||He!==r?(t(19,ke="asc"),t(20,He=r)):ke==="asc"?t(19,ke="des"):ke==="des"&&t(19,ke="asc")}let ee,ht=!1,te=!1;async function tt(r,f=!1){!w||u[1]!=="dynamic"||ee===r||(t(17,b=!1),t(21,te=r),t(28,ee=r),t(29,ht=f))}function tl(r){if(w)switch(r.key){case"Escape":case"Enter":case"Tab":r.preventDefault(),t(17,b=!1),t(21,te=ee),t(28,ee=!1),ge.focus();break}}async function ct(r){if(ge.focus(),c[1]!=="dynamic")return;if(h.length===0){t(56,d=[Array(o.length).fill("")]);return}const f=Array(h[0].length).fill(0).map((L,T)=>{const K=Ke();return t(25,G[K]={cell:null,input:null},G),{id:K,value:""}});r!==void 0&&r>=0&&r<=h.length?h.splice(r,0,f):h.push(f),t(18,h),t(56,d),t(59,m),t(17,b=[r!==void 0?r:h.length-1,0])}async function ll(r){if(ge.focus(),u[1]!=="dynamic")return;const f=r!==void 0?r:h[0].length;for(let L=0;L<h.length;L++){const T=Ke();t(25,G[T]={cell:null,input:null},G),h[L].splice(f,0,{id:T,value:""})}o.splice(f,0,`Header ${o.length+1}`),t(18,h),t(56,d),t(59,m),t(55,o),await De(),requestAnimationFrame(()=>{tt(f,!0);const L=ge.querySelectorAll("tbody")[1].offsetWidth;ge.querySelectorAll("table")[1].scrollTo({left:L})})}function lt(r){(se&&!r.target.closest(".cell-menu")||me&&!r.target.closest(".cell-menu"))&&(t(35,se=null),t(36,me=null)),r.stopImmediatePropagation();const[f]=r.composedPath();ge.contains(f)||(t(24,M=!1),t(28,ee=!1),t(21,te=!1),sl(),Z=null,t(35,se=null),t(36,me=null))}function dt(r){const f=new FileReader;function L(T){if(!T?.target?.result||typeof T.target.result!="string")return;const[K]=yn(T.target.result,[",","	"]),[ce,...ye]=Ql(K).parseRows(T.target.result);t(26,J=Ae(u[1]==="fixed"?ce.slice(0,u[0]):ce)),t(56,d=ye),f.removeEventListener("loadend",L)}f.addEventListener("loadend",L),f.readAsText(r)}let nt=!1,Ue=[],ge,st;function We(){const r=Ue.map((f,L)=>f?.clientWidth||0);if(r.length!==0)for(let f=0;f<r.length;f++)ge.style.setProperty(`--cell-width-${f}`,`${r[f]-Ye/r.length}px`)}let it=d.slice(0,R/d.length*37).length*37+37,Ye=0;function nl(r,f,L,T,K){let ce=null;if(b&&b[0]in h&&b[1]in h[b[0]]&&(ce=h[b[0]][b[1]].id),typeof T!="number"||!K)return;const ye=[...Array(r.length).keys()];if(K==="asc")ye.sort((Le,Be)=>r[Le][T].value<r[Be][T].value?-1:1);else if(K==="des")ye.sort((Le,Be)=>r[Le][T].value>r[Be][T].value?-1:1);else return;const Xe=[...r],vt=f?[...f]:null,pt=L?[...L]:null;if(ye.forEach((Le,Be)=>{r[Be]=Xe[Le],f&&vt&&(f[Be]=vt[Le]),L&&pt&&(L[Be]=pt[Le])}),t(18,h),t(56,d),t(59,m),ce){const[Le,Be]=$(ce);t(17,b=[Le,Be])}}let gt=!1;xe(()=>{const r=new IntersectionObserver((f,L)=>{f.forEach(T=>{T.isIntersecting&&!gt&&(We(),t(18,h),t(56,d),t(59,m)),gt=T.isIntersecting})});return r.observe(ge),()=>{r.disconnect()}});let se=null;function mt(r,f,L){if(r.stopPropagation(),se&&se.row===f&&se.col===L)t(35,se=null);else{const T=r.target.closest("td");if(T){const K=T.getBoundingClientRect();t(35,se={row:f,col:L,x:K.right,y:K.bottom})}}}function Ie(r,f){const L=f==="above"?r:r+1;ct(L),t(35,se=null),t(36,me=null)}function je(r,f){const L=f==="left"?r:r+1;ll(L),t(35,se=null),t(36,me=null)}function bt(){t(35,se=null),t(36,me=null),We()}xe(()=>(document.addEventListener("click",lt),window.addEventListener("resize",bt),()=>{document.removeEventListener("click",lt),window.removeEventListener("resize",bt)}));let Ee=null;function wt(r){Ee?.type==="header"&&Ee.col===r?Ee=null:Ee={type:"header",col:r}}function kt(r,f){Ee?.type==="cell"&&Ee.row===r&&Ee.col===f?Ee=null:Ee={type:"cell",row:r,col:f}}let me=null;function yt(r,f){if(r.stopPropagation(),me&&me.col===f)t(36,me=null);else{const L=r.target.closest("th");if(L){const T=L.getBoundingClientRect();t(36,me={col:f,x:T.right,y:T.bottom})}}}function sl(){t(17,b=!1),t(57,ie=null)}const il=()=>We();function ol(r,f){_e[r?"unshift":"push"](()=>{Ue[f]=r,t(22,Ue)})}function rl(){v=$t.entries.get(this)?.contentRect,t(23,v)}function al(r){_e[r?"unshift":"push"](()=>{st=r,t(32,st)})}function ul(r,f,L){n.$$.not_equal(h[f][L].value,r)&&(h[f][L].value=r,t(18,h),t(56,d),t(59,m))}function fl(r,f){n.$$.not_equal(G[f].input,r)&&(G[f].input=r,t(25,G))}const _l=()=>(t(27,ue=!1),ge.focus()),hl=(r,f,L)=>mt(L,r,f),cl=(r,f)=>ne(r,f),dl=(r,f)=>{fe(r,f),kt(r,f)},gl=(r,f)=>ne(r,f);function ml(r,f){n.$$.not_equal(J[f].value,r)&&(J[f].value=r,t(26,J))}function bl(r,f){n.$$.not_equal(G[f].input,r)&&(G[f].input=r,t(25,G))}const wl=r=>tt(r),kl=(r,f)=>{f.stopPropagation(),Qe(r)},yl=(r,f)=>yt(f,r),vl=r=>{wt(r)};function pl(r){h=r,t(18,h),t(56,d),t(59,m)}function Al(r){it=r,t(33,it)}function zl(r){Ye=r,t(34,Ye)}function El(r){nt=r,t(30,nt)}const Ll=r=>dt(vn(r.detail.data));function Bl(r){_e[r?"unshift":"push"](()=>{ge=r,t(31,ge)})}const Dl=r=>de(r),Ml=()=>Ie(se?.row??-1,"above"),Rl=()=>Ie(se?.row??-1,"below"),Sl=()=>je(se?.col??-1,"left"),Cl=()=>je(se?.col??-1,"right"),Nl=()=>Ie(se?.row??-1,"above"),Hl=()=>Ie(se?.row??-1,"below"),Tl=()=>je(me?.col??-1,"left"),ql=()=>je(me?.col??-1,"right");return n.$$set=r=>{"datatype"in r&&t(0,a=r.datatype),"label"in r&&t(1,_=r.label),"show_label"in r&&t(2,i=r.show_label),"headers"in r&&t(55,o=r.headers),"values"in r&&t(56,d=r.values),"col_count"in r&&t(3,u=r.col_count),"row_count"in r&&t(4,c=r.row_count),"latex_delimiters"in r&&t(5,g=r.latex_delimiters),"editable"in r&&t(6,w=r.editable),"wrap"in r&&t(7,B=r.wrap),"root"in r&&t(8,y=r.root),"i18n"in r&&t(9,k=r.i18n),"max_height"in r&&t(10,R=r.max_height),"line_breaks"in r&&t(11,P=r.line_breaks),"column_widths"in r&&t(12,D=r.column_widths),"upload"in r&&t(13,N=r.upload),"stream_handler"in r&&t(14,q=r.stream_handler),"display_value"in r&&t(15,E=r.display_value),"styling"in r&&t(16,W=r.styling)},n.$$.update=()=>{if(n.$$.dirty[1]&301989888&&(be(d,m)||(t(18,h=Re(d)),t(59,m=d))),n.$$.dirty[0]&393216|n.$$.dirty[1]&67108864&&b!==!1&&!be(b,ie)){const[r,f]=b;!isNaN(r)&&!isNaN(f)&&h[r]&&(H("select",{index:[r,f],value:X(r,f),row_value:h[r].map(L=>L.value)}),t(57,ie=b))}n.$$.dirty[1]&150994944&&(be(o,Q)||p()),n.$$.dirty[0]&2359296&&(h||te)&&he(),n.$$.dirty[0]&262144&&t(38,l=pn(h)),n.$$.dirty[0]&4194304&&Ue[0]&&We(),n.$$.dirty[0]&1933312&&nl(h,E,W,He,ke),n.$$.dirty[0]&131072&&t(37,s=!!b&&b[0])},[a,_,i,u,c,g,w,B,y,k,R,P,D,N,q,E,W,b,h,ke,He,te,Ue,v,M,G,J,ue,ee,ht,nt,ge,st,it,Ye,se,me,s,l,C,ne,de,fe,Qe,tt,tl,lt,dt,We,mt,Ie,je,wt,kt,yt,o,d,ie,Q,m,il,ol,rl,al,ul,fl,_l,hl,cl,dl,gl,ml,bl,wl,kl,yl,vl,pl,Al,zl,El,Ll,Bl,Dl,Ml,Rl,Sl,Cl,Nl,Hl,Tl,ql]}class zn extends Pe{constructor(e){super(),Fe(this,e,An,kn,Je,{datatype:0,label:1,show_label:2,headers:55,values:56,col_count:3,row_count:4,latex_delimiters:5,editable:6,wrap:7,root:8,i18n:9,max_height:10,line_breaks:11,column_widths:12,upload:13,stream_handler:14,display_value:15,styling:16},null,[-1,-1,-1,-1])}get datatype(){return this.$$.ctx[0]}set datatype(e){this.$$set({datatype:e}),z()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),z()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),z()}get headers(){return this.$$.ctx[55]}set headers(e){this.$$set({headers:e}),z()}get values(){return this.$$.ctx[56]}set values(e){this.$$set({values:e}),z()}get col_count(){return this.$$.ctx[3]}set col_count(e){this.$$set({col_count:e}),z()}get row_count(){return this.$$.ctx[4]}set row_count(e){this.$$set({row_count:e}),z()}get latex_delimiters(){return this.$$.ctx[5]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),z()}get editable(){return this.$$.ctx[6]}set editable(e){this.$$set({editable:e}),z()}get wrap(){return this.$$.ctx[7]}set wrap(e){this.$$set({wrap:e}),z()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),z()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),z()}get max_height(){return this.$$.ctx[10]}set max_height(e){this.$$set({max_height:e}),z()}get line_breaks(){return this.$$.ctx[11]}set line_breaks(e){this.$$set({line_breaks:e}),z()}get column_widths(){return this.$$.ctx[12]}set column_widths(e){this.$$set({column_widths:e}),z()}get upload(){return this.$$.ctx[13]}set upload(e){this.$$set({upload:e}),z()}get stream_handler(){return this.$$.ctx[14]}set stream_handler(e){this.$$set({stream_handler:e}),z()}get display_value(){return this.$$.ctx[15]}set display_value(e){this.$$set({display_value:e}),z()}get styling(){return this.$$.ctx[16]}set styling(e){this.$$set({styling:e}),z()}}const En=zn;function Ln(n){let e,t,l,s;const a=[{autoscroll:n[14].autoscroll},{i18n:n[14].i18n},n[17]];let _={};for(let i=0;i<a.length;i+=1)_=Wl(_,a[i]);return e=new Il({props:_}),e.$on("clear_status",n[28]),l=new En({props:{root:n[11],label:n[5],show_label:n[6],row_count:n[4],col_count:n[3],values:n[22],display_value:n[20],styling:n[21],headers:n[19],wrap:n[7],datatype:n[8],latex_delimiters:n[15],editable:n[18],max_height:n[16],i18n:n[14].i18n,line_breaks:n[12],column_widths:n[13],upload:n[29],stream_handler:n[30]}}),l.$on("change",n[31]),l.$on("select",n[32]),{c(){oe(e.$$.fragment),t=Y(),oe(l.$$.fragment)},m(i,o){re(e,i,o),I(i,t,o),re(l,i,o),s=!0},p(i,o){const d=o[0]&147456?jl(a,[o[0]&16384&&{autoscroll:i[14].autoscroll},o[0]&16384&&{i18n:i[14].i18n},o[0]&131072&&Kl(i[17])]):{};e.$set(d);const u={};o[0]&2048&&(u.root=i[11]),o[0]&32&&(u.label=i[5]),o[0]&64&&(u.show_label=i[6]),o[0]&16&&(u.row_count=i[4]),o[0]&8&&(u.col_count=i[3]),o[0]&4194304&&(u.values=i[22]),o[0]&1048576&&(u.display_value=i[20]),o[0]&2097152&&(u.styling=i[21]),o[0]&524288&&(u.headers=i[19]),o[0]&128&&(u.wrap=i[7]),o[0]&256&&(u.datatype=i[8]),o[0]&32768&&(u.latex_delimiters=i[15]),o[0]&262144&&(u.editable=i[18]),o[0]&65536&&(u.max_height=i[16]),o[0]&16384&&(u.i18n=i[14].i18n),o[0]&4096&&(u.line_breaks=i[12]),o[0]&8192&&(u.column_widths=i[13]),o[0]&16384&&(u.upload=i[29]),o[0]&16384&&(u.stream_handler=i[30]),l.$set(u)},i(i){s||(O(e.$$.fragment,i),O(l.$$.fragment,i),s=!0)},o(i){U(e.$$.fragment,i),U(l.$$.fragment,i),s=!1},d(i){i&&j(t),ae(e,i),ae(l,i)}}}function Bn(n){let e,t;return e=new Jl({props:{visible:n[2],padding:!1,elem_id:n[0],elem_classes:n[1],container:!1,scale:n[9],min_width:n[10],allow_overflow:!1,$$slots:{default:[Ln]},$$scope:{ctx:n}}}),{c(){oe(e.$$.fragment)},m(l,s){re(e,l,s),t=!0},p(l,s){const a={};s[0]&4&&(a.visible=l[2]),s[0]&1&&(a.elem_id=l[0]),s[0]&2&&(a.elem_classes=l[1]),s[0]&512&&(a.scale=l[9]),s[0]&1024&&(a.min_width=l[10]),s[0]&8387064|s[1]&8&&(a.$$scope={dirty:s,ctx:l}),e.$set(a)},i(l){t||(O(e.$$.fragment,l),t=!0)},o(l){U(e.$$.fragment,l),t=!1},d(l){ae(e,l)}}}function Dn(n,e,t){let{headers:l=[]}=e,{elem_id:s=""}=e,{elem_classes:a=[]}=e,{visible:_=!0}=e,{value:i={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,o="",{value_is_output:d=!1}=e,{col_count:u}=e,{row_count:c}=e,{label:g=null}=e,{show_label:w=!0}=e,{wrap:B}=e,{datatype:y}=e,{scale:k=null}=e,{min_width:R=void 0}=e,{root:P}=e,{line_breaks:D=!0}=e,{column_widths:N=[]}=e,{gradio:q}=e,{latex_delimiters:b}=e,{max_height:E=void 0}=e,{loading_status:W}=e,{interactive:v}=e,H,M,X,ie;async function G(m){let he=m||i;t(19,H=[...he.headers||l]),t(22,ie=he.data?[...he.data]:[]),t(20,M=he?.metadata?.display_value?[...he?.metadata?.display_value]:null),t(21,X=!v&&he?.metadata?.styling?[...he?.metadata?.styling]:null),await De(),q.dispatch("change"),d||q.dispatch("input")}G(),Ul(()=>{t(25,d=!1)}),(Array.isArray(i)&&i?.[0]?.length===0||i.data?.[0]?.length===0)&&(i={data:[Array(u?.[0]||3).fill("")],headers:Array(u?.[0]||3).fill("").map((m,he)=>`${he+1}`),metadata:null});async function Ae(m){JSON.stringify(m)!==o&&(t(24,i={...m}),t(27,o=JSON.stringify(i)),G(m))}const Re=()=>q.dispatch("clear_status",W),J=(...m)=>q.client.upload(...m),Q=(...m)=>q.client.stream(...m),p=m=>Ae(m.detail),h=m=>q.dispatch("select",m.detail);return n.$$set=m=>{"headers"in m&&t(26,l=m.headers),"elem_id"in m&&t(0,s=m.elem_id),"elem_classes"in m&&t(1,a=m.elem_classes),"visible"in m&&t(2,_=m.visible),"value"in m&&t(24,i=m.value),"value_is_output"in m&&t(25,d=m.value_is_output),"col_count"in m&&t(3,u=m.col_count),"row_count"in m&&t(4,c=m.row_count),"label"in m&&t(5,g=m.label),"show_label"in m&&t(6,w=m.show_label),"wrap"in m&&t(7,B=m.wrap),"datatype"in m&&t(8,y=m.datatype),"scale"in m&&t(9,k=m.scale),"min_width"in m&&t(10,R=m.min_width),"root"in m&&t(11,P=m.root),"line_breaks"in m&&t(12,D=m.line_breaks),"column_widths"in m&&t(13,N=m.column_widths),"gradio"in m&&t(14,q=m.gradio),"latex_delimiters"in m&&t(15,b=m.latex_delimiters),"max_height"in m&&t(16,E=m.max_height),"loading_status"in m&&t(17,W=m.loading_status),"interactive"in m&&t(18,v=m.interactive)},n.$$.update=()=>{n.$$.dirty[0]&150994944&&o&&JSON.stringify(i)!==o&&(t(27,o=JSON.stringify(i)),G())},[s,a,_,u,c,g,w,B,y,k,R,P,D,N,q,b,E,W,v,H,M,X,ie,Ae,i,d,l,o,Re,J,Q,p,h]}class qn extends Pe{constructor(e){super(),Fe(this,e,Dn,Bn,Je,{headers:26,elem_id:0,elem_classes:1,visible:2,value:24,value_is_output:25,col_count:3,row_count:4,label:5,show_label:6,wrap:7,datatype:8,scale:9,min_width:10,root:11,line_breaks:12,column_widths:13,gradio:14,latex_delimiters:15,max_height:16,loading_status:17,interactive:18},null,[-1,-1])}get headers(){return this.$$.ctx[26]}set headers(e){this.$$set({headers:e}),z()}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),z()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),z()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),z()}get value(){return this.$$.ctx[24]}set value(e){this.$$set({value:e}),z()}get value_is_output(){return this.$$.ctx[25]}set value_is_output(e){this.$$set({value_is_output:e}),z()}get col_count(){return this.$$.ctx[3]}set col_count(e){this.$$set({col_count:e}),z()}get row_count(){return this.$$.ctx[4]}set row_count(e){this.$$set({row_count:e}),z()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),z()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),z()}get wrap(){return this.$$.ctx[7]}set wrap(e){this.$$set({wrap:e}),z()}get datatype(){return this.$$.ctx[8]}set datatype(e){this.$$set({datatype:e}),z()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),z()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),z()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),z()}get line_breaks(){return this.$$.ctx[12]}set line_breaks(e){this.$$set({line_breaks:e}),z()}get column_widths(){return this.$$.ctx[13]}set column_widths(e){this.$$set({column_widths:e}),z()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),z()}get latex_delimiters(){return this.$$.ctx[15]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),z()}get max_height(){return this.$$.ctx[16]}set max_height(e){this.$$set({max_height:e}),z()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),z()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),z()}}export{En as BaseDataFrame,Fn as BaseExample,qn as default};
//# sourceMappingURL=Index-C9wkGla8.js.map
