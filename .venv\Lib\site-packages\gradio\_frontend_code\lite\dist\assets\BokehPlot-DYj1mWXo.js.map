{"version": 3, "file": "BokehPlot-DYj1mWXo.js", "sources": ["../../../plot/shared/plot_types/BokehPlot.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\t//@ts-nocheck\n\timport { onD<PERSON><PERSON>, createEventDispatcher } from \"svelte\";\n\n\texport let value;\n\texport let bokeh_version: string | null;\n\tconst div_id = `bokehDiv-${Math.random().toString(5).substring(2)}`;\n\tconst dispatch = createEventDispatcher<{ load: undefined }>();\n\t$: plot = value?.plot;\n\n\tasync function embed_bokeh(_plot: Record<string, any>): void {\n\t\tif (document) {\n\t\t\tif (document.getElementById(div_id)) {\n\t\t\t\tdocument.getElementById(div_id).innerHTML = \"\";\n\t\t\t}\n\t\t}\n\t\tif (window.Bokeh) {\n\t\t\tload_bokeh();\n\t\t\tlet plotObj = JSON.parse(_plot);\n\t\t\tconst y = await window.Bokeh.embed.embed_item(plotObj, div_id);\n\t\t\ty._roots.forEach(async (p) => {\n\t\t\t\tawait p.ready;\n\t\t\t\tdispatch(\"load\");\n\t\t\t});\n\t\t}\n\t}\n\n\t$: loaded && embed_bokeh(plot);\n\n\tconst main_src = `https://cdn.bokeh.org/bokeh/release/bokeh-${bokeh_version}.min.js`;\n\n\tconst plugins_src = [\n\t\t`https://cdn.pydata.org/bokeh/release/bokeh-widgets-${bokeh_version}.min.js`,\n\t\t`https://cdn.pydata.org/bokeh/release/bokeh-tables-${bokeh_version}.min.js`,\n\t\t`https://cdn.pydata.org/bokeh/release/bokeh-gl-${bokeh_version}.min.js`,\n\t\t`https://cdn.pydata.org/bokeh/release/bokeh-api-${bokeh_version}.min.js`\n\t];\n\n\tlet loaded = false;\n\tasync function load_plugins(): HTMLScriptElement[] {\n\t\tawait Promise.all(\n\t\t\tplugins_src.map((src, i) => {\n\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\tconst script = document.createElement(\"script\");\n\t\t\t\t\tscript.onload = resolve;\n\t\t\t\t\tscript.src = src;\n\t\t\t\t\tdocument.head.appendChild(script);\n\t\t\t\t\treturn script;\n\t\t\t\t});\n\t\t\t})\n\t\t);\n\n\t\tloaded = true;\n\t}\n\n\tlet plugin_scripts = [];\n\n\tfunction handle_bokeh_loaded(): void {\n\t\tplugin_scripts = load_plugins();\n\t}\n\n\tfunction load_bokeh(): HTMLScriptElement {\n\t\tconst script = document.createElement(\"script\");\n\t\tscript.onload = handle_bokeh_loaded;\n\t\tscript.src = main_src;\n\t\tconst is_bokeh_script_present = document.head.querySelector(\n\t\t\t`script[src=\"${main_src}\"]`\n\t\t);\n\t\tif (!is_bokeh_script_present) {\n\t\t\tdocument.head.appendChild(script);\n\t\t} else {\n\t\t\thandle_bokeh_loaded();\n\t\t}\n\t\treturn script;\n\t}\n\n\tconst main_script = bokeh_version ? load_bokeh() : null;\n\n\tonDestroy(() => {\n\t\tif (main_script in document.children) {\n\t\t\tdocument.removeChild(main_script);\n\t\t\tplugin_scripts.forEach((child) => document.removeChild(child));\n\t\t}\n\t});\n</script>\n\n<div data-testid={\"bokeh\"} id={div_id} class=\"gradio-bokeh\" />\n\n<style>\n\t.gradio-bokeh {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "value", "$$props", "bokeh_version", "div_id", "dispatch", "createEventDispatcher", "embed_bokeh", "_plot", "load_bokeh", "<PERSON><PERSON><PERSON><PERSON>", "p", "main_src", "plugins_src", "loaded", "load_plugins", "src", "i", "resolve", "script", "plugin_scripts", "handle_bokeh_loaded", "main_script", "onDestroy", "child", "$$invalidate", "plot"], "mappings": "kKAsFkB,OAAO,WAAMA,EAAM,CAAA,CAAA,qDAArCC,EAA6DC,EAAAC,EAAAC,CAAA,sDAlFjD,CAAA,MAAAC,CAAA,EAAAC,EACA,CAAA,cAAAC,CAAA,EAAAD,EACL,MAAAE,EAAA,YAAqB,KAAK,OAAS,EAAA,SAAS,CAAC,EAAE,UAAU,CAAC,CAAA,GAC1DC,EAAWC,mBAGFC,EAAYC,EAAA,IACtB,UACC,SAAS,eAAeJ,CAAM,IACjC,SAAS,eAAeA,CAAM,EAAE,UAAY,IAG1C,OAAO,MAAA,CACVK,QACIC,EAAU,KAAK,MAAMF,CAAK,GACxB,MAAU,OAAO,MAAM,MAAM,WAAWE,EAASN,CAAM,GAC3D,OAAO,QAAe,MAAAO,GAAA,OACjBA,EAAE,MACRN,EAAS,MAAM,KAOZ,MAAAO,EAAA,6CAAwDT,CAAa,UAErEU,EAAA,uDACiDV,CAAa,+DACdA,CAAa,2DACjBA,CAAa,4DACZA,CAAa,eAG5DW,EAAS,GACE,eAAAC,GAAA,OACR,QAAQ,IACbF,EAAY,IAAA,CAAKG,EAAKC,QACV,QAASC,GAAA,OACbC,EAAS,SAAS,cAAc,QAAQ,EAC9C,OAAAA,EAAO,OAASD,EAChBC,EAAO,IAAMH,EACb,SAAS,KAAK,YAAYG,CAAM,EACzBA,UAKVL,EAAS,EAAA,EAGN,IAAAM,EAAA,CAAA,EAEK,SAAAC,GAAA,CACRD,EAAiBL,EAAA,EAGT,SAAAN,GAAA,OACFU,EAAS,SAAS,cAAc,QAAQ,EAC9C,OAAAA,EAAO,OAASE,EAChBF,EAAO,IAAMP,EACmB,SAAS,KAAK,cAAA,eAC9BA,CAAQ,IAAA,EAKvBS,IAFA,SAAS,KAAK,YAAYF,CAAM,EAI1BA,EAGF,MAAAG,EAAcnB,EAAgBM,EAAe,EAAA,KAEnD,OAAAc,EAAA,IAAA,CACKD,KAAe,SAAS,WAC3B,SAAS,YAAYA,CAAW,EAChCF,EAAe,QAASI,GAAU,SAAS,YAAYA,CAAK,CAAA,yHAzE9DC,EAAA,EAAGC,EAAOzB,GAAO,IAAA,iBAmBda,GAAUP,EAAYmB,CAAI"}