import{a as p,i as y,s as x,f as m,p as N,k as Y,x as E,q as b,r as G,$ as z,l as M,v as g,n as K,w as Q,o as j,A as ae,ao as R,E as ee,aq as se,F as ne,B as ie,c as q,m as C,t as w,b as B,d as I,Y as re,S as ce,a0 as oe,a4 as ue,y as T,z as U}from"../lite.js";import{L as le}from"./LineChart-BgrmtJD8.js";import{B as fe}from"./BlockLabel-B0HN-MOU.js";import{E as _e}from"./Empty-C76eC2zW.js";function V(a,e,s){const l=a.slice();return l[5]=e[s],l[7]=s,l}function W(a){let e,s=R(a[0].confidences),l=[];for(let t=0;t<s.length;t+=1)l[t]=X(V(a,s,t));return{c(){for(let t=0;t<l.length;t+=1)l[t].c();e=ee()},m(t,n){for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(t,n);M(t,e,n)},p(t,n){if(n&13){s=R(t[0].confidences);let i;for(i=0;i<s.length;i+=1){const u=V(t,s,i);l[i]?l[i].p(u,n):(l[i]=X(u),l[i].c(),l[i].m(e.parentNode,e))}for(;i<l.length;i+=1)l[i].d(1);l.length=s.length}},d(t){t&&j(e),se(l,t)}}}function X(a){let e,s,l,t,n,i,u,f,d,o,$=a[5].label+"",h,S,r,_,L,k=Math.round(a[5].confidence*100)+"",J,A,c,D,H,P;function te(){return a[4](a[7],a[5])}return{c(){e=N("button"),s=N("div"),l=N("meter"),f=E(),d=N("dl"),o=N("dt"),h=Y($),S=E(),_=N("div"),L=N("dd"),J=Y(k),A=Y("%"),c=E(),b(l,"aria-labelledby",t=F(`meter-text-${a[5].label}`)),b(l,"aria-label",n=a[5].label),b(l,"aria-valuenow",i=Math.round(a[5].confidence*100)),b(l,"aria-valuemin","0"),b(l,"aria-valuemax","100"),b(l,"class","bar svelte-1l15rn0"),b(l,"min","0"),b(l,"max","1"),l.value=u=a[5].confidence,z(l,"width",a[5].confidence*100+"%"),z(l,"background","var(--stat-background-fill)"),b(o,"id",r=F(`meter-text-${a[5].label}`)),b(o,"class","text svelte-1l15rn0"),b(_,"class","line svelte-1l15rn0"),b(L,"class","confidence svelte-1l15rn0"),b(d,"class","label svelte-1l15rn0"),b(s,"class","inner-wrap svelte-1l15rn0"),b(e,"class","confidence-set group svelte-1l15rn0"),b(e,"data-testid",D=`${a[5].label}-confidence-set`),G(e,"selectable",a[2])},m(O,v){M(O,e,v),g(e,s),g(s,l),g(s,f),g(s,d),g(d,o),g(o,h),g(o,S),g(d,_),g(d,L),g(L,J),g(L,A),g(e,c),H||(P=ne(e,"click",te),H=!0)},p(O,v){a=O,v&1&&t!==(t=F(`meter-text-${a[5].label}`))&&b(l,"aria-labelledby",t),v&1&&n!==(n=a[5].label)&&b(l,"aria-label",n),v&1&&i!==(i=Math.round(a[5].confidence*100))&&b(l,"aria-valuenow",i),v&1&&u!==(u=a[5].confidence)&&(l.value=u),v&1&&z(l,"width",a[5].confidence*100+"%"),v&1&&$!==($=a[5].label+"")&&K(h,$),v&1&&r!==(r=F(`meter-text-${a[5].label}`))&&b(o,"id",r),v&1&&k!==(k=Math.round(a[5].confidence*100)+"")&&K(J,k),v&1&&D!==(D=`${a[5].label}-confidence-set`)&&b(e,"data-testid",D),v&4&&G(e,"selectable",a[2])},d(O){O&&j(e),H=!1,P()}}}function be(a){let e,s,l=a[0].label+"",t,n,i=typeof a[0]=="object"&&a[0].confidences&&W(a);return{c(){e=N("div"),s=N("h2"),t=Y(l),n=E(),i&&i.c(),b(s,"class","output-class svelte-1l15rn0"),b(s,"data-testid","label-output-value"),G(s,"no-confidence",!("confidences"in a[0])),z(s,"background-color",a[1]||"transparent"),b(e,"class","container svelte-1l15rn0")},m(u,f){M(u,e,f),g(e,s),g(s,t),g(e,n),i&&i.m(e,null)},p(u,[f]){f&1&&l!==(l=u[0].label+"")&&K(t,l),f&1&&G(s,"no-confidence",!("confidences"in u[0])),f&2&&z(s,"background-color",u[1]||"transparent"),typeof u[0]=="object"&&u[0].confidences?i?i.p(u,f):(i=W(u),i.c(),i.m(e,null)):i&&(i.d(1),i=null)},i:Q,o:Q,d(u){u&&j(e),i&&i.d()}}}function F(a){return a.replace(/\s/g,"-")}function de(a,e,s){let{value:l}=e;const t=ae();let{color:n=void 0}=e,{selectable:i=!1}=e;const u=(f,d)=>{t("select",{index:f,value:d.label})};return a.$$set=f=>{"value"in f&&s(0,l=f.value),"color"in f&&s(1,n=f.color),"selectable"in f&&s(2,i=f.selectable)},[l,n,i,t,u]}class me extends p{constructor(e){super(),y(this,e,de,be,x,{value:0,color:1,selectable:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get color(){return this.$$.ctx[1]}set color(e){this.$$set({color:e}),m()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),m()}}const he=me;function Z(a){let e,s;return e=new fe({props:{Icon:le,label:a[6],disable:a[7]===!1}}),{c(){q(e.$$.fragment)},m(l,t){C(e,l,t),s=!0},p(l,t){const n={};t&64&&(n.label=l[6]),t&128&&(n.disable=l[7]===!1),e.$set(n)},i(l){s||(w(e.$$.fragment,l),s=!0)},o(l){B(e.$$.fragment,l),s=!1},d(l){I(e,l)}}}function ge(a){let e,s;return e=new _e({props:{unpadded_box:!0,$$slots:{default:[ke]},$$scope:{ctx:a}}}),{c(){q(e.$$.fragment)},m(l,t){C(e,l,t),s=!0},p(l,t){const n={};t&131072&&(n.$$scope={dirty:t,ctx:l}),e.$set(n)},i(l){s||(w(e.$$.fragment,l),s=!0)},o(l){B(e.$$.fragment,l),s=!1},d(l){I(e,l)}}}function ve(a){let e,s;return e=new he({props:{selectable:a[12],value:a[5],color:a[4]}}),e.$on("select",a[16]),{c(){q(e.$$.fragment)},m(l,t){C(e,l,t),s=!0},p(l,t){const n={};t&4096&&(n.selectable=l[12]),t&32&&(n.value=l[5]),t&16&&(n.color=l[4]),e.$set(n)},i(l){s||(w(e.$$.fragment,l),s=!0)},o(l){B(e.$$.fragment,l),s=!1},d(l){I(e,l)}}}function ke(a){let e,s;return e=new le({}),{c(){q(e.$$.fragment)},m(l,t){C(e,l,t),s=!0},i(l){s||(w(e.$$.fragment,l),s=!0)},o(l){B(e.$$.fragment,l),s=!1},d(l){I(e,l)}}}function we(a){let e,s,l,t,n,i,u;const f=[{autoscroll:a[0].autoscroll},{i18n:a[0].i18n},a[10]];let d={};for(let r=0;r<f.length;r+=1)d=re(d,f[r]);e=new ce({props:d}),e.$on("clear_status",a[15]);let o=a[11]&&Z(a);const $=[ve,ge],h=[];function S(r,_){return r[13]!==void 0&&r[13]!==null?0:1}return t=S(a),n=h[t]=$[t](a),{c(){q(e.$$.fragment),s=E(),o&&o.c(),l=E(),n.c(),i=ee()},m(r,_){C(e,r,_),M(r,s,_),o&&o.m(r,_),M(r,l,_),h[t].m(r,_),M(r,i,_),u=!0},p(r,_){const L=_&1025?oe(f,[_&1&&{autoscroll:r[0].autoscroll},_&1&&{i18n:r[0].i18n},_&1024&&ue(r[10])]):{};e.$set(L),r[11]?o?(o.p(r,_),_&2048&&w(o,1)):(o=Z(r),o.c(),w(o,1),o.m(l.parentNode,l)):o&&(T(),B(o,1,1,()=>{o=null}),U());let k=t;t=S(r),t===k?h[t].p(r,_):(T(),B(h[k],1,1,()=>{h[k]=null}),U(),n=h[t],n?n.p(r,_):(n=h[t]=$[t](r),n.c()),w(n,1),n.m(i.parentNode,i))},i(r){u||(w(e.$$.fragment,r),w(o),w(n),u=!0)},o(r){B(e.$$.fragment,r),B(o),B(n),u=!1},d(r){r&&(j(s),j(l),j(i)),I(e,r),o&&o.d(r),h[t].d(r)}}}function $e(a){let e,s;return e=new ie({props:{test_id:"label",visible:a[3],elem_id:a[1],elem_classes:a[2],container:a[7],scale:a[8],min_width:a[9],padding:!1,$$slots:{default:[we]},$$scope:{ctx:a}}}),{c(){q(e.$$.fragment)},m(l,t){C(e,l,t),s=!0},p(l,[t]){const n={};t&8&&(n.visible=l[3]),t&2&&(n.elem_id=l[1]),t&4&&(n.elem_classes=l[2]),t&128&&(n.container=l[7]),t&256&&(n.scale=l[8]),t&512&&(n.min_width=l[9]),t&146673&&(n.$$scope={dirty:t,ctx:l}),e.$set(n)},i(l){s||(w(e.$$.fragment,l),s=!0)},o(l){B(e.$$.fragment,l),s=!1},d(l){I(e,l)}}}function Le(a,e,s){let l,{gradio:t}=e,{elem_id:n=""}=e,{elem_classes:i=[]}=e,{visible:u=!0}=e,{color:f=void 0}=e,{value:d={}}=e,o=null,{label:$=t.i18n("label.label")}=e,{container:h=!0}=e,{scale:S=null}=e,{min_width:r=void 0}=e,{loading_status:_}=e,{show_label:L=!0}=e,{_selectable:k=!1}=e;const J=()=>t.dispatch("clear_status",_),A=({detail:c})=>t.dispatch("select",c);return a.$$set=c=>{"gradio"in c&&s(0,t=c.gradio),"elem_id"in c&&s(1,n=c.elem_id),"elem_classes"in c&&s(2,i=c.elem_classes),"visible"in c&&s(3,u=c.visible),"color"in c&&s(4,f=c.color),"value"in c&&s(5,d=c.value),"label"in c&&s(6,$=c.label),"container"in c&&s(7,h=c.container),"scale"in c&&s(8,S=c.scale),"min_width"in c&&s(9,r=c.min_width),"loading_status"in c&&s(10,_=c.loading_status),"show_label"in c&&s(11,L=c.show_label),"_selectable"in c&&s(12,k=c._selectable)},a.$$.update=()=>{a.$$.dirty&16417&&JSON.stringify(d)!==JSON.stringify(o)&&(s(14,o=d),t.dispatch("change")),a.$$.dirty&32&&s(13,l=d.label)},[t,n,i,u,f,d,$,h,S,r,_,L,k,l,o,J,A]}class Me extends p{constructor(e){super(),y(this,e,Le,$e,x,{gradio:0,elem_id:1,elem_classes:2,visible:3,color:4,value:5,label:6,container:7,scale:8,min_width:9,loading_status:10,show_label:11,_selectable:12})}get gradio(){return this.$$.ctx[0]}set gradio(e){this.$$set({gradio:e}),m()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),m()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),m()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),m()}get color(){return this.$$.ctx[4]}set color(e){this.$$set({color:e}),m()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),m()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),m()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),m()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),m()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),m()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),m()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),m()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),m()}}export{he as BaseLabel,Me as default};
//# sourceMappingURL=Index-Dau3WiSM.js.map
