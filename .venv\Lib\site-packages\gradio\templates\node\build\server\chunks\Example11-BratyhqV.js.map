{"version": 3, "file": "Example11-BratyhqV.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example11.js"], "sourcesContent": ["import { create_ssr_component, escape } from \"svelte/internal\";\nconst css = {\n  code: \"div.svelte-rgtszb{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gallery.svelte-rgtszb{display:flex;align-items:center;cursor:pointer;padding:var(--size-1) var(--size-2);text-align:left}\",\n  map: '{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n>\\\\n\\\\t{value ? (Array.isArray(value) ? value.join(\\\\\", \\\\\") : value) : \\\\\"\\\\\"}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\tdiv {\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t}\\\\n\\\\t.gallery {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAcC,iBAAI,CACH,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,MACd,CACA,sBAAS,CACR,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,UAAU,CAAE,IACb\"}'\n};\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  $$result.css.add(css);\n  return `<div class=\"${[\n    \"svelte-rgtszb\",\n    (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\")\n  ].join(\" \").trim()}\">${escape(value ? Array.isArray(value) ? value.join(\", \") : value : \"\")} </div>`;\n});\nexport {\n  Example as default\n};\n"], "names": [], "mappings": ";;AACA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,wMAAwM;AAChN,EAAE,GAAG,EAAE,26BAA26B;AACl7B,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,eAAe;AACnB,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC;AACxH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AACvG,CAAC;;;;"}