import path from "path-browserify";
export const globalHomeDir = "/home/<USER>";
export const getAppHomeDir = (appId) => `${globalHomeDir}/${appId}`;
export const resolveAppHomeBasedPath = (appId, filePath) => {
    const normalized = path.normalize(filePath);
    return path.resolve(getAppHomeDir(appId), filePath);
};
function ensureParent(pyodide, filePath) {
    const normalized = path.normalize(filePath);
    const dirPath = path.dirname(normalized);
    const dirNames = dirPath.split("/");
    const chDirNames = [];
    for (const dirName of dirNames) {
        chDirNames.push(dirName);
        const dirPath = chDirNames.join("/");
        if (pyodide.FS.analyzePath(dirPath).exists) {
            if (pyodide.FS.isDir(dirPath)) {
                throw new Error(`"${dirPath}" already exists and is not a directory.`);
            }
            continue;
        }
        try {
            pyodide.FS.mkdir(dirPath);
        }
        catch (err) {
            console.error(`Failed to create a directory "${dirPath}"`);
            throw err;
        }
    }
}
export function writeFileWithParents(pyodide, filePath, data, opts) {
    ensureParent(pyodide, filePath);
    pyodide.FS.writeFile(filePath, data, opts);
}
export function renameWithParents(pyodide, oldPath, newPath) {
    ensureParent(pyodide, newPath);
    pyodide.FS.rename(oldPath, newPath);
}
