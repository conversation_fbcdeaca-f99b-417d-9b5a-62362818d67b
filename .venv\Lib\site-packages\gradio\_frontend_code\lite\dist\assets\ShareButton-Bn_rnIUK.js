import{a as l,i as h,s as g,f as c,W as _,c as d,m as S,t as b,b as p,d as v,A as y}from"../lite.js";import{C}from"./Community-_qL8Iyvr.js";import{S as k}from"./utils-BsGrhMNe.js";function w(o){let t,r;return t=new _({props:{Icon:C,label:o[2]("common.share"),pending:o[3]}}),t.$on("click",o[5]),{c(){d(t.$$.fragment)},m(e,a){S(t,e,a),r=!0},p(e,[a]){const s={};a&4&&(s.label=e[2]("common.share")),a&8&&(s.pending=e[3]),t.$set(s)},i(e){r||(b(t.$$.fragment,e),r=!0)},o(e){p(t.$$.fragment,e),r=!1},d(e){v(t,e)}}}function x(o,t,r){const e=y();let{formatter:a}=t,{value:s}=t,{i18n:m}=t,i=!1;const f=async()=>{try{r(3,i=!0);const n=await a(s);e("share",{description:n})}catch(n){console.error(n);let u=n instanceof k?n.message:"Share failed.";e("error",u)}finally{r(3,i=!1)}};return o.$$set=n=>{"formatter"in n&&r(0,a=n.formatter),"value"in n&&r(1,s=n.value),"i18n"in n&&r(2,m=n.i18n)},[a,s,m,i,e,f]}class q extends l{constructor(t){super(),h(this,t,x,w,g,{formatter:0,value:1,i18n:2})}get formatter(){return this.$$.ctx[0]}set formatter(t){this.$$set({formatter:t}),c()}get value(){return this.$$.ctx[1]}set value(t){this.$$set({value:t}),c()}get i18n(){return this.$$.ctx[2]}set i18n(t){this.$$set({i18n:t}),c()}}export{q as S};
//# sourceMappingURL=ShareButton-Bn_rnIUK.js.map
