import{a as c,i as d,s as g,f as i,p as h,q as o,r as n,l as v,w as u,o as m}from"../lite.js";function y(l){let e;return{c(){e=h("div"),o(e,"class","prose svelte-zvfedn"),n(e,"table",l[1]==="table"),n(e,"gallery",l[1]==="gallery"),n(e,"selected",l[2])},m(t,s){v(t,e,s),e.innerHTML=l[0]},p(t,[s]){s&1&&(e.innerHTML=t[0]),s&2&&n(e,"table",t[1]==="table"),s&2&&n(e,"gallery",t[1]==="gallery"),s&4&&n(e,"selected",t[2])},i:u,o:u,d(t){t&&m(e)}}}function b(l,e,t){let{value:s}=e,{type:f}=e,{selected:r=!1}=e;return l.$$set=a=>{"value"in a&&t(0,s=a.value),"type"in a&&t(1,f=a.type),"selected"in a&&t(2,r=a.selected)},[s,f,r]}class q extends c{constructor(e){super(),d(this,e,b,y,g,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),i()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),i()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),i()}}export{q as default};
//# sourceMappingURL=Example-DO1mIO_7.js.map
