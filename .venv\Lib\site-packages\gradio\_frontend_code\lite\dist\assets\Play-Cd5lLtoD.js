import{a as i,i as p,s as c,Q as r,q as e,l as u,v as g,w as n,o as d}from"../lite.js";function w(a){let t,o;return{c(){t=r("svg"),o=r("polygon"),e(o,"points","5 3 19 12 5 21 5 3"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","currentColor"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round")},m(s,l){u(s,t,l),g(t,o)},p:n,i:n,o:n,d(s){s&&d(t)}}}class m extends i{constructor(t){super(),p(this,t,null,w,c,{})}}export{m as P};
//# sourceMappingURL=Play-Cd5lLtoD.js.map
