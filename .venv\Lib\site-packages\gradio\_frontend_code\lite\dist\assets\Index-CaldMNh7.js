import{a as te,i as se,s as le,f,B as ie,c as j,m as z,t as d,b,d as B,A as ne,Y as oe,S as ae,x as re,E as _e,l as Y,a0 as ue,a4 as fe,y as he,z as ce,o as H,I as J,a5 as K,a6 as L}from"../lite.js";import{U as me}from"./UploadText-CW3z6Ye_.js";import ge from"./Gallery-BWHkv_Ie.js";import{B as we}from"./FileUpload-CRh22YvE.js";/* empty css                                              */import"./Upload-D_TzP4YC.js";import"./BlockLabel-B0HN-MOU.js";import"./Empty-C76eC2zW.js";import"./ShareButton-Bn_rnIUK.js";import"./Community-_qL8Iyvr.js";import"./utils-BsGrhMNe.js";import"./Download-CgLP-Xl6.js";import"./Minimize-DJwpjnSa.js";import"./Image-Cvn5Jo_E.js";import"./Play-Cd5lLtoD.js";import"./IconButtonWrapper-Ck50MZwX.js";/* empty css                                             */import"./ModifyUpload-CWo4TCq1.js";import"./Undo-DitIvwTU.js";import"./DownloadLink-B8hI46-W.js";import"./file-url-Co2ROWca.js";import"./Image-Bd-jnd8M.js";/* empty css                                                   */import"./Video-B2DSnbGg.js";import"./hls-CnVhpNcu.js";import"./File-Kbo-bXuF.js";import"./Upload-TGDabKXH.js";function de(l){let e,n,s,i;function a(o){l[31](o)}function w(o){l[32](o)}let g={label:l[4],show_label:l[3],columns:l[13],rows:l[14],height:l[15],preview:l[16],object_fit:l[18],interactive:l[20],allow_preview:l[17],show_share_button:l[19],show_download_button:l[21],i18n:l[22].i18n,_fetch:l[30],show_fullscreen_button:l[23]};return l[1]!==void 0&&(g.selected_index=l[1]),l[0]!==void 0&&(g.value=l[0]),e=new ge({props:g}),J.push(()=>K(e,"selected_index",a)),J.push(()=>K(e,"value",w)),e.$on("change",l[33]),e.$on("select",l[34]),e.$on("share",l[35]),e.$on("error",l[36]),{c(){j(e.$$.fragment)},m(o,u){z(e,o,u),i=!0},p(o,u){const _={};u[0]&16&&(_.label=o[4]),u[0]&8&&(_.show_label=o[3]),u[0]&8192&&(_.columns=o[13]),u[0]&16384&&(_.rows=o[14]),u[0]&32768&&(_.height=o[15]),u[0]&65536&&(_.preview=o[16]),u[0]&262144&&(_.object_fit=o[18]),u[0]&1048576&&(_.interactive=o[20]),u[0]&131072&&(_.allow_preview=o[17]),u[0]&524288&&(_.show_share_button=o[19]),u[0]&2097152&&(_.show_download_button=o[21]),u[0]&4194304&&(_.i18n=o[22].i18n),u[0]&4194304&&(_._fetch=o[30]),u[0]&8388608&&(_.show_fullscreen_button=o[23]),!n&&u[0]&2&&(n=!0,_.selected_index=o[1],L(()=>n=!1)),!s&&u[0]&1&&(s=!0,_.value=o[0],L(()=>s=!1)),e.$set(_)},i(o){i||(d(e.$$.fragment,o),i=!0)},o(o){b(e.$$.fragment,o),i=!1},d(o){B(e,o)}}}function be(l){let e,n;return e=new we({props:{value:null,root:l[5],label:l[4],max_file_size:l[22].max_file_size,file_count:"multiple",file_types:l[9],i18n:l[22].i18n,upload:l[26],stream_handler:l[27],$$slots:{default:[ve]},$$scope:{ctx:l}}}),e.$on("upload",l[28]),e.$on("error",l[29]),{c(){j(e.$$.fragment)},m(s,i){z(e,s,i),n=!0},p(s,i){const a={};i[0]&32&&(a.root=s[5]),i[0]&16&&(a.label=s[4]),i[0]&4194304&&(a.max_file_size=s[22].max_file_size),i[0]&512&&(a.file_types=s[9]),i[0]&4194304&&(a.i18n=s[22].i18n),i[0]&4194304&&(a.upload=s[26]),i[0]&4194304&&(a.stream_handler=s[27]),i[0]&4194304|i[1]&128&&(a.$$scope={dirty:i,ctx:s}),e.$set(a)},i(s){n||(d(e.$$.fragment,s),n=!0)},o(s){b(e.$$.fragment,s),n=!1},d(s){B(e,s)}}}function ve(l){let e,n;return e=new me({props:{i18n:l[22].i18n,type:"gallery"}}),{c(){j(e.$$.fragment)},m(s,i){z(e,s,i),n=!0},p(s,i){const a={};i[0]&4194304&&(a.i18n=s[22].i18n),e.$set(a)},i(s){n||(d(e.$$.fragment,s),n=!0)},o(s){b(e.$$.fragment,s),n=!1},d(s){B(e,s)}}}function ke(l){let e,n,s,i,a,w;const g=[{autoscroll:l[22].autoscroll},{i18n:l[22].i18n},l[2]];let o={};for(let r=0;r<g.length;r+=1)o=oe(o,g[r]);e=new ae({props:o}),e.$on("clear_status",l[25]);const u=[be,de],_=[];function c(r,h){return r[20]&&r[24]?0:1}return s=c(l),i=_[s]=u[s](l),{c(){j(e.$$.fragment),n=re(),i.c(),a=_e()},m(r,h){z(e,r,h),Y(r,n,h),_[s].m(r,h),Y(r,a,h),w=!0},p(r,h){const A=h[0]&4194308?ue(g,[h[0]&4194304&&{autoscroll:r[22].autoscroll},h[0]&4194304&&{i18n:r[22].i18n},h[0]&4&&fe(r[2])]):{};e.$set(A);let v=s;s=c(r),s===v?_[s].p(r,h):(he(),b(_[v],1,1,()=>{_[v]=null}),ce(),i=_[s],i?i.p(r,h):(i=_[s]=u[s](r),i.c()),d(i,1),i.m(a.parentNode,a))},i(r){w||(d(e.$$.fragment,r),d(i),w=!0)},o(r){b(e.$$.fragment,r),b(i),w=!1},d(r){r&&(H(n),H(a)),B(e,r),_[s].d(r)}}}function je(l){let e,n;return e=new ie({props:{visible:l[8],variant:"solid",padding:!1,elem_id:l[6],elem_classes:l[7],container:l[10],scale:l[11],min_width:l[12],allow_overflow:!1,height:typeof l[15]=="number"?l[15]:void 0,$$slots:{default:[ke]},$$scope:{ctx:l}}}),{c(){j(e.$$.fragment)},m(s,i){z(e,s,i),n=!0},p(s,i){const a={};i[0]&256&&(a.visible=s[8]),i[0]&64&&(a.elem_id=s[6]),i[0]&128&&(a.elem_classes=s[7]),i[0]&1024&&(a.container=s[10]),i[0]&2048&&(a.scale=s[11]),i[0]&4096&&(a.min_width=s[12]),i[0]&32768&&(a.height=typeof s[15]=="number"?s[15]:void 0),i[0]&33546815|i[1]&128&&(a.$$scope={dirty:i,ctx:s}),e.$set(a)},i(s){n||(d(e.$$.fragment,s),n=!0)},o(s){b(e.$$.fragment,s),n=!1},d(s){B(e,s)}}}function ze(l,e,n){let s,{loading_status:i}=e,{show_label:a}=e,{label:w}=e,{root:g}=e,{elem_id:o=""}=e,{elem_classes:u=[]}=e,{visible:_=!0}=e,{value:c=null}=e,{file_types:r=["image","video"]}=e,{container:h=!0}=e,{scale:A=null}=e,{min_width:v=void 0}=e,{columns:U=[2]}=e,{rows:E=void 0}=e,{height:G="auto"}=e,{preview:I}=e,{allow_preview:q=!0}=e,{selected_index:k=null}=e,{object_fit:C="cover"}=e,{show_share_button:D=!1}=e,{interactive:F}=e,{show_download_button:N=!1}=e,{gradio:m}=e,{show_fullscreen_button:T=!0}=e;const M=ne(),O=()=>m.dispatch("clear_status",i),P=(...t)=>m.client.upload(...t),Q=(...t)=>m.client.stream(...t),R=t=>{const ee=Array.isArray(t.detail)?t.detail:[t.detail];n(0,c=ee.map(S=>S.mime_type?.includes("video")?{video:S,caption:null}:{image:S,caption:null})),m.dispatch("upload",c)},V=({detail:t})=>{n(2,i=i||{}),n(2,i.status="error",i),m.dispatch("error",t)},W=(...t)=>m.client.fetch(...t);function X(t){k=t,n(1,k)}function Z(t){c=t,n(0,c)}const y=()=>m.dispatch("change",c),p=t=>m.dispatch("select",t.detail),x=t=>m.dispatch("share",t.detail),$=t=>m.dispatch("error",t.detail);return l.$$set=t=>{"loading_status"in t&&n(2,i=t.loading_status),"show_label"in t&&n(3,a=t.show_label),"label"in t&&n(4,w=t.label),"root"in t&&n(5,g=t.root),"elem_id"in t&&n(6,o=t.elem_id),"elem_classes"in t&&n(7,u=t.elem_classes),"visible"in t&&n(8,_=t.visible),"value"in t&&n(0,c=t.value),"file_types"in t&&n(9,r=t.file_types),"container"in t&&n(10,h=t.container),"scale"in t&&n(11,A=t.scale),"min_width"in t&&n(12,v=t.min_width),"columns"in t&&n(13,U=t.columns),"rows"in t&&n(14,E=t.rows),"height"in t&&n(15,G=t.height),"preview"in t&&n(16,I=t.preview),"allow_preview"in t&&n(17,q=t.allow_preview),"selected_index"in t&&n(1,k=t.selected_index),"object_fit"in t&&n(18,C=t.object_fit),"show_share_button"in t&&n(19,D=t.show_share_button),"interactive"in t&&n(20,F=t.interactive),"show_download_button"in t&&n(21,N=t.show_download_button),"gradio"in t&&n(22,m=t.gradio),"show_fullscreen_button"in t&&n(23,T=t.show_fullscreen_button)},l.$$.update=()=>{l.$$.dirty[0]&1&&n(24,s=c===null?!0:c.length===0),l.$$.dirty[0]&2&&M("prop_change",{selected_index:k})},[c,k,i,a,w,g,o,u,_,r,h,A,v,U,E,G,I,q,C,D,F,N,m,T,s,O,P,Q,R,V,W,X,Z,y,p,x,$]}class ye extends te{constructor(e){super(),se(this,e,ze,je,le,{loading_status:2,show_label:3,label:4,root:5,elem_id:6,elem_classes:7,visible:8,value:0,file_types:9,container:10,scale:11,min_width:12,columns:13,rows:14,height:15,preview:16,allow_preview:17,selected_index:1,object_fit:18,show_share_button:19,interactive:20,show_download_button:21,gradio:22,show_fullscreen_button:23},null,[-1,-1])}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),f()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),f()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),f()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),f()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[8]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get file_types(){return this.$$.ctx[9]}set file_types(e){this.$$set({file_types:e}),f()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),f()}get columns(){return this.$$.ctx[13]}set columns(e){this.$$set({columns:e}),f()}get rows(){return this.$$.ctx[14]}set rows(e){this.$$set({rows:e}),f()}get height(){return this.$$.ctx[15]}set height(e){this.$$set({height:e}),f()}get preview(){return this.$$.ctx[16]}set preview(e){this.$$set({preview:e}),f()}get allow_preview(){return this.$$.ctx[17]}set allow_preview(e){this.$$set({allow_preview:e}),f()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),f()}get object_fit(){return this.$$.ctx[18]}set object_fit(e){this.$$set({object_fit:e}),f()}get show_share_button(){return this.$$.ctx[19]}set show_share_button(e){this.$$set({show_share_button:e}),f()}get interactive(){return this.$$.ctx[20]}set interactive(e){this.$$set({interactive:e}),f()}get show_download_button(){return this.$$.ctx[21]}set show_download_button(e){this.$$set({show_download_button:e}),f()}get gradio(){return this.$$.ctx[22]}set gradio(e){this.$$set({gradio:e}),f()}get show_fullscreen_button(){return this.$$.ctx[23]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),f()}}export{ge as BaseGallery,ye as default};
//# sourceMappingURL=Index-CaldMNh7.js.map
