import{a as m,i as p,s as c,f,e as b,p as g,q as r,r as u,l as z,v,u as E,h as C,j as q,t as B,b as R,o as j,I as k}from"../lite.js";function I(s){let e,l,a;const d=s[5].default,n=b(d,s,s[4],null);return{c(){e=g("div"),l=g("div"),n&&n.c(),r(l,"class","icon svelte-1oiin9d"),r(e,"class","empty svelte-1oiin9d"),r(e,"aria-label","Empty value"),u(e,"small",s[0]==="small"),u(e,"large",s[0]==="large"),u(e,"unpadded_box",s[1]),u(e,"small_parent",s[3])},m(t,i){z(t,e,i),v(e,l),n&&n.m(l,null),s[6](e),a=!0},p(t,[i]){n&&n.p&&(!a||i&16)&&E(n,d,t,t[4],a?q(d,t[4],i,null):C(t[4]),null),(!a||i&1)&&u(e,"small",t[0]==="small"),(!a||i&1)&&u(e,"large",t[0]==="large"),(!a||i&2)&&u(e,"unpadded_box",t[1]),(!a||i&8)&&u(e,"small_parent",t[3])},i(t){a||(B(n,t),a=!0)},o(t){R(n,t),a=!1},d(t){t&&j(e),n&&n.d(t),s[6](null)}}}function S(s){if(!s)return!1;const{height:e}=s.getBoundingClientRect(),{height:l}=s.parentElement?.getBoundingClientRect()||{height:e};return e>l+2}function w(s,e,l){let a,{$$slots:d={},$$scope:n}=e,{size:t="small"}=e,{unpadded_box:i=!1}=e,_;function h(o){k[o?"unshift":"push"](()=>{_=o,l(2,_)})}return s.$$set=o=>{"size"in o&&l(0,t=o.size),"unpadded_box"in o&&l(1,i=o.unpadded_box),"$$scope"in o&&l(4,n=o.$$scope)},s.$$.update=()=>{s.$$.dirty&4&&l(3,a=S(_))},[t,i,_,a,n,d,h]}class A extends m{constructor(e){super(),p(this,e,w,I,c,{size:0,unpadded_box:1})}get size(){return this.$$.ctx[0]}set size(e){this.$$set({size:e}),f()}get unpadded_box(){return this.$$.ctx[1]}set unpadded_box(e){this.$$set({unpadded_box:e}),f()}}export{A as E};
//# sourceMappingURL=Empty-C76eC2zW.js.map
