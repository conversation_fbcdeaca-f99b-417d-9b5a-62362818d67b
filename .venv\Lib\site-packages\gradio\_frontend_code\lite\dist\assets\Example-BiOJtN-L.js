import{a as h,i as g,s as y,f,p as v,k as b,q as m,J as p,r,l as z,v as k,a2 as q,n as w,w as c,o as S,a3 as C,I as E}from"../lite.js";/* empty css                                              */function I(t){let e,a=(t[0]?t[0]:"")+"",i,u;return{c(){e=v("div"),i=b(a),m(e,"class","svelte-1viwdyg"),p(()=>t[5].call(e)),r(e,"table",t[1]==="table"),r(e,"gallery",t[1]==="gallery"),r(e,"selected",t[2])},m(s,l){z(s,e,l),k(e,i),u=q(e,t[5].bind(e)),t[6](e)},p(s,[l]){l&1&&a!==(a=(s[0]?s[0]:"")+"")&&w(i,a),l&2&&r(e,"table",s[1]==="table"),l&2&&r(e,"gallery",s[1]==="gallery"),l&4&&r(e,"selected",s[2])},i:c,o:c,d(s){s&&S(e),u(),t[6](null)}}}function J(t,e){t.style.setProperty("--local-text-width",`${e&&e<150?e:200}px`),t.style.whiteSpace="unset"}function M(t,e,a){let{value:i}=e,{type:u}=e,{selected:s=!1}=e,l,d;C(()=>{J(d,l)});function o(){l=this.clientWidth,a(3,l)}function _(n){E[n?"unshift":"push"](()=>{d=n,a(4,d)})}return t.$$set=n=>{"value"in n&&a(0,i=n.value),"type"in n&&a(1,u=n.type),"selected"in n&&a(2,s=n.selected)},[i,u,s,l,d,o,_]}class j extends h{constructor(e){super(),g(this,e,M,I,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),f()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),f()}}export{j as default};
//# sourceMappingURL=Example-BiOJtN-L.js.map
