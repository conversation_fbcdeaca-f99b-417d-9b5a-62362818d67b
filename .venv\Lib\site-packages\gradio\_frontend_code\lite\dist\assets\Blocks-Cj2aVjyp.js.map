{"version": 3, "file": "Blocks-Cj2aVjyp.js", "sources": ["../../../core/src/api_docs/img/clear.svelte", "../../../core/src/api_docs/NoApi.svelte", "../../../core/src/api_docs/img/api-logo.svg", "../../../core/src/api_docs/ApiBanner.svelte", "../../../core/src/api_docs/utils.ts", "../../../core/src/api_docs/ParametersSnippet.svelte", "../../../core/src/api_docs/CopyButton.svelte", "../../../core/src/api_docs/InstallSnippet.svelte", "../../../core/src/api_docs/EndpointDetail.svelte", "../../../core/src/api_docs/CodeSnippet.svelte", "../../../core/src/api_docs/RecordingSnippet.svelte", "../../../core/src/api_docs/img/python.svg", "../../../core/src/api_docs/img/javascript.svg", "../../../core/src/api_docs/img/bash.svg", "../../../core/src/api_docs/ResponseSnippet.svelte", "../../../core/src/api_docs/ApiDocs.svelte", "../../../core/src/api_docs/ApiRecorder.svelte", "../../../core/src/gradio_helper.ts", "../../../core/src/RenderComponent.svelte", "../../../core/src/Render.svelte", "../../../core/src/MountComponents.svelte", "../../../core/src/images/logo.svg", "../../../core/src/Blocks.svelte"], "sourcesContent": ["<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 5 5\"\n\tversion=\"1.1\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\txml:space=\"preserve\"\n\tstyle=\"fill:currentColor;fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;\"\n>\n\t<g>\n\t\t<path\n\t\t\td=\"M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z\"\n\t\t/>\n\t</g>\n</svg>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport Clear from \"./img/clear.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let root: string;\n</script>\n\n<div class=\"wrap prose\">\n\t<h1>API Docs</h1>\n\t<p class=\"attention\">\n\t\tNo API Routes found for\n\t\t<code>\n\t\t\t{root}\n\t\t</code>\n\t</p>\n\t<p>\n\t\tTo expose an API endpoint of your app in this page, set the <code>\n\t\t\tapi_name\n\t\t</code>\n\t\tparameter of the event listener.\n\t\t<br />\n\t\tFor more information, visit the\n\t\t<a href=\"https://gradio.app/sharing_your_app/#api-page\" target=\"_blank\">\n\t\t\tAPI Page guide\n\t\t</a>\n\t\t. To hide the API documentation button and this page, set\n\t\t<code>show_api=False</code>\n\t\tin the\n\t\t<code>Blocks.launch()</code>\n\t\tmethod.\n\t</p>\n</div>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\t.wrap {\n\t\tpadding: var(--size-6);\n\t}\n\n\t.attention {\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.attention code {\n\t\tborder: none;\n\t\tbackground: none;\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\t}\n</style>\n", "export default \"data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport api_logo from \"./img/api-logo.svg\";\n\timport Clear from \"./img/clear.svelte\";\n\timport { BaseButton } from \"@gradio/button\";\n\n\texport let root: string;\n\texport let api_count: number;\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<h2>\n\t<img src={api_logo} alt=\"\" />\n\t<div class=\"title\">\n\t\tAPI documentation\n\t\t<div class=\"url\">\n\t\t\t{root}\n\t\t</div>\n\t</div>\n\t<span class=\"counts\">\n\t\t<BaseButton\n\t\t\tsize=\"sm\"\n\t\t\tvariant=\"secondary\"\n\t\t\telem_id=\"start-api-recorder\"\n\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t>\n\t\t\t<div class=\"loading-dot self-baseline\"></div>\n\t\t\t<p class=\"self-baseline btn-text\">API Recorder</p>\n\t\t</BaseButton>\n\t\t<p>\n\t\t\t<span class=\"url\">{api_count}</span> API endpoint{#if api_count > 1}s{/if}<br\n\t\t\t/>\n\t\t</p>\n\t</span>\n</h2>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\th2 {\n\t\tdisplay: flex;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tgap: var(--size-4);\n\t}\n\n\th2 img {\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-4);\n\t\tdisplay: inline-block;\n\t}\n\n\t.url {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: normal;\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\n\t\th2 img {\n\t\t\twidth: var(--size-5);\n\t\t}\n\t}\n\n\t.counts {\n\t\tmargin-top: auto;\n\t\tmargin-right: var(--size-8);\n\t\tmargin-bottom: auto;\n\t\tmargin-left: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-light);\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 12px;\n\t\theight: 12px;\n\t\tborder-radius: 6px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tmargin-right: 0.3rem;\n\t}\n\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t.title {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n\t.btn-text {\n\t\tfont-size: var(--text-lg);\n\t}\n</style>\n", "// eslint-disable-next-line complexity\nexport function represent_value(\n\tvalue: string,\n\ttype: string | undefined,\n\tlang: \"js\" | \"py\" | \"bash\" | null = null\n): string | null | number | boolean | Record<string, unknown> {\n\tif (type === undefined) {\n\t\treturn lang === \"py\" ? \"None\" : null;\n\t}\n\tif (value === null && lang === \"py\") {\n\t\treturn \"None\";\n\t}\n\tif (type === \"string\" || type === \"str\") {\n\t\treturn lang === null ? value : '\"' + value + '\"';\n\t} else if (type === \"number\") {\n\t\treturn lang === null ? parseFloat(value) : value;\n\t} else if (type === \"boolean\" || type == \"bool\") {\n\t\tif (lang === \"py\") {\n\t\t\tvalue = String(value);\n\t\t\treturn value === \"true\" ? \"True\" : \"False\";\n\t\t} else if (lang === \"js\" || lang === \"bash\") {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === \"true\";\n\t} else if (type === \"List[str]\") {\n\t\tvalue = JSON.stringify(value);\n\t\treturn value;\n\t} else if (type.startsWith(\"Literal['\")) {\n\t\t// a literal of strings\n\t\treturn '\"' + value + '\"';\n\t}\n\t// assume object type\n\tif (lang === null) {\n\t\treturn value === \"\" ? null : JSON.parse(value);\n\t} else if (typeof value === \"string\") {\n\t\tif (value === \"\") {\n\t\t\treturn lang === \"py\" ? \"None\" : \"null\";\n\t\t}\n\t\treturn value;\n\t}\n\tif (lang === \"bash\") {\n\t\tvalue = simplify_file_data(value);\n\t}\n\tif (lang === \"py\") {\n\t\tvalue = replace_file_data_with_file_function(value);\n\t}\n\treturn stringify_except_file_function(value);\n}\n\nexport function is_potentially_nested_file_data(obj: any): boolean {\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tif (obj.hasOwnProperty(\"url\") && obj.hasOwnProperty(\"meta\")) {\n\t\t\tif (\n\t\t\t\ttypeof obj.meta === \"object\" &&\n\t\t\t\tobj.meta !== null &&\n\t\t\t\tobj.meta._type === \"gradio.FileData\"\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t}\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tfor (let key in obj) {\n\t\t\tif (typeof obj[key] === \"object\") {\n\t\t\t\tlet result = is_potentially_nested_file_data(obj[key]);\n\t\t\t\tif (result) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\treturn false;\n}\n\nfunction simplify_file_data(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\tobj.url &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn { path: obj.url };\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = simplify_file_data(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = simplify_file_data(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction replace_file_data_with_file_function(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\tobj.url &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn `handle_file('${obj.url}')`;\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = replace_file_data_with_file_function(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = replace_file_data_with_file_function(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction stringify_except_file_function(obj: any): string {\n\tlet jsonString = JSON.stringify(obj, (key, value) => {\n\t\tif (value === null) {\n\t\t\treturn \"UNQUOTEDNone\";\n\t\t}\n\t\tif (\n\t\t\ttypeof value === \"string\" &&\n\t\t\tvalue.startsWith(\"handle_file(\") &&\n\t\t\tvalue.endsWith(\")\")\n\t\t) {\n\t\t\treturn `UNQUOTED${value}`; // Flag the special strings\n\t\t}\n\t\treturn value;\n\t});\n\tconst regex = /\"UNQUOTEDhandle_file\\(([^)]*)\\)\"/g;\n\tjsonString = jsonString.replace(regex, (match, p1) => `handle_file(${p1})`);\n\tconst regexNone = /\"UNQUOTEDNone\"/g;\n\treturn jsonString.replace(regexNone, \"None\");\n}\n", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\timport { represent_value } from \"./utils\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot\" />\n\t</div>\n\tAccepts {endpoint_returns.length} parameter{#if endpoint_returns.length != 1}s{/if}:\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, python_type, component, parameter_name, parameter_has_default, parameter_default }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p style=\"white-space: nowrap; overflow-x: auto;\">\n\t\t\t\t<span class=\"code\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{current_language !== \"bash\" && parameter_name\n\t\t\t\t\t\t? parameter_name\n\t\t\t\t\t\t: \"[\" + i + \"]\"}</span\n\t\t\t\t>\n\t\t\t\t<span class=\"code highlight\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{#if parameter_has_default && parameter_default === null}&nbsp;|\n\t\t\t\t\t\t\tNone{/if}{:else}{js_returns[i].type || \"any\"}{/if}</span\n\t\t\t\t>\n\t\t\t\t{#if !parameter_has_default || current_language == \"bash\"}<span\n\t\t\t\t\t\tstyle=\"font-weight:bold\">Required</span\n\t\t\t\t\t>{:else}<span> Default: </span><span\n\t\t\t\t\t\tclass=\"code\"\n\t\t\t\t\t\tstyle=\"font-size: var(--text-sm);\"\n\t\t\t\t\t\t>{represent_value(parameter_default, python_type.type, \"py\")}</span\n\t\t\t\t\t>{/if}\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe input value that is provided in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->. {python_type.description}\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t\tmargin-bottom: 12px;\n\t}\n\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tdisplay: inline;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t\tmargin-top: var(--size-1);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-right: auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { BaseButton } from \"@gradio/button\";\n\texport let code: string;\n\tlet copy_text = \"copy\";\n\n\tfunction copy(): void {\n\t\tnavigator.clipboard.writeText(code);\n\t\tcopy_text = \"copied!\";\n\t\tsetTimeout(() => {\n\t\t\tcopy_text = \"copy\";\n\t\t}, 1500);\n\t}\n</script>\n\n<BaseButton size=\"sm\" on:click={copy}>\n\t{copy_text}\n</BaseButton>\n", "<script lang=\"ts\">\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n\n\tlet py_install = \"pip install gradio_client\";\n\tlet js_install = \"npm i -D @gradio/client\";\n\tlet bash_install = \"curl --version\";\n</script>\n\n<Block border_mode=\"contrast\">\n\t<code>\n\t\t{#if current_language === \"python\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={py_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {py_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={js_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {js_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"bash\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={bash_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {bash_install}</pre>\n\t\t\t</div>\n\t\t{/if}\n\t</code>\n</Block>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let api_name: string | null = null;\n\texport let fn_index: number | null = null;\n\texport let named: boolean;\n</script>\n\n{#if named}\n\t<h3>\n\t\tapi_name:\n\t\t<span class=\"post\">{\"/\" + api_name}</span>\n\t</h3>\n{:else}\n\t<h3>\n\t\tfn_index:\n\t\t<span class=\"post\">{fn_index}</span>\n\t</h3>\n{/if}\n\n<style>\n\th3 {\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.post {\n\t\tmargin-right: var(--size-2);\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: var(--size-1);\n\t\tpadding-left: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport Copy<PERSON>utton from \"./CopyButton.svelte\";\n\timport { represent_value, is_potentially_nested_file_data } from \"./utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport EndpointDetail from \"./EndpointDetail.svelte\";\n\n\tinterface EndpointParameter {\n\t\tlabel: string;\n\t\ttype: string;\n\t\tpython_type: { type: string };\n\t\tcomponent: string;\n\t\texample_input: string;\n\t\tserializer: string;\n\t}\n\n\texport let dependency: Dependency;\n\texport let dependency_index: number;\n\texport let root: string;\n\texport let api_prefix: string;\n\texport let space_id: string | null;\n\texport let endpoint_parameters: any;\n\texport let named: boolean;\n\texport let username: string | null;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n\n\tlet python_code: HTMLElement;\n\tlet js_code: HTMLElement;\n\tlet bash_post_code: HTMLElement;\n\tlet bash_get_code: HTMLElement;\n\n\tlet has_file_path = endpoint_parameters.some((param: EndpointParameter) =>\n\t\tis_potentially_nested_file_data(param.example_input)\n\t);\n\tlet blob_components = [\"Audio\", \"File\", \"Image\", \"Video\"];\n\tlet blob_examples: any[] = endpoint_parameters.filter(\n\t\t(param: EndpointParameter) => blob_components.includes(param.component)\n\t);\n\n\t$: normalised_api_prefix = api_prefix ? api_prefix : \"/\";\n\t$: normalised_root = root.replace(/\\/$/, \"\");\n</script>\n\n<div class=\"container\">\n\t{#if named}\n\t\t<EndpointDetail {named} api_name={dependency.api_name} />\n\t{:else}\n\t\t<EndpointDetail {named} fn_index={dependency_index} />\n\t{/if}\n\t{#if current_language === \"python\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client{#if has_file_path}, handle_file{/if}\n\nclient = Client(<span class=\"token string\">\"{space_id || root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, auth=(\"{username}\", **password**){/if})\nresult = client.<span class=\"highlight\">predict</span\n\t\t\t\t\t\t>(<!--\n-->{#each endpoint_parameters as { python_type, example_input, parameter_name, parameter_has_default, parameter_default }, i}<!--\n        -->\n\t\t{parameter_name\n\t\t\t\t\t\t\t\t? parameter_name + \"=\"\n\t\t\t\t\t\t\t\t: \"\"}<span\n\t\t\t\t\t\t\t\t>{represent_value(\n\t\t\t\t\t\t\t\t\tparameter_has_default ? parameter_default : example_input,\n\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t>,{/each}<!--\n\n\t\t-->\n\t\tapi_name=<span class=\"api-name\">\"/{dependency.api_name}\"</span><!--\n\t\t-->\n)\n<span class=\"highlight\">print</span>(result)</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_language === \"javascript\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; Client &rbrace; from \"@gradio/client\";\n{#each blob_examples as { component, example_input }, i}<!--\n-->\nconst response_{i} = await fetch(\"{example_input.url}\");\nconst example{component} = await response_{i}.blob();\n\t\t\t\t\t\t{/each}<!--\n-->\nconst client = await Client.connect(<span class=\"token string\"\n\t\t\t\t\t\t\t>\"{space_id || root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, &lbrace;auth: [\"{username}\", **password**]&rbrace;{/if});\nconst result = await client.predict({#if named}<span class=\"api-name\"\n\t\t\t\t\t\t\t\t>\"/{dependency.api_name}\"</span\n\t\t\t\t\t\t\t>{:else}{dependency_index}{/if}, &lbrace; <!--\n-->{#each endpoint_parameters as { label, parameter_name, type, python_type, component, example_input, serializer }, i}<!--\n\t\t-->{#if blob_components.includes(component)}<!--\n\t-->\n\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{parameter_name}: example{component}</span\n\t\t\t\t\t\t\t\t>, <!--\n\t\t--><span class=\"desc\"><!--\n\t\t--></span\n\t\t\t\t\t\t\t\t><!--\n\t\t-->{:else}<!--\n\t-->\t\t\n\t\t<span class=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{parameter_name}: {represent_value(\n\t\t\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t\t>, <!--\n--><!--\n-->{/if}\n\t\t\t\t\t\t{/each}\n&rbrace;);\n\nconsole.log(result.data);\n</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_language === \"bash\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={bash_post_code?.innerText}></CopyButton>\n\t\t\t\t</div>\n\n\t\t\t\t<div bind:this={bash_post_code}>\n\t\t\t\t\t<pre>curl -X POST {normalised_root}{normalised_api_prefix}/call/{dependency.api_name} -s -H \"Content-Type: application/json\" -d '{\"{\"}\n  \"data\": [{#each endpoint_parameters as { label, parameter_name, type, python_type, component, example_input, serializer }, i}\n\t\t\t\t\t\t\t<!-- \n-->{represent_value(\n\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\"bash\"\n\t\t\t\t\t\t\t)}{#if i < endpoint_parameters.length - 1},\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/each}\n]{\"}\"}' \\\n  | awk -F'\"' '{\"{\"} print $4{\"}\"}'  \\\n  | read EVENT_ID; curl -N {normalised_root}{normalised_api_prefix}/call/{dependency.api_name}/$EVENT_ID</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{/if}\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Dependency, Payload } from \"../types\";\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { represent_value } from \"./utils\";\n\timport { onMount, tick } from \"svelte\";\n\n\texport let dependencies: Dependency[];\n\texport let short_root: string;\n\texport let root: string;\n\texport let api_prefix = \"\";\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n\texport let username: string | null;\n\n\tlet python_code: HTMLElement;\n\tlet python_code_text: string;\n\tlet js_code: HTMLElement;\n\tlet bash_code: HTMLElement;\n\n\texport let api_calls: Payload[] = [];\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(\n\t\t\troot.replace(/\\/$/, \"\") + api_prefix + \"/info/?all_endpoints=true\"\n\t\t);\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\n\tlet endpoints_info: any;\n\tlet py_zipped: { call: string; api_name: string }[] = [];\n\tlet js_zipped: { call: string; api_name: string }[] = [];\n\tlet bash_zipped: { call: string; api_name: string }[] = [];\n\n\tfunction format_api_call(call: Payload, lang: \"py\" | \"js\" | \"bash\"): string {\n\t\tconst api_name = `/${dependencies[call.fn_index].api_name}`;\n\t\t// If an input is undefined (distinct from null) then it corresponds to a State component.\n\t\tlet call_data_excluding_state = call.data.filter(\n\t\t\t(d) => typeof d !== \"undefined\"\n\t\t);\n\n\t\tconst params = call_data_excluding_state\n\t\t\t.map((param, index) => {\n\t\t\t\tif (endpoints_info[api_name]) {\n\t\t\t\t\tconst param_info = endpoints_info[api_name].parameters[index];\n\t\t\t\t\tif (!param_info) {\n\t\t\t\t\t\treturn undefined;\n\t\t\t\t\t}\n\t\t\t\t\tconst param_name = param_info.parameter_name;\n\t\t\t\t\tconst python_type = param_info.python_type.type;\n\t\t\t\t\tif (lang === \"py\") {\n\t\t\t\t\t\treturn `  ${param_name}=${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t} else if (lang === \"js\") {\n\t\t\t\t\t\treturn `    ${param_name}: ${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t} else if (lang === \"bash\") {\n\t\t\t\t\t\treturn `    ${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"bash\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn `  ${represent_value(param as string, undefined, lang)}`;\n\t\t\t})\n\t\t\t.filter((d) => typeof d !== \"undefined\")\n\t\t\t.join(\",\\n\");\n\t\tif (params) {\n\t\t\tif (lang === \"py\") {\n\t\t\t\treturn `${params},\\n`;\n\t\t\t} else if (lang === \"js\") {\n\t\t\t\treturn `{\\n${params},\\n}`;\n\t\t\t} else if (lang === \"bash\") {\n\t\t\t\treturn `\\n${params}\\n`;\n\t\t\t}\n\t\t}\n\t\tif (lang === \"py\") {\n\t\t\treturn \"\";\n\t\t}\n\t\treturn \"\\n\";\n\t}\n\n\tonMount(async () => {\n\t\tconst data = await get_info();\n\t\tendpoints_info = data[\"named_endpoints\"];\n\t\tlet py_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"py\")\n\t\t);\n\t\tlet js_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"js\")\n\t\t);\n\t\tlet bash_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"bash\")\n\t\t);\n\t\tlet api_names: string[] = api_calls.map(\n\t\t\t(call) => dependencies[call.fn_index].api_name || \"\"\n\t\t);\n\t\tpy_zipped = py_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\t\tjs_zipped = js_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\t\tbash_zipped = bash_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\n\t\tawait tick();\n\n\t\tpython_code_text = python_code.innerText;\n\t});\n</script>\n\n<div class=\"container\">\n\t<!-- <EndpointDetail {named} api_name={dependency.api_name} /> -->\n\t<Block border_mode={\"focus\"}>\n\t\t{#if current_language === \"python\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code_text} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client, file\n\nclient = Client(<span class=\"token string\">\"{short_root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, auth=(\"{username}\", **password**){/if})\n{#each py_zipped as { call, api_name }}<!--\n-->\nclient.<span class=\"highlight\"\n\t\t\t\t\t\t\t\t>predict(\n{call}  api_name=<span class=\"api-name\">\"/{api_name}\"</span>\n)\n</span>{/each}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; Client &rbrace; from \"@gradio/client\";\n\nconst app = await Client.connect(<span class=\"token string\">\"{short_root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, &lbrace;auth: [\"{username}\", **password**]&rbrace;{/if});\n\t\t\t\t\t{#each js_zipped as { call, api_name }}<!--\n\t\t\t\t\t-->\nawait client.predict(<span\n\t\t\t\t\t\t\t\tclass=\"api-name\">\n  \"/{api_name}\"</span\n\t\t\t\t\t\t\t>{#if call}, {call}{/if});\n\t\t\t\t\t\t{/each}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{:else if current_language === \"bash\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={bash_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={bash_code}>\n\t\t\t\t\t{#each bash_zipped as { call, api_name }}\n\t\t\t\t\t\t<pre>curl -X POST {short_root}call/{api_name} -s -H \"Content-Type: application/json\" -d '{\"{\"} \n\t\"data\": [{call}]{\"}\"}' \\\n  | awk -F'\"' '{\"{\"} print $4{\"}\"}' \\\n  | read EVENT_ID; curl -N {short_root}call/{api_name}/$EVENT_ID</pre>\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{/if}\n\t</Block>\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot toggle-right\" />\n\t</div>\n\tReturns {#if endpoint_returns.length > 1}\n\t\t{current_language == \"python\" ? \"tuple\" : \"list\"} of {endpoint_returns.length}\n\t\telements{:else}\n\t\t1 element{/if}\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, type, python_type, component, serializer }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p>\n\t\t\t\t{#if endpoint_returns.length > 1}\n\t\t\t\t\t<span class=\"code\">[{i}]</span>\n\t\t\t\t{/if}\n\t\t\t\t<span class=\"code highlight\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{:else}{js_returns[\n\t\t\t\t\t\t\ti\n\t\t\t\t\t\t].type}{/if}</span\n\t\t\t\t>\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe output value that appears in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->.\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t}\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tmargin-right: 10px;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-left: auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\t/* eslint-disable */\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport NoApi from \"./NoApi.svelte\";\n\timport type { Client } from \"@gradio/client\";\n\timport type { Payload } from \"../types\";\n\n\timport ApiBanner from \"./ApiBanner.svelte\";\n\timport { BaseButton as Button } from \"@gradio/button\";\n\timport ParametersSnippet from \"./ParametersSnippet.svelte\";\n\timport InstallSnippet from \"./InstallSnippet.svelte\";\n\timport CodeSnippet from \"./CodeSnippet.svelte\";\n\timport RecordingSnippet from \"./RecordingSnippet.svelte\";\n\n\timport python from \"./img/python.svg\";\n\timport javascript from \"./img/javascript.svg\";\n\timport bash from \"./img/bash.svg\";\n\timport ResponseSnippet from \"./ResponseSnippet.svelte\";\n\n\texport let dependencies: Dependency[];\n\texport let root: string;\n\texport let app: Awaited<ReturnType<typeof Client.connect>>;\n\texport let space_id: string | null;\n\texport let root_node: ComponentMeta;\n\texport let username: string | null;\n\n\tconst js_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-js-client\";\n\tconst py_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-python-client\";\n\tconst bash_docs =\n\t\t\"https://www.gradio.app/guides/querying-gradio-apps-with-curl\";\n\tconst spaces_docs_suffix = \"#connecting-to-a-hugging-face-space\";\n\n\tlet api_count = dependencies.filter(\n\t\t(dependency) => dependency.show_api\n\t).length;\n\n\tif (root === \"\") {\n\t\troot = location.protocol + \"//\" + location.host + location.pathname;\n\t}\n\tif (!root.endsWith(\"/\")) {\n\t\troot += \"/\";\n\t}\n\n\texport let api_calls: Payload[] = [];\n\tlet current_language: \"python\" | \"javascript\" | \"bash\" = \"python\";\n\n\tconst langs = [\n\t\t[\"python\", python],\n\t\t[\"javascript\", javascript],\n\t\t[\"bash\", bash]\n\t] as const;\n\n\tlet is_running = false;\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(\n\t\t\troot.replace(/\\/$/, \"\") + app.api_prefix + \"/info\"\n\t\t);\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\tasync function get_js_info(): Promise<Record<string, any>> {\n\t\tlet js_api_info = await app.view_api();\n\t\treturn js_api_info;\n\t}\n\n\tlet info: {\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t};\n\n\tlet js_info: Record<string, any>;\n\n\tget_info().then((data) => {\n\t\tinfo = data;\n\t});\n\n\tget_js_info().then((js_api_info) => {\n\t\tjs_info = js_api_info;\n\t});\n\n\tconst dispatch = createEventDispatcher();\n\n\tonMount(() => {\n\t\tdocument.body.style.overflow = \"hidden\";\n\t\tif (\"parentIFrame\" in window) {\n\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t}\n\t\treturn () => {\n\t\t\tdocument.body.style.overflow = \"auto\";\n\t\t};\n\t});\n</script>\n\n{#if info}\n\t{#if api_count}\n\t\t<div class=\"banner-wrap\">\n\t\t\t<ApiBanner on:close root={space_id || root} {api_count} />\n\t\t</div>\n\n\t\t<div class=\"docs-wrap\">\n\t\t\t<div class=\"client-doc\">\n\t\t\t\t<p style=\"font-size: var(--text-lg);\">\n\t\t\t\t\tChoose a language to see the code snippets for interacting with the\n\t\t\t\t\tAPI.\n\t\t\t\t</p>\n\t\t\t</div>\n\t\t\t<div class=\"endpoint\">\n\t\t\t\t<div class=\"snippets\">\n\t\t\t\t\t{#each langs as [language, img]}\n\t\t\t\t\t\t<li\n\t\t\t\t\t\t\tclass=\"snippet\n\t\t\t\t\t\t{current_language === language ? 'current-lang' : 'inactive-lang'}\"\n\t\t\t\t\t\t\ton:click={() => (current_language = language)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<img src={img} alt=\"\" />\n\t\t\t\t\t\t\t{language}\n\t\t\t\t\t\t</li>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t\t{#if api_calls.length}\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<p\n\t\t\t\t\t\t\tid=\"num-recorded-api-calls\"\n\t\t\t\t\t\t\tstyle=\"font-size: var(--text-lg); font-weight:bold; margin: 10px 0px;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t🪄 Recorded API Calls <span class=\"api-count\"\n\t\t\t\t\t\t\t\t>[{api_calls.length}]</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</p>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\tHere is the code snippet to replay the most recently recorded API\n\t\t\t\t\t\t\tcalls using the {current_language}\n\t\t\t\t\t\t\tclient.\n\t\t\t\t\t\t</p>\n\n\t\t\t\t\t\t<RecordingSnippet\n\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t{api_calls}\n\t\t\t\t\t\t\t{dependencies}\n\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\tapi_prefix={app.api_prefix}\n\t\t\t\t\t\t\tshort_root={space_id || root}\n\t\t\t\t\t\t\t{username}\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\tNote: Some API calls only affect the UI, so when using the\n\t\t\t\t\t\t\tclients, the desired result may be achieved with only a subset of\n\t\t\t\t\t\t\tthe recorded calls.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<p\n\t\t\t\t\t\tstyle=\"font-size: var(--text-lg); font-weight:bold; margin: 30px 0px 10px;\"\n\t\t\t\t\t>\n\t\t\t\t\t\tAPI Documentation\n\t\t\t\t\t</p>\n\t\t\t\t{:else}\n\t\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t\t{#if current_language == \"python\" || current_language == \"javascript\"}\n\t\t\t\t\t\t\t1. Install the\n\t\t\t\t\t\t\t<span style=\"text-transform:capitalize\">{current_language}</span>\n\t\t\t\t\t\t\tclient (<a\n\t\t\t\t\t\t\t\thref={current_language == \"python\" ? py_docs : js_docs}\n\t\t\t\t\t\t\t\ttarget=\"_blank\">docs</a\n\t\t\t\t\t\t\t>) if you don't already have it installed.\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t1. Confirm that you have cURL installed on your system.\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</p>\n\n\t\t\t\t\t<InstallSnippet {current_language} />\n\n\t\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t\t2. Find the API endpoint below corresponding to your desired\n\t\t\t\t\t\tfunction in the app. Copy the code snippet, replacing the\n\t\t\t\t\t\tplaceholder values with your own input data.\n\t\t\t\t\t\t{#if space_id}If this is a private Space, you may need to pass your\n\t\t\t\t\t\t\tHugging Face token as well (<a\n\t\t\t\t\t\t\t\thref={current_language == \"python\"\n\t\t\t\t\t\t\t\t\t? py_docs + spaces_docs_suffix\n\t\t\t\t\t\t\t\t\t: current_language == \"javascript\"\n\t\t\t\t\t\t\t\t\t\t? js_docs + spaces_docs_suffix\n\t\t\t\t\t\t\t\t\t\t: bash_docs}\n\t\t\t\t\t\t\t\tclass=\"underline\"\n\t\t\t\t\t\t\t\ttarget=\"_blank\">read more</a\n\t\t\t\t\t\t\t>).{/if}\n\n\t\t\t\t\t\tOr use the\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"loading-dot\"></div>\n\t\t\t\t\t\t\t<p class=\"self-baseline\">API Recorder</p>\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\tto automatically generate your API requests.\n\t\t\t\t\t\t{#if current_language == \"bash\"}<br />&nbsp;<br />Making a\n\t\t\t\t\t\t\tprediction and getting a result requires\n\t\t\t\t\t\t\t<strong>2 requests</strong>: a\n\t\t\t\t\t\t\t<code>POST</code>\n\t\t\t\t\t\t\tand a <code>GET</code> request. The <code>POST</code> request\n\t\t\t\t\t\t\treturns an <code>EVENT_ID</code>, which is used in the second\n\t\t\t\t\t\t\t<code>GET</code> request to fetch the results. In these snippets,\n\t\t\t\t\t\t\twe've used <code>awk</code> and <code>read</code> to parse the\n\t\t\t\t\t\t\tresults, combining these two requests into one command for ease of\n\t\t\t\t\t\t\tuse. {#if username !== null}\n\t\t\t\t\t\t\t\tNote: connecting to an authenticated app requires an additional\n\t\t\t\t\t\t\t\trequest.{/if} See\n\t\t\t\t\t\t\t<a href={bash_docs} target=\"_blank\">curl docs</a>.\n\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t<!-- <span\n\t\t\t\t\t\t\tid=\"api-recorder\"\n\t\t\t\t\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t\t\t\t\t>🪄 API Recorder</span\n\t\t\t\t\t\t> to automatically generate your API requests! -->\n\t\t\t\t\t</p>\n\t\t\t\t{/if}\n\n\t\t\t\t{#each dependencies as dependency, dependency_index}\n\t\t\t\t\t{#if dependency.show_api && info.named_endpoints[\"/\" + dependency.api_name]}\n\t\t\t\t\t\t<div class=\"endpoint-container\">\n\t\t\t\t\t\t\t<CodeSnippet\n\t\t\t\t\t\t\t\tnamed={true}\n\t\t\t\t\t\t\t\tendpoint_parameters={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\t{dependency}\n\t\t\t\t\t\t\t\t{dependency_index}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t{space_id}\n\t\t\t\t\t\t\t\t{username}\n\t\t\t\t\t\t\t\tapi_prefix={app.api_prefix}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<ParametersSnippet\n\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t.parameters}\n\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<ResponseSnippet\n\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].returns}\n\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t.returns}\n\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</div>\n\t{:else}\n\t\t<NoApi {root} on:close />\n\t{/if}\n{/if}\n\n<style>\n\t.banner-wrap {\n\t\tposition: relative;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tpadding: var(--size-4) var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t@media (--screen-md) {\n\t\t.banner-wrap {\n\t\t\tfont-size: var(--text-xl);\n\t\t}\n\t}\n\n\t.docs-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t}\n\n\t.endpoint {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-6);\n\t\tpadding-top: var(--size-1);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.client-doc {\n\t\tpadding-top: var(--size-6);\n\t\tpadding-right: var(--size-6);\n\t\tpadding-left: var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.library {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding: 0px var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-size: var(--text-md);\n\t\ttext-decoration: none;\n\t}\n\n\t.snippets {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-4);\n\t}\n\n\t.snippets > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.snippet {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-1) var(--size-1-5);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: 1;\n\t\tuser-select: none;\n\t\ttext-transform: capitalize;\n\t}\n\n\t.current-lang {\n\t\tborder: 1px solid var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.inactive-lang {\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.inactive-lang:hover,\n\t.inactive-lang:focus {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.snippet img {\n\t\tmargin-right: var(--size-1-5);\n\t\twidth: var(--size-3);\n\t}\n\n\t.header {\n\t\tmargin-top: var(--size-6);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.endpoint-container {\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t\tborder: 1px solid var(--body-text-color);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-3);\n\t\tpadding-top: 0;\n\t}\n\n\ta {\n\t\ttext-decoration: underline;\n\t}\n\n\tp.padded {\n\t\tpadding: 15px 0px;\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t#api-recorder {\n\t\tborder: 1px solid var(--color-accent);\n\t\tbackground-color: var(--color-accent-soft);\n\t\tpadding: 0px var(--size-2);\n\t\tborder-radius: var(--size-1);\n\t\tcursor: pointer;\n\t}\n\n\tcode {\n\t\tfont-size: var(--text-md);\n\t}\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tborder-radius: 5px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tmargin-right: 0.25rem;\n\t}\n\t:global(.docs-wrap .sm.secondary) {\n\t\tpadding-top: 1px;\n\t\tpadding-bottom: 1px;\n\t}\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t.api-count {\n\t\tfont-weight: bold;\n\t\tcolor: #fd7b00;\n\t\talign-self: baseline;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-md);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Payload, Dependency } from \"../types\";\n\timport { BaseButton as <PERSON><PERSON> } from \"@gradio/button\";\n\n\texport let api_calls: Payload[] = [];\n\texport let dependencies: Dependency[];\n</script>\n\n<div id=\"api-recorder\">\n\t<Button size=\"sm\" variant=\"secondary\">\n\t\t<div class=\"loading-dot self-baseline\"></div>\n\t\t<p class=\"self-baseline\">Recording API Calls:</p>\n\t\t<p class=\"self-baseline api-section\">\n\t\t\t<span class=\"api-count\">\n\t\t\t\t[{api_calls.length}]\n\t\t\t</span>\n\t\t\t{#if api_calls.length > 0}\n\t\t\t\t<span class=\"api-name\"\n\t\t\t\t\t>/{dependencies[api_calls[api_calls.length - 1].fn_index]\n\t\t\t\t\t\t.api_name}</span\n\t\t\t\t>\n\t\t\t{/if}\n\t\t</p>\n\t</Button>\n</div>\n\n<style>\n\t.api-name {\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t\tcolor: #fd7b00;\n\t}\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tborder-radius: 5px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tanimation: loading-dot 2s infinite linear;\n\t\tanimation-delay: 0.25s;\n\t\tmargin-left: 0.25rem;\n\t\tmargin-right: 0.5rem;\n\t}\n\t:global(.docs-wrap .sm.secondary) {\n\t\tpadding-top: 1px;\n\t\tpadding-bottom: 1px;\n\t}\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t@keyframes loading-dot {\n\t\t0% {\n\t\t\tbox-shadow: 9999px 0 0 -1px;\n\t\t}\n\t\t50% {\n\t\t\tbox-shadow: 9999px 0 0 2px;\n\t\t}\n\t\t100% {\n\t\t\tbox-shadow: 9999px 0 0 -1px;\n\t\t}\n\t}\n\t.api-count {\n\t\tfont-weight: bold;\n\t\tcolor: #fd7b00;\n\t\talign-self: baseline;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-sm);\n\t}\n\t.api-section {\n\t\tmargin-left: 4px;\n\t}\n</style>\n", "import { format } from \"svelte-i18n\";\nimport { get } from \"svelte/store\";\nexport { Gradio } from \"@gradio/utils\";\n\nexport const formatter = get(format);\n\nexport type I18nFormatter = typeof formatter;\n", "<svelte:options immutable={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"./gradio_helper\";\n\timport type { ComponentMeta, ThemeMode } from \"./types\";\n\timport type { SvelteComponent, ComponentType } from \"svelte\";\n\t// @ts-ignore\n\timport { bind, binding_callbacks } from \"svelte/internal\";\n\n\texport let root: string;\n\texport let component: ComponentMeta[\"component\"];\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let instance: ComponentMeta[\"instance\"];\n\texport let value: any;\n\t// export let gradio: Gradio;\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let _id: number;\n\n\tconst s = (id: number, p: string, v: any): CustomEvent =>\n\t\tnew CustomEvent(\"prop_change\", { detail: { id, prop: p, value: v } });\n\n\tfunction wrap(\n\t\tcomponent: ComponentType<SvelteComponent>\n\t): ComponentType<SvelteComponent> {\n\t\tconst ProxiedMyClass = new Proxy(component, {\n\t\t\tconstruct(_target, args: Record<string, any>[]) {\n\t\t\t\t//@ts-ignore\n\t\t\t\tconst instance = new _target(...args);\n\t\t\t\tconst props = Object.keys(instance.$$.props);\n\n\t\t\t\tfunction report(props: string) {\n\t\t\t\t\treturn function (propargs: any) {\n\t\t\t\t\t\tif (!target) return;\n\t\t\t\t\t\tconst ev = s(_id, props, propargs);\n\t\t\t\t\t\ttarget.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tprops.forEach((v) => {\n\t\t\t\t\tbinding_callbacks.push(() => bind(instance, v, report(v)));\n\t\t\t\t});\n\n\t\t\t\treturn instance;\n\t\t\t}\n\t\t});\n\n\t\treturn ProxiedMyClass;\n\t}\n\n\tconst _component = wrap(component);\n</script>\n\n<svelte:component\n\tthis={_component}\n\tbind:this={instance}\n\tbind:value\n\ton:prop_change\n\t{elem_id}\n\t{elem_classes}\n\t{target}\n\t{...$$restProps}\n\t{theme_mode}\n\t{root}\n>\n\t<slot />\n</svelte:component>\n", "<script lang=\"ts\">\n\timport { Gradio, formatter } from \"./gradio_helper\";\n\timport { onMount, createEventDispatcher, setContext } from \"svelte\";\n\timport type { ComponentMeta, ThemeMode } from \"./types\";\n\timport type { Client } from \"@gradio/client\";\n\timport RenderComponent from \"./RenderComponent.svelte\";\n\timport { load_component } from \"virtual:component-loader\";\n\n\texport let root: string;\n\n\texport let node: ComponentMeta;\n\texport let parent: string | null = null;\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let version: string;\n\texport let autoscroll: boolean;\n\texport let max_file_size: number | null;\n\texport let client: Client;\n\n\tconst dispatch = createEventDispatcher<{ mount: number; destroy: number }>();\n\tlet filtered_children: ComponentMeta[] = [];\n\n\tonMount(() => {\n\t\tdispatch(\"mount\", node.id);\n\n\t\tfor (const child of filtered_children) {\n\t\t\tdispatch(\"mount\", child.id);\n\t\t}\n\n\t\treturn () => {\n\t\t\tdispatch(\"destroy\", node.id);\n\n\t\t\tfor (const child of filtered_children) {\n\t\t\t\tdispatch(\"mount\", child.id);\n\t\t\t}\n\t\t};\n\t});\n\n\t$: {\n\t\tif (node) {\n\t\t\tnode.children =\n\t\t\t\tnode.children &&\n\t\t\t\tnode.children.filter((v) => {\n\t\t\t\t\tconst valid_node = node.type !== \"statustracker\";\n\t\t\t\t\tif (!valid_node) {\n\t\t\t\t\t\tfiltered_children.push(v);\n\t\t\t\t\t}\n\t\t\t\t\treturn valid_node;\n\t\t\t\t});\n\t\t}\n\t}\n\n\tsetContext(\"BLOCK_KEY\", parent);\n\n\t$: {\n\t\tif (node && node.type === \"form\") {\n\t\t\tif (node.children?.every((c) => !c.props.visible)) {\n\t\t\t\tnode.props.visible = false;\n\t\t\t} else {\n\t\t\t\tnode.props.visible = true;\n\t\t\t}\n\t\t}\n\t}\n\n\t$: node.props.gradio = new Gradio<Record<string, any>>(\n\t\tnode.id,\n\t\ttarget,\n\t\ttheme_mode,\n\t\tversion,\n\t\troot,\n\t\tautoscroll,\n\t\tmax_file_size,\n\t\tformatter,\n\t\tclient,\n\t\tload_component\n\t);\n</script>\n\n<RenderComponent\n\t_id={node?.id}\n\tcomponent={node.component}\n\tbind:instance={node.instance}\n\tbind:value={node.props.value}\n\telem_id={(\"elem_id\" in node.props && node.props.elem_id) ||\n\t\t`component-${node.id}`}\n\telem_classes={(\"elem_classes\" in node.props && node.props.elem_classes) || []}\n\t{target}\n\t{...node.props}\n\t{theme_mode}\n\t{root}\n>\n\t{#if node.children && node.children.length}\n\t\t{#each node.children as _node (_node.id)}\n\t\t\t<svelte:self\n\t\t\t\tnode={_node}\n\t\t\t\tcomponent={_node.component}\n\t\t\t\t{target}\n\t\t\t\tid={_node.id}\n\t\t\t\t{root}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:destroy\n\t\t\t\ton:mount\n\t\t\t\t{max_file_size}\n\t\t\t\t{client}\n\t\t\t/>\n\t\t{/each}\n\t{/if}\n</RenderComponent>\n", "<script lang=\"ts\">\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { Client } from \"@gradio/client\";\n\timport Render from \"./Render.svelte\";\n\n\texport let rootNode: any;\n\texport let root: any;\n\texport let target: any;\n\texport let theme_mode: any;\n\texport let version: any;\n\texport let autoscroll: boolean;\n\texport let max_file_size: number | null = null;\n\texport let client: Client;\n\n\tconst dispatch = createEventDispatcher<{ mount?: never }>();\n\tonMount(() => {\n\t\tdispatch(\"mount\");\n\t});\n</script>\n\n{#if rootNode}\n\t<Render\n\t\tnode={rootNode}\n\t\t{root}\n\t\t{target}\n\t\t{theme_mode}\n\t\t{version}\n\t\t{autoscroll}\n\t\t{max_file_size}\n\t\t{client}\n\t/>\n{/if}\n", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\timport { tick, onMount } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport { Client } from \"@gradio/client\";\n\n\timport type { LoadingStatus, LoadingStatusCollection } from \"./stores\";\n\n\timport type { ComponentMeta, Dependency, LayoutNode } from \"./types\";\n\timport type { UpdateTransaction } from \"./init\";\n\timport { setupi18n } from \"./i18n\";\n\timport { ApiDocs, ApiRecorder } from \"./api_docs/\";\n\timport type { ThemeMode, Payload } from \"./types\";\n\timport { Toast } from \"@gradio/statustracker\";\n\timport type { ToastMessage } from \"@gradio/statustracker\";\n\timport type { ShareData, ValueData } from \"@gradio/utils\";\n\timport MountComponents from \"./MountComponents.svelte\";\n\timport { prefix_css } from \"./css\";\n\n\timport logo from \"./images/logo.svg\";\n\timport api_logo from \"./api_docs/img/api-logo.svg\";\n\timport { create_components, AsyncFunction } from \"./init\";\n\timport type {\n\t\tLogMessage,\n\t\tRenderMessage,\n\t\tStatusMessage\n\t} from \"@gradio/client\";\n\n\tsetupi18n();\n\n\texport let root: string;\n\texport let components: ComponentMeta[];\n\texport let layout: LayoutNode;\n\texport let dependencies: Dependency[];\n\texport let title = \"Gradio\";\n\texport let target: HTMLElement;\n\texport let autoscroll: boolean;\n\texport let show_api = true;\n\texport let show_footer = true;\n\texport let control_page_title = false;\n\texport let app_mode: boolean;\n\texport let theme_mode: ThemeMode;\n\texport let app: Awaited<ReturnType<typeof Client.connect>>;\n\texport let space_id: string | null;\n\texport let version: string;\n\texport let js: string | null;\n\texport let fill_height = false;\n\texport let ready: boolean;\n\texport let username: string | null;\n\texport let api_prefix = \"\";\n\texport let max_file_size: number | undefined = undefined;\n\texport let initial_layout: ComponentMeta | undefined = undefined;\n\texport let css: string | null | undefined = null;\n\tlet {\n\t\tlayout: _layout,\n\t\ttargets,\n\t\tupdate_value,\n\t\tget_data,\n\t\tmodify_stream,\n\t\tget_stream_state,\n\t\tset_time_limit,\n\t\tloading_status,\n\t\tscheduled_updates,\n\t\tcreate_layout,\n\t\trerender_layout\n\t} = create_components(initial_layout);\n\n\t$: components, layout, dependencies, root, app, fill_height, target, run();\n\n\t$: {\n\t\tready = !!$_layout;\n\t}\n\n\tasync function run(): Promise<void> {\n\t\tawait create_layout({\n\t\t\tcomponents,\n\t\t\tlayout,\n\t\t\tdependencies,\n\t\t\troot: root + api_prefix,\n\t\t\tapp,\n\t\t\toptions: {\n\t\t\t\tfill_height\n\t\t\t}\n\t\t});\n\t}\n\n\texport let search_params: URLSearchParams;\n\tlet api_docs_visible = search_params.get(\"view\") === \"api\" && show_api;\n\tlet api_recorder_visible =\n\t\tsearch_params.get(\"view\") === \"api-recorder\" && show_api;\n\tfunction set_api_docs_visible(visible: boolean): void {\n\t\tapi_recorder_visible = false;\n\t\tapi_docs_visible = visible;\n\t\tlet params = new URLSearchParams(window.location.search);\n\t\tif (visible) {\n\t\t\tparams.set(\"view\", \"api\");\n\t\t} else {\n\t\t\tparams.delete(\"view\");\n\t\t}\n\t\thistory.replaceState(null, \"\", \"?\" + params.toString());\n\t}\n\tlet api_calls: Payload[] = [];\n\n\texport let render_complete = false;\n\tasync function handle_update(data: any, fn_index: number): Promise<void> {\n\t\tconst outputs = dependencies.find((dep) => dep.id == fn_index)!.outputs;\n\n\t\tconst meta_updates = data?.map((value: any, i: number) => {\n\t\t\treturn {\n\t\t\t\tid: outputs[i],\n\t\t\t\tprop: \"value_is_output\",\n\t\t\t\tvalue: true\n\t\t\t};\n\t\t});\n\n\t\tupdate_value(meta_updates);\n\n\t\tawait tick();\n\n\t\tconst updates: UpdateTransaction[] = [];\n\n\t\tdata?.forEach((value: any, i: number) => {\n\t\t\tif (\n\t\t\t\ttypeof value === \"object\" &&\n\t\t\t\tvalue !== null &&\n\t\t\t\tvalue.__type__ === \"update\"\n\t\t\t) {\n\t\t\t\tfor (const [update_key, update_value] of Object.entries(value)) {\n\t\t\t\t\tif (update_key === \"__type__\") {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tupdates.push({\n\t\t\t\t\t\t\tid: outputs[i],\n\t\t\t\t\t\t\tprop: update_key,\n\t\t\t\t\t\t\tvalue: update_value\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tupdates.push({\n\t\t\t\t\tid: outputs[i],\n\t\t\t\t\tprop: \"value\",\n\t\t\t\t\tvalue\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\tupdate_value(updates);\n\n\t\tawait tick();\n\t}\n\n\tlet submit_map: Map<number, ReturnType<typeof app.submit>> = new Map();\n\n\tlet messages: (ToastMessage & { fn_index: number })[] = [];\n\tfunction new_message(\n\t\ttitle: string,\n\t\tmessage: string,\n\t\tfn_index: number,\n\t\ttype: ToastMessage[\"type\"],\n\t\tduration: number | null = 10,\n\t\tvisible = true\n\t): ToastMessage & { fn_index: number } {\n\t\treturn {\n\t\t\ttitle,\n\t\t\tmessage,\n\t\t\tfn_index,\n\t\t\ttype,\n\t\t\tid: ++_error_id,\n\t\t\tduration,\n\t\t\tvisible\n\t\t};\n\t}\n\n\texport function add_new_message(\n\t\ttitle: string,\n\t\tmessage: string,\n\t\ttype: ToastMessage[\"type\"]\n\t): void {\n\t\tmessages = [new_message(title, message, -1, type), ...messages];\n\t}\n\n\tlet _error_id = -1;\n\n\tlet user_left_page = false;\n\n\tconst MESSAGE_QUOTE_RE = /^'([^]+)'$/;\n\n\tconst DUPLICATE_MESSAGE = $_(\"blocks.long_requests_queue\");\n\tconst MOBILE_QUEUE_WARNING = $_(\"blocks.connection_can_break\");\n\tconst MOBILE_RECONNECT_MESSAGE = $_(\"blocks.lost_connection\");\n\tconst WAITING_FOR_INPUTS_MESSAGE = $_(\"blocks.waiting_for_inputs\");\n\tconst SHOW_DUPLICATE_MESSAGE_ON_ETA = 15;\n\tconst SHOW_MOBILE_QUEUE_WARNING_ON_ETA = 10;\n\tlet is_mobile_device = false;\n\tlet showed_duplicate_message = false;\n\tlet showed_mobile_warning = false;\n\tlet inputs_waiting: number[] = [];\n\n\t// as state updates are not synchronous, we need to ensure updates are flushed before triggering any requests\n\tfunction wait_then_trigger_api_call(\n\t\tdep_index: number,\n\t\ttrigger_id: number | null = null,\n\t\tevent_data: unknown = null\n\t): void {\n\t\tlet _unsub = (): void => {};\n\t\tfunction unsub(): void {\n\t\t\t_unsub();\n\t\t}\n\t\tif ($scheduled_updates) {\n\t\t\t_unsub = scheduled_updates.subscribe((updating) => {\n\t\t\t\tif (!updating) {\n\t\t\t\t\ttick().then(() => {\n\t\t\t\t\t\ttrigger_api_call(dep_index, trigger_id, event_data);\n\t\t\t\t\t\tunsub();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\ttrigger_api_call(dep_index, trigger_id, event_data);\n\t\t}\n\t}\n\n\tasync function get_component_value_or_event_data(\n\t\tcomponent_id: number,\n\t\ttrigger_id: number | null,\n\t\tevent_data: unknown\n\t): Promise<any> {\n\t\tif (\n\t\t\tcomponent_id === trigger_id &&\n\t\t\tevent_data &&\n\t\t\t(event_data as ValueData).is_value_data === true\n\t\t) {\n\t\t\t// @ts-ignore\n\t\t\treturn event_data.value;\n\t\t}\n\t\treturn get_data(component_id);\n\t}\n\n\tasync function trigger_api_call(\n\t\tdep_index: number,\n\t\ttrigger_id: number | null = null,\n\t\tevent_data: unknown = null\n\t): Promise<void> {\n\t\tlet dep = dependencies.find((dep) => dep.id === dep_index)!;\n\t\tif (inputs_waiting.length > 0) {\n\t\t\tfor (const input of inputs_waiting) {\n\t\t\t\tif (dep.inputs.includes(input)) {\n\t\t\t\t\tadd_new_message(\"Warning\", WAITING_FOR_INPUTS_MESSAGE, \"warning\");\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tconst current_status = loading_status.get_status_for_fn(dep_index);\n\t\tmessages = messages.filter(({ fn_index }) => fn_index !== dep_index);\n\t\tif (current_status === \"pending\" || current_status === \"generating\") {\n\t\t\tdep.pending_request = true;\n\t\t}\n\n\t\tlet payload: Payload = {\n\t\t\tfn_index: dep_index,\n\t\t\tdata: await Promise.all(\n\t\t\t\tdep.inputs.map((id) =>\n\t\t\t\t\tget_component_value_or_event_data(id, trigger_id, event_data)\n\t\t\t\t)\n\t\t\t),\n\t\t\tevent_data: dep.collects_event_data ? event_data : null,\n\t\t\ttrigger_id: trigger_id\n\t\t};\n\n\t\tif (dep.frontend_fn) {\n\t\t\tdep\n\t\t\t\t.frontend_fn(\n\t\t\t\t\tpayload.data.concat(\n\t\t\t\t\t\tawait Promise.all(dep.outputs.map((id) => get_data(id)))\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t\t.then((v: unknown[]) => {\n\t\t\t\t\tif (dep.backend_fn) {\n\t\t\t\t\t\tpayload.data = v;\n\t\t\t\t\t\ttrigger_prediction(dep, payload);\n\t\t\t\t\t} else {\n\t\t\t\t\t\thandle_update(v, dep_index);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t} else if (dep.types.cancel && dep.cancels) {\n\t\t\tawait Promise.all(\n\t\t\t\tdep.cancels.map(async (fn_index) => {\n\t\t\t\t\tconst submission = submit_map.get(fn_index);\n\t\t\t\t\tsubmission?.cancel();\n\t\t\t\t\treturn submission;\n\t\t\t\t})\n\t\t\t);\n\t\t} else {\n\t\t\tif (dep.backend_fn) {\n\t\t\t\ttrigger_prediction(dep, payload);\n\t\t\t}\n\t\t}\n\n\t\tfunction trigger_prediction(dep: Dependency, payload: Payload): void {\n\t\t\tif (dep.trigger_mode === \"once\") {\n\t\t\t\tif (!dep.pending_request)\n\t\t\t\t\tmake_prediction(payload, dep.connection == \"stream\");\n\t\t\t} else if (dep.trigger_mode === \"multiple\") {\n\t\t\t\tmake_prediction(payload, dep.connection == \"stream\");\n\t\t\t} else if (dep.trigger_mode === \"always_last\") {\n\t\t\t\tif (!dep.pending_request) {\n\t\t\t\t\tmake_prediction(payload, dep.connection == \"stream\");\n\t\t\t\t} else {\n\t\t\t\t\tdep.final_event = payload;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tasync function make_prediction(\n\t\t\tpayload: Payload,\n\t\t\tstreaming = false\n\t\t): Promise<void> {\n\t\t\tif (api_recorder_visible) {\n\t\t\t\tapi_calls = [...api_calls, JSON.parse(JSON.stringify(payload))];\n\t\t\t}\n\n\t\t\tlet submission: ReturnType<typeof app.submit>;\n\t\t\tapp.set_current_payload(payload);\n\t\t\tif (streaming) {\n\t\t\t\tif (!submit_map.has(dep_index)) {\n\t\t\t\t\tdep.inputs.forEach((id) => modify_stream(id, \"waiting\"));\n\t\t\t\t} else if (\n\t\t\t\t\tsubmit_map.has(dep_index) &&\n\t\t\t\t\tdep.inputs.some((id) => get_stream_state(id) === \"waiting\")\n\t\t\t\t) {\n\t\t\t\t\treturn;\n\t\t\t\t} else if (\n\t\t\t\t\tsubmit_map.has(dep_index) &&\n\t\t\t\t\tdep.inputs.some((id) => get_stream_state(id) === \"open\")\n\t\t\t\t) {\n\t\t\t\t\tawait app.send_ws_message(\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t`${app.config.root + app.config.api_prefix}/stream/${submit_map.get(dep_index).event_id()}`,\n\t\t\t\t\t\t{ ...payload, session_hash: app.session_hash }\n\t\t\t\t\t);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tsubmission = app.submit(\n\t\t\t\t\tpayload.fn_index,\n\t\t\t\t\tpayload.data as unknown[],\n\t\t\t\t\tpayload.event_data,\n\t\t\t\t\tpayload.trigger_id\n\t\t\t\t);\n\t\t\t} catch (e) {\n\t\t\t\tconst fn_index = 0; // Mock value for fn_index\n\t\t\t\tmessages = [\n\t\t\t\t\tnew_message(\"Error\", String(e), fn_index, \"error\"),\n\t\t\t\t\t...messages\n\t\t\t\t];\n\t\t\t\tloading_status.update({\n\t\t\t\t\tstatus: \"error\",\n\t\t\t\t\tfn_index,\n\t\t\t\t\teta: 0,\n\t\t\t\t\tqueue: false,\n\t\t\t\t\tqueue_position: null\n\t\t\t\t});\n\t\t\t\tset_status($loading_status);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tsubmit_map.set(dep_index, submission);\n\n\t\t\tfor await (const message of submission) {\n\t\t\t\tif (message.type === \"data\") {\n\t\t\t\t\thandle_data(message);\n\t\t\t\t} else if (message.type === \"render\") {\n\t\t\t\t\thandle_render(message);\n\t\t\t\t} else if (message.type === \"status\") {\n\t\t\t\t\thandle_status_update(message);\n\t\t\t\t} else if (message.type === \"log\") {\n\t\t\t\t\thandle_log(message);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction handle_data(message: Payload): void {\n\t\t\t\tconst { data, fn_index } = message;\n\t\t\t\tif (dep.pending_request && dep.final_event) {\n\t\t\t\t\tdep.pending_request = false;\n\t\t\t\t\tmake_prediction(dep.final_event, dep.connection == \"stream\");\n\t\t\t\t}\n\t\t\t\tdep.pending_request = false;\n\t\t\t\thandle_update(data, fn_index);\n\t\t\t\tset_status($loading_status);\n\t\t\t}\n\n\t\t\tfunction handle_render(message: RenderMessage): void {\n\t\t\t\tconst { data } = message;\n\t\t\t\tlet _components: ComponentMeta[] = data.components;\n\t\t\t\tlet render_layout: LayoutNode = data.layout;\n\t\t\t\tlet _dependencies: Dependency[] = data.dependencies;\n\t\t\t\tlet render_id = data.render_id;\n\n\t\t\t\tlet deps_to_remove: number[] = [];\n\t\t\t\tdependencies.forEach((dep, i) => {\n\t\t\t\t\tif (dep.rendered_in === render_id) {\n\t\t\t\t\t\tdeps_to_remove.push(i);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tdeps_to_remove.reverse().forEach((i) => {\n\t\t\t\t\tdependencies.splice(i, 1);\n\t\t\t\t});\n\t\t\t\t_dependencies.forEach((dep) => {\n\t\t\t\t\tdependencies.push(dep);\n\t\t\t\t});\n\n\t\t\t\trerender_layout({\n\t\t\t\t\tcomponents: _components,\n\t\t\t\t\tlayout: render_layout,\n\t\t\t\t\troot: root,\n\t\t\t\t\tdependencies: dependencies,\n\t\t\t\t\trender_id: render_id\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tfunction handle_log(msg: LogMessage): void {\n\t\t\t\tconst { title, log, fn_index, level, duration, visible } = msg;\n\t\t\t\tmessages = [\n\t\t\t\t\tnew_message(title, log, fn_index, level, duration, visible),\n\t\t\t\t\t...messages\n\t\t\t\t];\n\t\t\t}\n\n\t\t\tfunction open_stream_events(\n\t\t\t\tstatus: StatusMessage,\n\t\t\t\tid: number,\n\t\t\t\tdep: Dependency\n\t\t\t): void {\n\t\t\t\tif (\n\t\t\t\t\tstatus.original_msg === \"process_starts\" &&\n\t\t\t\t\tdep.connection === \"stream\"\n\t\t\t\t) {\n\t\t\t\t\tmodify_stream(id, \"open\");\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* eslint-disable complexity */\n\t\t\tfunction handle_status_update(message: StatusMessage): void {\n\t\t\t\tconst { fn_index, ...status } = message;\n\t\t\t\tif (status.stage === \"streaming\" && status.time_limit) {\n\t\t\t\t\tdep.inputs.forEach((id) => {\n\t\t\t\t\t\tset_time_limit(id, status.time_limit);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tdep.inputs.forEach((id) => {\n\t\t\t\t\topen_stream_events(message, id, dep);\n\t\t\t\t});\n\t\t\t\t//@ts-ignore\n\t\t\t\tloading_status.update({\n\t\t\t\t\t...status,\n\t\t\t\t\ttime_limit: status.time_limit,\n\t\t\t\t\tstatus: status.stage,\n\t\t\t\t\tprogress: status.progress_data,\n\t\t\t\t\tfn_index\n\t\t\t\t});\n\t\t\t\tset_status($loading_status);\n\t\t\t\tif (\n\t\t\t\t\t!showed_duplicate_message &&\n\t\t\t\t\tspace_id !== null &&\n\t\t\t\t\tstatus.position !== undefined &&\n\t\t\t\t\tstatus.position >= 2 &&\n\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\tstatus.eta > SHOW_DUPLICATE_MESSAGE_ON_ETA\n\t\t\t\t) {\n\t\t\t\t\tshowed_duplicate_message = true;\n\t\t\t\t\tmessages = [\n\t\t\t\t\t\tnew_message(\"Warning\", DUPLICATE_MESSAGE, fn_index, \"warning\"),\n\t\t\t\t\t\t...messages\n\t\t\t\t\t];\n\t\t\t\t}\n\t\t\t\tif (\n\t\t\t\t\t!showed_mobile_warning &&\n\t\t\t\t\tis_mobile_device &&\n\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\tstatus.eta > SHOW_MOBILE_QUEUE_WARNING_ON_ETA\n\t\t\t\t) {\n\t\t\t\t\tshowed_mobile_warning = true;\n\t\t\t\t\tmessages = [\n\t\t\t\t\t\tnew_message(\"Warning\", MOBILE_QUEUE_WARNING, fn_index, \"warning\"),\n\t\t\t\t\t\t...messages\n\t\t\t\t\t];\n\t\t\t\t}\n\n\t\t\t\tif (status.stage === \"complete\" || status.stage === \"generating\") {\n\t\t\t\t\tstatus.changed_state_ids?.forEach((id) => {\n\t\t\t\t\t\tdependencies\n\t\t\t\t\t\t\t.filter((dep) => dep.targets.some(([_id, _]) => _id === id))\n\t\t\t\t\t\t\t.forEach((dep) => {\n\t\t\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tif (status.stage === \"complete\") {\n\t\t\t\t\tdependencies.forEach(async (dep) => {\n\t\t\t\t\t\tif (dep.trigger_after === fn_index) {\n\t\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tdep.inputs.forEach((id) => {\n\t\t\t\t\t\tmodify_stream(id, \"closed\");\n\t\t\t\t\t});\n\t\t\t\t\tsubmit_map.delete(dep_index);\n\t\t\t\t}\n\t\t\t\tif (status.broken && is_mobile_device && user_left_page) {\n\t\t\t\t\twindow.setTimeout(() => {\n\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\tnew_message(\"Error\", MOBILE_RECONNECT_MESSAGE, fn_index, \"error\"),\n\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t];\n\t\t\t\t\t}, 0);\n\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id, event_data);\n\t\t\t\t\tuser_left_page = false;\n\t\t\t\t} else if (status.stage === \"error\") {\n\t\t\t\t\tif (status.message) {\n\t\t\t\t\t\tconst _message = status.message.replace(\n\t\t\t\t\t\t\tMESSAGE_QUOTE_RE,\n\t\t\t\t\t\t\t(_, b) => b\n\t\t\t\t\t\t);\n\t\t\t\t\t\tconst _title = status.title ?? \"Error\";\n\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\tnew_message(\n\t\t\t\t\t\t\t\t_title,\n\t\t\t\t\t\t\t\t_message,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\"error\",\n\t\t\t\t\t\t\t\tstatus.duration,\n\t\t\t\t\t\t\t\tstatus.visible\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t];\n\t\t\t\t\t}\n\t\t\t\t\tdependencies.map(async (dep) => {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tdep.trigger_after === fn_index &&\n\t\t\t\t\t\t\t!dep.trigger_only_on_success\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t/* eslint-enable complexity */\n\n\tfunction trigger_share(title: string | undefined, description: string): void {\n\t\tif (space_id === null) {\n\t\t\treturn;\n\t\t}\n\t\tconst discussion_url = new URL(\n\t\t\t`https://huggingface.co/spaces/${space_id}/discussions/new`\n\t\t);\n\t\tif (title !== undefined && title.length > 0) {\n\t\t\tdiscussion_url.searchParams.set(\"title\", title);\n\t\t}\n\t\tdiscussion_url.searchParams.set(\"description\", description);\n\t\twindow.open(discussion_url.toString(), \"_blank\");\n\t}\n\n\tfunction handle_error_close(e: Event & { detail: number }): void {\n\t\tconst _id = e.detail;\n\t\tmessages = messages.filter((m) => m.id !== _id);\n\t}\n\n\tconst is_external_url = (link: string | null): boolean =>\n\t\t!!(link && new URL(link, location.href).origin !== location.origin);\n\n\tasync function handle_mount(): Promise<void> {\n\t\tif (js) {\n\t\t\tlet blocks_frontend_fn = new AsyncFunction(\n\t\t\t\t`let result = await (${js})();\n\t\t\t\t\treturn (!Array.isArray(result)) ? [result] : result;`\n\t\t\t);\n\t\t\tawait blocks_frontend_fn();\n\t\t}\n\n\t\tawait tick();\n\n\t\tvar a = target.getElementsByTagName(\"a\");\n\n\t\tfor (var i = 0; i < a.length; i++) {\n\t\t\tconst _target = a[i].getAttribute(\"target\");\n\t\t\tconst _link = a[i].getAttribute(\"href\");\n\n\t\t\t// only target anchor tags with external links\n\t\t\tif (is_external_url(_link) && _target !== \"_blank\")\n\t\t\t\ta[i].setAttribute(\"target\", \"_blank\");\n\t\t}\n\n\t\t// handle load triggers\n\t\tdependencies.forEach((dep) => {\n\t\t\tif (dep.targets.some((dep) => dep[1] === \"load\")) {\n\t\t\t\twait_then_trigger_api_call(dep.id);\n\t\t\t}\n\t\t});\n\n\t\tif (!target || render_complete) return;\n\n\t\ttarget.addEventListener(\"prop_change\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\t\t\tconst { id, prop, value } = e.detail;\n\t\t\tupdate_value([{ id, prop, value }]);\n\t\t\tif (prop === \"input_ready\" && value === false) {\n\t\t\t\tinputs_waiting.push(id);\n\t\t\t}\n\t\t\tif (prop === \"input_ready\" && value === true) {\n\t\t\t\tinputs_waiting = inputs_waiting.filter((item) => item !== id);\n\t\t\t}\n\t\t});\n\t\ttarget.addEventListener(\"gradio\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\n\t\t\tconst { id, event, data } = e.detail;\n\n\t\t\tif (event === \"share\") {\n\t\t\t\tconst { title, description } = data as ShareData;\n\t\t\t\ttrigger_share(title, description);\n\t\t\t} else if (event === \"error\") {\n\t\t\t\tmessages = [new_message(\"Error\", data, -1, event), ...messages];\n\t\t\t} else if (event === \"warning\") {\n\t\t\t\tmessages = [new_message(\"Warning\", data, -1, event), ...messages];\n\t\t\t} else if (event == \"clear_status\") {\n\t\t\t\tupdate_status(id, \"complete\", data);\n\t\t\t} else if (event == \"close_stream\") {\n\t\t\t\tconst deps = $targets[id]?.[data];\n\t\t\t\tdeps?.forEach((dep_id) => {\n\t\t\t\t\tif (submit_map.has(dep_id)) {\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\tconst url = `${app.config.root + app.config.api_prefix}/stream/${submit_map.get(dep_id).event_id()}`;\n\t\t\t\t\t\tapp.post_data(`${url}/close`, {});\n\t\t\t\t\t\tapp.close_ws(url);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tconst deps = $targets[id]?.[event];\n\n\t\t\t\tdeps?.forEach((dep_id) => {\n\t\t\t\t\trequestAnimationFrame(() => {\n\t\t\t\t\t\twait_then_trigger_api_call(dep_id, id, data);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\trender_complete = true;\n\t}\n\n\t$: set_status($loading_status);\n\n\tfunction update_status(\n\t\tid: number,\n\t\tstatus: \"error\" | \"complete\" | \"pending\",\n\t\tdata: LoadingStatus\n\t): void {\n\t\tdata.status = status;\n\t\tupdate_value([\n\t\t\t{\n\t\t\t\tid,\n\t\t\t\tprop: \"loading_status\",\n\t\t\t\tvalue: data\n\t\t\t}\n\t\t]);\n\t}\n\n\tfunction set_status(statuses: LoadingStatusCollection): void {\n\t\tlet updates: {\n\t\t\tid: number;\n\t\t\tprop: string;\n\t\t\tvalue: LoadingStatus;\n\t\t}[] = [];\n\t\tObject.entries(statuses).forEach(([id, loading_status]) => {\n\t\t\tlet dependency = dependencies.find(\n\t\t\t\t(dep) => dep.id == loading_status.fn_index\n\t\t\t);\n\t\t\tif (dependency === undefined) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tloading_status.scroll_to_output = dependency.scroll_to_output;\n\t\t\tloading_status.show_progress = dependency.show_progress;\n\t\t\tupdates.push({\n\t\t\t\tid: parseInt(id),\n\t\t\t\tprop: \"loading_status\",\n\t\t\t\tvalue: loading_status\n\t\t\t});\n\t\t});\n\n\t\tconst inputs_to_update = loading_status.get_inputs_to_update();\n\t\tconst additional_updates = Array.from(inputs_to_update).map(\n\t\t\t([id, pending_status]) => {\n\t\t\t\treturn {\n\t\t\t\t\tid,\n\t\t\t\t\tprop: \"pending\",\n\t\t\t\t\tvalue: pending_status === \"pending\"\n\t\t\t\t};\n\t\t\t}\n\t\t);\n\n\t\tupdate_value([...updates, ...additional_updates]);\n\t}\n\n\tfunction isCustomEvent(event: Event): event is CustomEvent {\n\t\treturn \"detail\" in event;\n\t}\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"visibilitychange\", function () {\n\t\t\tif (document.visibilityState === \"hidden\") {\n\t\t\t\tuser_left_page = true;\n\t\t\t}\n\t\t});\n\n\t\tis_mobile_device =\n\t\t\t/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n\t\t\t\tnavigator.userAgent\n\t\t\t);\n\t});\n</script>\n\n<svelte:head>\n\t{#if control_page_title}\n\t\t<title>{title}</title>\n\t{/if}\n\t{#if css}\n\t\t{@html `\\<style\\>${prefix_css(css, version)}</style>`}\n\t{/if}\n</svelte:head>\n\n<div class=\"wrap\" style:min-height={app_mode ? \"100%\" : \"auto\"}>\n\t<div class=\"contain\" style:flex-grow={app_mode ? \"1\" : \"auto\"}>\n\t\t{#if $_layout && app.config}\n\t\t\t<MountComponents\n\t\t\t\trootNode={$_layout}\n\t\t\t\t{root}\n\t\t\t\t{target}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:mount={handle_mount}\n\t\t\t\t{version}\n\t\t\t\t{autoscroll}\n\t\t\t\t{max_file_size}\n\t\t\t\tclient={app}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\n\t{#if show_footer}\n\t\t<footer>\n\t\t\t{#if show_api}\n\t\t\t\t<button\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tset_api_docs_visible(!api_docs_visible);\n\t\t\t\t\t}}\n\t\t\t\t\tclass=\"show-api\"\n\t\t\t\t>\n\t\t\t\t\t{$_(\"errors.use_via_api\")}\n\t\t\t\t\t<img src={api_logo} alt={$_(\"common.logo\")} />\n\t\t\t\t</button>\n\t\t\t\t<div>·</div>\n\t\t\t{/if}\n\t\t\t<a\n\t\t\t\thref=\"https://gradio.app\"\n\t\t\t\tclass=\"built-with\"\n\t\t\t\ttarget=\"_blank\"\n\t\t\t\trel=\"noreferrer\"\n\t\t\t>\n\t\t\t\t{$_(\"common.built_with_gradio\")}\n\t\t\t\t<img src={logo} alt={$_(\"common.logo\")} />\n\t\t\t</a>\n\t\t</footer>\n\t{/if}\n</div>\n\n{#if api_recorder_visible}\n\t<!-- TODO: fix -->\n\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t<div\n\t\tid=\"api-recorder-container\"\n\t\ton:click={() => {\n\t\t\tset_api_docs_visible(true);\n\t\t\tapi_recorder_visible = false;\n\t\t}}\n\t>\n\t\t<ApiRecorder {api_calls} {dependencies} />\n\t</div>\n{/if}\n\n{#if api_docs_visible && $_layout}\n\t<div class=\"api-docs\">\n\t\t<!-- TODO: fix -->\n\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t<div\n\t\t\tclass=\"backdrop\"\n\t\t\ton:click={() => {\n\t\t\t\tset_api_docs_visible(false);\n\t\t\t}}\n\t\t/>\n\t\t<div class=\"api-docs-wrap\">\n\t\t\t<ApiDocs\n\t\t\t\troot_node={$_layout}\n\t\t\t\ton:close={(event) => {\n\t\t\t\t\tset_api_docs_visible(false);\n\t\t\t\t\tapi_calls = [];\n\t\t\t\t\tapi_recorder_visible = event.detail?.api_recorder_visible;\n\t\t\t\t}}\n\t\t\t\t{dependencies}\n\t\t\t\t{root}\n\t\t\t\t{app}\n\t\t\t\t{space_id}\n\t\t\t\t{api_calls}\n\t\t\t\t{username}\n\t\t\t/>\n\t\t</div>\n\t</div>\n{/if}\n\n{#if messages}\n\t<Toast {messages} on:close={handle_error_close} />\n{/if}\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-grow: 1;\n\t\tflex-direction: column;\n\t\twidth: var(--size-full);\n\t\tfont-weight: var(--body-text-weight);\n\t\tfont-size: var(--body-text-size);\n\t}\n\n\t.contain {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\tfooter {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin-top: var(--size-4);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tfooter > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.show-api {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t.show-api:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.show-api img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-2);\n\t\twidth: var(--size-3);\n\t}\n\n\t.built-with {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.built-with:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.built-with img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-1);\n\t\tmargin-bottom: 1px;\n\t\twidth: var(--size-4);\n\t}\n\n\t.api-docs {\n\t\tdisplay: flex;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-top);\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\twidth: var(--size-screen);\n\t\theight: var(--size-screen-h);\n\t}\n\n\t.backdrop {\n\t\tflex: 1 1 0%;\n\t\t-webkit-backdrop-filter: blur(4px);\n\t\tbackdrop-filter: blur(4px);\n\t}\n\n\t.api-docs-wrap {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tbackground: var(--background-fill-primary);\n\t\toverflow-x: hidden;\n\t\toverflow-y: auto;\n\t}\n\n\t@media (--screen-md) {\n\t\t.api-docs-wrap {\n\t\t\tborder-top-left-radius: var(--radius-lg);\n\t\t\tborder-bottom-left-radius: var(--radius-lg);\n\t\t\twidth: 950px;\n\t\t}\n\t}\n\n\t@media (--screen-xxl) {\n\t\t.api-docs-wrap {\n\t\t\twidth: 1150px;\n\t\t}\n\t}\n\n\t#api-recorder-container {\n\t\tposition: fixed;\n\t\tleft: 10px;\n\t\tbottom: 10px;\n\t\tz-index: 1000;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "g", "path0", "path1", "ctx", "div", "h1", "p0", "code0", "p1", "button", "dispatch", "createEventDispatcher", "root", "$$props", "click_handler", "api_logo", "p", "if_block", "create_if_block", "attr", "img", "img_src_value", "h2", "div1", "div0", "span1", "span0", "br", "api_count", "click_handler_1", "represent_value", "value", "type", "lang", "simplify_file_data", "replace_file_data_with_file_function", "stringify_except_file_function", "is_potentially_nested_file_data", "obj", "key", "item", "index", "jsonString", "regex", "match", "regexNone", "t_value", "dirty", "set_data", "create_if_block_3", "t1_value", "t1", "span", "t10_value", "create_if_block_2", "create_if_block_1", "hr", "t6", "t6_value", "t8", "t8_value", "t10", "create_if_block_4", "i", "h4", "div2", "current", "is_running", "endpoint_returns", "js_returns", "current_language", "code", "copy_text", "copy", "bash_install", "pre", "js_install", "py_install", "h3", "api_name", "fn_index", "named", "endpointdetail_changes", "create_if_block_8", "t5_value", "t19_value", "copybutton_changes", "t5", "t19", "t3_value", "t3", "t4_value", "if_block0", "create_if_block_6", "create_if_block_5", "t4", "t7_value", "t14_value", "if_block1", "span2", "span3", "span4", "span5", "t7", "t14", "dependency", "dependency_index", "api_prefix", "space_id", "endpoint_parameters", "username", "python_code", "js_code", "bash_post_code", "has_file_path", "param", "blob_components", "blob_examples", "$$value", "$$invalidate", "normalised_api_prefix", "normalised_root", "t17", "t17_value", "t2", "t2_value", "dependencies", "short_root", "python_code_text", "bash_code", "api_calls", "get_info", "endpoints_info", "py_zipped", "js_zipped", "bash_zipped", "format_api_call", "call", "params", "d", "param_info", "param_name", "python_type", "onMount", "py_api_calls", "js_api_calls", "bash_api_calls", "api_names", "tick", "python", "javascript", "bash", "t0_value", "t0", "div4", "div3", "apibanner_changes", "each_blocks", "img_1", "img_1_src_value", "li", "li_class_value", "create_if_block_7", "if_block2", "p2", "p3", "recordingsnippet_changes", "a", "a_href_value", "py_docs", "js_docs", "spaces_docs_suffix", "bash_docs", "br0", "br1", "strong", "code1", "code2", "code3", "code4", "code5", "code6", "codesnippet_changes", "app", "root_node", "langs", "get_js_info", "info", "js_info", "data", "js_api_info", "language", "formatter", "get", "format", "component", "theme_mode", "instance", "elem_id", "elem_classes", "_id", "s", "id", "v", "wrap", "_target", "args", "props", "report", "propargs", "ev", "binding_callbacks", "bind", "_component", "each_value", "ensure_array_like", "get_key", "render_changes", "rendercomponent_props", "get_spread_object", "rendercomponent_changes", "node", "parent", "version", "autoscroll", "max_file_size", "client", "filtered_children", "child", "setContext", "$$self", "valid_node", "c", "Gradio", "load_component", "rootNode", "logo", "prefix_css", "html_tag", "raw_value", "img_alt_value", "footer", "if_block5", "MESSAGE_QUOTE_RE", "SHOW_DUPLICATE_MESSAGE_ON_ETA", "SHOW_MOBILE_QUEUE_WARNING_ON_ETA", "isCustomEvent", "event", "setupi18n", "components", "layout", "title", "show_api", "show_footer", "control_page_title", "app_mode", "js", "fill_height", "ready", "initial_layout", "css", "_layout", "targets", "update_value", "get_data", "modify_stream", "get_stream_state", "set_time_limit", "loading_status", "scheduled_updates", "create_layout", "rerender_layout", "create_components", "run", "search_params", "api_docs_visible", "api_recorder_visible", "set_api_docs_visible", "visible", "render_complete", "handle_update", "outputs", "dep", "meta_updates", "updates", "update_key", "submit_map", "messages", "new_message", "message", "duration", "_error_id", "add_new_message", "user_left_page", "DUPLICATE_MESSAGE", "$_", "MOBILE_QUEUE_WARNING", "MOBILE_RECONNECT_MESSAGE", "WAITING_FOR_INPUTS_MESSAGE", "is_mobile_device", "showed_duplicate_message", "showed_mobile_warning", "inputs_waiting", "wait_then_trigger_api_call", "dep_index", "trigger_id", "event_data", "_unsub", "unsub", "$scheduled_updates", "updating", "trigger_api_call", "get_component_value_or_event_data", "component_id", "input", "current_status", "payload", "trigger_prediction", "submission", "make_prediction", "streaming", "e", "set_status", "$loading_status", "handle_data", "handle_render", "handle_status_update", "handle_log", "_components", "render_layout", "_dependencies", "render_id", "deps_to_remove", "msg", "log", "level", "open_stream_events", "status", "_", "_message", "b", "_title", "trigger_share", "description", "discussion_url", "handle_error_close", "m", "is_external_url", "link", "handle_mount", "AsyncFunction", "_link", "prop", "update_status", "$targets", "dep_id", "url", "statuses", "inputs_to_update", "additional_updates", "pending_status", "$_layout"], "mappings": "8gDAAAA,EAkBKC,EAAAC,EAAAC,CAAA,EARJC,EAOGF,EAAAG,CAAA,EANFD,EAECC,EAAAC,CAAA,EACDF,EAECC,EAAAE,CAAA,iPCLkB;AAAA,GAEnB,kBACEC,EAAI,CAAA,CAAA;;;;;;;;;iMALRR,EAwBKC,EAAAQ,EAAAN,CAAA,EAvBJC,EAAgBK,EAAAC,CAAA,SAChBN,EAKGK,EAAAE,CAAA,SAHFP,EAEMO,EAAAC,CAAA,gBAEPR,EAeGK,EAAAI,CAAA,WAGJb,EAEQC,EAAAa,EAAAX,CAAA,2EAvBJK,EAAI,CAAA,CAAA,sIAVDO,EAAWC,KAEN,GAAA,CAAA,KAAAC,CAAA,EAAAC,EA6BY,MAAAC,EAAA,IAAAJ,EAAS,OAAO,uMCnCxC,MAAeK,GAAA,8hEC2BZpB,EAA4CC,EAAAQ,EAAAN,CAAA,WAC5CH,EAAiDC,EAAAoB,EAAAlB,CAAA,uEAGmB,GAAC,8OAAf,IAAAmB,EAAAd,KAAY,GAACe,GAAA,qEAjBnD;AAAA,GAEjB,iBACEf,EAAI,CAAA,CAAA,oEAccA,EAAS,CAAA,CAAA,MAAQ,eAAa,qEAlBzCY,EAAQ,GAAAI,EAAAC,EAAA,MAAAC,CAAA,+PADnB1B,EAuBIC,EAAA0B,EAAAxB,CAAA,EAtBHC,EAA4BuB,EAAAF,CAAA,SAC5BrB,EAKKuB,EAAAC,CAAA,SAHJxB,EAEKwB,EAAAC,CAAA,gBAENzB,EAcMuB,EAAAG,CAAA,qBAJL1B,EAGG0B,EAAAT,CAAA,EAFFjB,EAAoCiB,EAAAU,CAAA,+BAAsC3B,EACzEiB,EAAAW,CAAA,WAKJhC,EAEQC,EAAAa,EAAAX,CAAA,2EAtBJK,EAAI,CAAA,CAAA,wEAccA,EAAS,CAAA,CAAA,EAA0BA,KAAY,kOAzBzD,GAAA,CAAA,KAAAS,CAAA,EAAAC,EACA,CAAA,UAAAe,CAAA,EAAAf,QAELH,EAAWC,KAgBCG,EAAA,IAAAJ,EAAS,QAAW,CAAA,qBAAsB,EAAI,CAAA,EAYzCmB,EAAA,IAAAnB,EAAS,OAAO,gVCpCjC,SAASoB,GACfC,EACAC,EACAC,EAAoC,KACyB,CAC7D,OAAID,IAAS,OACLC,IAAS,KAAO,OAAS,KAE7BF,IAAU,MAAQE,IAAS,KACvB,OAEJD,IAAS,UAAYA,IAAS,MAC1BC,IAAS,KAAOF,EAAQ,IAAMA,EAAQ,IACnCC,IAAS,SACZC,IAAS,KAAO,WAAWF,CAAK,EAAIA,EACjCC,IAAS,WAAaA,GAAQ,OACpCC,IAAS,MACZF,EAAQ,OAAOA,CAAK,EACbA,IAAU,OAAS,OAAS,SACzBE,IAAS,MAAQA,IAAS,OAC7BF,EAEDA,IAAU,OACPC,IAAS,aACXD,EAAA,KAAK,UAAUA,CAAK,EACrBA,GACGC,EAAK,WAAW,WAAW,EAE9B,IAAMD,EAAQ,IAGlBE,IAAS,KACLF,IAAU,GAAK,KAAO,KAAK,MAAMA,CAAK,EACnC,OAAOA,GAAU,SACvBA,IAAU,GACNE,IAAS,KAAO,OAAS,OAE1BF,GAEJE,IAAS,SACZF,EAAQG,GAAmBH,CAAK,GAE7BE,IAAS,OACZF,EAAQI,GAAqCJ,CAAK,GAE5CK,GAA+BL,CAAK,EAC5C,CAEO,SAASM,GAAgCC,EAAmB,CAClE,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MAClCA,EAAI,eAAe,KAAK,GAAKA,EAAI,eAAe,MAAM,GAExD,OAAOA,EAAI,MAAS,UACpBA,EAAI,OAAS,MACbA,EAAI,KAAK,QAAU,kBAEZ,MAAA,GAIV,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MACtC,QAASC,KAAOD,EACf,GAAI,OAAOA,EAAIC,CAAG,GAAM,UACVF,GAAgCC,EAAIC,CAAG,CAAC,EAE7C,MAAA,GAKJ,MAAA,EACR,CAEA,SAASL,GAAmBI,EAAe,CACtC,OAAA,OAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACTA,EAAI,KACJ,SAAUA,GACVA,EAAI,MAAM,QAAU,kBAEb,CAAE,KAAMA,EAAI,MAGjB,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACE,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCF,EAAAG,CAAK,EAAIP,GAAmBM,CAAI,EACrC,CACA,EACS,OAAOF,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIL,GAAmBI,EAAIC,CAAG,CAAC,CAAA,CACtC,EAEKD,EACR,CAEA,SAASH,GAAqCG,EAAe,CACxD,OAAA,OAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACTA,EAAI,KACJ,SAAUA,GACVA,EAAI,MAAM,QAAU,kBAEb,gBAAgBA,EAAI,GAAG,MAG5B,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACE,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCF,EAAAG,CAAK,EAAIN,GAAqCK,CAAI,EACvD,CACA,EACS,OAAOF,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIJ,GAAqCG,EAAIC,CAAG,CAAC,CAAA,CACxD,EAEKD,EACR,CAEA,SAASF,GAA+BE,EAAkB,CACzD,IAAII,EAAa,KAAK,UAAUJ,EAAK,CAACC,EAAKR,IACtCA,IAAU,KACN,eAGP,OAAOA,GAAU,UACjBA,EAAM,WAAW,cAAc,GAC/BA,EAAM,SAAS,GAAG,EAEX,WAAWA,CAAK,GAEjBA,CACP,EACD,MAAMY,EAAQ,oCACDD,EAAAA,EAAW,QAAQC,EAAO,CAACC,EAAOpC,IAAO,eAAeA,CAAE,GAAG,EAC1E,MAAMqC,EAAY,kBACX,OAAAH,EAAW,QAAQG,EAAW,MAAM,CAC5C,0OChI8E,GAAC,kDAevD,IAAAC,GAAA3C,EAAW,CAAA,EAAAA,EAAG,EAAA,CAAA,EAAA,MAAQ,OAAK,gDAA3B4C,EAAA,GAAAD,KAAAA,GAAA3C,EAAW,CAAA,EAAAA,EAAG,EAAA,CAAA,EAAA,MAAQ,OAAK,KAAA6C,EAAA,EAAAF,CAAA,iCADT,IAAAA,EAAA3C,KAAY,KAAI,SAAMA,EAAqB,CAAA,GAAIA,EAAiB,CAAA,IAAK,MAAI8C,GAAA,kFAAzEF,EAAA,GAAAD,KAAAA,EAAA3C,KAAY,KAAI,KAAA6C,EAAA,EAAAF,CAAA,EAAM3C,EAAqB,CAAA,GAAIA,EAAiB,CAAA,IAAK,mIAAK;AAAA,YACzG,0DAOH+C,EAAApB,GAAgB3B,EAAiB,CAAA,EAAEA,EAAY,CAAA,EAAA,KAAM,IAAI,EAAA,uJAHpDR,EAAuBC,EAAA8B,EAAA5B,CAAA,EAAAH,EAI9BC,EAAA6B,EAAA3B,CAAA,iBADEiD,EAAA,GAAAG,KAAAA,EAAApB,GAAgB3B,EAAiB,CAAA,EAAEA,EAAY,CAAA,EAAA,KAAM,IAAI,EAAA,KAAA6C,EAAAG,EAAAD,CAAA,iIALHvD,EAExDC,EAAAwD,EAAAtD,CAAA,uDAVCK,EAAgB,CAAA,IAAK,QAAUA,EAAA,CAAA,EAC9BA,EAAA,CAAA,EACA,IAAMA,EAAC,EAAA,EAAG,KAAG,mBAeyBA,EAAK,CAAA,EAAA,SAC9CA,EAAS,CAAA,EAAA,OAEPkD,EAAAlD,KAAY,YAAW,uBAfnB,OAAAA,OAAqB,SAAQmD,+CAG9BnD,EAAqB,CAAA,GAAIA,EAAgB,CAAA,GAAI,OAAMoD,iJAQ3C,2CAC2B,aAAO,IAAE,aAGlD;AAAA,gBAAE,gTAxBJ5D,EAAgBC,EAAA4D,EAAA1D,CAAA,WAChBH,EAyBKC,EAAAQ,EAAAN,CAAA,EAxBJC,EAiBGK,EAAAE,CAAA,EAhBFP,EAIAO,EAAAoB,CAAA,gBACA3B,EAGAO,EAAAmB,CAAA,wCASD1B,EAKGK,EAAAI,CAAA,yEArBCL,EAAgB,CAAA,IAAK,QAAUA,EAAA,CAAA,EAC9BA,EAAA,CAAA,EACA,IAAMA,EAAC,EAAA,EAAG,KAAG,KAAA6C,EAAAG,EAAAD,CAAA,8IAeyB/C,EAAK,CAAA,EAAA,KAAA6C,EAAAS,EAAAC,CAAA,cAC9CvD,EAAS,CAAA,EAAA,KAAA6C,EAAAW,EAAAC,CAAA,EAEPb,EAAA,GAAAM,KAAAA,EAAAlD,KAAY,YAAW,KAAA6C,EAAAa,EAAAR,CAAA,kGAOZ,EAAK,CAAA,CAAA,oEADtB1D,EAEKC,EAAAQ,EAAAN,CAAA,kIArCIoD,EAAA/C,KAAiB,OAAM,qBAAgBA,EAAgB,CAAA,EAAC,QAAU,GAAC2D,GAAA,OAIrE3D,EAAgB,CAAA,CAAA,uBAArB,OAAI4D,GAAA,2BA8BF5D,EAAU,CAAA,GAAAe,GAAA,kGAnCT;AAAA,UACG,aAAyB,YAAU,eAAwC,GACpF,kKAEiBf,EAAU,CAAA,CAAA,UAP3BR,EAKIC,EAAAoE,EAAAlE,CAAA,EAJHC,EAEKiE,EAAAzC,CAAA,sDAIN5B,EA8BKC,EAAAqE,EAAAnE,CAAA,oGAjCK,CAAAoE,GAAAnB,EAAA,IAAAG,KAAAA,EAAA/C,KAAiB,OAAM,KAAA6C,EAAAG,EAAAD,CAAA,EAAgB/C,EAAgB,CAAA,EAAC,QAAU,2DAIpEA,EAAgB,CAAA,CAAA,oBAArB,OAAI4D,GAAA,EAAA,mHAAJ,8BADc5D,EAAU,CAAA,CAAA,EA+BtBA,EAAU,CAAA,wNA5CH,GAAA,CAAA,WAAAgE,CAAA,EAAAtD,EACA,CAAA,iBAAAuD,CAAA,EAAAvD,EACA,CAAA,WAAAwD,CAAA,EAAAxD,EACA,CAAA,iBAAAyD,CAAA,EAAAzD,0vBCQVV,EAAS,CAAA,CAAA,oCAATA,EAAS,CAAA,CAAA,mIADqBA,EAAI,CAAA,CAAA,iNAZxB,GAAA,CAAA,KAAAoE,CAAA,EAAA1D,EACP2D,EAAY,OAEP,SAAAC,GAAA,CACR,UAAU,UAAU,UAAUF,CAAI,MAClCC,EAAY,SAAA,EACZ,oBACCA,EAAY,MAAA,GACV,uQCmBiBE,EAAY,CAAA,CAAA,kFAGtBA,EAAY,0EAJrB/E,EAEKC,EAAA4B,EAAA1B,CAAA,uBACLH,EAEKC,EAAA2B,EAAAzB,CAAA,EADJC,EAA0BwB,EAAAoD,CAAA,0KAVRC,EAAU,CAAA,CAAA,kFAGpBA,EAAU,0EAJnBjF,EAEKC,EAAA4B,EAAA1B,CAAA,uBACLH,EAEKC,EAAA2B,EAAAzB,CAAA,EADJC,EAAwBwB,EAAAoD,CAAA,0KAVNE,EAAU,CAAA,CAAA,kFAGpBA,EAAU,0EAJnBlF,EAEKC,EAAA4B,EAAA1B,CAAA,uBACLH,EAEKC,EAAA2B,EAAAzB,CAAA,EADJC,EAAwBwB,EAAAoD,CAAA,iLALrB,OAAAxE,OAAqB,SAAQ,EAOxBA,OAAqB,aAAY,EAOjCA,OAAqB,OAAM,sGAftCR,EAuBMC,EAAA2E,EAAAzE,CAAA,8iBA7BF+E,GAAa,4BACbD,GAAa,0BACbF,GAAe,oCAJR,GAAA,CAAA,iBAAAJ,CAAA,EAAAzD,+TCQR;AAAA,GAEF,kBAAoBV,EAAQ,CAAA,CAAA,yEAF7BR,EAGIC,EAAAkF,EAAAhF,CAAA,SADHC,EAAmC+E,EAAA1B,CAAA,0BAAfjD,EAAQ,CAAA,CAAA,2CALR+C,EAAA,IAAM/C,EAAQ,CAAA,6BAFhC;AAAA,GAEF,4FAFDR,EAGIC,EAAAkF,EAAAhF,CAAA,SADHC,EAAyC+E,EAAA1B,CAAA,iBAArBL,EAAA,GAAAG,KAAAA,EAAA,IAAM/C,EAAQ,CAAA,IAAA6C,EAAAG,EAAAD,CAAA,8DAH/B/C,EAAK,CAAA,EAAAe,oNALE,SAAA6D,EAA0B,IAAA,EAAAlE,GAC1B,SAAAmE,EAA0B,IAAA,EAAAnE,EAC1B,CAAA,MAAAoE,CAAA,EAAApE,wsCC4CwBV,EAAgB,CAAA,gHAAhBA,EAAgB,CAAA,uJAFhB,SAAAA,KAAW,mGAAX4C,EAAA,CAAA,EAAA,IAAAmC,EAAA,SAAA/E,KAAW,y+BAuGE,GAC1C,wDALH+C,EAAApB,GACI3B,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,MAAK,EAAA,OACCc,EAAAd,EAAI,EAAA,EAAAA,EAAoB,CAAA,EAAA,OAAS,GAACgF,GAAA,iBAN8E;AAAA,QACvH,gFACHpC,EAAA,CAAA,EAAA,IAAAG,KAAAA,EAAApB,GACI3B,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,MAAK,EAAA,KAAA6C,EAAAG,EAAAD,CAAA,EACC/C,EAAI,EAAA,EAAAA,EAAoB,CAAA,EAAA,OAAS,wIAPuBiF,EAAAjF,KAAW,SAAQ,SAA8C,YAUrI,UACc,UAAc,cAC4CkF,EAAAlF,KAAW,SAAQ,2BAhBtE,KAAAA,OAAgB,sBAKrBA,EAAmB,CAAA,CAAA,wBAAxB,OAAI4D,GAAA,yGADP,eAAa,MAAC5D,EAAe,EAAA,CAAA,MAAEA,EAAqB,EAAA,CAAA,MAAC,QAAM,aAAqB,8CAA4C,aAAI;AAAA,YAC/H,6CAQC;AAAA,EACX,aAAK;AAAA,gBACS,aAAK,WAAS,aAAK;AAAA,4BACP,MAACA,EAAe,EAAA,CAAA,MAAEA,EAAqB,EAAA,CAAA,MAAC,QAAM,cAAqB,YAAU,0GAlBrGR,EAoBMC,EAAA2E,EAAAzE,EAAA,EAnBLC,EAEKwE,EAAA/C,CAAA,qBAELzB,EAcKwE,EAAAhD,CAAA,EAbJxB,EAYwGwB,EAAAoD,CAAA,8OAhBtF5B,GAAA,CAAA,EAAA,OAAAuC,GAAA,KAAAnF,OAAgB,4CAIfA,EAAe,EAAA,CAAA,wBAAEA,EAAqB,EAAA,CAAA,GAAQ,CAAA+D,GAAAnB,GAAA,CAAA,EAAA,IAAAqC,KAAAA,EAAAjF,KAAW,SAAQ,KAAA6C,EAAAuC,EAAAH,CAAA,iBACvEjF,EAAmB,CAAA,CAAA,uBAAxB,OAAI4D,IAAA,EAAA,qIAAJ,6BAWe5D,EAAe,EAAA,CAAA,wBAAEA,EAAqB,EAAA,CAAA,GAAQ,CAAA+D,GAAAnB,GAAA,CAAA,EAAA,IAAAsC,KAAAA,EAAAlF,KAAW,SAAQ,KAAA6C,EAAAwC,EAAAH,CAAA,sIA3D1DI,EAAAtF,MAAc,IAAG,SACtCA,EAAS,EAAA,EAAA,0BAFrB;AAAA,gBACa,MAACA,EAAC,EAAA,CAAA,MAAC,kBAAgB,aAAmB;AAAA,cACxC,aAAW,oBAAkB,MAACA,EAAC,EAAA,CAAA,MAAC;AAAA,OACvC,kMAIwB,aAAkB,MAACA,EAAQ,CAAA,CAAA,MAAC,mBAAwB,0DAAjCA,EAAQ,CAAA,CAAA,kEAGzCA,EAAgB,CAAA,CAAA,uCAAhBA,EAAgB,CAAA,CAAA,yCADpB+C,EAAA/C,KAAW,SAAQ,kCAAtB,IAAE,aAAqB,GAAC,iDADcR,EAEvCC,EAAAwD,EAAAtD,CAAA,+BADIiD,EAAA,CAAA,EAAA,GAAAG,KAAAA,EAAA/C,KAAW,SAAQ,KAAA6C,EAAAG,EAAAD,CAAA,2CAepB/C,EAAc,EAAA,EAAA,OAAIsF,EAAA3D,GACnB3B,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,IAAG,EAAA,sBALT;AAAA,GACF,yBACwB,IAAE,aAKnB,IAAE,iDANTR,EAMOC,EAAAwD,EAAAtD,CAAA,wDALEK,EAAc,EAAA,EAAA,KAAA6C,EAAAG,EAAAD,CAAA,EAAIH,EAAA,CAAA,EAAA,IAAA0C,KAAAA,EAAA3D,GACnB3B,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,IAAG,EAAA,KAAA6C,EAAA0C,EAAAD,CAAA,uDAXFtF,EAAc,EAAA,EAAA,SAAWA,EAAS,EAAA,EAAA,wBAH1C;AAAA,KACC,yBAEsB,WAAS,aAC1B,IAAE,+GAHPR,EAGKC,EAAA8B,EAAA5B,CAAA,gCACJH,EAEIC,EAAA6B,EAAA3B,CAAA,0BAJEK,EAAc,EAAA,EAAA,KAAA6C,EAAAG,EAAAD,CAAA,kBAAW/C,EAAS,EAAA,EAAA,KAAA6C,EAAA0C,EAAAD,CAAA,iHAJnCtF,EAAe,EAAA,EAAC,SAASA,EAAS,EAAA,CAAA,uOANlCwF,GAAAxF,MAAYA,EAAI,CAAA,GAAA,yBAXH,MAAA,CAAA,KAAAA,MAAS,SAAS,aAIlCA,EAAa,EAAA,CAAA,uBAAlB,OAAI4D,GAAA,qBAQM,IAAA6B,EAAAzF,OAAa,MAAI0F,GAAA1F,CAAA,yBACYA,EAAK,CAAA,EAAA2F,6BAGpC3F,EAAmB,CAAA,CAAA,uBAAxB,OAAI4D,GAAA,wGAbC;AAAA,CACV,2CAKE;AAAA,qCACkC,kBAC5B,GAAC,aAAkB,GAAC,eAC2D;AAAA,qCACnD,YAEE,MAAW,2CAsBrC;AAAA;AAAA;AAAA;AAAA,CAIZ,oJA3CGpE,EA6CMC,EAAA2E,EAAAzE,CAAA,EA5CLC,EAEKwE,EAAA/C,CAAA,qBACLzB,EAwCKwE,EAAAhD,CAAA,EAvCJxB,EAsCAwB,EAAAoD,CAAA,iEA/B+B5E,EAE7B4E,EAAAvB,CAAA,4JAZgBL,EAAA,CAAA,EAAA,MAAAuC,EAAA,KAAAnF,MAAS,sCAIzBA,EAAa,EAAA,CAAA,oBAAlB,OAAI4D,GAAA,EAAA,gHAAJ,WAOQ,CAAAG,GAAAnB,EAAA,CAAA,EAAA,KAAA4C,KAAAA,GAAAxF,MAAYA,EAAI,CAAA,GAAA,KAAA6C,EAAA+C,EAAAJ,CAAA,EACdxF,OAAa,0IAIfA,EAAmB,CAAA,CAAA,oBAAxB,OAAI4D,GAAA,EAAA,gHAAJ,6KA9C4B,eAAa,2EAGhB,WAAS,MAAC5D,EAAQ,CAAA,CAAA,MAAC,kBAAgB,0DAAzBA,EAAQ,CAAA,CAAA,mDAK7C+C,EAAA/C,EAAA,EAAA,EACOA,MAAiB,IACjB,SACA2B,GACD3B,EAAqB,EAAA,EAAGA,EAAiB,EAAA,EAAGA,EAAa,EAAA,EACzDA,MAAY,KACZ,4BAPC;AAAA,GACR,gCAQM,GAAC,4BANIR,EAMLC,EAAAwD,EAAAtD,CAAA,0BARLiD,EAAA,CAAA,EAAA,IAAAG,KAAAA,EAAA/C,EAAA,EAAA,EACOA,MAAiB,IACjB,KAAE6C,EAAAG,EAAAD,CAAA,kBACFpB,GACD3B,EAAqB,EAAA,EAAGA,EAAiB,EAAA,EAAGA,EAAa,EAAA,EACzDA,MAAY,KACZ,gGAZoC6F,GAAA7F,MAAYA,EAAI,CAAA,GAAA,mBAiBxB8F,EAAA9F,KAAW,SAAQ,yBAxBjC,MAAA,CAAA,KAAAA,MAAa,SAAS,UAK1BA,EAAa,EAAA,GAAAmD,GAAA,EAGpB4C,EAAA/F,OAAa,MAAIoD,GAAApD,CAAA,QAGnBA,EAAmB,CAAA,CAAA,wBAAxB,OAAI4D,GAAA,0IARoC,iBAAe,yCAErD,SAAO,eAAoC;AAAA;AAAA,iBAElC,kBAA2B,GAAC,aAAkB,GAAC,eACO;AAAA,iBACtD,0CACT,GAAC,2CAaJ;AAAA,YACO,kBAAuB,IAAE,aAAqB,GAAC,MACtD;AAAA;AAAA,CAEJ,wCAAoC,UAAQ,gSA7BzCpE,EA+BMC,EAAA2E,EAAAzE,EAAA,EA9BLC,EAEKwE,EAAA/C,CAAA,qBACLzB,EA0BKwE,EAAAhD,CAAA,EAzBJxB,EAwB4CwB,EAAAoD,CAAA,EAxBvC5E,EAAmC4E,EAAAjD,CAAA,SAAe3B,EAErD4E,EAAAlD,CAAA,+BAES1B,EACT4E,EAAAwB,CAAA,6CACSpG,EACT4E,EAAAyB,CAAA,iEAcIrG,EAAsD4E,EAAA0B,CAAA,8BAGjEtG,EAAoC4E,EAAA2B,CAAA,8CA3BbvD,GAAA,CAAA,EAAA,MAAAuC,EAAA,KAAAnF,MAAa,sBAOS,CAAA+D,GAAAnB,GAAA,CAAA,EAAA,KAAAiD,KAAAA,GAAA7F,MAAYA,EAAI,CAAA,GAAA,KAAA6C,EAAAuD,EAAAP,CAAA,EACjD7F,OAAa,6EAGfA,EAAmB,CAAA,CAAA,wBAAxB,OAAI4D,IAAA,EAAA,+HAAJ,QAagC,CAAAG,GAAAnB,GAAA,CAAA,EAAA,IAAAkD,KAAAA,EAAA9F,KAAW,SAAQ,KAAA6C,EAAAwD,EAAAP,CAAA,2MAjClD9F,EAAK,CAAA,EAAA,kEAKL,OAAAA,OAAqB,SAAQ,EAmCxBA,OAAqB,aAAY,EAiDjCA,OAAqB,OAAM,4HA1FtCR,EAmHKC,EAAAQ,EAAAN,CAAA,ocA9IO,CAAA,WAAA2G,CAAA,EAAA5F,EACA,CAAA,iBAAA6F,CAAA,EAAA7F,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,WAAA8F,CAAA,EAAA9F,EACA,CAAA,SAAA+F,CAAA,EAAA/F,EACA,CAAA,oBAAAgG,CAAA,EAAAhG,EACA,CAAA,MAAAoE,CAAA,EAAApE,EACA,CAAA,SAAAiG,CAAA,EAAAjG,EACA,CAAA,iBAAAyD,CAAA,EAAAzD,EAEPkG,EACAC,EACAC,EAGAC,EAAgBL,EAAoB,KAAMM,GAC7C9E,GAAgC8E,EAAM,aAAa,CAAA,EAEhDC,EAAmB,CAAA,QAAS,OAAQ,QAAS,OAAO,EACpDC,EAAuBR,EAAoB,OAC7CM,GAA6BC,EAAgB,SAASD,EAAM,SAAS,CAAA,4CAmBpDJ,EAAWO,qDAmCXN,EAAOM,qDAkDPL,EAAcK,yaArG9BC,EAAA,GAAAC,EAAwBb,GAA0B,GAAA,mBACrDY,EAAA,GAAGE,EAAkB7G,EAAK,QAAQ,MAAO,EAAE,CAAA,k0CCoIrB,MAAA,CAAA,KAAAT,MAAW,SAAS,aAG/BA,EAAW,CAAA,CAAA,uBAAhB,OAAI4D,GAAA,qMALRpE,EAaMC,EAAA2E,EAAAzE,CAAA,EAZLC,EAEKwE,EAAA/C,CAAA,qBACLzB,EAQKwE,EAAAhD,CAAA,uFAVcwB,EAAA,KAAAuC,EAAA,KAAAnF,MAAW,iCAGtBA,EAAW,CAAA,CAAA,oBAAhB,OAAI4D,GAAA,EAAA,mHAAJ,yKAtBgB,MAAA,CAAA,KAAA5D,MAAS,SAAS,IAM7B,IAAAc,EAAAd,OAAa,MAAI2D,GAAA3D,CAAA,OACjBA,EAAS,CAAA,CAAA,uBAAd,OAAI4D,GAAA,wGAJD;AAAA;AAAA,kCAEuB,kBAA2B,GAAC,MAAC5D,EAAU,CAAA,CAAA,MAAC,GAAC,eACa;AAAA,MAClF,qLATFR,EAiBMC,EAAA2E,EAAAzE,CAAA,EAhBLC,EAEKwE,EAAA/C,CAAA,qBACLzB,EAYKwE,EAAAhD,CAAA,EAXJxB,EAUawB,EAAAoD,CAAA,SARe5E,EAC1B4E,EAAAvB,CAAA,kIANgBL,EAAA,KAAAuC,EAAA,KAAAnF,MAAS,oCAK8BA,EAAU,CAAA,CAAA,EAC5DA,OAAa,wEACbA,EAAS,CAAA,CAAA,oBAAd,OAAI4D,GAAA,EAAA,mHAAJ,oMA5BgB5D,EAAgB,CAAA,CAAA,IAQ3B,IAAAc,EAAAd,OAAa,MAAIoD,GAAApD,CAAA,OACtBA,EAAS,CAAA,CAAA,uBAAd,OAAI4D,GAAA,yIANuC,iBAAe,yCAErD;AAAA;AAAA,iBAES,kBAA2B,GAAC,MAAC5D,EAAU,CAAA,CAAA,MAAC,GAAC,eACa;AAAA,CACtE,uOAXGR,EAmBMC,EAAA2E,EAAAzE,CAAA,EAlBLC,EAEKwE,EAAA/C,CAAA,qBACLzB,EAcKwE,EAAAhD,CAAA,EAbJxB,EAYcwB,EAAAoD,CAAA,EAZT5E,EAAmC4E,EAAAjD,CAAA,SAAe3B,EAErD4E,EAAAlD,CAAA,SAES1B,EACT4E,EAAAwB,CAAA,gJARgBhG,EAAgB,CAAA,4BAOMA,EAAU,CAAA,CAAA,EAC3CA,OAAa,wEAClBA,EAAS,CAAA,CAAA,oBAAd,OAAI4D,GAAA,EAAA,mHAAJ,wJAmCwC5D,EAAQ,EAAA,EAAA,SAA8C,UACrFA,EAAI,EAAA,EAAA,SAAG,UACF,UAAc,cACeA,EAAQ,EAAA,EAAA,qCAH1C,eAAa,MAACA,EAAU,CAAA,CAAA,MAAC,OAAK,aAAU,8CAA4C,aAAK;AAAA,WAC1F,aAAM,GAAC,aAAK;AAAA,gBACP,aAAK,WAAS,aAAK;AAAA,4BACP,MAACA,EAAU,CAAA,CAAA,MAAC,OAAK,aAAU,YAAU,uDAH1DR,EAG+DC,EAAA+E,EAAA7E,CAAA,gJAC/DH,EAAKC,EAAA+B,EAAA7B,CAAA,mBAJcK,EAAU,CAAA,CAAA,gBAAOA,EAAQ,EAAA,EAAA,KAAA6C,EAAA0C,EAAAD,CAAA,gBACvCtF,EAAI,EAAA,EAAA,KAAA6C,EAAAuD,EAAAP,CAAA,WAEa7F,EAAU,CAAA,CAAA,gBAAOA,EAAQ,EAAA,EAAA,KAAA6C,EAAA0E,EAAAC,CAAA,sEApBvB,aAAkB,MAACxH,EAAQ,CAAA,CAAA,MAAC,mBAAwB,sDAAjCA,EAAQ,CAAA,CAAA,qDAMpCA,EAAI,EAAA,EAAA,oBAAP,IAAE,yDAACA,EAAI,EAAA,EAAA,KAAA6C,EAAAG,EAAAD,CAAA,oDADpB/C,EAAQ,EAAA,EAAA,WACAA,EAAI,EAAA,GAAA8C,GAAA9C,CAAA,iBAJV;AAAA,sBACc,kBACG;AAAA,KACpB,aAAU,GAAC,eACgB;AAAA,OACzB,yDAJeR,EAGbC,EAAAwD,EAAAtD,CAAA,kEADHK,EAAQ,EAAA,EAAA,KAAA6C,EAAA4E,EAAAC,CAAA,EACA1H,EAAI,EAAA,kJAzBa,WAAS,MAACA,EAAQ,CAAA,CAAA,MAAC,kBAAgB,sDAAzBA,EAAQ,CAAA,CAAA,yDAK/CA,EAAI,EAAA,EAAA,aAAsCA,EAAQ,EAAA,EAAA,wBAHjD;AAAA,QACK,kBACE;AAAA,CACT,aAAM,aAAW,kBAAuB,IAAE,aAAU,GAAC,MAAM;AAAA;AAAA,CAE3D,kFAJOR,EAIAC,EAAA6B,EAAA3B,CAAA,uBAFUC,EAA0C0B,EAAAC,CAAA,oDAA1DvB,EAAI,EAAA,EAAA,KAAA6C,EAAA4E,EAAAC,CAAA,gBAAsC1H,EAAQ,EAAA,EAAA,KAAA6C,EAAAuC,EAAAH,CAAA,4FAhB5C,OAAAjF,OAAqB,SAAQ,EAqBxBA,OAAqB,aAAY,EAmBjCA,OAAqB,OAAM,4YAzClB,kIAFrBR,EA4DKC,EAAAQ,EAAAN,CAAA,oMAnLO,GAAA,CAAA,aAAAgI,CAAA,EAAAjH,EACA,CAAA,WAAAkH,CAAA,EAAAlH,EACA,CAAA,KAAAD,CAAA,EAAAC,GACA,WAAA8F,EAAa,EAAA,EAAA9F,EACb,CAAA,iBAAAyD,CAAA,EAAAzD,EACA,CAAA,SAAAiG,CAAA,EAAAjG,EAEPkG,EACAiB,EACAhB,EACAiB,EAEO,CAAA,UAAAC,EAAA,EAAA,EAAArH,EAEI,eAAAsH,GAAA,CAQP,OADH,MAHA,MAAiB,MACpBvH,EAAK,QAAQ,MAAO,EAAE,EAAI+F,EAAa,2BAAA,GAEd,OAIvB,IAAAyB,EACAC,EAAA,CAAA,EACAC,EAAA,CAAA,EACAC,EAAA,CAAA,EAEK,SAAAC,EAAgBC,EAAexG,EAAA,CACjC,MAAA8C,EAAA,IAAe+C,EAAaW,EAAK,QAAQ,EAAE,QAAQ,GAMnDC,EAJ0BD,EAAK,KAAK,OACxCE,GAAA,OAAaA,EAAM,GAAA,EAInB,KAAKxB,EAAO1E,IAAA,CACR,GAAA2F,EAAerD,CAAQ,EAAA,CACpB,MAAA6D,EAAaR,EAAerD,CAAQ,EAAE,WAAWtC,CAAK,EACvD,GAAA,CAAAmG,EACG,OAEF,MAAAC,EAAaD,EAAW,eACxBE,GAAcF,EAAW,YAAY,QACvC3G,IAAS,gBACA4G,CAAU,IAAI/G,GACzBqF,EACA2B,GACA,IAAA,CAAA,MAES7G,IAAS,kBACL4G,CAAU,KAAK/G,GAC5BqF,EACA2B,GACA,IAAA,CAAA,MAES7G,IAAS,OACL,MAAA,OAAAH,GACbqF,EACA2B,GACA,MAAA,CAAA,cAIShH,GAAgBqF,EAAA,OAA4BlF,CAAI,CAAA,EAE5D,CAAA,EAAA,OAAQ0G,GAAa,OAAAA,EAAM,GAAW,EACtC,KAAK;AAAA,CAAK,EACR,GAAAD,EAAA,IACCzG,IAAS,cACFyG,CAAM;AAAA,KACNzG,IAAS;EACNyG,CAAM;AAAA,MACTzG,IAAS;EACPyG,CAAM;AAAA,SAGhBzG,IAAS,KACL,GAED;AAAA,EAGR8G,GAAA,SAAA,CAECX,GADmB,MAAAD,KACG,gBAClB,IAAAa,EAAyBd,EAAU,IAAKO,GAC3CD,EAAgBC,EAAM,IAAI,CAAA,EAEvBQ,EAAyBf,EAAU,IAAKO,GAC3CD,EAAgBC,EAAM,IAAI,CAAA,EAEvBS,EAA2BhB,EAAU,IAAKO,GAC7CD,EAAgBC,EAAM,MAAM,CAAA,EAEzBU,EAAsBjB,EAAU,IAClCO,GAASX,EAAaW,EAAK,QAAQ,EAAE,UAAY,EAAA,EAEnDlB,EAAA,EAAAc,EAAYW,EAAa,IAAK,CAAAP,EAAMhG,MACnC,KAAAgG,EACA,SAAUU,EAAU1G,CAAK,CAAA,EAAA,CAAA,EAE1B8E,EAAA,EAAAe,EAAYW,EAAa,IAAK,CAAAR,EAAMhG,MACnC,KAAAgG,EACA,SAAUU,EAAU1G,CAAK,CAAA,EAAA,CAAA,EAE1B8E,EAAA,EAAAgB,EAAcW,EAAe,IAAK,CAAAT,EAAMhG,MACvC,KAAAgG,EACA,SAAUU,EAAU1G,CAAK,CAAA,EAAA,CAAA,EAGpB,MAAA2G,GAAA,EAEN7B,EAAA,EAAAS,EAAmBjB,EAAY,SAAA,8CAYbA,EAAWO,qDAqBXN,EAAOM,qDAmBPW,EAASX,wlCC9K7B,MAAe+B,GAAA,wpCCAAC,GAAA,0yBCAAC,GAAA,o7DCeC,WACL,uDAFR,IAAAC,EAAArJ,MAAoB,SAAW,QAAU,WAAY0H,EAAA1H,KAAiB,OAAM,6BAA5B,MAAI,aAAwB;AAAA,WACrE,sDADP4C,EAAA,GAAAyG,KAAAA,EAAArJ,MAAoB,SAAW,QAAU,SAAM6C,EAAAyG,EAAAD,CAAA,EAAMzG,EAAA,GAAA8E,KAAAA,EAAA1H,KAAiB,OAAM,KAAA6C,EAAA4E,EAAAC,CAAA,iGAWrD1H,EAAC,EAAA,CAAA,+CAAtBR,EAA8BC,EAAAwD,EAAAtD,CAAA,uCAGgCK,EAAU,CAAA,EACtEA,OACC,KAAI,4DAFuDA,EAAU,CAAA,EACtEA,OACC,KAAI,KAAA6C,EAAA,EAAAF,CAAA,iCAF8B,IAAAA,EAAA3C,KAAY,KAAI,gDAAhB4C,EAAA,GAAAD,KAAAA,EAAA3C,KAAY,KAAI,KAAA6C,EAAA,EAAAF,CAAA,yDAMf3C,EAAK,CAAA,EAAA,SAC3CA,EAAS,CAAA,EAAA,WAXLA,EAAgB,CAAA,EAAC,OAAS,GAACmD,GAAAnD,CAAA,kBAIzB,OAAAA,OAAqB,SAAQoD,2HAKtB,wCACwB,aAAO,IAAE,aAG/C;AAAA,eACD,uJAjBD5D,EAAgBC,EAAA4D,EAAA1D,CAAA,WAChBH,EAiBKC,EAAAQ,EAAAN,CAAA,EAhBJC,EASGK,EAAAE,CAAA,wBALFP,EAIAO,EAAA8C,CAAA,qBAEDrD,EAKGK,EAAAI,CAAA,oDAdGL,EAAgB,CAAA,EAAC,OAAS,6HAUQA,EAAK,CAAA,EAAA,KAAA6C,EAAA+C,EAAAJ,CAAA,cAC3CxF,EAAS,CAAA,EAAA,KAAA6C,EAAAS,EAAAC,CAAA,qGASI,EAAK,CAAA,CAAA,oEADtB/D,EAEKC,EAAAQ,EAAAN,CAAA,mKAhCQK,EAAgB,CAAA,EAAC,OAAS,EAAC8C,6BAOjC9C,EAAgB,CAAA,CAAA,uBAArB,OAAI4D,GAAA,2BAsBF5D,EAAU,CAAA,GAAAe,GAAA,+GA9BT;AAAA,UACG,wKAMQf,EAAU,CAAA,CAAA,UAV3BR,EAQIC,EAAAoE,EAAAlE,CAAA,EAPHC,EAEKiE,EAAAzC,CAAA,8BAON5B,EAsBKC,EAAAqE,EAAAnE,CAAA,8KArBGK,EAAgB,CAAA,CAAA,oBAArB,OAAI4D,GAAA,EAAA,mHAAJ,8BADc5D,EAAU,CAAA,CAAA,EAuBtBA,EAAU,CAAA,qNAvCH,GAAA,CAAA,WAAAgE,CAAA,EAAAtD,EACA,CAAA,iBAAAuD,CAAA,EAAAvD,EACA,CAAA,WAAAwD,CAAA,EAAAxD,EACA,CAAA,iBAAAyD,CAAA,EAAAzD,w6BC+FNV,EAAS,CAAA,EAAA,geAEc,KAAAA,MAAYA,EAAI,CAAA,kDAYjCA,EAAK,EAAA,CAAA,uBAAV,OAAI4D,GAAA,0DAWF,OAAA5D,KAAU,OAAM,mCAoGdA,EAAY,CAAA,CAAA,uBAAjB,OAAI4D,GAAA;mVA5HRpE,EAEKC,EAAA4B,EAAA1B,CAAA,uBAELH,EAgKKC,EAAA8J,EAAA5J,CAAA,EA/JJC,EAKK2J,EAAAnI,CAAA,SACLxB,EAwJK2J,EAAAC,CAAA,EAvJJ5J,EAWK4J,EAAA1F,CAAA,4JAtBoBlB,EAAA,IAAA6G,EAAA,KAAAzJ,MAAYA,EAAI,CAAA,0BAYjCA,EAAK,EAAA,CAAA,oBAAV,OAAI4D,GAAA,EAAA,mHAAJ,wJA+GI5D,EAAY,CAAA,CAAA,oBAAjB,OAAI4D,GAAA,EAAA,4GAAJ,OAAIA,EAAA8F,EAAA,OAAA9F,GAAA,kEAAJ,OAAIA,GAAA,kMAxGF5D,EAAQ,EAAA,EAAA,4GADCA,EAAG,EAAA,CAAA,GAAAgB,EAAA2I,EAAA,MAAAC,CAAA,6CAHb5I,EAAA6I,EAAA,QAAAC,EAAA,YAAA9J,OAAqBA,EAAQ,EAAA,EAAG,eAAiB,iBAAe,gBAAA,UAFjER,EAOIC,EAAAoK,EAAAlK,CAAA,EAFHC,EAAuBiK,EAAAF,CAAA,+DAHvB/G,EAAA,IAAAkH,KAAAA,EAAA,YAAA9J,OAAqBA,EAAQ,EAAA,EAAG,eAAiB,iBAAe,kHA8C5D,OAAAA,EAAoB,CAAA,GAAA,UAAYA,MAAoB,aAAY+J,wEAkBhE/J,EAAQ,CAAA,GAAA0F,GAAA1F,CAAA,gHAqBR,IAAAgK,EAAAhK,MAAoB,QAAM2D,GAAA3D,CAAA,sEAzBf;AAAA;AAAA;AAAA,OAIhB,eASQ;AAAA;AAAA;AAAA,OAGR,uBAOQ;AAAA;AAAA,OAER,2FAxCDR,EAWGC,EAAAU,EAAAR,CAAA,yCAIHH,EA6CGC,EAAAY,EAAAV,CAAA,mMAzCGK,EAAQ,CAAA,sHAqBRA,MAAoB,0QAtEpB0H,EAAA1H,KAAU,OAAM,wHAcR,WAAAA,KAAI,WACJ,WAAAA,MAAYA,EAAI,CAAA,gDAjB7B,wBACuB,kBACpB,GAAC,aAAkB,GAAC,qBAGrB;AAAA,wBAEe,MAACA,EAAgB,CAAA,CAAA,MAAA;AAAA,eAElC;;oVAbDR,EA6BKC,EAAAQ,EAAAN,CAAA,EA5BJC,EAOGK,EAAAE,CAAA,SAHoBP,EAEtBO,EAAA8C,CAAA,8BAEDrD,EAIGK,EAAAI,CAAA,iDAWHT,EAIGK,EAAAgK,CAAA,WAEJzK,EAIGC,EAAAyK,EAAAvK,CAAA,gBA5BG,CAAAoE,GAAAnB,EAAA,KAAA8E,KAAAA,EAAA1H,KAAU,OAAM,KAAA6C,EAAA4E,EAAAC,CAAA,kBAKH1H,EAAgB,CAAA,CAAA,oHASrB4C,EAAA,IAAAuH,EAAA,WAAAnK,KAAI,YACJ4C,EAAA,IAAAuH,EAAA,WAAAnK,MAAYA,EAAI,CAAA,2KAuBvB,yDAEN,0FATqE;AAAA,QAEpE,kBAAyCA,EAAgB,CAAA,CAAA,MAAO;AAAA,gBACxD,eAES,MAAI,MACpB,2CACF,sCAHQgB,EAAAoJ,EAAA,OAAAC,EAAArK,MAAoB,SAAWsK,GAAUC,EAAO,uEAFvD/K,EAAgEC,EAAAwD,EAAAtD,CAAA,kBACxDH,EAGPC,EAAA2K,EAAAzK,CAAA,oCAJwCK,EAAgB,CAAA,CAAA,EAElD4C,EAAA,IAAAyH,KAAAA,EAAArK,MAAoB,SAAWsK,GAAUC,qGAcnC;AAAA,oCACe,eAOX,WAAS,MACzB,IAAE,EAPIvJ,EAAAoJ,EAAA,OAAAC,EAAArK,MAAoB,SACvBsK,GAAUE,GACVxK,MAAoB,aACnBuK,GAAUC,GACVC,EAAS,iFALcjL,EAQ3BC,EAAA2K,EAAAzK,CAAA,0BAPMiD,EAAA,IAAAyH,KAAAA,EAAArK,MAAoB,SACvBsK,GAAUE,GACVxK,MAAoB,aACnBuK,GAAUC,GACVC,sOAWLjL,EAA8BC,EAAAQ,EAAAN,CAAA,WAC9BH,EAAwCC,EAAAoB,EAAAlB,CAAA,sGAY9BmB,EAAAd,OAAa,MAAI2F,GAAA,2BATU,GAAM,gBAAM;AAAA;AAAA,QAEjD,+CAA2B;AAAA,QAC3B,uCAAgB;AAAA,cACV,sCAAgB,gBAAc,uCAAiB;AAAA,mBAC1C,2CAAqB;AAAA,QAChC,sCAAgB;AAAA,mBACL,sCAAgB,OAAK,uCAAiB;AAAA;AAAA,aAE5C,eAES;AAAA,QACd,eAAoC,WAAS,MAAI,GAClD,wNADU8E,EAAS,8DAZajL,EAAMC,EAAAiL,EAAA/K,CAAA,WAAMH,EAAMC,EAAAkL,EAAAhL,CAAA,WAEjDH,EAA2BC,EAAAmL,EAAAjL,CAAA,WAC3BH,EAAgBC,EAAAW,EAAAT,CAAA,WACVH,EAAgBC,EAAAoL,EAAAlL,CAAA,WAAcH,EAAiBC,EAAAqL,EAAAnL,CAAA,WAC1CH,EAAqBC,EAAAsL,EAAApL,CAAA,WAChCH,EAAgBC,EAAAuL,EAAArL,CAAA,WACLH,EAAgBC,EAAAwL,EAAAtL,CAAA,WAAKH,EAAiBC,EAAAyL,EAAAvL,CAAA,gCAKjDH,EAAiDC,EAAA2K,EAAAzK,CAAA,0BAHvCK,OAAa,4OAAI;AAAA,iBAElB,oGAgBD,uBACcA,EAAI,CAAA,EAAC,gBACzB,IAAMA,EAAU,EAAA,EAAC,UAChB,+GAOU,WAAAA,KAAI,gDAIEA,EAAI,CAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,sBACUA,EAAO,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,oFAMgBA,EAAI,CAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,mBACUA,EAAO,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,4LA9BJR,EAkCKC,EAAAQ,EAAAN,CAAA,iHA/BkBK,EAAI,CAAA,EAAC,gBACzB,IAAMA,EAAU,EAAA,EAAC,UAChB,wIAOU4C,EAAA,IAAAuI,EAAA,WAAAnL,KAAI,4DAIEA,EAAI,CAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,iCACUA,EAAO,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,4FAMgBA,EAAI,CAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,8BACUA,EAAO,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,wPA/BAA,EAAU,EAAA,EAAC,UAAYA,EAAI,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,GAAAmD,GAAAnD,CAAA,uEAArEA,EAAU,EAAA,EAAC,UAAYA,EAAI,CAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,qMA/H1EA,EAAI,CAAA,GAAAe,GAAAf,CAAA,yEAAJA,EAAI,CAAA,kLAzEFuK,GACL,mEACKD,GACL,uEACKG,GACL,+DACKD,GAAqB,0CAsBvBxG,GAAa,sBAnCN,GAAA,CAAA,aAAA2D,CAAA,EAAAjH,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,IAAA0K,CAAA,EAAA1K,EACA,CAAA,SAAA+F,CAAA,EAAA/F,EACA,CAAA,UAAA2K,CAAA,EAAA3K,EACA,CAAA,SAAAiG,CAAA,EAAAjG,EAUPe,EAAYkG,EAAa,OAC3BrB,GAAeA,EAAW,QAC1B,EAAA,OAEE7F,IAAS,KACZA,EAAO,SAAS,SAAW,KAAO,SAAS,KAAO,SAAS,UAEvDA,EAAK,SAAS,GAAG,IACrBA,GAAQ,KAGE,GAAA,CAAA,UAAAsH,EAAA,EAAA,EAAArH,EACPyD,EAAqD,SAEnD,MAAAmH,EAAA,CAAA,CACJ,SAAUpC,EAAM,EAAA,CAChB,aAAcC,EAAU,EAAA,CACxB,OAAQC,EAAI,CAAA,EAKC,eAAApB,GAAA,CAQP,OADH,MAHA,MAAiB,MACpBvH,EAAK,QAAQ,MAAO,EAAE,EAAI2K,EAAI,WAAa,OAAA,GAElB,OAGZ,eAAAG,GAAA,CAEP,OADH,MAAoBH,EAAI,WAIzB,IAAAI,EAKAC,EAEJzD,EAAA,EAAW,KAAM0D,GAAA,KAChBF,EAAOE,CAAA,IAGRH,EAAA,EAAc,KAAMI,GAAA,KACnBF,EAAUE,CAAA,UAGLpL,EAAWC,KAEjBoI,GAAA,KACC,SAAS,KAAK,MAAM,SAAW,SAC3B,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,OAGlC,SAAS,KAAK,MAAM,SAAW,0CAwBV,MAAAjI,EAAAiL,GAAAxE,EAAA,EAAAjD,EAAmByH,CAAQ,EA8E5BlK,EAAA,IAAAnB,EAAS,QAAW,CAAA,qBAAsB,EAAI,CAAA,+hCCnL7DP,EAAY,CAAA,EAACA,EAAS,CAAA,EAACA,EAAS,CAAA,EAAC,OAAS,CAAC,EAAE,QAAQ,EACtD,SAAQ,gCADT,GAAC,uDADHR,EAGAC,EAAAwD,EAAAtD,CAAA,oCAFIK,EAAY,CAAA,EAACA,EAAS,CAAA,EAACA,EAAS,CAAA,EAAC,OAAS,CAAC,EAAE,QAAQ,EACtD,SAAQ,KAAA6C,EAAAG,EAAAD,CAAA,mDALTyC,EAAAxF,KAAU,OAAM,WAEdA,EAAS,CAAA,EAAC,OAAS,GAACe,GAAAf,CAAA,2GAHF,GACrB,aAAkB,GACpB,yNALDR,EAA4CC,EAAAQ,EAAAN,CAAA,WAC5CH,EAAgDC,EAAAU,EAAAR,CAAA,WAChDH,EAUGC,EAAAY,EAAAV,CAAA,EATFC,EAEMS,EAAA4C,CAAA,qDADHL,EAAA,GAAA4C,KAAAA,EAAAxF,KAAU,OAAM,KAAA6C,EAAA+C,EAAAJ,CAAA,EAEdxF,EAAS,CAAA,EAAC,OAAS,sSAR3BR,EAgBKC,EAAAQ,EAAAN,CAAA,4LApBO,GAAA,CAAA,UAAAoI,EAAA,EAAA,EAAArH,EACA,CAAA,aAAAiH,CAAA,EAAAjH,0XCDC,MAAAmL,GAAYC,GAAIC,EAAM,kVCyD9B/L,EAAW,CAAA,8DAPTA,EAAU,CAAA,8NAOZA,EAAW,CAAA,CAAA,uQAPTA,EAAU,CAAA,GAAA,2TAOZA,EAAW,CAAA,CAAA,wZApDJ,CAAA,KAAAS,CAAA,EAAAC,EACA,CAAA,UAAAsL,CAAA,EAAAtL,EACA,CAAA,OAAAjB,CAAA,EAAAiB,EACA,CAAA,WAAAuL,CAAA,EAAAvL,EACA,CAAA,SAAAwL,CAAA,EAAAxL,EACA,CAAA,MAAAkB,CAAA,EAAAlB,EAEA,CAAA,QAAAyL,CAAA,EAAAzL,EACA,CAAA,aAAA0L,CAAA,EAAA1L,EACA,CAAA,IAAA2L,CAAA,EAAA3L,QAEL4L,EAAK,CAAAC,EAAY1L,EAAW2L,IAC7B,IAAA,YAAY,eAAiB,OAAU,CAAA,GAAAD,EAAI,KAAM1L,EAAG,MAAO2L,CAAA,CAAA,CAAA,WAEvDC,EACRT,EAAAA,CAuBO,OArBD,IAAqB,MAAMA,GAChC,UAAUU,EAASC,EAAA,CAEZT,MAAAA,EAAAA,IAAeQ,KAAWC,CAAI,EAC9BC,EAAQ,OAAO,KAAKV,EAAS,GAAG,KAAK,WAElCW,EAAOD,EAAAA,CACE,OAAA,SAAAE,EAAA,CACX,GAAA,CAAArN,EAAA,aACCsN,EAAKT,EAAED,EAAKO,EAAOE,CAAQ,EACjCrN,EAAO,cAAcsN,CAAE,GAGzB,OAAAH,EAAM,QAASJ,GAAA,CACdQ,GAAkB,KAAW,IAAAC,GAAKf,EAAUM,EAAGK,EAAOL,CAAC,CAAA,CAAA,IAGjDN,KAOJ,MAAAgB,EAAaT,EAAKT,CAAS,4CAKtBE,EAAQ/E,06CCqCXgG,EAAAC,GAAApN,KAAK,QAAQ,EAAW,MAAAqN,EAAArN,GAAAA,MAAM,mBAAnC,OAAI4D,GAAA,EAAA,wLAACuJ,EAAAC,GAAApN,KAAK,QAAQ,sFAAlB,OAAI4D,GAAA,6KAEE5D,EAAK,EAAA,EACA,UAAAA,MAAM,sBAEb,GAAAA,MAAM,wOAHJA,EAAK,EAAA,GACA4C,EAAA,IAAA0K,EAAA,UAAAtN,MAAM,gCAEb4C,EAAA,IAAA0K,EAAA,GAAAtN,MAAM,4NANRc,EAAAd,KAAK,UAAYA,EAAK,CAAA,EAAA,SAAS,QAAMe,GAAAf,CAAA,uEAArCA,KAAK,UAAYA,EAAK,CAAA,EAAA,SAAS,mNAZ/B,CAAA,IAAAA,MAAM,EAAE,EACF,CAAA,UAAAA,KAAK,SAAS,GAGf,QAAA,YAAaA,EAAK,CAAA,EAAA,OAASA,EAAK,CAAA,EAAA,MAAM,SAClC,aAAAA,KAAK,EAAE,kBACN,iBAAkBA,EAAK,CAAA,EAAA,OAASA,EAAI,CAAA,EAAC,MAAM,cAAY,CAAA,iBAElEA,KAAK,2KANM,OAAAA,KAAK,WAAQ,SAAbuN,EAAA,SAAAvN,KAAK,UACRA,EAAI,CAAA,EAAC,MAAM,QAAK,iBAAhBA,EAAI,CAAA,EAAC,MAAM,iKAHlB4C,EAAA,GAAA,CAAA,IAAA5C,MAAM,EAAE,EACF4C,EAAA,GAAA,CAAA,UAAA5C,KAAK,SAAS,QAGf,QAAA,YAAaA,EAAK,CAAA,EAAA,OAASA,EAAK,CAAA,EAAA,MAAM,SAClC,aAAAA,KAAK,EAAE,uBACN,iBAAkBA,EAAK,CAAA,EAAA,OAASA,EAAI,CAAA,EAAC,MAAM,cAAY,CAAA,sBAElE4C,EAAA,GAAA4K,GAAAxN,KAAK,KAAK,oGANCyN,EAAA,SAAAzN,KAAK,8CACRA,EAAI,CAAA,EAAC,MAAM,iIA1EZ,GAAA,CAAA,KAAAS,CAAA,EAAAC,EAEA,CAAA,KAAAgN,CAAA,EAAAhN,GACA,OAAAiN,EAAwB,IAAA,EAAAjN,EACxB,CAAA,OAAAjB,CAAA,EAAAiB,EACA,CAAA,WAAAuL,CAAA,EAAAvL,EACA,CAAA,QAAAkN,CAAA,EAAAlN,EACA,CAAA,WAAAmN,CAAA,EAAAnN,EACA,CAAA,cAAAoN,CAAA,EAAApN,EACA,CAAA,OAAAqN,CAAA,EAAArN,QAELH,EAAWC,KACb,IAAAwN,EAAA,CAAA,EAEJpF,GAAA,IAAA,CACCrI,EAAS,QAASmN,EAAK,EAAE,YAEdO,KAASD,EACnBzN,EAAS,QAAS0N,EAAM,EAAE,aAI1B1N,EAAS,UAAWmN,EAAK,EAAE,YAEhBO,KAASD,EACnBzN,EAAS,QAAS0N,EAAM,EAAE,KAmB7BC,GAAW,YAAaP,CAAM,gFA6BfQ,EAAA,GAAA,UAAAT,EAAK,SAAQ9L,CAAA,IAAb8L,EAAK,SAAQ9L,gGAChB8L,EAAK,MAAM,MAAK9L,CAAA,IAAhB8L,EAAK,MAAM,MAAK9L,4ZA3CvB8L,OACHA,EAAK,SACJA,EAAK,UACLA,EAAK,SAAS,OAAQlB,GAAA,CACf,MAAA4B,EAAaV,EAAK,OAAS,gBAC5B,OAAAU,GACJJ,EAAkB,KAAKxB,CAAC,EAElB4B,qBAQNV,GAAQA,EAAK,OAAS,SACrBA,EAAK,UAAU,MAAOW,GAAO,CAAAA,EAAE,MAAM,OAAO,EAC/CjH,EAAA,EAAAsG,EAAK,MAAM,QAAU,GAAAA,CAAA,EAErBtG,EAAA,EAAAsG,EAAK,MAAM,QAAU,GAAAA,CAAA,mBAKxBtG,EAAA,EAAGsG,EAAK,MAAM,OAAa,IAAAY,GAC1BZ,EAAK,GACLjO,EACAwM,EACA2B,EACAnN,EACAoN,EACAC,EACAjC,GACAkC,EACAQ,EAAA,EAAAb,CAAA,k8BCpDM1N,EAAQ,CAAA,mLAARA,EAAQ,CAAA,4RAFXA,EAAQ,CAAA,GAAAe,GAAAf,CAAA,yEAARA,EAAQ,CAAA,6LAfD,GAAA,CAAA,SAAAwO,CAAA,EAAA9N,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,OAAAjB,CAAA,EAAAiB,EACA,CAAA,WAAAuL,CAAA,EAAAvL,EACA,CAAA,QAAAkN,CAAA,EAAAlN,EACA,CAAA,WAAAmN,CAAA,EAAAnN,GACA,cAAAoN,EAA+B,IAAA,EAAApN,EAC/B,CAAA,OAAAqN,CAAA,EAAArN,QAELH,EAAWC,KACjB,OAAAoI,GAAA,IAAA,CACCrI,EAAS,OAAO,6lCChBlB,MAAekO,GAAA,01CCqtBLzO,EAAK,CAAA,oDAGM0O,GAAW1O,EAAG,EAAA,EAAEA,EAAO,EAAA,CAAA,CAAA,mHAAvB0O,GAAW1O,EAAG,EAAA,EAAEA,EAAO,EAAA,CAAA,CAAA,aAAA2O,EAAA,EAAAC,CAAA,kFAQ9B5O,EAAQ,EAAA,iGAQVA,EAAG,EAAA,mBAJDA,EAAY,EAAA,CAAA,yFAJZA,EAAQ,EAAA,kMAQVA,EAAG,EAAA,sHAyBV+C,EAAA/C,MAAG,0BAA0B,EAAA,eAlB1BA,EAAQ,CAAA,GAAA2D,GAAA3D,CAAA,sFAmBFyO,EAAI,GAAAzN,EAAAC,EAAA,MAAAC,CAAA,EAAOF,EAAAC,EAAA,MAAA4N,EAAA7O,MAAG,aAAa,CAAA,+LApBvCR,EAsBQC,EAAAqP,EAAAnP,CAAA,wBATPC,EAQGkP,EAAA1E,CAAA,gBADFxK,EAAyCwK,EAAAnJ,CAAA,UAnBrCjB,EAAQ,CAAA,yDAkBX4C,EAAA,CAAA,EAAA,SAAAG,KAAAA,EAAA/C,MAAG,0BAA0B,EAAA,KAAA6C,EAAAG,EAAAD,CAAA,EACTH,EAAA,CAAA,EAAA,SAAAiM,KAAAA,EAAA7O,MAAG,aAAa,+DAZnCqJ,EAAArJ,MAAG,oBAAoB,EAAA,oHACdY,EAAQ,GAAAI,EAAAC,EAAA,MAAAC,CAAA,EAAOF,EAAAC,EAAA,MAAA4N,EAAA7O,MAAG,aAAa,CAAA,6GAP1CR,EAQQC,EAAAa,EAAAX,CAAA,gBADPC,EAA6CU,EAAAW,CAAA,WAE9CzB,EAAWC,EAAAQ,EAAAN,CAAA,0CAHTiD,EAAA,CAAA,EAAA,SAAAyG,KAAAA,EAAArJ,MAAG,oBAAoB,EAAA,KAAA6C,EAAAyG,EAAAD,CAAA,EACCzG,EAAA,CAAA,EAAA,SAAAiM,KAAAA,EAAA7O,MAAG,aAAa,0PAqB7CR,EAQKC,EAAAQ,EAAAN,CAAA,mTAgBSK,EAAQ,EAAA,6SAZtBR,EA0BKC,EAAAqE,EAAAnE,CAAA,EAtBJC,EAKCkE,EAAAzC,CAAA,SACDzB,EAeKkE,EAAA1C,CAAA,+FAbQpB,EAAQ,EAAA,qWAkBMA,EAAkB,EAAA,CAAA,uOAlGzCA,EAAkB,CAAA,GAAA+J,GAAA/J,CAAA,IAGlBA,EAAG,EAAA,GAAA0F,GAAA1F,CAAA,IAOFA,EAAQ,EAAA,GAAIA,EAAG,EAAA,EAAC,QAAM2F,GAAA3F,CAAA,IAevBA,EAAW,CAAA,GAAA8C,GAAA9C,CAAA,IA2BZA,EAAoB,EAAA,GAAAmD,GAAAnD,CAAA,EAepB+O,EAAA/O,OAAoBA,EAAQ,EAAA,GAAAoD,GAAApD,CAAA,IA8B5BA,EAAQ,EAAA,GAAAe,GAAAf,CAAA,4MAxF0BA,EAAQ,CAAA,EAAG,IAAM,MAAM,uDAD1BA,EAAQ,CAAA,EAAG,OAAS,MAAM,uFAA9DR,EA0CKC,EAAA2B,EAAAzB,CAAA,EAzCJC,EAcKwB,EAAAC,CAAA,4HAvBArB,EAAkB,CAAA,4DAGlBA,EAAG,EAAA,oEAOFA,EAAQ,EAAA,GAAIA,EAAG,EAAA,EAAC,0IADgBA,EAAQ,CAAA,EAAG,IAAM,MAAM,EAgBxDA,EAAW,CAAA,wFAjBmBA,EAAQ,CAAA,EAAG,OAAS,MAAM,EA4CzDA,EAAoB,EAAA,mHAepBA,OAAoBA,EAAQ,EAAA,mHA8B5BA,EAAQ,EAAA,sTA7nBNgP,GAAmB,aAMnBC,GAAgC,GAChCC,GAAmC,YAkgBhCC,GAAcC,EAAA,OACf,WAAYA,yDAvqBpBC,KAEW,GAAA,CAAA,KAAA5O,CAAA,EAAAC,EACA,CAAA,WAAA4O,CAAA,EAAA5O,EACA,CAAA,OAAA6O,CAAA,EAAA7O,EACA,CAAA,aAAAiH,CAAA,EAAAjH,GACA,MAAA8O,EAAQ,QAAA,EAAA9O,EACR,CAAA,OAAAjB,CAAA,EAAAiB,EACA,CAAA,WAAAmN,CAAA,EAAAnN,GACA,SAAA+O,EAAW,EAAA,EAAA/O,GACX,YAAAgP,EAAc,EAAA,EAAAhP,GACd,mBAAAiP,EAAqB,EAAA,EAAAjP,EACrB,CAAA,SAAAkP,CAAA,EAAAlP,EACA,CAAA,WAAAuL,CAAA,EAAAvL,EACA,CAAA,IAAA0K,CAAA,EAAA1K,EACA,CAAA,SAAA+F,CAAA,EAAA/F,EACA,CAAA,QAAAkN,CAAA,EAAAlN,EACA,CAAA,GAAAmP,CAAA,EAAAnP,GACA,YAAAoP,EAAc,EAAA,EAAApP,EACd,CAAA,MAAAqP,CAAA,EAAArP,EACA,CAAA,SAAAiG,CAAA,EAAAjG,GACA,WAAA8F,EAAa,EAAA,EAAA9F,GACb,cAAAoN,EAAoC,MAAA,EAAApN,GACpC,eAAAsP,EAA4C,MAAA,EAAAtP,GAC5C,IAAAuP,EAAiC,IAAA,EAAAvP,EAE3C,CAAA,OAAQwP,EACR,QAAAC,GACA,aAAAC,EACA,SAAAC,EACA,cAAAC,GACA,iBAAAC,EACA,eAAAC,GACA,eAAAC,GACA,kBAAAC,GACA,cAAAC,GACA,gBAAAC,EACG,EAAAC,GAAkBb,CAAc,yFAQrB,eAAAc,IAAA,CACR,MAAAH,GAAA,CACL,WAAArB,EACA,OAAAC,EACA,aAAA5H,EACA,KAAMlH,EAAO+F,EACb,IAAA4E,EACA,QACC,CAAA,YAAA0E,CAAA,IAKQ,GAAA,CAAA,cAAAiB,EAAA,EAAArQ,EACPsQ,GAAmBD,GAAc,IAAI,MAAM,IAAM,OAAStB,EAC1DwB,GACHF,GAAc,IAAI,MAAM,IAAM,gBAAkBtB,WACxCyB,GAAqBC,EAAA,MAC7BF,GAAuB,EAAA,OACvBD,GAAmBG,CAAA,MACf5I,GAAa,IAAA,gBAAgB,OAAO,SAAS,MAAM,EACnD4I,EACH5I,GAAO,IAAI,OAAQ,KAAK,EAExBA,GAAO,OAAO,MAAM,EAErB,QAAQ,aAAa,KAAM,GAAI,IAAMA,GAAO,SAAA,CAAA,EAEzC,IAAAR,GAAA,CAAA,GAEO,gBAAAqJ,GAAkB,EAAA,EAAA1Q,EACd,eAAA2Q,GAAc3F,EAAW7G,GAAA,CACjC,MAAAyM,EAAU3J,EAAa,KAAM4J,GAAQA,EAAI,IAAM1M,EAAQ,EAAG,QAE1D2M,EAAe9F,GAAM,KAAK9J,EAAYgC,OAE1C,GAAI0N,EAAQ1N,EAAC,EACb,KAAM,kBACN,MAAO,MAITwM,EAAaoB,CAAY,EAEnB,MAAAvI,GAAA,EAEA,MAAAwI,GAAA,CAAA,EAEN/F,GAAM,SAAS9J,EAAYgC,KAAA,CAElB,GAAA,OAAAhC,GAAU,UACjBA,IAAU,MACVA,EAAM,WAAa,SAEP,SAAA,CAAA8P,GAAYtB,CAAY,IAAK,OAAO,QAAQxO,CAAK,EACxD8P,KAAe,YAGlBD,GAAQ,KAAA,CACP,GAAIH,EAAQ1N,EAAC,EACb,KAAM8N,GACN,MAAOtB,SAKVqB,GAAQ,MACP,GAAIH,EAAQ1N,EAAC,EACb,KAAM,QACN,MAAAhC,CAAA,CAAA,IAIHwO,EAAaqB,EAAO,EAEd,MAAAxI,GAAA,EAGH,IAAA0I,GAA6D,IAAA,IAE7DC,GAAA,CAAA,EACK,SAAAC,GACRrC,EACAsC,GACAjN,EACAhD,EACAkQ,GAA0B,GAC1BZ,EAAU,GAAA,QAGT,MAAA3B,EACA,QAAAsC,GACA,SAAAjN,EACA,KAAAhD,EACA,GAAM,EAAAmQ,GACN,SAAAD,GACA,QAAAZ,GAIc,SAAAc,GACfzC,EACAsC,GACAjQ,EAAA,CAEAuF,EAAA,GAAAwK,GAAA,CAAYC,GAAYrC,EAAOsC,GAAA,GAAajQ,CAAI,KAAM+P,EAAQ,CAAA,MAG3DI,GAAY,GAEZE,GAAiB,GAIf,MAAAC,GAAoBC,EAAG,4BAA4B,EACnDC,GAAuBD,EAAG,6BAA6B,EACvDE,GAA2BF,EAAG,wBAAwB,EACtDG,GAA6BH,EAAG,2BAA2B,MAG7DI,GAAmB,GACnBC,GAA2B,GAC3BC,GAAwB,GACxBC,GAAA,CAAA,WAGKC,GACRC,EACAC,GAA4B,KAC5BC,EAAsB,KAAA,CAElB,IAAAC,EAAA,IAAA,GACK,SAAAC,IAAA,CACRD,IAEGE,EACHF,EAAStC,GAAkB,UAAWyC,GAAA,CAChCA,GACJlK,GAAO,EAAA,KAAA,IAAA,CACNmK,GAAiBP,EAAWC,GAAYC,CAAU,EAClDE,SAKHG,GAAiBP,EAAWC,GAAYC,CAAU,EAIrC,eAAAM,GACdC,EACAR,GACAC,EAAA,QAGCO,IAAiBR,IACjBC,GACCA,EAAyB,gBAAkB,GAGrCA,EAAW,MAEZ1C,EAASiD,CAAY,iBAGdF,GACdP,EACAC,GAA4B,KAC5BC,EAAsB,KAAA,CAElB,IAAAxB,EAAM5J,EAAa,KAAM4J,GAAQA,EAAI,KAAOsB,CAAS,EACrD,GAAAF,GAAe,OAAS,aAChBY,KAASZ,MACfpB,EAAI,OAAO,SAASgC,CAAK,EAAA,CAC5BtB,GAAgB,UAAWM,GAA4B,SAAS,gBAK7DiB,GAAiB/C,GAAe,kBAAkBoC,CAAS,EACjEzL,EAAA,GAAAwK,GAAWA,GAAS,OAAA,CAAA,CAAU,SAAA/M,KAAeA,IAAagO,CAAS,CAAA,GAC/DW,KAAmB,WAAaA,KAAmB,gBACtDjC,EAAI,gBAAkB,IAGnB,IAAAkC,EAAA,CACH,SAAUZ,EACV,KAAY,MAAA,QAAQ,IACnBtB,EAAI,OAAO,IAAKhF,GACf8G,GAAkC9G,EAAIuG,GAAYC,CAAU,CAAA,CAAA,EAG9D,WAAYxB,EAAI,oBAAsBwB,EAAa,KACnD,WAAAD,IAGGvB,EAAI,YACPA,EACE,YACAkC,EAAQ,KAAK,OAAA,MACN,QAAQ,IAAIlC,EAAI,QAAQ,IAAKhF,GAAO8D,EAAS9D,CAAE,MAGtD,KAAMC,GAAA,CACF+E,EAAI,YACPkC,EAAQ,KAAOjH,EACfkH,GAAmBnC,EAAKkC,CAAO,GAE/BpC,GAAc7E,EAAGqG,CAAS,IAGnBtB,EAAI,MAAM,QAAUA,EAAI,cAC5B,QAAQ,IACbA,EAAI,QAAQ,IAAW,MAAA1M,GAAA,OAChB8O,GAAahC,GAAW,IAAI9M,CAAQ,EAC1C,OAAA8O,IAAY,OAAA,EACLA,MAILpC,EAAI,YACPmC,GAAmBnC,EAAKkC,CAAO,EAIxB,SAAAC,GAAmBnC,EAAiBkC,GAAAA,CACxClC,EAAI,eAAiB,OACnBA,EAAI,iBACRqC,GAAgBH,GAASlC,EAAI,YAAc,QAAQ,EAC1CA,EAAI,eAAiB,WAC/BqC,GAAgBH,GAASlC,EAAI,YAAc,QAAQ,EACzCA,EAAI,eAAiB,gBAC1BA,EAAI,gBAGRA,EAAI,YAAckC,GAFlBG,GAAgBH,GAASlC,EAAI,YAAc,QAAQ,GAOvC,eAAAqC,GACdH,EACAI,GAAY,GAAA,CAER5C,IACH7J,EAAA,GAAAW,GAAA,CAAA,GAAgBA,GAAW,KAAK,MAAM,KAAK,UAAU0L,CAAO,CAAA,CAAA,CAAA,EAGzD,IAAAE,GAEA,GADJvI,EAAI,oBAAoBqI,CAAO,EAC3BI,GACE,GAAA,CAAAlC,GAAW,IAAIkB,CAAS,EAC5BtB,EAAI,OAAO,QAAShF,IAAO+D,GAAc/D,GAAI,SAAS,CAAA,UAEtDoF,GAAW,IAAIkB,CAAS,GACxBtB,EAAI,OAAO,KAAMhF,IAAOgE,EAAiBhE,EAAE,IAAM,SAAS,YAI1DoF,GAAW,IAAIkB,CAAS,GACxBtB,EAAI,OAAO,KAAMhF,IAAOgE,EAAiBhE,EAAE,IAAM,MAAM,EAAA,OAEjDnB,EAAI,mBAENA,EAAI,OAAO,KAAOA,EAAI,OAAO,UAAU,WAAWuG,GAAW,IAAIkB,CAAS,EAAE,SAAA,CAAA,IAC1EY,GAAAA,EAAS,aAAcrI,EAAI,2BAMlCuI,GAAavI,EAAI,OAChBqI,EAAQ,SACRA,EAAQ,KACRA,EAAQ,WACRA,EAAQ,UAAA,CAED,OAAAK,GAAA,CAER1M,EAAA,GAAAwK,GAAA,CACCC,GAAY,QAAS,OAAOiC,EAAC,EAAG,EAAU,OAAO,EAC9C,GAAAlC,EAAA,CAAA,EAEJnB,GAAe,OAAA,CACd,OAAQ,QACR,WACA,IAAK,EACL,MAAO,GACP,eAAgB,OAEjBsD,GAAWC,CAAe,SAI3BrC,GAAW,IAAIkB,EAAWc,EAAU,kBAEnB7B,MAAW6B,GACvB7B,GAAQ,OAAS,OACpBmC,GAAYnC,EAAO,EACTA,GAAQ,OAAS,SAC3BoC,GAAcpC,EAAO,EACXA,GAAQ,OAAS,SAC3BqC,GAAqBrC,EAAO,EAClBA,GAAQ,OAAS,OAC3BsC,GAAWtC,EAAO,WAIXmC,GAAYnC,GAAA,CACZ,KAAA,CAAA,KAAApG,GAAM,SAAA7G,EAAa,EAAAiN,GACvBP,EAAI,iBAAmBA,EAAI,cAC9BA,EAAI,gBAAkB,GACtBqC,GAAgBrC,EAAI,YAAaA,EAAI,YAAc,QAAQ,GAE5DA,EAAI,gBAAkB,GACtBF,GAAc3F,GAAM7G,EAAQ,EAC5BkP,GAAWC,CAAe,WAGlBE,GAAcpC,GAAA,OACd,KAAApG,EAAS,EAAAoG,GACb,IAAAuC,GAA+B3I,GAAK,WACpC4I,GAA4B5I,GAAK,OACjC6I,GAA8B7I,GAAK,aACnC8I,GAAY9I,GAAK,UAEjB+I,GAAA,CAAA,EACJ9M,EAAa,SAAS4J,GAAK3N,KAAA,CACtB2N,GAAI,cAAgBiD,IACvBC,GAAe,KAAK7Q,EAAC,IAGvB6Q,GAAe,UAAU,QAAS7Q,IAAA,CACjC+D,EAAa,OAAO/D,GAAG,CAAC,IAEzB2Q,GAAc,QAAShD,IAAAA,CACtB5J,EAAa,KAAK4J,EAAG,IAGtBX,GAAA,CACC,WAAYyD,GACZ,OAAQC,GACR,KAAA7T,EACA,aAAAkH,EACA,UAAA6M,cAIOJ,GAAWM,GAAA,CACX,KAAA,CAAA,MAAAlF,GAAO,IAAAmF,GAAK,SAAA9P,GAAU,MAAA+P,GAAO,SAAA7C,GAAU,QAAAZ,EAAY,EAAAuD,GAC3DtN,EAAA,GAAAwK,GAAA,CACCC,GAAYrC,GAAOmF,GAAK9P,GAAU+P,GAAO7C,GAAUZ,EAAO,EACvD,GAAAS,KAII,SAAAiD,GACRC,GACAvI,GACAgF,GAAAA,CAGCuD,GAAO,eAAiB,kBACxBvD,GAAI,aAAe,UAEnBjB,GAAc/D,GAAI,MAAM,WAKjB4H,GAAqBrC,GAAA,CACrB,KAAA,CAAA,SAAAjN,GAAA,GAAaiQ,EAAW,EAAAhD,GAiE5B,GAhEAgD,GAAO,QAAU,aAAeA,GAAO,YAC1CvD,EAAI,OAAO,QAAShF,IAAA,CACnBiE,GAAejE,GAAIuI,GAAO,UAAU,IAGtCvD,EAAI,OAAO,QAAShF,IAAA,CACnBsI,GAAmB/C,GAASvF,GAAIgF,CAAG,IAGpCd,GAAe,OAAA,CACX,GAAAqE,GACH,WAAYA,GAAO,WACnB,OAAQA,GAAO,MACf,SAAUA,GAAO,cACjB,SAAAjQ,KAEDkP,GAAWC,CAAe,EAExB,CAAAvB,IACDhM,IAAa,MACbqO,GAAO,WAAA,QACPA,GAAO,UAAY,GACnBA,GAAO,MAAA,QACPA,GAAO,IAAM7F,KAEbwD,GAA2B,GAC3BrL,EAAA,GAAAwK,GAAA,CACCC,GAAY,UAAWM,GAAmBtN,GAAU,SAAS,EAC1D,GAAA+M,MAIH,CAAAc,IACDF,IACAsC,GAAO,MAAQ,QACfA,GAAO,IAAM5F,KAEbwD,GAAwB,GACxBtL,EAAA,GAAAwK,GAAA,CACCC,GAAY,UAAWQ,GAAsBxN,GAAU,SAAS,EAC7D,GAAA+M,OAIDkD,GAAO,QAAU,YAAcA,GAAO,QAAU,eACnDA,GAAO,mBAAmB,QAASvI,IAAA,CAClC5E,EACE,OAAQ4J,IAAQA,GAAI,QAAQ,KAAA,CAAA,CAAOlF,GAAK0I,EAAC,IAAM1I,KAAQE,EAAE,CAAA,EACzD,QAASgF,IAAAA,CACTqB,GAA2BrB,GAAI,GAAIkC,EAAQ,UAAU,MAIrDqB,GAAO,QAAU,aACpBnN,EAAa,QAAe4J,MAAAA,IAAAA,CACvBA,GAAI,gBAAkB1M,IACzB+N,GAA2BrB,GAAI,GAAIkC,EAAQ,UAAU,IAGvDlC,EAAI,OAAO,QAAShF,IAAA,CACnB+D,GAAc/D,GAAI,QAAQ,IAE3BoF,GAAW,OAAOkB,CAAS,GAExBiC,GAAO,QAAUtC,IAAoBN,GACxC,OAAO,gBACN9K,EAAA,GAAAwK,GAAA,CACCC,GAAY,QAASS,GAA0BzN,GAAU,OAAO,EAC7D,GAAA+M,MAEF,GACHgB,GAA2BrB,EAAI,GAAIkC,EAAQ,WAAYV,CAAU,EACjEb,GAAiB,WACP4C,GAAO,QAAU,QAAA,IACvBA,GAAO,QAAA,CACJ,MAAAE,GAAWF,GAAO,QAAQ,QAC/B9F,GAAA,CACC+F,GAAGE,KAAMA,EAAA,EAELC,GAASJ,GAAO,OAAS,QAC/B1N,EAAA,GAAAwK,GAAA,CACCC,GACCqD,GACAF,GACAnQ,GACA,QACAiQ,GAAO,SACPA,GAAO,OAAA,EAEL,GAAAlD,KAGLjK,EAAa,IAAW4J,MAAAA,IAAAA,CAEtBA,GAAI,gBAAkB1M,KACrB0M,GAAI,yBAELqB,GAA2BrB,GAAI,GAAIkC,EAAQ,UAAU,OASlD,SAAA0B,GAAc3F,EAA2B4F,GAAA,IAC7C3O,IAAa,YAGX,MAAA4O,EAAA,IAAqB,qCACO5O,CAAQ,kBAAA,EAEtC+I,IAAU,QAAaA,EAAM,OAAS,GACzC6F,EAAe,aAAa,IAAI,QAAS7F,CAAK,EAE/C6F,EAAe,aAAa,IAAI,cAAeD,EAAW,EAC1D,OAAO,KAAKC,EAAe,SAAA,EAAY,QAAQ,WAGvCC,GAAmBxB,EAAA,CACrB,MAAAzH,GAAMyH,EAAE,OACd1M,EAAA,GAAAwK,GAAWA,GAAS,OAAQ2D,GAAMA,EAAE,KAAOlJ,EAAG,CAAA,QAGzCmJ,GAAmBC,GAAA,CAAA,EACrBA,OAAY,IAAIA,EAAM,SAAS,IAAI,EAAE,SAAW,SAAS,QAE9C,eAAAC,IAAA,CACV7F,GAKG,MAJF,IAAyB8F,0BACL9F,CAAE;AAAA,4DAGpB,EAGD,MAAA5G,GAAA,UAEFmB,EAAI3K,EAAO,qBAAqB,GAAG,EAE9BmE,GAAI,EAAGA,GAAIwG,EAAE,OAAQxG,KAAA,CACvB,MAAA8I,EAAUtC,EAAExG,EAAC,EAAE,aAAa,QAAQ,EACpCgS,EAAQxL,EAAExG,EAAC,EAAE,aAAa,MAAM,EAGlC4R,GAAgBI,CAAK,GAAKlJ,IAAY,UACzCtC,EAAExG,EAAC,EAAE,aAAa,SAAU,QAAQ,EAItC+D,EAAa,QAAS4J,GAAA,CACjBA,EAAI,QAAQ,KAAMA,GAAQA,EAAI,CAAC,IAAM,MAAM,GAC9CqB,GAA2BrB,EAAI,EAAE,OAI9B9R,GAAU2R,MAEf3R,EAAO,iBAAiB,cAAgBqU,GAAA,KAClC3E,GAAc2E,CAAC,EAAa,MAAA,IAAA,MAAM,oBAAoB,QACnD,GAAAvH,EAAI,KAAAsJ,GAAM,MAAAjU,GAAUkS,EAAE,OAC9B1D,EAAA,CAAA,CAAgB,GAAA7D,EAAI,KAAAsJ,GAAM,MAAAjU,CAAA,CAAA,CAAA,EACtBiU,KAAS,eAAiBjU,IAAU,IACvC+Q,GAAe,KAAKpG,CAAE,EAEnBsJ,KAAS,eAAiBjU,IAAU,KACvC+Q,GAAiBA,GAAe,OAAQtQ,IAASA,KAASkK,CAAE,KAG9D9M,EAAO,iBAAiB,SAAWqU,GAAA,KAC7B3E,GAAc2E,CAAC,EAAa,MAAA,IAAA,MAAM,oBAAoB,QAEnD,GAAAvH,EAAI,MAAA6C,GAAO,KAAA1D,GAASoI,EAAE,UAE1B1E,KAAU,QAAA,CACL,KAAA,CAAA,MAAAI,GAAO,YAAA4F,EAAgB,EAAA1J,EAC/ByJ,GAAc3F,GAAO4F,EAAW,OACtBhG,KAAU,QACpBhI,EAAA,GAAAwK,GAAA,CAAYC,GAAY,QAASnG,EAAA,GAAU0D,EAAK,KAAMwC,EAAQ,CAAA,EACpDxC,KAAU,UACpBhI,EAAA,GAAAwK,GAAA,CAAYC,GAAY,UAAWnG,EAAA,GAAU0D,EAAK,KAAMwC,EAAQ,CAAA,EACtDxC,IAAS,eACnB0G,GAAcvJ,EAAI,WAAYb,CAAI,EACxB0D,IAAS,eACN2G,EAASxJ,CAAE,IAAIb,CAAI,GAC1B,QAASsK,IAAA,CACV,GAAArE,GAAW,IAAIqE,EAAM,EAAA,OAElBC,EAAS,GAAA7K,EAAI,OAAO,KAAOA,EAAI,OAAO,UAAU,WAAWuG,GAAW,IAAIqE,EAAM,EAAE,SAAA,CAAA,GACxF5K,EAAI,aAAa6K,CAAG,SAAA,CAAA,CAAA,EACpB7K,EAAI,SAAS6K,CAAG,KAILF,EAASxJ,CAAE,IAAI6C,EAAK,GAE3B,QAAS4G,IAAA,CACd,sBAAA,IAAA,CACCpD,GAA2BoD,GAAQzJ,EAAIb,CAAI,aAM/C0F,GAAkB,EAAA,GAKV,SAAA0E,GACRvJ,EACAuI,GACApJ,EAAA,CAEAA,EAAK,OAASoJ,GACd1E,EAEE,CAAA,CAAA,GAAA7D,EACA,KAAM,iBACN,MAAOb,CAAA,CAAA,CAAA,WAKDqI,GAAWmC,EAAA,CACf,IAAAzE,GAAA,CAAA,EAKJ,OAAO,QAAQyE,CAAQ,EAAE,QAAA,CAAA,CAAU3J,GAAIkE,CAAc,IAAA,CAChD,IAAAnK,GAAaqB,EAAa,KAC5B4J,IAAQA,GAAI,IAAMd,EAAe,QAAA,EAE/BnK,KAAe,SAGnBmK,EAAe,iBAAmBnK,GAAW,iBAC7CmK,EAAe,cAAgBnK,GAAW,cAC1CmL,GAAQ,KAAA,CACP,GAAI,SAASlF,EAAE,EACf,KAAM,iBACN,MAAOkE,OAIH,MAAA0F,EAAmB1F,GAAe,uBAClC2F,EAAqB,MAAM,KAAKD,CAAgB,EAAE,IAAA,CAAA,CACrD5J,GAAI8J,CAAc,MAElB,GAAA9J,GACA,KAAM,UACN,MAAO8J,IAAmB,aAK7BjG,EAAA,CAAA,GAAiBqB,MAAY2E,CAAkB,CAAA,EAOhDxN,GAAA,IAAA,CACC,SAAS,iBAAiB,mBAAA,UAAA,CACrB,SAAS,kBAAoB,WAChCsJ,GAAiB,MAInBM,GACC,iEAAiE,KAChE,UAAU,SAAA,kBAoCRtB,IAAsBF,EAAgB,WA6BzCE,GAAqB,EAAI,EACzB9J,EAAA,GAAA6J,GAAuB,EAAK,WAe3BC,GAAqB,EAAK,MAMf9B,GAAK,CACf8B,GAAqB,EAAK,OAC1BnJ,GAAS,CAAA,CAAA,EACTX,EAAA,GAAA6J,GAAuB7B,EAAM,QAAQ,oBAAoB,w9BAtuBQ0B,GAAA,4BAGpEf,EAAU,CAAA,CAAAuG,CAAA,qBAukBRvC,GAAWC,CAAe"}