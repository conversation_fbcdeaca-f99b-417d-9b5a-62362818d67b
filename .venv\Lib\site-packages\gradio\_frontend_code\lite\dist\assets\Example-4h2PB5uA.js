import{a as d,i as o,s as m,f as c,p as v,k as y,q as _,r as n,l as b,v as q,n as x,w as r,o as k}from"../lite.js";function w(i){let e,a;return{c(){e=v("div"),a=y(i[2]),_(e,"class","svelte-1ayixqk"),n(e,"table",i[0]==="table"),n(e,"gallery",i[0]==="gallery"),n(e,"selected",i[1])},m(t,l){b(t,e,l),q(e,a)},p(t,[l]){l&4&&x(a,t[2]),l&1&&n(e,"table",t[0]==="table"),l&1&&n(e,"gallery",t[0]==="gallery"),l&2&&n(e,"selected",t[1])},i:r,o:r,d(t){t&&k(e)}}}function C(i,e,a){let{value:t}=e,{type:l}=e,{selected:h=!1}=e,{choices:f}=e,u;if(t===null)u="";else{let s=f.find(g=>g[1]===t);u=s?s[0]:""}return i.$$set=s=>{"value"in s&&a(3,t=s.value),"type"in s&&a(0,l=s.type),"selected"in s&&a(1,h=s.selected),"choices"in s&&a(4,f=s.choices)},[l,h,u,t,f]}class S extends d{constructor(e){super(),o(this,e,C,w,m,{value:3,type:0,selected:1,choices:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),c()}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),c()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),c()}get choices(){return this.$$.ctx[4]}set choices(e){this.$$set({choices:e}),c()}}export{S as default};
//# sourceMappingURL=Example-4h2PB5uA.js.map
