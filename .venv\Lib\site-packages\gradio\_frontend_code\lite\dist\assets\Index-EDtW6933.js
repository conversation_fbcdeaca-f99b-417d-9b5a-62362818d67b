import{a as Ye,i as je,s as Ae,f as q,B as Fe,c as I,m as Q,t as b,b as v,d as S,p as N,Q as ne,x as A,k as W,E as J,q as d,l as z,v as H,n as x,y as C,z as R,o as B,ao as P,aq as G,F,P as ve,r as se,w as Ie,as as O,$ as ie,R as oe,a0 as T,a4 as U,Y as V}from"../lite.js";/* empty css                                              */import Qe from"./Example-BiOJtN-L.js";/* empty css                                              */function re(s,e,t){const n=s.slice();return n[37]=e[t],n}function ae(s,e,t){const n=s.slice();return n[40]=e[t],n[42]=t,n}function fe(s,e,t){const n=s.slice();n[0]=e[t].value,n[44]=e[t].component,n[47]=t;const l=n[1][n[47]];return n[45]=l,n}function ce(s,e,t){const n=s.slice();return n[48]=e[t],n}function _e(s,e,t){const n=s.slice();return n[40]=e[t],n[42]=t,n}function Se(s){let e,t,n,l,o,a,i,r=P(s[5]),f=[];for(let _=0;_<r.length;_+=1)f[_]=ue(ce(s,r,_));let h=P(s[20]),u=[];for(let _=0;_<h.length;_+=1)u[_]=pe(ae(s,h,_));const k=_=>v(u[_],1,1,()=>{u[_]=null});return{c(){e=N("div"),t=N("table"),n=N("thead"),l=N("tr");for(let _=0;_<f.length;_+=1)f[_].c();o=A(),a=N("tbody");for(let _=0;_<u.length;_+=1)u[_].c();d(l,"class","tr-head svelte-p5q82i"),d(t,"tabindex","0"),d(t,"role","grid"),d(t,"class","svelte-p5q82i"),d(e,"class","table-wrap svelte-p5q82i")},m(_,g){z(_,e,g),H(e,t),H(t,n),H(n,l);for(let c=0;c<f.length;c+=1)f[c]&&f[c].m(l,null);H(t,o),H(t,a);for(let c=0;c<u.length;c+=1)u[c]&&u[c].m(a,null);i=!0},p(_,g){if(g[0]&32){r=P(_[5]);let c;for(c=0;c<r.length;c+=1){const p=ce(_,r,c);f[c]?f[c].p(p,g):(f[c]=ue(p),f[c].c(),f[c].m(l,null))}for(;c<f.length;c+=1)f[c].d(1);f.length=r.length}if(g[0]&30985231){h=P(_[20]);let c;for(c=0;c<h.length;c+=1){const p=ae(_,h,c);u[c]?(u[c].p(p,g),b(u[c],1)):(u[c]=pe(p),u[c].c(),b(u[c],1),u[c].m(a,null))}for(C(),c=h.length;c<u.length;c+=1)k(c);R()}},i(_){if(!i){for(let g=0;g<h.length;g+=1)b(u[g]);i=!0}},o(_){u=u.filter(Boolean);for(let g=0;g<u.length;g+=1)v(u[g]);i=!1},d(_){_&&B(e),G(f,_),G(u,_)}}}function De(s){let e,t,n=P(s[17]),l=[];for(let a=0;a<n.length;a+=1)l[a]=de(_e(s,n,a));const o=a=>v(l[a],1,1,()=>{l[a]=null});return{c(){e=N("div");for(let a=0;a<l.length;a+=1)l[a].c();d(e,"class","gallery svelte-p5q82i")},m(a,i){z(a,e,i);for(let r=0;r<l.length;r+=1)l[r]&&l[r].m(e,null);t=!0},p(a,i){if(i[0]&31116367){n=P(a[17]);let r;for(r=0;r<n.length;r+=1){const f=_e(a,n,r);l[r]?(l[r].p(f,i),b(l[r],1)):(l[r]=de(f),l[r].c(),b(l[r],1),l[r].m(e,null))}for(C(),r=n.length;r<l.length;r+=1)o(r);R()}},i(a){if(!t){for(let i=0;i<n.length;i+=1)b(l[i]);t=!0}},o(a){l=l.filter(Boolean);for(let i=0;i<l.length;i+=1)v(l[i]);t=!1},d(a){a&&B(e),G(l,a)}}}function ue(s){let e,t=s[48]+"",n,l;return{c(){e=N("th"),n=W(t),l=A(),d(e,"class","svelte-p5q82i")},m(o,a){z(o,e,a),H(e,n),H(e,l)},p(o,a){a[0]&32&&t!==(t=o[48]+"")&&x(n,t)},d(o){o&&B(e)}}}function me(s){let e,t,n,l;const o=[s[2][s[47]],{value:s[0]},{samples_dir:s[22]},{type:"table"},{selected:s[19]===s[42]},{index:s[42]},{root:s[10]}];var a=s[44];function i(r,f){let h={};for(let u=0;u<o.length;u+=1)h=V(h,o[u]);return f!==void 0&&f[0]&5768196&&(h=V(h,T(o,[f[0]&4&&U(r[2][r[47]]),f[0]&1048576&&{value:r[0]},f[0]&4194304&&{samples_dir:r[22]},o[3],f[0]&524288&&{selected:r[19]===r[42]},o[5],f[0]&1024&&{root:r[10]}]))),{props:h}}return a&&(t=O(a,i(s))),{c(){e=N("td"),t&&I(t.$$.fragment),ie(e,"max-width",s[45]==="textbox"?"35ch":"auto"),d(e,"class",n=oe(s[45])+" svelte-p5q82i")},m(r,f){z(r,e,f),t&&Q(t,e,null),l=!0},p(r,f){if(f[0]&1048576&&a!==(a=r[44])){if(t){C();const h=t;v(h.$$.fragment,1,0,()=>{S(h,1)}),R()}a?(t=O(a,i(r,f)),I(t.$$.fragment),b(t.$$.fragment,1),Q(t,e,null)):t=null}else if(a){const h=f[0]&5768196?T(o,[f[0]&4&&U(r[2][r[47]]),f[0]&1048576&&{value:r[0]},f[0]&4194304&&{samples_dir:r[22]},o[3],f[0]&524288&&{selected:r[19]===r[42]},o[5],f[0]&1024&&{root:r[10]}]):{};t.$set(h)}(!l||f[0]&2)&&ie(e,"max-width",r[45]==="textbox"?"35ch":"auto"),(!l||f[0]&2&&n!==(n=oe(r[45])+" svelte-p5q82i"))&&d(e,"class",n)},i(r){l||(t&&b(t.$$.fragment,r),l=!0)},o(r){t&&v(t.$$.fragment,r),l=!1},d(r){r&&B(e),t&&S(t)}}}function he(s){let e=s[45]!==void 0&&s[3].get(s[45])!==void 0,t,n,l=e&&me(s);return{c(){l&&l.c(),t=J()},m(o,a){l&&l.m(o,a),z(o,t,a),n=!0},p(o,a){a[0]&10&&(e=o[45]!==void 0&&o[3].get(o[45])!==void 0),e?l?(l.p(o,a),a[0]&10&&b(l,1)):(l=me(o),l.c(),b(l,1),l.m(t.parentNode,t)):l&&(C(),v(l,1,1,()=>{l=null}),R())},i(o){n||(b(l),n=!0)},o(o){v(l),n=!1},d(o){o&&B(t),l&&l.d(o)}}}function pe(s){let e,t,n,l,o,a=P(s[40]),i=[];for(let u=0;u<a.length;u+=1)i[u]=he(fe(s,a,u));const r=u=>v(i[u],1,1,()=>{i[u]=null});function f(){return s[32](s[42])}function h(){return s[33](s[42])}return{c(){e=N("tr");for(let u=0;u<i.length;u+=1)i[u].c();t=A(),d(e,"class","tr-body svelte-p5q82i")},m(u,k){z(u,e,k);for(let _=0;_<i.length;_+=1)i[_]&&i[_].m(e,null);H(e,t),n=!0,l||(o=[F(e,"click",f),F(e,"mouseenter",h),F(e,"mouseleave",s[34])],l=!0)},p(u,k){if(s=u,k[0]&5768206){a=P(s[40]);let _;for(_=0;_<a.length;_+=1){const g=fe(s,a,_);i[_]?(i[_].p(g,k),b(i[_],1)):(i[_]=he(g),i[_].c(),b(i[_],1),i[_].m(e,t))}for(C(),_=a.length;_<i.length;_+=1)r(_);R()}},i(u){if(!n){for(let k=0;k<a.length;k+=1)b(i[k]);n=!0}},o(u){i=i.filter(Boolean);for(let k=0;k<i.length;k+=1)v(i[k]);n=!1},d(u){u&&B(e),G(i,u),l=!1,ve(o)}}}function ge(s){let e,t,n,l,o,a,i,r;const f=[Je,Ge],h=[];function u(g,c){return c[0]&1048586&&(t=null),g[6]?0:(t==null&&(t=!!(g[20].length&&g[3].get(g[1][0]))),t?1:-1)}~(n=u(s,[-1,-1]))&&(l=h[n]=f[n](s));function k(){return s[29](s[42],s[40])}function _(){return s[30](s[42])}return{c(){e=N("button"),l&&l.c(),o=A(),d(e,"class","gallery-item svelte-p5q82i")},m(g,c){z(g,e,c),~n&&h[n].m(e,null),H(e,o),a=!0,i||(r=[F(e,"click",k),F(e,"mouseenter",_),F(e,"mouseleave",s[31])],i=!0)},p(g,c){s=g;let p=n;n=u(s,c),n===p?~n&&h[n].p(s,c):(l&&(C(),v(h[p],1,1,()=>{h[p]=null}),R()),~n?(l=h[n],l?l.p(s,c):(l=h[n]=f[n](s),l.c()),b(l,1),l.m(e,o)):l=null)},i(g){a||(b(l),a=!0)},o(g){v(l),a=!1},d(g){g&&B(e),~n&&h[n].d(),i=!1,ve(r)}}}function Ge(s){let e,t,n;const l=[s[2][0],{value:s[40][0]},{samples_dir:s[22]},{type:"gallery"},{selected:s[19]===s[42]},{index:s[42]},{root:s[10]}];var o=s[20][0][0].component;function a(i,r){let f={};for(let h=0;h<l.length;h+=1)f=V(f,l[h]);return r!==void 0&&r[0]&4850692&&(f=V(f,T(l,[r[0]&4&&U(i[2][0]),r[0]&131072&&{value:i[40][0]},r[0]&4194304&&{samples_dir:i[22]},l[3],r[0]&524288&&{selected:i[19]===i[42]},l[5],r[0]&1024&&{root:i[10]}]))),{props:f}}return o&&(e=O(o,a(s))),{c(){e&&I(e.$$.fragment),t=J()},m(i,r){e&&Q(e,i,r),z(i,t,r),n=!0},p(i,r){if(r[0]&1048576&&o!==(o=i[20][0][0].component)){if(e){C();const f=e;v(f.$$.fragment,1,0,()=>{S(f,1)}),R()}o?(e=O(o,a(i,r)),I(e.$$.fragment),b(e.$$.fragment,1),Q(e,t.parentNode,t)):e=null}else if(o){const f=r[0]&4850692?T(l,[r[0]&4&&U(i[2][0]),r[0]&131072&&{value:i[40][0]},r[0]&4194304&&{samples_dir:i[22]},l[3],r[0]&524288&&{selected:i[19]===i[42]},l[5],r[0]&1024&&{root:i[10]}]):{};e.$set(f)}},i(i){n||(e&&b(e.$$.fragment,i),n=!0)},o(i){e&&v(e.$$.fragment,i),n=!1},d(i){i&&B(t),e&&S(e,i)}}}function Je(s){let e,t;return e=new Qe({props:{value:s[40][0],selected:s[19]===s[42],type:"gallery"}}),{c(){I(e.$$.fragment)},m(n,l){Q(e,n,l),t=!0},p(n,l){const o={};l[0]&131072&&(o.value=n[40][0]),l[0]&524288&&(o.selected=n[19]===n[42]),e.$set(o)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function de(s){let e,t,n=s[40][0]&&ge(s);return{c(){n&&n.c(),e=J()},m(l,o){n&&n.m(l,o),z(l,e,o),t=!0},p(l,o){l[40][0]?n?(n.p(l,o),o[0]&131072&&b(n,1)):(n=ge(l),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(C(),v(n,1,1,()=>{n=null}),R())},i(l){t||(b(n),t=!0)},o(l){v(n),t=!1},d(l){l&&B(e),n&&n.d(l)}}}function be(s){let e,t,n=P(s[18]),l=[];for(let o=0;o<n.length;o+=1)l[o]=ke(re(s,n,o));return{c(){e=N("div"),t=W(`Pages:
			`);for(let o=0;o<l.length;o+=1)l[o].c();d(e,"class","paginate svelte-p5q82i")},m(o,a){z(o,e,a),H(e,t);for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(e,null)},p(o,a){if(a[0]&294912){n=P(o[18]);let i;for(i=0;i<n.length;i+=1){const r=re(o,n,i);l[i]?l[i].p(r,a):(l[i]=ke(r),l[i].c(),l[i].m(e,null))}for(;i<l.length;i+=1)l[i].d(1);l.length=n.length}},d(o){o&&B(e),G(l,o)}}}function Ke(s){let e,t=s[37]+1+"",n,l,o,a;function i(){return s[35](s[37])}return{c(){e=N("button"),n=W(t),l=A(),d(e,"class","svelte-p5q82i"),se(e,"current-page",s[15]===s[37])},m(r,f){z(r,e,f),H(e,n),H(e,l),o||(a=F(e,"click",i),o=!0)},p(r,f){s=r,f[0]&262144&&t!==(t=s[37]+1+"")&&x(n,t),f[0]&294912&&se(e,"current-page",s[15]===s[37])},d(r){r&&B(e),o=!1,a()}}}function Le(s){let e;return{c(){e=N("div"),e.textContent="..."},m(t,n){z(t,e,n)},p:Ie,d(t){t&&B(e)}}}function ke(s){let e;function t(o,a){return o[37]===-1?Le:Ke}let n=t(s),l=n(s);return{c(){l.c(),e=J()},m(o,a){l.m(o,a),z(o,e,a)},p(o,a){n===(n=t(o))&&l?l.p(o,a):(l.d(1),l=n(o),l&&(l.c(),l.m(e.parentNode,e)))},d(o){o&&B(e),l.d(o)}}}function Oe(s){let e,t,n,l,o,a,i,r,f,h,u;const k=[De,Se],_=[];function g(p,w){return p[21]?0:1}i=g(s),r=_[i]=k[i](s);let c=s[16]&&be(s);return{c(){e=N("div"),t=ne("svg"),n=ne("path"),l=A(),o=W(s[4]),a=A(),r.c(),f=A(),c&&c.c(),h=J(),d(n,"fill","currentColor"),d(n,"d","M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z"),d(t,"xmlns","http://www.w3.org/2000/svg"),d(t,"xmlns:xlink","http://www.w3.org/1999/xlink"),d(t,"aria-hidden","true"),d(t,"role","img"),d(t,"width","1em"),d(t,"height","1em"),d(t,"preserveAspectRatio","xMidYMid meet"),d(t,"viewBox","0 0 32 32"),d(t,"class","svelte-p5q82i"),d(e,"class","label svelte-p5q82i")},m(p,w){z(p,e,w),H(e,t),H(t,n),H(e,l),H(e,o),z(p,a,w),_[i].m(p,w),z(p,f,w),c&&c.m(p,w),z(p,h,w),u=!0},p(p,w){(!u||w[0]&16)&&x(o,p[4]);let E=i;i=g(p),i===E?_[i].p(p,w):(C(),v(_[E],1,1,()=>{_[E]=null}),R(),r=_[i],r?r.p(p,w):(r=_[i]=k[i](p),r.c()),b(r,1),r.m(f.parentNode,f)),p[16]?c?c.p(p,w):(c=be(p),c.c(),c.m(h.parentNode,h)):c&&(c.d(1),c=null)},i(p){u||(b(r),u=!0)},o(p){v(r),u=!1},d(p){p&&(B(e),B(a),B(f),B(h)),_[i].d(p),c&&c.d(p)}}}function Te(s){let e,t;return e=new Fe({props:{visible:s[9],padding:!1,elem_id:s[7],elem_classes:s[8],scale:s[12],min_width:s[13],allow_overflow:!1,container:!1,$$slots:{default:[Oe]},$$scope:{ctx:s}}}),{c(){I(e.$$.fragment)},m(n,l){Q(e,n,l),t=!0},p(n,l){const o={};l[0]&512&&(o.visible=n[9]),l[0]&128&&(o.elem_id=n[7]),l[0]&256&&(o.elem_classes=n[8]),l[0]&4096&&(o.scale=n[12]),l[0]&8192&&(o.min_width=n[13]),l[0]&4181119|l[1]&1048576&&(o.$$scope={dirty:l,ctx:n}),e.$set(o)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Ue(s,e,t){let n,{components:l}=e,{component_props:o}=e,{component_map:a}=e,{label:i="Examples"}=e,{headers:r}=e,{samples:f=null}=e,h=null,{sample_labels:u=null}=e,{elem_id:k=""}=e,{elem_classes:_=[]}=e,{visible:g=!0}=e,{value:c=null}=e,{root:p}=e,{proxy_url:w}=e,{samples_per_page:E=10}=e,{scale:ee=null}=e,{min_width:le=void 0}=e,{gradio:D}=e,we=w?`/proxy=${w}file=`:`${p}/file=`,Y=0,X=f?f.length>E:!1,K,L,j=[],Z=-1;function y(m){t(19,Z=m)}function $(){t(19,Z=-1)}let te=[];async function qe(m){t(20,te=await Promise.all(m&&m.map(async M=>await Promise.all(M.map(async(Ce,Re)=>({value:Ce,component:(await a.get(l[Re]))?.default}))))))}const ze=(m,M)=>{t(0,c=m+Y*E),D.dispatch("click",c),D.dispatch("select",{index:c,value:M})},Be=m=>y(m),He=()=>$(),Ne=m=>{t(0,c=m+Y*E),D.dispatch("click",c)},Ee=m=>y(m),Me=()=>$(),Pe=m=>t(15,Y=m);return s.$$set=m=>{"components"in m&&t(1,l=m.components),"component_props"in m&&t(2,o=m.component_props),"component_map"in m&&t(3,a=m.component_map),"label"in m&&t(4,i=m.label),"headers"in m&&t(5,r=m.headers),"samples"in m&&t(25,f=m.samples),"sample_labels"in m&&t(6,u=m.sample_labels),"elem_id"in m&&t(7,k=m.elem_id),"elem_classes"in m&&t(8,_=m.elem_classes),"visible"in m&&t(9,g=m.visible),"value"in m&&t(0,c=m.value),"root"in m&&t(10,p=m.root),"proxy_url"in m&&t(26,w=m.proxy_url),"samples_per_page"in m&&t(11,E=m.samples_per_page),"scale"in m&&t(12,ee=m.scale),"min_width"in m&&t(13,le=m.min_width),"gradio"in m&&t(14,D=m.gradio)},s.$$.update=()=>{s.$$.dirty[0]&66&&t(21,n=l.length<2||u!==null),s.$$.dirty[0]&436570176&&(u?t(25,f=u.map(m=>[m])):f||t(25,f=[]),f!==h&&(t(15,Y=0),t(27,h=f)),t(16,X=f.length>E),X?(t(18,j=[]),t(17,K=f.slice(Y*E,(Y+1)*E)),t(28,L=Math.ceil(f.length/E)),[0,Y,L-1].forEach(m=>{for(let M=m-2;M<=m+2;M++)M>=0&&M<L&&!j.includes(M)&&(j.length>0&&M-j[j.length-1]>1&&j.push(-1),j.push(M))})):t(17,K=f.slice())),s.$$.dirty[0]&131080&&qe(K)},[c,l,o,a,i,r,u,k,_,g,p,E,ee,le,D,Y,X,K,j,Z,te,n,we,y,$,f,w,h,L,ze,Be,He,Ne,Ee,Me,Pe]}class ye extends Ye{constructor(e){super(),je(this,e,Ue,Te,Ae,{components:1,component_props:2,component_map:3,label:4,headers:5,samples:25,sample_labels:6,elem_id:7,elem_classes:8,visible:9,value:0,root:10,proxy_url:26,samples_per_page:11,scale:12,min_width:13,gradio:14},null,[-1,-1])}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),q()}get component_props(){return this.$$.ctx[2]}set component_props(e){this.$$set({component_props:e}),q()}get component_map(){return this.$$.ctx[3]}set component_map(e){this.$$set({component_map:e}),q()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),q()}get headers(){return this.$$.ctx[5]}set headers(e){this.$$set({headers:e}),q()}get samples(){return this.$$.ctx[25]}set samples(e){this.$$set({samples:e}),q()}get sample_labels(){return this.$$.ctx[6]}set sample_labels(e){this.$$set({sample_labels:e}),q()}get elem_id(){return this.$$.ctx[7]}set elem_id(e){this.$$set({elem_id:e}),q()}get elem_classes(){return this.$$.ctx[8]}set elem_classes(e){this.$$set({elem_classes:e}),q()}get visible(){return this.$$.ctx[9]}set visible(e){this.$$set({visible:e}),q()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),q()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),q()}get proxy_url(){return this.$$.ctx[26]}set proxy_url(e){this.$$set({proxy_url:e}),q()}get samples_per_page(){return this.$$.ctx[11]}set samples_per_page(e){this.$$set({samples_per_page:e}),q()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),q()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),q()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),q()}}export{ye as default};
//# sourceMappingURL=Index-EDtW6933.js.map
