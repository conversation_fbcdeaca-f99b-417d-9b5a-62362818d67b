import{a as ae,i as ue,s as re,Q as De,q as g,l as M,v as D,w as ee,o as P,f as y,p as S,a3 as ye,a8 as Fe,ao as Se,x as X,$ as le,F as j,b4 as je,b5 as ze,P as Te,ap as me,c as C,r as J,m as A,t as E,y as te,b as T,z as ne,d as I,I as Z,a5 as $,a6 as x,E as he,k as ce,n as Ee,W as Ve,a1 as Re,aw as Oe,A as Ne,av as We,H as G,D as Ge,G as Qe}from"../lite.js";import{B as Je}from"./BlockLabel-B0HN-MOU.js";import{E as Ke}from"./Empty-C76eC2zW.js";import{S as Ye}from"./ShareButton-Bn_rnIUK.js";import{D as Ze}from"./Download-CgLP-Xl6.js";import{V as Xe}from"./Video-BIoWYjY-.js";import{I as $e}from"./IconButtonWrapper-Ck50MZwX.js";import{f as oe,u as xe}from"./utils-BsGrhMNe.js";import{D as et}from"./DownloadLink-B8hI46-W.js";import{T as tt,P as nt}from"./Trim-CMIh4Ypu.js";import{P as lt}from"./Play-Cd5lLtoD.js";import{U as Ue}from"./Undo-DitIvwTU.js";import{b as it,t as ot,V as rt}from"./Video-B2DSnbGg.js";/* empty css                                             */import{M as st}from"./ModifyUpload-CWo4TCq1.js";function at(l){let e,n;return{c(){e=De("svg"),n=De("path"),g(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),g(e,"xmlns","http://www.w3.org/2000/svg"),g(e,"width","100%"),g(e,"height","100%"),g(e,"viewBox","0 0 24 24"),g(e,"fill","none"),g(e,"stroke","currentColor"),g(e,"stroke-width","1.5"),g(e,"stroke-linecap","round"),g(e,"stroke-linejoin","round")},m(t,i){M(t,e,i),D(e,n)},p:ee,i:ee,o:ee,d(t){t&&P(e)}}}class ut extends ae{constructor(e){super(),ue(this,e,null,at,re,{})}}function Le(l,e,n){const t=l.slice();return t[20]=e[n],t[22]=n,t}function ft(l){let e,n,t,i,o,r=[],s=new Map,c,b,f,u,d=Se(l[1]);const _=v=>v[22];for(let v=0;v<d.length;v+=1){let k=Le(l,d,v),V=_(k);s.set(V,r[v]=Me(V,k))}return{c(){e=S("div"),n=S("button"),t=X(),i=S("div"),o=X();for(let v=0;v<r.length;v+=1)r[v].c();c=X(),b=S("button"),g(n,"aria-label","start drag handle for trimming video"),g(n,"class","handle left svelte-10c4beq"),le(n,"left",l[2]+"%"),g(i,"class","opaque-layer svelte-10c4beq"),le(i,"left",l[2]+"%"),le(i,"right",100-l[3]+"%"),g(b,"aria-label","end drag handle for trimming video"),g(b,"class","handle right svelte-10c4beq"),le(b,"left",l[3]+"%"),g(e,"id","timeline"),g(e,"class","thumbnail-wrapper svelte-10c4beq")},m(v,k){M(v,e,k),D(e,n),D(e,t),D(e,i),D(e,o);for(let V=0;V<r.length;V+=1)r[V]&&r[V].m(e,null);D(e,c),D(e,b),f||(u=[j(n,"mousedown",l[10]),j(n,"blur",l[5]),j(n,"keydown",l[11]),j(b,"mousedown",l[12]),j(b,"blur",l[5]),j(b,"keydown",l[13])],f=!0)},p(v,k){k&4&&le(n,"left",v[2]+"%"),k&4&&le(i,"left",v[2]+"%"),k&8&&le(i,"right",100-v[3]+"%"),k&2&&(d=Se(v[1]),r=je(r,k,_,1,v,d,s,e,ze,Me,c,Le)),k&8&&le(b,"left",v[3]+"%")},d(v){v&&P(e);for(let k=0;k<r.length;k+=1)r[k].d();f=!1,Te(u)}}}function dt(l){let e;return{c(){e=S("div"),e.innerHTML='<span aria-label="loading timeline" class="loader svelte-10c4beq"></span>',g(e,"class","load-wrap svelte-10c4beq")},m(n,t){M(n,e,t)},p:ee,d(n){n&&P(e)}}}function Me(l,e){let n,t,i;return{key:l,first:null,c(){n=S("img"),me(n.src,t=e[20])||g(n,"src",t),g(n,"alt",i=`frame-${e[22]}`),g(n,"draggable","false"),g(n,"class","svelte-10c4beq"),this.first=n},m(o,r){M(o,n,r)},p(o,r){e=o,r&2&&!me(n.src,t=e[20])&&g(n,"src",t),r&2&&i!==(i=`frame-${e[22]}`)&&g(n,"alt",i)},d(o){o&&P(n)}}}function _t(l){let e;function n(o,r){return o[0]?dt:ft}let t=n(l),i=t(l);return{c(){e=S("div"),i.c(),g(e,"class","container svelte-10c4beq")},m(o,r){M(o,e,r),i.m(e,null)},p(o,[r]){t===(t=n(o))&&i?i.p(o,r):(i.d(1),i=t(o),i&&(i.c(),i.m(e,null)))},i:ee,o:ee,d(o){o&&P(e),i.d()}}}let pe=10;function ct(l,e,n){let{videoElement:t}=e,{trimmedDuration:i}=e,{dragStart:o}=e,{dragEnd:r}=e,{loadingTimeline:s}=e,c=[],b,f=0,u=100,d=null;const _=h=>{d=h},v=()=>{d=null},k=(h,R)=>{if(d){const m=document.getElementById("timeline");if(!m)return;const L=m.getBoundingClientRect();let H=(h.clientX-L.left)/L.width*100;if(R?H=d==="left"?f+R:u+R:H=(h.clientX-L.left)/L.width*100,H=Math.max(0,Math.min(H,100)),d==="left"){n(2,f=Math.min(H,u));const N=f/100*b;n(6,t.currentTime=N,t),n(8,o=N)}else if(d==="right"){n(3,u=Math.max(H,f));const N=u/100*b;n(6,t.currentTime=N,t),n(9,r=N)}const K=f/100*b,p=u/100*b;n(7,i=p-K),n(2,f),n(3,u)}},V=h=>{if(d){const R=1/b*100;h.key==="ArrowLeft"?k({clientX:0},-R):h.key==="ArrowRight"&&k({clientX:0},R)}},U=()=>{const h=document.createElement("canvas"),R=h.getContext("2d");if(!R)return;h.width=t.videoWidth,h.height=t.videoHeight,R.drawImage(t,0,0,h.width,h.height);const m=h.toDataURL("image/jpeg",.7);n(1,c=[...c,m])};ye(()=>{const h=()=>{b=t.duration;const R=b/pe;let m=0;const L=()=>{U(),m++,m<pe?n(6,t.currentTime+=R,t):t.removeEventListener("seeked",L)};t.addEventListener("seeked",L),n(6,t.currentTime=0,t)};t.readyState>=1?h():t.addEventListener("loadedmetadata",h)}),Fe(()=>{window.removeEventListener("mousemove",k),window.removeEventListener("mouseup",v),window.removeEventListener("keydown",V)}),ye(()=>{window.addEventListener("mousemove",k),window.addEventListener("mouseup",v),window.addEventListener("keydown",V)});const z=()=>_("left"),F=h=>{(h.key==="ArrowLeft"||h.key=="ArrowRight")&&_("left")},W=()=>_("right"),O=h=>{(h.key==="ArrowLeft"||h.key=="ArrowRight")&&_("right")};return l.$$set=h=>{"videoElement"in h&&n(6,t=h.videoElement),"trimmedDuration"in h&&n(7,i=h.trimmedDuration),"dragStart"in h&&n(8,o=h.dragStart),"dragEnd"in h&&n(9,r=h.dragEnd),"loadingTimeline"in h&&n(0,s=h.loadingTimeline)},l.$$.update=()=>{l.$$.dirty&2&&n(0,s=c.length!==pe)},[s,c,f,u,_,v,t,i,o,r,z,F,W,O]}class mt extends ae{constructor(e){super(),ue(this,e,ct,_t,re,{videoElement:6,trimmedDuration:7,dragStart:8,dragEnd:9,loadingTimeline:0})}get videoElement(){return this.$$.ctx[6]}set videoElement(e){this.$$set({videoElement:e}),y()}get trimmedDuration(){return this.$$.ctx[7]}set trimmedDuration(e){this.$$set({trimmedDuration:e}),y()}get dragStart(){return this.$$.ctx[8]}set dragStart(e){this.$$set({dragStart:e}),y()}get dragEnd(){return this.$$.ctx[9]}set dragEnd(e){this.$$set({dragEnd:e}),y()}get loadingTimeline(){return this.$$.ctx[0]}set loadingTimeline(e){this.$$set({loadingTimeline:e}),y()}}function Pe(l){let e,n,t,i,o,r,s;function c(_){l[18](_)}function b(_){l[19](_)}function f(_){l[20](_)}function u(_){l[21](_)}let d={videoElement:l[2]};return l[14]!==void 0&&(d.dragStart=l[14]),l[15]!==void 0&&(d.dragEnd=l[15]),l[12]!==void 0&&(d.trimmedDuration=l[12]),l[16]!==void 0&&(d.loadingTimeline=l[16]),n=new mt({props:d}),Z.push(()=>$(n,"dragStart",c)),Z.push(()=>$(n,"dragEnd",b)),Z.push(()=>$(n,"trimmedDuration",f)),Z.push(()=>$(n,"loadingTimeline",u)),{c(){e=S("div"),C(n.$$.fragment),g(e,"class","timeline-wrapper svelte-7yrr5f")},m(_,v){M(_,e,v),A(n,e,null),s=!0},p(_,v){const k={};v&4&&(k.videoElement=_[2]),!t&&v&16384&&(t=!0,k.dragStart=_[14],x(()=>t=!1)),!i&&v&32768&&(i=!0,k.dragEnd=_[15],x(()=>i=!1)),!o&&v&4096&&(o=!0,k.trimmedDuration=_[12],x(()=>o=!1)),!r&&v&65536&&(r=!0,k.loadingTimeline=_[16],x(()=>r=!1)),n.$set(k)},i(_){s||(E(n.$$.fragment,_),s=!0)},o(_){T(n.$$.fragment,_),s=!1},d(_){_&&P(e),I(n)}}}function ht(l){let e;return{c(){e=S("div"),g(e,"class","svelte-7yrr5f")},m(n,t){M(n,e,t)},p:ee,d(n){n&&P(e)}}}function gt(l){let e,n=oe(l[12])+"",t,i,o,r,s,c,b,f;return{c(){e=S("time"),t=ce(n),i=X(),o=S("div"),r=S("button"),r.textContent="Trim",s=X(),c=S("button"),c.textContent="Cancel",g(e,"aria-label","duration of selected region in seconds"),g(e,"class","svelte-7yrr5f"),J(e,"hidden",l[16]),g(r,"class","text-button svelte-7yrr5f"),J(r,"hidden",l[16]),g(c,"class","text-button svelte-7yrr5f"),J(c,"hidden",l[16]),g(o,"class","edit-buttons svelte-7yrr5f")},m(u,d){M(u,e,d),D(e,t),M(u,i,d),M(u,o,d),D(o,r),D(o,s),D(o,c),b||(f=[j(r,"click",l[22]),j(c,"click",l[17])],b=!0)},p(u,d){d&4096&&n!==(n=oe(u[12])+"")&&Ee(t,n),d&65536&&J(e,"hidden",u[16]),d&65536&&J(r,"hidden",u[16]),d&65536&&J(c,"hidden",u[16])},d(u){u&&(P(e),P(i),P(o)),b=!1,Te(f)}}}function qe(l){let e,n;return e=new Ve({props:{Icon:Ue,label:"Reset video to initial value",disabled:l[1]||!l[11]}}),e.$on("click",l[23]),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},p(t,i){const o={};i&2050&&(o.disabled=t[1]||!t[11]),e.$set(o)},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function Be(l){let e,n;return e=new Ve({props:{Icon:tt,label:"Trim video to selection",disabled:l[1]}}),e.$on("click",l[17]),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},p(t,i){const o={};i&2&&(o.disabled=t[1]),e.$set(o)},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function bt(l){let e,n,t,i=l[3]&&l[0]===""&&qe(l),o=l[4]&&l[0]===""&&Be(l);return{c(){i&&i.c(),e=X(),o&&o.c(),n=he()},m(r,s){i&&i.m(r,s),M(r,e,s),o&&o.m(r,s),M(r,n,s),t=!0},p(r,s){r[3]&&r[0]===""?i?(i.p(r,s),s&9&&E(i,1)):(i=qe(r),i.c(),E(i,1),i.m(e.parentNode,e)):i&&(te(),T(i,1,1,()=>{i=null}),ne()),r[4]&&r[0]===""?o?(o.p(r,s),s&17&&E(o,1)):(o=Be(r),o.c(),E(o,1),o.m(n.parentNode,n)):o&&(te(),T(o,1,1,()=>{o=null}),ne())},i(r){t||(E(i),E(o),t=!0)},o(r){T(i),T(o),t=!1},d(r){r&&(P(e),P(n)),i&&i.d(r),o&&o.d(r)}}}function wt(l){let e,n,t,i,o,r,s=l[0]==="edit"&&Pe(l);function c(u,d){return u[0]==="edit"&&u[12]!==null?gt:ht}let b=c(l),f=b(l);return o=new st({props:{i18n:l[7],download:l[9]?l[8]?.url:null,$$slots:{default:[bt]},$$scope:{ctx:l}}}),o.$on("clear",l[24]),{c(){e=S("div"),s&&s.c(),n=X(),t=S("div"),f.c(),i=X(),C(o.$$.fragment),g(t,"class","controls svelte-7yrr5f"),g(t,"data-testid","waveform-controls"),g(e,"class","container svelte-7yrr5f"),J(e,"hidden",l[0]!=="edit")},m(u,d){M(u,e,d),s&&s.m(e,null),D(e,n),D(e,t),f.m(t,null),M(u,i,d),A(o,u,d),r=!0},p(u,[d]){u[0]==="edit"?s?(s.p(u,d),d&1&&E(s,1)):(s=Pe(u),s.c(),E(s,1),s.m(e,n)):s&&(te(),T(s,1,1,()=>{s=null}),ne()),b===(b=c(u))&&f?f.p(u,d):(f.d(1),f=b(u),f&&(f.c(),f.m(t,null))),(!r||d&1)&&J(e,"hidden",u[0]!=="edit");const _={};d&128&&(_.i18n=u[7]),d&768&&(_.download=u[9]?u[8]?.url:null),d&33556539&&(_.$$scope={dirty:d,ctx:u}),o.$set(_)},i(u){r||(E(s),E(o.$$.fragment,u),r=!0)},o(u){T(s),T(o.$$.fragment,u),r=!1},d(u){u&&(P(e),P(i)),s&&s.d(),f.d(),I(o,u)}}}function vt(l,e,n){let{videoElement:t}=e,{showRedo:i=!1}=e,{interactive:o=!0}=e,{mode:r=""}=e,{handle_reset_value:s}=e,{handle_trim_video:c}=e,{processingVideo:b=!1}=e,{i18n:f}=e,{value:u=null}=e,{show_download_button:d=!1}=e,{handle_clear:_=()=>{}}=e,{has_change_history:v=!1}=e,k;ye(async()=>{n(13,k=await it())});let V=null,U=0,z=0,F=!1;const W=()=>{r==="edit"?(n(0,r=""),n(12,V=t.duration)):n(0,r="edit")};function O(p){U=p,n(14,U)}function h(p){z=p,n(15,z)}function R(p){V=p,n(12,V),n(0,r),n(2,t)}function m(p){F=p,n(16,F)}const L=()=>{n(0,r=""),n(1,b=!0),ot(k,U,z,t).then(p=>{c(p)}).then(()=>{n(1,b=!1)})},H=()=>{s(),n(0,r="")},K=()=>_();return l.$$set=p=>{"videoElement"in p&&n(2,t=p.videoElement),"showRedo"in p&&n(3,i=p.showRedo),"interactive"in p&&n(4,o=p.interactive),"mode"in p&&n(0,r=p.mode),"handle_reset_value"in p&&n(5,s=p.handle_reset_value),"handle_trim_video"in p&&n(6,c=p.handle_trim_video),"processingVideo"in p&&n(1,b=p.processingVideo),"i18n"in p&&n(7,f=p.i18n),"value"in p&&n(8,u=p.value),"show_download_button"in p&&n(9,d=p.show_download_button),"handle_clear"in p&&n(10,_=p.handle_clear),"has_change_history"in p&&n(11,v=p.has_change_history)},l.$$.update=()=>{l.$$.dirty&4101&&r==="edit"&&V===null&&t&&n(12,V=t.duration)},[r,b,t,i,o,s,c,f,u,d,_,v,V,k,U,z,F,W,O,h,R,m,L,H,K]}class kt extends ae{constructor(e){super(),ue(this,e,vt,wt,re,{videoElement:2,showRedo:3,interactive:4,mode:0,handle_reset_value:5,handle_trim_video:6,processingVideo:1,i18n:7,value:8,show_download_button:9,handle_clear:10,has_change_history:11})}get videoElement(){return this.$$.ctx[2]}set videoElement(e){this.$$set({videoElement:e}),y()}get showRedo(){return this.$$.ctx[3]}set showRedo(e){this.$$set({showRedo:e}),y()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),y()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),y()}get handle_reset_value(){return this.$$.ctx[5]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),y()}get handle_trim_video(){return this.$$.ctx[6]}set handle_trim_video(e){this.$$set({handle_trim_video:e}),y()}get processingVideo(){return this.$$.ctx[1]}set processingVideo(e){this.$$set({processingVideo:e}),y()}get i18n(){return this.$$.ctx[7]}set i18n(e){this.$$set({i18n:e}),y()}get value(){return this.$$.ctx[8]}set value(e){this.$$set({value:e}),y()}get show_download_button(){return this.$$.ctx[9]}set show_download_button(e){this.$$set({show_download_button:e}),y()}get handle_clear(){return this.$$.ctx[10]}set handle_clear(e){this.$$set({handle_clear:e}),y()}get has_change_history(){return this.$$.ctx[11]}set has_change_history(e){this.$$set({has_change_history:e}),y()}}function pt(l){let e,n;return{c(){e=S("track"),g(e,"kind","captions"),me(e.src,n=l[1])||g(e,"src",n),e.default=!0},m(t,i){M(t,e,i)},p(t,i){i[0]&2&&!me(e.src,n=t[1])&&g(e,"src",n)},d(t){t&&P(e)}}}function yt(l){let e,n;return e=new nt({}),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function Et(l){let e,n;return e=new lt({}),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function Tt(l){let e,n;return e=new Ue({}),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function Ce(l){let e,n,t;function i(r){l[37](r)}let o={videoElement:l[17],showRedo:!0,handle_trim_video:l[23],handle_reset_value:l[7],value:l[11],i18n:l[9],show_download_button:l[10],handle_clear:l[12],has_change_history:l[13]};return l[18]!==void 0&&(o.processingVideo=l[18]),e=new kt({props:o}),Z.push(()=>$(e,"processingVideo",i)),{c(){C(e.$$.fragment)},m(r,s){A(e,r,s),t=!0},p(r,s){const c={};s[0]&131072&&(c.videoElement=r[17]),s[0]&128&&(c.handle_reset_value=r[7]),s[0]&2048&&(c.value=r[11]),s[0]&512&&(c.i18n=r[9]),s[0]&1024&&(c.show_download_button=r[10]),s[0]&4096&&(c.handle_clear=r[12]),s[0]&8192&&(c.has_change_history=r[13]),!n&&s[0]&262144&&(n=!0,c.processingVideo=r[18],x(()=>n=!1)),e.$set(c)},i(r){t||(E(e.$$.fragment,r),t=!0)},o(r){T(e.$$.fragment,r),t=!1},d(r){I(e,r)}}}function Vt(l){let e,n,t,i,o,r,s,c,b,f,u,d,_,v,k,V=oe(l[14])+"",U,z,F=oe(l[15])+"",W,O,h,R,m,L,H,K,p,N,se,fe;function ge(w){l[28](w)}function be(w){l[29](w)}function we(w){l[30](w)}function ve(w){l[31](w)}let ie={src:l[0],preload:"auto",autoplay:l[3],loop:l[4],is_stream:l[8],"data-testid":`${l[5]}-player`,processingVideo:l[18],$$slots:{default:[pt]},$$scope:{ctx:l}};l[14]!==void 0&&(ie.currentTime=l[14]),l[15]!==void 0&&(ie.duration=l[15]),l[16]!==void 0&&(ie.paused=l[16]),l[17]!==void 0&&(ie.node=l[17]),t=new rt({props:ie}),Z.push(()=>$(t,"currentTime",ge)),Z.push(()=>$(t,"duration",be)),Z.push(()=>$(t,"paused",we)),Z.push(()=>$(t,"node",ve)),t.$on("click",l[20]),t.$on("play",l[32]),t.$on("pause",l[33]),t.$on("ended",l[22]),t.$on("loadstart",l[34]),t.$on("loadeddata",l[35]),t.$on("loadedmetadata",l[36]);const de=[Tt,Et,yt],Y=[];function _e(w,a){return w[14]===w[15]?0:w[16]?1:2}d=_e(l),_=Y[d]=de[d](l),H=new ut({});let q=l[6]&&Ce(l);return{c(){e=S("div"),n=S("div"),C(t.$$.fragment),c=X(),b=S("div"),f=S("div"),u=S("span"),_.c(),v=X(),k=S("span"),U=ce(V),z=ce(" / "),W=ce(F),O=X(),h=S("progress"),m=X(),L=S("div"),C(H.$$.fragment),K=X(),q&&q.c(),p=he(),g(n,"class","mirror-wrap svelte-euo1cw"),J(n,"mirror",l[2]),g(u,"role","button"),g(u,"tabindex","0"),g(u,"class","icon svelte-euo1cw"),g(u,"aria-label","play-pause-replay-button"),g(k,"class","time svelte-euo1cw"),h.value=R=l[14]/l[15]||0,g(h,"class","svelte-euo1cw"),g(L,"role","button"),g(L,"tabindex","0"),g(L,"class","icon svelte-euo1cw"),g(L,"aria-label","full-screen"),g(f,"class","inner svelte-euo1cw"),g(b,"class","controls svelte-euo1cw"),g(e,"class","wrap svelte-euo1cw")},m(w,a){M(w,e,a),D(e,n),A(t,n,null),D(e,c),D(e,b),D(b,f),D(f,u),Y[d].m(u,null),D(f,v),D(f,k),D(k,U),D(k,z),D(k,W),D(f,O),D(f,h),D(f,m),D(f,L),A(H,L,null),M(w,K,a),q&&q.m(w,a),M(w,p,a),N=!0,se||(fe=[j(u,"click",l[20]),j(u,"keydown",l[20]),j(h,"mousemove",l[19]),j(h,"touchmove",Re(l[19])),j(h,"click",Oe(Re(l[21]))),j(L,"click",l[24]),j(L,"keypress",l[24])],se=!0)},p(w,a){const B={};a[0]&1&&(B.src=w[0]),a[0]&8&&(B.autoplay=w[3]),a[0]&16&&(B.loop=w[4]),a[0]&256&&(B.is_stream=w[8]),a[0]&32&&(B["data-testid"]=`${w[5]}-player`),a[0]&262144&&(B.processingVideo=w[18]),a[0]&2|a[1]&256&&(B.$$scope={dirty:a,ctx:w}),!i&&a[0]&16384&&(i=!0,B.currentTime=w[14],x(()=>i=!1)),!o&&a[0]&32768&&(o=!0,B.duration=w[15],x(()=>o=!1)),!r&&a[0]&65536&&(r=!0,B.paused=w[16],x(()=>r=!1)),!s&&a[0]&131072&&(s=!0,B.node=w[17],x(()=>s=!1)),t.$set(B),(!N||a[0]&4)&&J(n,"mirror",w[2]);let Q=d;d=_e(w),d!==Q&&(te(),T(Y[Q],1,1,()=>{Y[Q]=null}),ne(),_=Y[d],_||(_=Y[d]=de[d](w),_.c()),E(_,1),_.m(u,null)),(!N||a[0]&16384)&&V!==(V=oe(w[14])+"")&&Ee(U,V),(!N||a[0]&32768)&&F!==(F=oe(w[15])+"")&&Ee(W,F),(!N||a[0]&49152&&R!==(R=w[14]/w[15]||0))&&(h.value=R),w[6]?q?(q.p(w,a),a[0]&64&&E(q,1)):(q=Ce(w),q.c(),E(q,1),q.m(p.parentNode,p)):q&&(te(),T(q,1,1,()=>{q=null}),ne())},i(w){N||(E(t.$$.fragment,w),E(_),E(H.$$.fragment,w),E(q),N=!0)},o(w){T(t.$$.fragment,w),T(_),T(H.$$.fragment,w),T(q),N=!1},d(w){w&&(P(e),P(K),P(p)),I(t),Y[d].d(),I(H),q&&q.d(w),se=!1,Te(fe)}}}function Dt(l,e,n){let{root:t=""}=e,{src:i}=e,{subtitle:o=null}=e,{mirror:r}=e,{autoplay:s}=e,{loop:c}=e,{label:b="test"}=e,{interactive:f=!1}=e,{handle_change:u=()=>{}}=e,{handle_reset_value:d=()=>{}}=e,{upload:_}=e,{is_stream:v}=e,{i18n:k}=e,{show_download_button:V=!1}=e,{value:U=null}=e,{handle_clear:z=()=>{}}=e,{has_change_history:F=!1}=e;const W=Ne();let O=0,h,R=!0,m,L=!1;function H(a){if(!h)return;if(a.type==="click"){p(a);return}if(a.type!=="touchmove"&&!(a.buttons&1))return;const B=a.type==="touchmove"?a.touches[0].clientX:a.clientX,{left:Q,right:ke}=a.currentTarget.getBoundingClientRect();n(14,O=h*(B-Q)/(ke-Q))}async function K(){document.fullscreenElement!=m&&(m.currentTime>0&&!m.paused&&!m.ended&&m.readyState>m.HAVE_CURRENT_DATA?m.pause():await m.play())}function p(a){const{left:B,right:Q}=a.currentTarget.getBoundingClientRect();n(14,O=h*(a.clientX-B)/(Q-B))}function N(){W("stop"),W("end")}const se=async a=>{let B=new File([a],"video.mp4");const Q=await We([B]);let ke=(await _(Q,t))?.filter(Boolean)[0];u(ke)};function fe(){m.requestFullscreen()}function ge(a){O=a,n(14,O)}function be(a){h=a,n(15,h)}function we(a){R=a,n(16,R)}function ve(a){m=a,n(17,m)}function ie(a){G.call(this,l,a)}function de(a){G.call(this,l,a)}function Y(a){G.call(this,l,a)}function _e(a){G.call(this,l,a)}function q(a){G.call(this,l,a)}function w(a){L=a,n(18,L)}return l.$$set=a=>{"root"in a&&n(25,t=a.root),"src"in a&&n(0,i=a.src),"subtitle"in a&&n(1,o=a.subtitle),"mirror"in a&&n(2,r=a.mirror),"autoplay"in a&&n(3,s=a.autoplay),"loop"in a&&n(4,c=a.loop),"label"in a&&n(5,b=a.label),"interactive"in a&&n(6,f=a.interactive),"handle_change"in a&&n(26,u=a.handle_change),"handle_reset_value"in a&&n(7,d=a.handle_reset_value),"upload"in a&&n(27,_=a.upload),"is_stream"in a&&n(8,v=a.is_stream),"i18n"in a&&n(9,k=a.i18n),"show_download_button"in a&&n(10,V=a.show_download_button),"value"in a&&n(11,U=a.value),"handle_clear"in a&&n(12,z=a.handle_clear),"has_change_history"in a&&n(13,F=a.has_change_history)},[i,o,r,s,c,b,f,d,v,k,V,U,z,F,O,h,R,m,L,H,K,p,N,se,fe,t,u,_,ge,be,we,ve,ie,de,Y,_e,q,w]}class St extends ae{constructor(e){super(),ue(this,e,Dt,Vt,re,{root:25,src:0,subtitle:1,mirror:2,autoplay:3,loop:4,label:5,interactive:6,handle_change:26,handle_reset_value:7,upload:27,is_stream:8,i18n:9,show_download_button:10,value:11,handle_clear:12,has_change_history:13},null,[-1,-1])}get root(){return this.$$.ctx[25]}set root(e){this.$$set({root:e}),y()}get src(){return this.$$.ctx[0]}set src(e){this.$$set({src:e}),y()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),y()}get mirror(){return this.$$.ctx[2]}set mirror(e){this.$$set({mirror:e}),y()}get autoplay(){return this.$$.ctx[3]}set autoplay(e){this.$$set({autoplay:e}),y()}get loop(){return this.$$.ctx[4]}set loop(e){this.$$set({loop:e}),y()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),y()}get interactive(){return this.$$.ctx[6]}set interactive(e){this.$$set({interactive:e}),y()}get handle_change(){return this.$$.ctx[26]}set handle_change(e){this.$$set({handle_change:e}),y()}get handle_reset_value(){return this.$$.ctx[7]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),y()}get upload(){return this.$$.ctx[27]}set upload(e){this.$$set({upload:e}),y()}get is_stream(){return this.$$.ctx[8]}set is_stream(e){this.$$set({is_stream:e}),y()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),y()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),y()}get value(){return this.$$.ctx[11]}set value(e){this.$$set({value:e}),y()}get handle_clear(){return this.$$.ctx[12]}set handle_clear(e){this.$$set({handle_clear:e}),y()}get has_change_history(){return this.$$.ctx[13]}set has_change_history(e){this.$$set({has_change_history:e}),y()}}const Rt=St;function Lt(l){let e=l[0].url,n,t,i,o,r=Ae(l);return i=new $e({props:{$$slots:{default:[qt]},$$scope:{ctx:l}}}),{c(){r.c(),n=X(),t=S("div"),C(i.$$.fragment),g(t,"data-testid","download-div")},m(s,c){r.m(s,c),M(s,n,c),M(s,t,c),A(i,t,null),o=!0},p(s,c){c&1&&re(e,e=s[0].url)?(te(),T(r,1,1,ee),ne(),r=Ae(s),r.c(),E(r,1),r.m(n.parentNode,n)):r.p(s,c);const b={};c&2097505&&(b.$$scope={dirty:c,ctx:s}),i.$set(b)},i(s){o||(E(r),E(i.$$.fragment,s),o=!0)},o(s){T(r),T(i.$$.fragment,s),o=!1},d(s){s&&(P(n),P(t)),r.d(s),I(i)}}}function Mt(l){let e,n;return e=new Ke({props:{unpadded_box:!0,size:"large",$$slots:{default:[Bt]},$$scope:{ctx:l}}}),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},p(t,i){const o={};i&2097152&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function Ae(l){let e,n;return e=new Rt({props:{src:l[0].url,subtitle:l[1]?.url,is_stream:l[0].is_stream,autoplay:l[4],mirror:!1,label:l[2],loop:l[7],interactive:!1,upload:l[9],i18n:l[8]}}),e.$on("play",l[11]),e.$on("pause",l[12]),e.$on("stop",l[13]),e.$on("end",l[14]),e.$on("loadedmetadata",l[15]),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},p(t,i){const o={};i&1&&(o.src=t[0].url),i&2&&(o.subtitle=t[1]?.url),i&1&&(o.is_stream=t[0].is_stream),i&16&&(o.autoplay=t[4]),i&4&&(o.label=t[2]),i&128&&(o.loop=t[7]),i&512&&(o.upload=t[9]),i&256&&(o.i18n=t[8]),e.$set(o)},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function Ie(l){let e,n;return e=new et({props:{href:l[0].is_stream?l[0].url?.replace("playlist.m3u8","playlist-file"):l[0].url,download:l[0].orig_name||l[0].path,$$slots:{default:[Pt]},$$scope:{ctx:l}}}),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},p(t,i){const o={};i&1&&(o.href=t[0].is_stream?t[0].url?.replace("playlist.m3u8","playlist-file"):t[0].url),i&1&&(o.download=t[0].orig_name||t[0].path),i&2097152&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function Pt(l){let e,n;return e=new Ve({props:{Icon:Ze,label:"Download"}}),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},p:ee,i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function He(l){let e,n;return e=new Ye({props:{i18n:l[8],value:l[0],formatter:l[16]}}),e.$on("error",l[17]),e.$on("share",l[18]),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},p(t,i){const o={};i&256&&(o.i18n=t[8]),i&1&&(o.value=t[0]),e.$set(o)},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function qt(l){let e,n,t,i=l[6]&&Ie(l),o=l[5]&&He(l);return{c(){i&&i.c(),e=X(),o&&o.c(),n=he()},m(r,s){i&&i.m(r,s),M(r,e,s),o&&o.m(r,s),M(r,n,s),t=!0},p(r,s){r[6]?i?(i.p(r,s),s&64&&E(i,1)):(i=Ie(r),i.c(),E(i,1),i.m(e.parentNode,e)):i&&(te(),T(i,1,1,()=>{i=null}),ne()),r[5]?o?(o.p(r,s),s&32&&E(o,1)):(o=He(r),o.c(),E(o,1),o.m(n.parentNode,n)):o&&(te(),T(o,1,1,()=>{o=null}),ne())},i(r){t||(E(i),E(o),t=!0)},o(r){T(i),T(o),t=!1},d(r){r&&(P(e),P(n)),i&&i.d(r),o&&o.d(r)}}}function Bt(l){let e,n;return e=new Xe({}),{c(){C(e.$$.fragment)},m(t,i){A(e,t,i),n=!0},i(t){n||(E(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function Ct(l){let e,n,t,i,o,r;e=new Je({props:{show_label:l[3],Icon:Xe,label:l[2]||"Video"}});const s=[Mt,Lt],c=[];function b(f,u){return!f[0]||f[0].url===void 0?0:1}return t=b(l),i=c[t]=s[t](l),{c(){C(e.$$.fragment),n=X(),i.c(),o=he()},m(f,u){A(e,f,u),M(f,n,u),c[t].m(f,u),M(f,o,u),r=!0},p(f,[u]){const d={};u&8&&(d.show_label=f[3]),u&4&&(d.label=f[2]||"Video"),e.$set(d);let _=t;t=b(f),t===_?c[t].p(f,u):(te(),T(c[_],1,1,()=>{c[_]=null}),ne(),i=c[t],i?i.p(f,u):(i=c[t]=s[t](f),i.c()),E(i,1),i.m(o.parentNode,o))},i(f){r||(E(e.$$.fragment,f),E(i),r=!0)},o(f){T(e.$$.fragment,f),T(i),r=!1},d(f){f&&(P(n),P(o)),I(e,f),c[t].d(f)}}}function At(l,e,n){let{value:t=null}=e,{subtitle:i=null}=e,{label:o=void 0}=e,{show_label:r=!0}=e,{autoplay:s}=e,{show_share_button:c=!0}=e,{show_download_button:b=!0}=e,{loop:f}=e,{i18n:u}=e,{upload:d}=e,_=null,v=null;const k=Ne();Ge(async()=>{t!==_&&i!==v&&v!==null&&(_=t,n(0,t=null),await Qe(),n(0,t=_)),_=t,v=i});function V(m){G.call(this,l,m)}function U(m){G.call(this,l,m)}function z(m){G.call(this,l,m)}function F(m){G.call(this,l,m)}const W=()=>{k("load")},O=async m=>m?await xe(m.data):"";function h(m){G.call(this,l,m)}function R(m){G.call(this,l,m)}return l.$$set=m=>{"value"in m&&n(0,t=m.value),"subtitle"in m&&n(1,i=m.subtitle),"label"in m&&n(2,o=m.label),"show_label"in m&&n(3,r=m.show_label),"autoplay"in m&&n(4,s=m.autoplay),"show_share_button"in m&&n(5,c=m.show_share_button),"show_download_button"in m&&n(6,b=m.show_download_button),"loop"in m&&n(7,f=m.loop),"i18n"in m&&n(8,u=m.i18n),"upload"in m&&n(9,d=m.upload)},l.$$.update=()=>{l.$$.dirty&1&&t&&k("change",t)},[t,i,o,r,s,c,b,f,u,d,k,V,U,z,F,W,O,h,R]}class It extends ae{constructor(e){super(),ue(this,e,At,Ct,re,{value:0,subtitle:1,label:2,show_label:3,autoplay:4,show_share_button:5,show_download_button:6,loop:7,i18n:8,upload:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),y()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),y()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),y()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),y()}get autoplay(){return this.$$.ctx[4]}set autoplay(e){this.$$set({autoplay:e}),y()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),y()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),y()}get loop(){return this.$$.ctx[7]}set loop(e){this.$$set({loop:e}),y()}get i18n(){return this.$$.ctx[8]}set i18n(e){this.$$set({i18n:e}),y()}get upload(){return this.$$.ctx[9]}set upload(e){this.$$set({upload:e}),y()}}const $t=Object.freeze(Object.defineProperty({__proto__:null,default:It},Symbol.toStringTag,{value:"Module"}));export{Rt as P,It as V,$t as a};
//# sourceMappingURL=VideoPreview-CYXODlxd.js.map
