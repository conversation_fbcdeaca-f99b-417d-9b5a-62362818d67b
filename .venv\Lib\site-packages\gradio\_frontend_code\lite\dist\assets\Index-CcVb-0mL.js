import{a as pe,i as Me,s as Be,f as w,B as Ee,c as q,m as z,t as k,b as I,d as F,a3 as qe,Y as ze,S as Fe,x as N,p as L,q as v,l as p,a0 as Ne,a4 as Pe,y as O,z as Q,o as M,I as Se,ao as V,E as ae,ap as Y,r as E,v as $,aq as oe,k as Le,$ as Z,F as W,n as We,P as $e,W as ue,w as re}from"../lite.js";import{B as je}from"./BlockLabel-B0HN-MOU.js";import{E as Ce}from"./Empty-C76eC2zW.js";import{M as Ve,a as Ye}from"./Minimize-DJwpjnSa.js";import{I as fe}from"./Image-Cvn5Jo_E.js";import{I as Ae}from"./IconButtonWrapper-Ck50MZwX.js";import{r as y}from"./file-url-Co2ROWca.js";function x(t,e,n){const l=t.slice();return l[32]=e[n],l[34]=n,l}function ee(t,e,n){const l=t.slice();return l[32]=e[n],l[34]=n,l}function De(t){let e,n,l,s,i,a,f,b,g;n=new Ae({props:{$$slots:{default:[He]},$$scope:{ctx:t}}});let h=V(t[15]?t[15]?.annotations:[]),_=[];for(let r=0;r<h.length;r+=1)_[r]=ne(ee(t,h,r));let c=t[6]&&t[15]&&se(t);return{c(){e=L("div"),q(n.$$.fragment),l=N(),s=L("img"),a=N();for(let r=0;r<_.length;r+=1)_[r].c();f=N(),c&&c.c(),b=ae(),v(s,"class","base-image svelte-303fln"),Y(s.src,i=t[15]?t[15].image.url:null)||v(s,"src",i),v(s,"alt","the base file that is annotated"),E(s,"fit-height",t[7]&&!t[17]),v(e,"class","image-container svelte-303fln")},m(r,u){p(r,e,u),z(n,e,null),$(e,l),$(e,s),$(e,a);for(let m=0;m<_.length;m+=1)_[m]&&_[m].m(e,null);t[26](e),p(r,f,u),c&&c.m(r,u),p(r,b,u),g=!0},p(r,u){const m={};if(u[0]&147456|u[1]&32&&(m.$$scope={dirty:u,ctx:r}),n.$set(m),(!g||u[0]&32768&&!Y(s.src,i=r[15]?r[15].image.url:null))&&v(s,"src",i),(!g||u[0]&131200)&&E(s,"fit-height",r[7]&&!r[17]),u[0]&229904){h=V(r[15]?r[15]?.annotations:[]);let d;for(d=0;d<h.length;d+=1){const B=ee(r,h,d);_[d]?_[d].p(B,u):(_[d]=ne(B),_[d].c(),_[d].m(e,null))}for(;d<_.length;d+=1)_[d].d(1);_.length=h.length}r[6]&&r[15]?c?c.p(r,u):(c=se(r),c.c(),c.m(b.parentNode,b)):c&&(c.d(1),c=null)},i(r){g||(k(n.$$.fragment,r),g=!0)},o(r){I(n.$$.fragment,r),g=!1},d(r){r&&(M(e),M(f),M(b)),F(n),oe(_,r),t[26](null),c&&c.d(r)}}}function Ge(t){let e,n;return e=new Ce({props:{size:"large",unpadded_box:!0,$$slots:{default:[Je]},$$scope:{ctx:t}}}),{c(){q(e.$$.fragment)},m(l,s){z(e,l,s),n=!0},p(l,s){const i={};s[1]&32&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){n||(k(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){F(e,l)}}}function le(t){let e,n;return e=new ue({props:{Icon:Ve,label:"View in full screen"}}),e.$on("click",t[19]),{c(){q(e.$$.fragment)},m(l,s){z(e,l,s),n=!0},p:re,i(l){n||(k(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){F(e,l)}}}function te(t){let e,n;return e=new ue({props:{Icon:Ye,label:"Exit full screen"}}),e.$on("click",t[19]),{c(){q(e.$$.fragment)},m(l,s){z(e,l,s),n=!0},p:re,i(l){n||(k(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){F(e,l)}}}function He(t){let e,n,l,s=!t[17]&&t[14]&&le(t),i=t[17]&&te(t);return{c(){s&&s.c(),e=N(),i&&i.c(),n=ae()},m(a,f){s&&s.m(a,f),p(a,e,f),i&&i.m(a,f),p(a,n,f),l=!0},p(a,f){!a[17]&&a[14]?s?(s.p(a,f),f[0]&147456&&k(s,1)):(s=le(a),s.c(),k(s,1),s.m(e.parentNode,e)):s&&(O(),I(s,1,1,()=>{s=null}),Q()),a[17]?i?(i.p(a,f),f[0]&131072&&k(i,1)):(i=te(a),i.c(),k(i,1),i.m(n.parentNode,n)):i&&(O(),I(i,1,1,()=>{i=null}),Q())},i(a){l||(k(s),k(i),l=!0)},o(a){I(s),I(i),l=!1},d(a){a&&(M(e),M(n)),s&&s.d(a),i&&i.d(a)}}}function ne(t){let e,n,l,s;return{c(){e=L("img"),v(e,"alt",n="segmentation mask identifying "+t[4]+" within the uploaded file"),v(e,"class","mask fit-height svelte-303fln"),Y(e.src,l=t[32].image.url)||v(e,"src",l),v(e,"style",s=t[9]&&t[32].label in t[9]?null:`filter: hue-rotate(${Math.round(t[34]*360/t[15]?.annotations.length)}deg);`),E(e,"fit-height",!t[17]),E(e,"active",t[16]==t[32].label),E(e,"inactive",t[16]!=t[32].label&&t[16]!=null)},m(i,a){p(i,e,a)},p(i,a){a[0]&16&&n!==(n="segmentation mask identifying "+i[4]+" within the uploaded file")&&v(e,"alt",n),a[0]&32768&&!Y(e.src,l=i[32].image.url)&&v(e,"src",l),a[0]&33280&&s!==(s=i[9]&&i[32].label in i[9]?null:`filter: hue-rotate(${Math.round(i[34]*360/i[15]?.annotations.length)}deg);`)&&v(e,"style",s),a[0]&131072&&E(e,"fit-height",!i[17]),a[0]&98304&&E(e,"active",i[16]==i[32].label),a[0]&98304&&E(e,"inactive",i[16]!=i[32].label&&i[16]!=null)},d(i){i&&M(e)}}}function se(t){let e,n=V(t[15].annotations),l=[];for(let s=0;s<n.length;s+=1)l[s]=ie(x(t,n,s));return{c(){e=L("div");for(let s=0;s<l.length;s+=1)l[s].c();v(e,"class","legend svelte-303fln")},m(s,i){p(s,e,i);for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(e,null)},p(s,i){if(i[0]&7373312){n=V(s[15].annotations);let a;for(a=0;a<n.length;a+=1){const f=x(s,n,a);l[a]?l[a].p(f,i):(l[a]=ie(f),l[a].c(),l[a].m(e,null))}for(;a<l.length;a+=1)l[a].d(1);l.length=n.length}},d(s){s&&M(e),oe(l,s)}}}function ie(t){let e,n=t[32].label+"",l,s,i,a;function f(){return t[27](t[32])}function b(){return t[28](t[32])}function g(){return t[31](t[34],t[32])}return{c(){e=L("button"),l=Le(n),s=N(),v(e,"class","legend-item svelte-303fln"),Z(e,"background-color",t[9]&&t[32].label in t[9]?t[9][t[32].label]+"88":`hsla(${Math.round(t[34]*360/t[15].annotations.length)}, 100%, 50%, 0.3)`)},m(h,_){p(h,e,_),$(e,l),$(e,s),i||(a=[W(e,"mouseover",f),W(e,"focus",b),W(e,"mouseout",t[29]),W(e,"blur",t[30]),W(e,"click",g)],i=!0)},p(h,_){t=h,_[0]&32768&&n!==(n=t[32].label+"")&&We(l,n),_[0]&33280&&Z(e,"background-color",t[9]&&t[32].label in t[9]?t[9][t[32].label]+"88":`hsla(${Math.round(t[34]*360/t[15].annotations.length)}, 100%, 50%, 0.3)`)},d(h){h&&M(e),i=!1,$e(a)}}}function Je(t){let e,n;return e=new fe({}),{c(){q(e.$$.fragment)},m(l,s){z(e,l,s),n=!0},i(l){n||(k(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){F(e,l)}}}function Ke(t){let e,n,l,s,i,a,f,b;const g=[{autoscroll:t[3].autoscroll},{i18n:t[3].i18n},t[13]];let h={};for(let u=0;u<g.length;u+=1)h=ze(h,g[u]);e=new Fe({props:h}),l=new je({props:{show_label:t[5],Icon:fe,label:t[4]||t[3].i18n("image.image")}});const _=[Ge,De],c=[];function r(u,m){return u[15]==null?0:1}return a=r(t),f=c[a]=_[a](t),{c(){q(e.$$.fragment),n=N(),q(l.$$.fragment),s=N(),i=L("div"),f.c(),v(i,"class","container svelte-303fln")},m(u,m){z(e,u,m),p(u,n,m),z(l,u,m),p(u,s,m),p(u,i,m),c[a].m(i,null),b=!0},p(u,m){const d=m[0]&8200?Ne(g,[m[0]&8&&{autoscroll:u[3].autoscroll},m[0]&8&&{i18n:u[3].i18n},m[0]&8192&&Pe(u[13])]):{};e.$set(d);const B={};m[0]&32&&(B.show_label=u[5]),m[0]&24&&(B.label=u[4]||u[3].i18n("image.image")),l.$set(B);let P=a;a=r(u),a===P?c[a].p(u,m):(O(),I(c[P],1,1,()=>{c[P]=null}),Q(),f=c[a],f?f.p(u,m):(f=c[a]=_[a](u),f.c()),k(f,1),f.m(i,null))},i(u){b||(k(e.$$.fragment,u),k(l.$$.fragment,u),k(f),b=!0)},o(u){I(e.$$.fragment,u),I(l.$$.fragment,u),I(f),b=!1},d(u){u&&(M(n),M(s),M(i)),F(e,u),F(l,u),c[a].d()}}}function Oe(t){let e,n;return e=new Ee({props:{visible:t[2],elem_id:t[0],elem_classes:t[1],padding:!1,height:t[7],width:t[8],allow_overflow:!1,container:t[10],scale:t[11],min_width:t[12],$$slots:{default:[Ke]},$$scope:{ctx:t}}}),{c(){q(e.$$.fragment)},m(l,s){z(e,l,s),n=!0},p(l,s){const i={};s[0]&4&&(i.visible=l[2]),s[0]&1&&(i.elem_id=l[0]),s[0]&2&&(i.elem_classes=l[1]),s[0]&128&&(i.height=l[7]),s[0]&256&&(i.width=l[8]),s[0]&1024&&(i.container=l[10]),s[0]&2048&&(i.scale=l[11]),s[0]&4096&&(i.min_width=l[12]),s[0]&516856|s[1]&32&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){n||(k(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){F(e,l)}}}function Qe(t,e,n){let{elem_id:l=""}=e,{elem_classes:s=[]}=e,{visible:i=!0}=e,{value:a=null}=e,f=null,b=null,{gradio:g}=e,{label:h=g.i18n("annotated_image.annotated_image")}=e,{show_label:_=!0}=e,{show_legend:c=!0}=e,{height:r}=e,{width:u}=e,{color_map:m}=e,{container:d=!0}=e,{scale:B=null}=e,{min_width:P=void 0}=e,A=null,{loading_status:R}=e,{show_fullscreen_button:T=!0}=e,D=!1,j;qe(()=>{document.addEventListener("fullscreenchange",()=>{n(17,D=!!document.fullscreenElement)})});const _e=async()=>{D?await document.exitFullscreen():await j.requestFullscreen()};let G=null;function H(o){n(16,A=o)}function J(){n(16,A=null)}function U(o,C){g.dispatch("select",{value:h,index:o})}function ce(o){Se[o?"unshift":"push"](()=>{j=o,n(18,j)})}const me=o=>H(o.label),he=o=>H(o.label),ge=()=>J(),be=()=>J(),de=(o,C)=>U(o,C.label);return t.$$set=o=>{"elem_id"in o&&n(0,l=o.elem_id),"elem_classes"in o&&n(1,s=o.elem_classes),"visible"in o&&n(2,i=o.visible),"value"in o&&n(23,a=o.value),"gradio"in o&&n(3,g=o.gradio),"label"in o&&n(4,h=o.label),"show_label"in o&&n(5,_=o.show_label),"show_legend"in o&&n(6,c=o.show_legend),"height"in o&&n(7,r=o.height),"width"in o&&n(8,u=o.width),"color_map"in o&&n(9,m=o.color_map),"container"in o&&n(10,d=o.container),"scale"in o&&n(11,B=o.scale),"min_width"in o&&n(12,P=o.min_width),"loading_status"in o&&n(13,R=o.loading_status),"show_fullscreen_button"in o&&n(14,T=o.show_fullscreen_button)},t.$$.update=()=>{if(t.$$.dirty[0]&58720264)if(a!==f&&(n(24,f=a),g.dispatch("change")),a){const o={image:a.image,annotations:a.annotations.map(S=>({image:S.image,label:S.label}))};n(15,b=o);const C=y(o.image.url),we=Promise.all(o.annotations.map(S=>y(S.image.url))),K=Promise.all([C,we]);n(25,G=K),K.then(([S,ke])=>{if(G!==K)return;const ve={image:{...o.image,url:S??void 0},annotations:o.annotations.map((X,Ie)=>({...X,image:{...X.image,url:ke[Ie]??void 0}}))};n(15,b=ve)})}else n(15,b=null)},[l,s,i,g,h,_,c,r,u,m,d,B,P,R,T,b,A,D,j,_e,H,J,U,a,f,G,ce,me,he,ge,be,de]}class el extends pe{constructor(e){super(),Me(this,e,Qe,Oe,Be,{elem_id:0,elem_classes:1,visible:2,value:23,gradio:3,label:4,show_label:5,show_legend:6,height:7,width:8,color_map:9,container:10,scale:11,min_width:12,loading_status:13,show_fullscreen_button:14},null,[-1,-1])}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[23]}set value(e){this.$$set({value:e}),w()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),w()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),w()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),w()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),w()}get height(){return this.$$.ctx[7]}set height(e){this.$$set({height:e}),w()}get width(){return this.$$.ctx[8]}set width(e){this.$$set({width:e}),w()}get color_map(){return this.$$.ctx[9]}set color_map(e){this.$$set({color_map:e}),w()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),w()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),w()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),w()}}export{el as default};
//# sourceMappingURL=Index-CcVb-0mL.js.map
