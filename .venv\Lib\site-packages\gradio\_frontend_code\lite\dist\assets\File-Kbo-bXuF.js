import{a as h,i as c,s as d,Q as n,q as t,l as f,v as l,w as r,o as u}from"../lite.js";function v(i){let e,s,a;return{c(){e=n("svg"),s=n("path"),a=n("polyline"),t(s,"d","M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"),t(a,"points","13 2 13 9 20 9"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-file")},m(o,p){f(o,e,p),l(e,s),l(e,a)},p:r,i:r,o:r,d(o){o&&u(e)}}}class g extends h{constructor(e){super(),c(this,e,null,v,d,{})}}export{g as F};
//# sourceMappingURL=File-Kbo-bXuF.js.map
