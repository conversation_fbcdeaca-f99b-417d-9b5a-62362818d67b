import{a as m,i as u,s as c,f as o,p as f,c as p,q as g,r as n,l as d,m as h,t as _,b,o as v,d as y}from"../lite.js";import{I as $}from"./Image-Bd-jnd8M.js";/* empty css                                                   */import"./ImageUploader-DjVwxXyF.js";/* empty css                                              */import"./file-url-Co2ROWca.js";import"./BlockLabel-B0HN-MOU.js";import"./Image-Cvn5Jo_E.js";import"./SelectSource-CfNi7fcQ.js";import"./Upload-D_TzP4YC.js";import"./utils-Gtzs_Zla.js";import"./DropdownArrow-B85Vabzi.js";import"./Square-7hK0fZD1.js";import"./index-DCygRGWm.js";import"./StreamingBar-C8nPcBp-.js";import"./Upload-TGDabKXH.js";/* empty css                                             */import"./IconButtonWrapper-Ck50MZwX.js";function k(i){let e,s,r;return s=new $({props:{src:i[0].composite?.url||i[0].background?.url,alt:""}}),{c(){e=f("div"),p(s.$$.fragment),g(e,"class","container svelte-jhlhb0"),n(e,"table",i[1]==="table"),n(e,"gallery",i[1]==="gallery"),n(e,"selected",i[2])},m(t,a){d(t,e,a),h(s,e,null),r=!0},p(t,[a]){const l={};a&1&&(l.src=t[0].composite?.url||t[0].background?.url),s.$set(l),(!r||a&2)&&n(e,"table",t[1]==="table"),(!r||a&2)&&n(e,"gallery",t[1]==="gallery"),(!r||a&4)&&n(e,"selected",t[2])},i(t){r||(_(s.$$.fragment,t),r=!0)},o(t){b(s.$$.fragment,t),r=!1},d(t){t&&v(e),y(s)}}}function q(i,e,s){let{value:r}=e,{type:t}=e,{selected:a=!1}=e;return i.$$set=l=>{"value"in l&&s(0,r=l.value),"type"in l&&s(1,t=l.type),"selected"in l&&s(2,a=l.selected)},[r,t,a]}class O extends m{constructor(e){super(),u(this,e,q,k,c,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),o()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),o()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),o()}}export{O as default};
//# sourceMappingURL=Example-Cn7RbPbW.js.map
