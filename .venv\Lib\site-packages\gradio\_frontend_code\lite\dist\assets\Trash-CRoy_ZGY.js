import{a as x,i as m,s as u,Q as s,k as v,q as t,l as _,v as a,w as g,o as V}from"../lite.js";function y(w){let e,c,h,p,n,r,o,i,l;return{c(){e=s("svg"),c=s("defs"),h=s("style"),p=v(`.cls-1 {
				fill: none;
			}`),n=s("rect"),r=s("rect"),o=s("path"),i=s("rect"),l=s("rect"),t(n,"x","12"),t(n,"y","12"),t(n,"width","2"),t(n,"height","12"),t(r,"x","18"),t(r,"y","12"),t(r,"width","2"),t(r,"height","12"),t(o,"d","M4,6V8H6V28a2,2,0,0,0,2,2H24a2,2,0,0,0,2-2V8h2V6ZM8,28V8H24V28Z"),t(i,"x","12"),t(i,"y","2"),t(i,"width","8"),t(i,"height","2"),t(l,"id","_Transparent_Rectangle_"),t(l,"data-name","<Transparent Rectangle>"),t(l,"class","cls-1"),t(l,"width","32"),t(l,"height","32"),t(e,"id","icon"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","0 0 32 32"),t(e,"fill","currentColor"),t(e,"width","100%"),t(e,"height","100%")},m(d,f){_(d,e,f),a(e,c),a(c,h),a(h,p),a(e,n),a(e,r),a(e,o),a(e,i),a(e,l)},p:g,i:g,o:g,d(d){d&&V(e)}}}class H extends x{constructor(e){super(),m(this,e,null,y,u,{})}}export{H as T};
//# sourceMappingURL=Trash-CRoy_ZGY.js.map
