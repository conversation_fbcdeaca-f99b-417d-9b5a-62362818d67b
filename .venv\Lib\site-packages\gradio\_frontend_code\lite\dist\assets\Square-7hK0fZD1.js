import{a as f,i as u,s as w,f as n,Q as a,q as e,l as c,v as d,w as h,o as _}from"../lite.js";function k(o){let t,s,l;return{c(){t=a("svg"),s=a("rect"),e(s,"x","3"),e(s,"y","3"),e(s,"width","18"),e(s,"height","18"),e(s,"rx","2"),e(s,"ry","2"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill",o[0]),e(t,"stroke","currentColor"),e(t,"stroke-width",l=`${o[1]}`),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-square")},m(i,r){c(i,t,r),d(t,s)},p(i,[r]){r&1&&e(t,"fill",i[0]),r&2&&l!==(l=`${i[1]}`)&&e(t,"stroke-width",l)},i:h,o:h,d(i){i&&_(t)}}}function g(o,t,s){let{fill:l="currentColor"}=t,{stroke_width:i=1.5}=t;return o.$$set=r=>{"fill"in r&&s(0,l=r.fill),"stroke_width"in r&&s(1,i=r.stroke_width)},[l,i]}class m extends f{constructor(t){super(),u(this,t,g,k,w,{fill:0,stroke_width:1})}get fill(){return this.$$.ctx[0]}set fill(t){this.$$set({fill:t}),n()}get stroke_width(){return this.$$.ctx[1]}set stroke_width(t){this.$$set({stroke_width:t}),n()}}export{m as S};
//# sourceMappingURL=Square-7hK0fZD1.js.map
