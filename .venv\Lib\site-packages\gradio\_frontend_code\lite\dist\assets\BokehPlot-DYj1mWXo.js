import{a as j,i as x,s as B,f as g,p as C,q as l,l as S,w as u,o as q,A as D,a8 as P}from"../lite.js";function $(s){let e;return{c(){e=C("div"),l(e,"data-testid","bokeh"),l(e,"id",s[0]),l(e,"class","gradio-bokeh svelte-1rhu6ax")},m(o,a){S(o,e,a)},p:u,i:u,o:u,d(o){o&&q(e)}}}function I(s,e,o){let a,{value:c}=e,{bokeh_version:n}=e;const r=`bokehDiv-${Math.random().toString(5).substring(2)}`,v=D();async function y(t){if(document&&document.getElementById(r)&&(document.getElementById(r).innerHTML=""),window.Bokeh){k();let h=JSON.parse(t);(await window.Bokeh.embed.embed_item(h,r))._roots.forEach(async i=>{await i.ready,v("load")})}}const m=`https://cdn.bokeh.org/bokeh/release/bokeh-${n}.min.js`,w=[`https://cdn.pydata.org/bokeh/release/bokeh-widgets-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-tables-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-gl-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-api-${n}.min.js`];let d=!1;async function E(){await Promise.all(w.map((t,h)=>new Promise(f=>{const i=document.createElement("script");return i.onload=f,i.src=t,document.head.appendChild(i),i}))),o(3,d=!0)}let p=[];function b(){p=E()}function k(){const t=document.createElement("script");return t.onload=b,t.src=m,document.head.querySelector(`script[src="${m}"]`)?b():document.head.appendChild(t),t}const _=n?k():null;return P(()=>{_ in document.children&&(document.removeChild(_),p.forEach(t=>document.removeChild(t)))}),s.$$set=t=>{"value"in t&&o(1,c=t.value),"bokeh_version"in t&&o(2,n=t.bokeh_version)},s.$$.update=()=>{s.$$.dirty&2&&o(4,a=c?.plot),s.$$.dirty&24&&d&&y(a)},[r,c,n,d,a]}class O extends j{constructor(e){super(),x(this,e,I,$,B,{value:1,bokeh_version:2})}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),g()}get bokeh_version(){return this.$$.ctx[2]}set bokeh_version(e){this.$$set({bokeh_version:e}),g()}}export{O as default};
//# sourceMappingURL=BokehPlot-DYj1mWXo.js.map
