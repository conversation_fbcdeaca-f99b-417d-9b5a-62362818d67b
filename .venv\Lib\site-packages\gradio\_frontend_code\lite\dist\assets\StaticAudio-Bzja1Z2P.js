import{a as te,i as ie,s as ne,Q as F,q as d,l as z,v as C,w as j,o as O,k as me,f as P,E as ve,y as $,b as S,z as ee,t as E,c as B,m as I,d as U,p as H,F as x,P as Ee,a3 as Ue,I as J,x as Y,$ as ye,r as ke,n as Fe,a5 as pe,a6 as _e,A as qe,H as le,W as Ze}from"../lite.js";import{f as Ce,u as Ye}from"./utils-BsGrhMNe.js";import{B as xe}from"./BlockLabel-B0HN-MOU.js";import{E as Xe}from"./Empty-C76eC2zW.js";import{S as Ke}from"./ShareButton-Bn_rnIUK.js";import{D as Qe}from"./Download-CgLP-Xl6.js";import{M as Se}from"./Music-FnJcUklZ.js";import{I as Je}from"./IconButtonWrapper-Ck50MZwX.js";import{P as $e,T as et}from"./Trim-CMIh4Ypu.js";import{P as tt}from"./Play-Cd5lLtoD.js";import{U as it}from"./Undo-DitIvwTU.js";import{r as Ae}from"./file-url-Co2ROWca.js";import{H as he}from"./hls-CnVhpNcu.js";import{D as nt}from"./DownloadLink-B8hI46-W.js";function st(r){let e,t;return{c(){e=F("svg"),t=F("path"),d(t,"stroke","currentColor"),d(t,"stroke-width","1.5"),d(t,"stroke-linecap","round"),d(t,"stroke-linejoin","round"),d(t,"d","M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"width","24px"),d(e,"height","24px"),d(e,"fill","currentColor"),d(e,"stroke-width","1.5"),d(e,"viewBox","0 0 24 24"),d(e,"color","currentColor")},m(i,n){z(i,e,n),C(e,t)},p:j,i:j,o:j,d(i){i&&O(e)}}}class rt extends te{constructor(e){super(),ie(this,e,null,st,ne,{})}}function ot(r){let e,t;return{c(){e=F("svg"),t=F("path"),d(t,"stroke","currentColor"),d(t,"stroke-width","1.5"),d(t,"stroke-linecap","round"),d(t,"stroke-linejoin","round"),d(t,"d","M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"width","24px"),d(e,"height","24px"),d(e,"fill","currentColor"),d(e,"stroke-width","1.5"),d(e,"viewBox","0 0 24 24"),d(e,"color","currentColor")},m(i,n){z(i,e,n),C(e,t)},p:j,i:j,o:j,d(i){i&&O(e)}}}class lt extends te{constructor(e){super(),ie(this,e,null,ot,ne,{})}}function at(r){let e,t,i,n,s;return{c(){e=F("svg"),t=F("title"),i=me("Low volume"),n=F("path"),s=F("path"),d(n,"d","M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5"),d(n,"stroke-width","1.5"),d(n,"stroke-linecap","round"),d(n,"stroke-linejoin","round"),d(s,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),d(s,"stroke-width","1.5"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"stroke-width","1.5"),d(e,"fill","none"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"stroke","currentColor"),d(e,"color","currentColor")},m(o,a){z(o,e,a),C(e,t),C(t,i),C(e,n),C(e,s)},p:j,i:j,o:j,d(o){o&&O(e)}}}class ut extends te{constructor(e){super(),ie(this,e,null,at,ne,{})}}function dt(r){let e,t,i,n,s,o;return{c(){e=F("svg"),t=F("title"),i=me("High volume"),n=F("path"),s=F("path"),o=F("path"),d(n,"d","M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z"),d(n,"stroke-width","1.5"),d(s,"d","M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5"),d(s,"stroke-width","1.5"),d(s,"stroke-linecap","round"),d(s,"stroke-linejoin","round"),d(o,"d","M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5"),d(o,"stroke-width","1.5"),d(o,"stroke-linecap","round"),d(o,"stroke-linejoin","round"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"stroke-width","1.5"),d(e,"fill","none"),d(e,"stroke","currentColor"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"color","currentColor")},m(a,u){z(a,e,u),C(e,t),C(t,i),C(e,n),C(e,s),C(e,o)},p:j,i:j,o:j,d(a){a&&O(e)}}}class ht extends te{constructor(e){super(),ie(this,e,null,dt,ne,{})}}function ct(r){let e,t,i,n,s,o,a,u,c;return{c(){e=F("svg"),t=F("title"),i=me("Muted volume"),n=F("g"),s=F("path"),o=F("path"),a=F("defs"),u=F("clipPath"),c=F("rect"),d(s,"d","M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14"),d(s,"stroke-width","1.5"),d(s,"stroke-linecap","round"),d(s,"stroke-linejoin","round"),d(o,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),d(o,"stroke-width","1.5"),d(n,"clip-path","url(#clip0_3173_16686)"),d(c,"width","24"),d(c,"height","24"),d(c,"fill","white"),d(u,"id","clip0_3173_16686"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"stroke-width","1.5"),d(e,"fill","none"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"stroke","currentColor"),d(e,"color","currentColor")},m(l,h){z(l,e,h),C(e,t),C(t,i),C(e,n),C(n,s),C(n,o),C(e,a),C(a,u),C(u,c)},p:j,i:j,o:j,d(l){l&&O(e)}}}class ft extends te{constructor(e){super(),ie(this,e,null,ct,ne,{})}}var mt=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{c(i.next(l))}catch(h){o(h)}}function u(l){try{c(i.throw(l))}catch(h){o(h)}}function c(l){l.done?s(l.value):n(l.value).then(a,u)}c((i=i.apply(r,e||[])).next())})};function gt(r,e){return mt(this,void 0,void 0,function*(){const t=new AudioContext({sampleRate:e});return t.decodeAudioData(r).finally(()=>t.close())})}function pt(r){const e=r[0];if(e.some(t=>t>1||t<-1)){const t=e.length;let i=0;for(let n=0;n<t;n++){const s=Math.abs(e[n]);s>i&&(i=s)}for(const n of r)for(let s=0;s<t;s++)n[s]/=i}return r}function _t(r,e){return typeof r[0]=="number"&&(r=[r]),pt(r),{duration:e,length:r[0].length,sampleRate:r[0].length/e,numberOfChannels:r.length,getChannelData:t=>r?.[t],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}const Me={decode:gt,createBuffer:_t};var We=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{c(i.next(l))}catch(h){o(h)}}function u(l){try{c(i.throw(l))}catch(h){o(h)}}function c(l){l.done?s(l.value):n(l.value).then(a,u)}c((i=i.apply(r,e||[])).next())})};function vt(r,e,t){var i,n;return We(this,void 0,void 0,function*(){const s=yield fetch(r,t);{const o=(i=s.clone().body)===null||i===void 0?void 0:i.getReader(),a=Number((n=s.headers)===null||n===void 0?void 0:n.get("Content-Length"));let u=0;const c=(l,h)=>We(this,void 0,void 0,function*(){if(l)return;u+=h?.length||0;const _=Math.round(u/a*100);return e(_),o?.read().then(({done:f,value:g})=>c(f,g))});o?.read().then(({done:l,value:h})=>c(l,h))}return s.blob()})}const bt={fetchBlob:vt};class Le{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class wt extends Le{constructor(e){super(),this.isExternalMedia=!1,e.media?(this.media=e.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),e.mediaControls&&(this.media.controls=!0),e.autoplay&&(this.media.autoplay=!0),e.playbackRate!=null&&this.onceMediaEvent("canplay",()=>{e.playbackRate!=null&&(this.media.playbackRate=e.playbackRate)})}onMediaEvent(e,t,i){return this.media.addEventListener(e,t,i),()=>this.media.removeEventListener(e,t)}onceMediaEvent(e,t){return this.onMediaEvent(e,t,{once:!0})}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const e=this.getSrc();e.startsWith("blob:")&&URL.revokeObjectURL(e)}setSrc(e,t){if(this.getSrc()===e)return;this.revokeSrc();const n=t instanceof Blob?URL.createObjectURL(t):e;this.media.src=n,this.media.load()}destroy(){this.media.pause(),!this.isExternalMedia&&(this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(e){this.media=e}play(){return this.media.play()}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(e){this.media.currentTime=e}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(e){this.media.volume=e}getMuted(){return this.media.muted}setMuted(e){this.media.muted=e}getPlaybackRate(){return this.media.playbackRate}setPlaybackRate(e,t){t!=null&&(this.media.preservesPitch=t),this.media.playbackRate=e}getMediaElement(){return this.media}setSinkId(e){return this.media.setSinkId(e)}}function yt(r,e,t,i,n=5){let s=()=>{};if(!r)return s;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),r.style.touchAction="none";let u=a.clientX,c=a.clientY,l=!1;const h=g=>{g.preventDefault(),g.stopPropagation();const k=g.clientX,M=g.clientY;if(l||Math.abs(k-u)>=n||Math.abs(M-c)>=n){const{left:w,top:y}=r.getBoundingClientRect();l||(l=!0,t?.(u-w,c-y)),e(k-u,M-c,k-w,M-y),u=k,c=M}},_=g=>{l&&(g.preventDefault(),g.stopPropagation())},f=()=>{r.style.touchAction="",l&&i?.(),s()};document.addEventListener("pointermove",h),document.addEventListener("pointerup",f),document.addEventListener("pointerleave",f),document.addEventListener("click",_,!0),s=()=>{document.removeEventListener("pointermove",h),document.removeEventListener("pointerup",f),document.removeEventListener("pointerleave",f),setTimeout(()=>{document.removeEventListener("click",_,!0)},10)}};return r.addEventListener("pointerdown",o),()=>{s(),r.removeEventListener("pointerdown",o)}}class Re extends Le{constructor(e,t){super(),this.timeouts=[],this.isScrolling=!1,this.audioData=null,this.resizeObserver=null,this.isDragging=!1,this.options=e;const i=this.parentFromOptionsContainer(e.container);this.parent=i;const[n,s]=this.initHtml();i.appendChild(n),this.container=n,this.scrollContainer=s.querySelector(".scroll"),this.wrapper=s.querySelector(".wrapper"),this.canvasWrapper=s.querySelector(".canvases"),this.progressWrapper=s.querySelector(".progress"),this.cursor=s.querySelector(".cursor"),t&&s.appendChild(t),this.initEvents()}parentFromOptionsContainer(e){let t;if(typeof e=="string"?t=document.querySelector(e):e instanceof HTMLElement&&(t=e),!t)throw new Error("Container not found");return t}initEvents(){const e=i=>{const n=this.wrapper.getBoundingClientRect(),s=i.clientX-n.left,o=i.clientX-n.left,a=s/n.width,u=o/n.height;return[a,u]};this.wrapper.addEventListener("click",i=>{const[n,s]=e(i);this.emit("click",n,s)}),this.wrapper.addEventListener("dblclick",i=>{const[n,s]=e(i);this.emit("dblclick",n,s)}),this.options.dragToSeek&&this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:i,scrollWidth:n,clientWidth:s}=this.scrollContainer,o=i/n,a=(i+s)/n;this.emit("scroll",o,a)});const t=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{t(()=>this.reRender())}),this.resizeObserver.observe(this.scrollContainer)}initDrag(){yt(this.wrapper,(e,t,i)=>{this.emit("drag",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))},()=>this.isDragging=!0,()=>this.isDragging=!1)}getHeight(){return this.options.height==null?128:isNaN(Number(this.options.height))?this.options.height==="auto"&&this.parent.clientHeight||128:Number(this.options.height)}initHtml(){const e=document.createElement("div"),t=e.attachShadow({mode:"open"});return t.innerHTML=`
      <style>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight()}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[e,t]}setOptions(e){if(this.options.container!==e.container){const t=this.parentFromOptionsContainer(e.container);t.appendChild(this.container),this.parent=t}e.dragToSeek&&!this.options.dragToSeek&&this.initDrag(),this.options=e,this.reRender()}getWrapper(){return this.wrapper}getScroll(){return this.scrollContainer.scrollLeft}destroy(){var e;this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect()}createDelay(e=10){const t={};return this.timeouts.push(t),i=>{t.timeout&&clearTimeout(t.timeout),t.timeout=setTimeout(i,e)}}convertColorValues(e){if(!Array.isArray(e))return e||"";if(e.length<2)return e[0]||"";const t=document.createElement("canvas"),n=t.getContext("2d").createLinearGradient(0,0,0,t.height),s=1/(e.length-1);return e.forEach((o,a)=>{const u=a*s;n.addColorStop(u,o)}),n}renderBarWaveform(e,t,i,n){const s=e[0],o=e[1]||e[0],a=s.length,{width:u,height:c}=i.canvas,l=c/2,h=window.devicePixelRatio||1,_=t.barWidth?t.barWidth*h:1,f=t.barGap?t.barGap*h:t.barWidth?_/2:0,g=t.barRadius||0,k=u/(_+f)/a,M=g&&"roundRect"in i?"roundRect":"rect";i.beginPath();let w=0,y=0,T=0;for(let R=0;R<=a;R++){const m=Math.round(R*k);if(m>w){const L=Math.round(y*l*n),W=Math.round(T*l*n),G=L+W||1;let K=l-L;t.barAlign==="top"?K=0:t.barAlign==="bottom"&&(K=c-G),i[M](w*(_+f),K,_,G,g),w=m,y=0,T=0}const A=Math.abs(s[R]||0),v=Math.abs(o[R]||0);A>y&&(y=A),v>T&&(T=v)}i.fill(),i.closePath()}renderLineWaveform(e,t,i,n){const s=o=>{const a=e[o]||e[0],u=a.length,{height:c}=i.canvas,l=c/2,h=i.canvas.width/u;i.moveTo(0,l);let _=0,f=0;for(let g=0;g<=u;g++){const k=Math.round(g*h);if(k>_){const w=Math.round(f*l*n)||1,y=l+w*(o===0?-1:1);i.lineTo(_,y),_=k,f=0}const M=Math.abs(a[g]||0);M>f&&(f=M)}i.lineTo(_,l)};i.beginPath(),s(0),s(1),i.fill(),i.closePath()}renderWaveform(e,t,i){if(i.fillStyle=this.convertColorValues(t.waveColor),t.renderFunction){t.renderFunction(e,i);return}let n=t.barHeight||1;if(t.normalize){const s=Array.from(e[0]).reduce((o,a)=>Math.max(o,Math.abs(a)),0);n=s?1/s:1}if(t.barWidth||t.barGap||t.barAlign){this.renderBarWaveform(e,t,i,n);return}this.renderLineWaveform(e,t,i,n)}renderSingleCanvas(e,t,i,n,s,o,a,u){const c=window.devicePixelRatio||1,l=document.createElement("canvas"),h=e[0].length;l.width=Math.round(i*(o-s)/h),l.height=n*c,l.style.width=`${Math.floor(l.width/c)}px`,l.style.height=`${n}px`,l.style.left=`${Math.floor(s*i/c/h)}px`,a.appendChild(l);const _=l.getContext("2d");if(this.renderWaveform(e.map(f=>f.slice(s,o)),t,_),l.width>0&&l.height>0){const f=l.cloneNode(),g=f.getContext("2d");g.drawImage(l,0,0),g.globalCompositeOperation="source-in",g.fillStyle=this.convertColorValues(t.progressColor),g.fillRect(0,0,l.width,l.height),u.appendChild(f)}}renderChannel(e,t,i){const n=document.createElement("div"),s=this.getHeight();n.style.height=`${s}px`,this.canvasWrapper.style.minHeight=`${s}px`,this.canvasWrapper.appendChild(n);const o=n.cloneNode();this.progressWrapper.appendChild(o);const{scrollLeft:a,scrollWidth:u,clientWidth:c}=this.scrollContainer,l=e[0].length,h=l/u;let _=Math.min(Re.MAX_CANVAS_WIDTH,c);if(t.barWidth||t.barGap){const m=t.barWidth||.5,A=t.barGap||m/2,v=m+A;_%v!==0&&(_=Math.floor(_/v)*v)}const f=Math.floor(Math.abs(a)*h),g=Math.floor(f+_*h),k=g-f,M=(m,A)=>{this.renderSingleCanvas(e,t,i,s,Math.max(0,m),Math.min(A,l),n,o)},w=this.createDelay(),y=this.createDelay(),T=(m,A)=>{M(m,A),m>0&&w(()=>{T(m-k,A-k)})},R=(m,A)=>{M(m,A),A<l&&y(()=>{R(m+k,A+k)})};T(f,g),g<l&&R(g,g+k)}render(e){this.timeouts.forEach(a=>a.timeout&&clearTimeout(a.timeout)),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.wrapper.style.width="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const t=window.devicePixelRatio||1,i=this.scrollContainer.clientWidth,n=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrolling=n>i;const s=this.options.fillParent&&!this.isScrolling,o=(s?i:n)*t;if(this.wrapper.style.width=s?"100%":`${n}px`,this.scrollContainer.style.overflowX=this.isScrolling?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.options.splitChannels)for(let a=0;a<e.numberOfChannels;a++){const u=Object.assign(Object.assign({},this.options),this.options.splitChannels[a]);this.renderChannel([e.getChannelData(a)],u,o)}else{const a=[e.getChannelData(0)];e.numberOfChannels>1&&a.push(e.getChannelData(1)),this.renderChannel(a,this.options,o)}this.audioData=e,this.emit("render")}reRender(){if(!this.audioData)return;const e=this.progressWrapper.clientWidth;this.render(this.audioData);const t=this.progressWrapper.clientWidth;this.scrollContainer.scrollLeft+=t-e}zoom(e){this.options.minPxPerSec=e,this.reRender()}scrollIntoView(e,t=!1){const{clientWidth:i,scrollLeft:n,scrollWidth:s}=this.scrollContainer,o=s*e,a=i/2,u=t&&this.options.autoCenter&&!this.isDragging?a:i;if(o>n+u||o<n)if(this.options.autoCenter&&!this.isDragging){const c=a/20;o-(n+a)>=c&&o<n+i?this.scrollContainer.scrollLeft+=c:this.scrollContainer.scrollLeft=o-a}else this.isDragging?this.scrollContainer.scrollLeft=o<n?o-10:o-i+10:this.scrollContainer.scrollLeft=o;{const{scrollLeft:c}=this.scrollContainer,l=c/s,h=(c+i)/s;this.emit("scroll",l,h)}}renderProgress(e,t){if(isNaN(e))return;const i=e*100;this.canvasWrapper.style.clipPath=`polygon(${i}% 0, 100% 0, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.marginLeft=Math.round(i)===100?`-${this.options.cursorWidth}px`:"",this.isScrolling&&this.options.autoScroll&&this.scrollIntoView(e,t)}}Re.MAX_CANVAS_WIDTH=4e3;class kt extends Le{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}var De=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{c(i.next(l))}catch(h){o(h)}}function u(l){try{c(i.throw(l))}catch(h){o(h)}}function c(l){l.done?s(l.value):n(l.value).then(a,u)}c((i=i.apply(r,e||[])).next())})};class Ct extends Le{constructor(e=new AudioContext){super(),this.bufferNode=null,this.autoplay=!1,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.audioContext=e,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return De(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(e){this.currentSrc=e,fetch(e).then(t=>t.arrayBuffer()).then(t=>this.audioContext.decodeAudioData(t)).then(t=>{this.buffer=t,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play()})}_play(){var e;this.paused&&(this.paused=!1,(e=this.bufferNode)===null||e===void 0||e.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.bufferNode.buffer=this.buffer,this.bufferNode.connect(this.gainNode),this.playedDuration>=this.duration&&(this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,this.playedDuration),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))})}_pause(){var e;this.paused||(this.paused=!0,(e=this.bufferNode)===null||e===void 0||e.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime)}play(){return De(this,void 0,void 0,function*(){this._play(),this.emit("play")})}pause(){this._pause(),this.emit("pause")}setSinkId(e){return De(this,void 0,void 0,function*(){return this.audioContext.setSinkId(e)})}get playbackRate(){var e,t;return(t=(e=this.bufferNode)===null||e===void 0?void 0:e.playbackRate.value)!==null&&t!==void 0?t:1}set playbackRate(e){this.bufferNode&&(this.bufferNode.playbackRate.value=e)}get currentTime(){return this.paused?this.playedDuration:this.playedDuration+this.audioContext.currentTime-this.playStartTime}set currentTime(e){this.emit("seeking"),this.paused?this.playedDuration=e:(this._pause(),this.playedDuration=e,this._play()),this.emit("timeupdate")}get duration(){var e;return((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}get volume(){return this.gainNode.gain.value}set volume(e){this.gainNode.gain.value=e,this.emit("volumechange")}get muted(){return this._muted}set muted(e){this._muted!==e&&(this._muted=e,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}getGainNode(){return this.gainNode}}var ce=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{c(i.next(l))}catch(h){o(h)}}function u(l){try{c(i.throw(l))}catch(h){o(h)}}function c(l){l.done?s(l.value):n(l.value).then(a,u)}c((i=i.apply(r,e||[])).next())})};const Et={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class Pe extends wt{static create(e){return new Pe(e)}constructor(e){const t=e.media||(e.backend==="WebAudio"?new Ct:void 0);super({media:t,mediaControls:e.mediaControls,autoplay:e.autoplay,playbackRate:e.audioRate}),this.plugins=[],this.decodedData=null,this.subscriptions=[],this.mediaSubscriptions=[],this.options=Object.assign({},Et,e),this.timer=new kt;const i=t?void 0:this.getMediaElement();this.renderer=new Re(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const n=this.options.url||this.getSrc();n?this.load(n,this.options.peaks,this.options.duration):this.options.peaks&&this.options.duration&&this.loadPredecoded()}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),!0),this.emit("timeupdate",e),this.emit("audioprocess",e)}))}initPlayerEvents(){this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),this.isPlaying()),this.emit("timeupdate",e)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop()}),this.onMediaEvent("emptied",()=>{this.timer.stop()}),this.onMediaEvent("ended",()=>{this.emit("finish")}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(e,t)=>{this.options.interact&&(this.seekTo(e),this.emit("interaction",e*this.getDuration()),this.emit("click",e,t))}),this.renderer.on("dblclick",(e,t)=>{this.emit("dblclick",e,t)}),this.renderer.on("scroll",(e,t)=>{const i=this.getDuration();this.emit("scroll",e*i,t*i)}),this.renderer.on("render",()=>{this.emit("redraw")}));{let e;this.subscriptions.push(this.renderer.on("drag",t=>{this.options.interact&&(this.renderer.renderProgress(t),clearTimeout(e),e=setTimeout(()=>{this.seekTo(t)},this.isPlaying()?0:200),this.emit("interaction",t*this.getDuration()),this.emit("drag",t))}))}}initPlugins(){var e;!((e=this.options.plugins)===null||e===void 0)&&e.length&&this.options.plugins.forEach(t=>{this.registerPlugin(t)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(e=>e()),this.mediaSubscriptions=[]}setOptions(e){this.options=Object.assign({},this.options,e),this.renderer.setOptions(this.options),e.audioRate&&this.setPlaybackRate(e.audioRate),e.mediaControls!=null&&(this.getMediaElement().controls=e.mediaControls)}registerPlugin(e){return e.init(this),this.plugins.push(e),this.subscriptions.push(e.once("destroy",()=>{this.plugins=this.plugins.filter(t=>t!==e)})),e}getWrapper(){return this.renderer.getWrapper()}getScroll(){return this.renderer.getScroll()}getActivePlugins(){return this.plugins}loadPredecoded(){return ce(this,void 0,void 0,function*(){this.options.peaks&&this.options.duration&&(this.decodedData=Me.createBuffer(this.options.peaks,this.options.duration),yield Promise.resolve(),this.renderDecoded())})}renderDecoded(){return ce(this,void 0,void 0,function*(){this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData))})}loadAudio(e,t,i,n){return ce(this,void 0,void 0,function*(){if(this.emit("load",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,!t&&!i){const s=o=>this.emit("loading",o);t=yield bt.fetchBlob(e,s,this.options.fetchParams)}if(this.setSrc(e,t),n=(yield Promise.resolve(n||this.getDuration()))||(yield new Promise(s=>{this.onceMediaEvent("loadedmetadata",()=>s(this.getDuration()))}))||(yield Promise.resolve(0)),i)this.decodedData=Me.createBuffer(i,n);else if(t){const s=yield t.arrayBuffer();this.decodedData=yield Me.decode(s,this.options.sampleRate)}this.renderDecoded(),this.emit("ready",this.getDuration())})}load(e,t,i){return ce(this,void 0,void 0,function*(){yield this.loadAudio(e,void 0,t,i)})}loadBlob(e,t,i){return ce(this,void 0,void 0,function*(){yield this.loadAudio("blob",e,t,i)})}zoom(e){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(e),this.emit("zoom",e)}getDecodedData(){return this.decodedData}exportPeaks({channels:e=2,maxLength:t=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const n=Math.min(e,this.decodedData.numberOfChannels),s=[];for(let o=0;o<n;o++){const a=this.decodedData.getChannelData(o),u=[],c=Math.round(a.length/t);for(let l=0;l<t;l++){const h=a.slice(l*c,(l+1)*c),_=Math.max(...h);u.push(Math.round(_*i)/i)}s.push(u)}return s}getDuration(){let e=super.getDuration()||0;return(e===0||e===1/0)&&this.decodedData&&(e=this.decodedData.duration),e}toggleInteraction(e){this.options.interact=e}seekTo(e){const t=this.getDuration()*e;this.setTime(t)}playPause(){return ce(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(e){this.setTime(this.getCurrentTime()+e)}empty(){this.load("",[[0]],.001)}setMediaElement(e){this.unsubscribePlayerEvents(),super.setMediaElement(e),this.initPlayerEvents()}destroy(){this.emit("destroy"),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}function Lt(r){const e=r.numberOfChannels,t=r.length*e*2+44,i=new ArrayBuffer(t),n=new DataView(i);let s=0;const o=function(a,u,c){for(let l=0;l<c.length;l++)a.setUint8(u+l,c.charCodeAt(l))};o(n,s,"RIFF"),s+=4,n.setUint32(s,t-8,!0),s+=4,o(n,s,"WAVE"),s+=4,o(n,s,"fmt "),s+=4,n.setUint32(s,16,!0),s+=4,n.setUint16(s,1,!0),s+=2,n.setUint16(s,e,!0),s+=2,n.setUint32(s,r.sampleRate,!0),s+=4,n.setUint32(s,r.sampleRate*2*e,!0),s+=4,n.setUint16(s,e*2,!0),s+=2,n.setUint16(s,16,!0),s+=2,o(n,s,"data"),s+=4,n.setUint32(s,r.length*e*2,!0),s+=4;for(let a=0;a<r.length;a++)for(let u=0;u<e;u++){const c=Math.max(-1,Math.min(1,r.getChannelData(u)[a]));n.setInt16(s,c*32767,!0),s+=2}return new Uint8Array(i)}const Rt=async(r,e,t,i)=>{const n=new AudioContext({sampleRate:i||r.sampleRate}),s=r.numberOfChannels,o=i||r.sampleRate;let a=r.length,u=0;e&&t&&(u=Math.round(e*o),a=Math.round(t*o)-u);const c=n.createBuffer(s,a,o);for(let l=0;l<s;l++){const h=r.getChannelData(l),_=c.getChannelData(l);for(let f=0;f<a;f++)_[f]=h[u+f]}return Lt(c)},ze=(r,e)=>{r&&r.skip(e)},fe=(r,e)=>(e||(e=5),r/100*e||5);class Ge{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class Mt extends Ge{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}function we(r,e,t,i,n=5){let s=()=>{};if(!r)return s;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),r.style.touchAction="none";let u=a.clientX,c=a.clientY,l=!1;const h=g=>{g.preventDefault(),g.stopPropagation();const k=g.clientX,M=g.clientY;if(l||Math.abs(k-u)>=n||Math.abs(M-c)>=n){const{left:w,top:y}=r.getBoundingClientRect();l||(l=!0,t?.(u-w,c-y)),e(k-u,M-c,k-w,M-y),u=k,c=M}},_=g=>{l&&(g.preventDefault(),g.stopPropagation())},f=()=>{r.style.touchAction="",l&&i?.(),s()};document.addEventListener("pointermove",h),document.addEventListener("pointerup",f),document.addEventListener("pointerleave",f),document.addEventListener("click",_,!0),s=()=>{document.removeEventListener("pointermove",h),document.removeEventListener("pointerup",f),document.removeEventListener("pointerleave",f),setTimeout(()=>{document.removeEventListener("click",_,!0)},10)}};return r.addEventListener("pointerdown",o),()=>{s(),r.removeEventListener("pointerdown",o)}}class Oe extends Ge{constructor(e,t,i=0){var n,s,o,a,u,c,l;super(),this.totalDuration=t,this.numberOfChannels=i,this.minLength=0,this.maxLength=1/0,this.id=e.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(e.start),this.end=this.clampPosition((n=e.end)!==null&&n!==void 0?n:e.start),this.drag=(s=e.drag)===null||s===void 0||s,this.resize=(o=e.resize)===null||o===void 0||o,this.color=(a=e.color)!==null&&a!==void 0?a:"rgba(0, 0, 0, 0.1)",this.minLength=(u=e.minLength)!==null&&u!==void 0?u:this.minLength,this.maxLength=(c=e.maxLength)!==null&&c!==void 0?c:this.maxLength,this.channelIdx=(l=e.channelIdx)!==null&&l!==void 0?l:-1,this.element=this.initElement(),this.setContent(e.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(e){return Math.max(0,Math.min(this.totalDuration,e))}setPart(){const e=this.start===this.end;this.element.setAttribute("part",`${e?"marker":"region"} ${this.id}`)}addResizeHandles(e){const t=document.createElement("div");t.setAttribute("data-resize","left"),t.setAttribute("style",`
        position: absolute;
        z-index: 2;
        width: 6px;
        height: 100%;
        top: 0;
        left: 0;
        border-left: 2px solid rgba(0, 0, 0, 0.5);
        border-radius: 2px 0 0 2px;
        cursor: ew-resize;
        word-break: keep-all;
      `),t.setAttribute("part","region-handle region-handle-left");const i=t.cloneNode();i.setAttribute("data-resize","right"),i.style.left="",i.style.right="0",i.style.borderRight=i.style.borderLeft,i.style.borderLeft="",i.style.borderRadius="0 2px 2px 0",i.setAttribute("part","region-handle region-handle-right"),e.appendChild(t),e.appendChild(i),we(t,n=>this.onResize(n,"start"),()=>null,()=>this.onEndResizing(),1),we(i,n=>this.onResize(n,"end"),()=>null,()=>this.onEndResizing(),1)}removeResizeHandles(e){const t=e.querySelector('[data-resize="left"]'),i=e.querySelector('[data-resize="right"]');t&&e.removeChild(t),i&&e.removeChild(i)}initElement(){const e=document.createElement("div"),t=this.start===this.end;let i=0,n=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(n=100/this.numberOfChannels,i=n*this.channelIdx),e.setAttribute("style",`
      position: absolute;
      top: ${i}%;
      height: ${n}%;
      background-color: ${t?"none":this.color};
      border-left: ${t?"2px solid "+this.color:"none"};
      border-radius: 2px;
      box-sizing: border-box;
      transition: background-color 0.2s ease;
      cursor: ${this.drag?"grab":"default"};
      pointer-events: all;
    `),!t&&this.resize&&this.addResizeHandles(e),e}renderPosition(){const e=this.start/this.totalDuration,t=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*e+"%",this.element.style.right=100*t+"%"}initMouseEvents(){const{element:e}=this;e&&(e.addEventListener("click",t=>this.emit("click",t)),e.addEventListener("mouseenter",t=>this.emit("over",t)),e.addEventListener("mouseleave",t=>this.emit("leave",t)),e.addEventListener("dblclick",t=>this.emit("dblclick",t)),we(e,t=>this.onMove(t),()=>this.onStartMoving(),()=>this.onEndMoving()))}onStartMoving(){this.drag&&(this.element.style.cursor="grabbing")}onEndMoving(){this.drag&&(this.element.style.cursor="grab",this.emit("update-end"))}_onUpdate(e,t){if(!this.element.parentElement)return;const i=e/this.element.parentElement.clientWidth*this.totalDuration,n=t&&t!=="start"?this.start:this.start+i,s=t&&t!=="end"?this.end:this.end+i,o=s-n;n>=0&&s<=this.totalDuration&&n<=s&&o>=this.minLength&&o<=this.maxLength&&(this.start=n,this.end=s,this.renderPosition(),this.emit("update"))}onMove(e){this.drag&&this._onUpdate(e)}onResize(e,t){this.resize&&this._onUpdate(e,t)}onEndResizing(){this.resize&&this.emit("update-end")}_setTotalDuration(e){this.totalDuration=e,this.renderPosition()}play(){this.emit("play")}setContent(e){var t;if((t=this.content)===null||t===void 0||t.remove(),e){if(typeof e=="string"){this.content=document.createElement("div");const i=this.start===this.end;this.content.style.padding=`0.2em ${i?.2:.4}em`,this.content.textContent=e}else this.content=e;this.content.setAttribute("part","region-content"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(e){var t,i;if(e.color&&(this.color=e.color,this.element.style.backgroundColor=this.color),e.drag!==void 0&&(this.drag=e.drag,this.element.style.cursor=this.drag?"grab":"default"),e.start!==void 0||e.end!==void 0){const n=this.start===this.end;this.start=this.clampPosition((t=e.start)!==null&&t!==void 0?t:this.start),this.end=this.clampPosition((i=e.end)!==null&&i!==void 0?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(e.content&&this.setContent(e.content),e.id&&(this.id=e.id,this.setPart()),e.resize!==void 0&&e.resize!==this.resize){const n=this.start===this.end;this.resize=e.resize,this.resize&&!n?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit("remove"),this.element.remove(),this.element=null}}class Te extends Mt{constructor(e){super(e),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(e){return new Te(e)}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let e=[];this.subscriptions.push(this.wavesurfer.on("timeupdate",t=>{const i=this.regions.filter(n=>n.start<=t&&n.end>=t);i.forEach(n=>{e.includes(n)||this.emit("region-in",n)}),e.forEach(n=>{i.includes(n)||this.emit("region-out",n)}),e=i}))}initRegionsContainer(){const e=document.createElement("div");return e.setAttribute("style",`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      pointer-events: none;
    `),e}getRegions(){return this.regions}avoidOverlapping(e){if(!e.content)return;const t=e.content,i=t.getBoundingClientRect().left,n=e.element.scrollWidth,s=this.regions.filter(o=>{if(o===e||!o.content)return!1;const a=o.content.getBoundingClientRect().left,u=o.element.scrollWidth;return i<a+u&&a<i+n}).map(o=>{var a;return((a=o.content)===null||a===void 0?void 0:a.getBoundingClientRect().height)||0}).reduce((o,a)=>o+a,0);t.style.marginTop=`${s}px`}saveRegion(e){this.regionsContainer.appendChild(e.element),this.avoidOverlapping(e),this.regions.push(e);const t=[e.on("update-end",()=>{this.avoidOverlapping(e),this.emit("region-updated",e)}),e.on("play",()=>{var i,n;(i=this.wavesurfer)===null||i===void 0||i.play(),(n=this.wavesurfer)===null||n===void 0||n.setTime(e.start)}),e.on("click",i=>{this.emit("region-clicked",e,i)}),e.on("dblclick",i=>{this.emit("region-double-clicked",e,i)}),e.once("remove",()=>{t.forEach(i=>i()),this.regions=this.regions.filter(i=>i!==e)})];this.subscriptions.push(...t),this.emit("region-created",e)}addRegion(e){var t,i;if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const n=this.wavesurfer.getDuration(),s=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getDecodedData())===null||i===void 0?void 0:i.numberOfChannels,o=new Oe(e,n,s);return n?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once("ready",a=>{o._setTotalDuration(a),this.saveRegion(o)})),o}enableDragSelection(e){var t,i;const n=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getWrapper())===null||i===void 0?void 0:i.querySelector("div");if(!n)return()=>{};let s=null,o=0;return we(n,(a,u,c)=>{s&&s._onUpdate(a,c>o?"end":"start")},a=>{var u,c;if(o=a,!this.wavesurfer)return;const l=this.wavesurfer.getDuration(),h=(c=(u=this.wavesurfer)===null||u===void 0?void 0:u.getDecodedData())===null||c===void 0?void 0:c.numberOfChannels,_=this.wavesurfer.getWrapper().clientWidth,f=a/_*l,g=(a+5)/_*l;s=new Oe(Object.assign(Object.assign({},e),{start:f,end:g}),l,h),this.regionsContainer.appendChild(s.element)},()=>{s&&(this.saveRegion(s),s=null)})}clearRegions(){this.regions.forEach(e=>e.remove())}destroy(){this.clearRegions(),super.destroy()}}function Dt(r){let e,t;return e=new ht({}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function St(r){let e,t;return e=new ut({}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function Pt(r){let e,t;return e=new ft({}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function Tt(r){let e,t,i,n;const s=[Pt,St,Dt],o=[];function a(u,c){return u[0]==0?0:u[0]<.5?1:u[0]>=.5?2:-1}return~(e=a(r))&&(t=o[e]=s[e](r)),{c(){t&&t.c(),i=ve()},m(u,c){~e&&o[e].m(u,c),z(u,i,c),n=!0},p(u,[c]){let l=e;e=a(u),e!==l&&(t&&($(),S(o[l],1,1,()=>{o[l]=null}),ee()),~e?(t=o[e],t||(t=o[e]=s[e](u),t.c()),E(t,1),t.m(i.parentNode,i)):t=null)},i(u){n||(E(t),n=!0)},o(u){S(t),n=!1},d(u){u&&O(i),~e&&o[e].d(u)}}}function At(r,e,t){let{currentVolume:i}=e;return r.$$set=n=>{"currentVolume"in n&&t(0,i=n.currentVolume)},[i]}class Wt extends te{constructor(e){super(),ie(this,e,At,Tt,ne,{currentVolume:0})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),P()}}function zt(r){let e,t,i;return{c(){e=H("input"),d(e,"id","volume"),d(e,"class","volume-slider svelte-wuo8j5"),d(e,"type","range"),d(e,"min","0"),d(e,"max","1"),d(e,"step","0.01"),e.value=r[0]},m(n,s){z(n,e,s),r[4](e),t||(i=[x(e,"focusout",r[5]),x(e,"input",r[6])],t=!0)},p(n,[s]){s&1&&(e.value=n[0])},i:j,o:j,d(n){n&&O(e),r[4](null),t=!1,Ee(i)}}}function Ot(r,e,t){let{currentVolume:i=1}=e,{show_volume_slider:n=!1}=e,{waveform:s}=e,o;Ue(()=>{a()});const a=()=>{let h=o;h&&(h.style.background=`linear-gradient(to right, var(--color-accent) ${i*100}%, var(--neutral-400) ${i*100}%)`)};function u(h){J[h?"unshift":"push"](()=>{o=h,t(3,o)})}const c=()=>t(1,n=!1),l=h=>{h.target instanceof HTMLInputElement&&(t(0,i=parseFloat(h.target.value)),s?.setVolume(i))};return r.$$set=h=>{"currentVolume"in h&&t(0,i=h.currentVolume),"show_volume_slider"in h&&t(1,n=h.show_volume_slider),"waveform"in h&&t(2,s=h.waveform)},r.$$.update=()=>{r.$$.dirty&1&&a()},[i,n,s,o,u,c,l]}class Ht extends te{constructor(e){super(),ie(this,e,Ot,zt,ne,{currentVolume:0,show_volume_slider:1,waveform:2})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),P()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),P()}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),P()}}function He(r){let e,t,i,n;function s(u){r[27](u)}function o(u){r[28](u)}let a={waveform:r[2]};return r[12]!==void 0&&(a.currentVolume=r[12]),r[1]!==void 0&&(a.show_volume_slider=r[1]),e=new Ht({props:a}),J.push(()=>pe(e,"currentVolume",s)),J.push(()=>pe(e,"show_volume_slider",o)),{c(){B(e.$$.fragment)},m(u,c){I(e,u,c),n=!0},p(u,c){const l={};c[0]&4&&(l.waveform=u[2]),!t&&c[0]&4096&&(t=!0,l.currentVolume=u[12],_e(()=>t=!1)),!i&&c[0]&2&&(i=!0,l.show_volume_slider=u[1],_e(()=>i=!1)),e.$set(l)},i(u){n||(E(e.$$.fragment,u),n=!0)},o(u){S(e.$$.fragment,u),n=!1},d(u){U(e,u)}}}function Vt(r){let e,t;return e=new tt({}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function Nt(r){let e,t;return e=new $e({}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function Ve(r){let e,t,i,n,s,o=r[6]&&r[0]===""&&Ne(r);const a=[Bt,jt],u=[];function c(l,h){return l[0]===""?0:1}return t=c(r),i=u[t]=a[t](r),{c(){o&&o.c(),e=Y(),i.c(),n=ve()},m(l,h){o&&o.m(l,h),z(l,e,h),u[t].m(l,h),z(l,n,h),s=!0},p(l,h){l[6]&&l[0]===""?o?(o.p(l,h),h[0]&65&&E(o,1)):(o=Ne(l),o.c(),E(o,1),o.m(e.parentNode,e)):o&&($(),S(o,1,1,()=>{o=null}),ee());let _=t;t=c(l),t===_?u[t].p(l,h):($(),S(u[_],1,1,()=>{u[_]=null}),ee(),i=u[t],i?i.p(l,h):(i=u[t]=a[t](l),i.c()),E(i,1),i.m(n.parentNode,n))},i(l){s||(E(o),E(i),s=!0)},o(l){S(o),S(i),s=!1},d(l){l&&(O(e),O(n)),o&&o.d(l),u[t].d(l)}}}function Ne(r){let e,t,i,n,s;return t=new it({}),{c(){e=H("button"),B(t.$$.fragment),d(e,"class","action icon svelte-ije4bl"),d(e,"aria-label","Reset audio")},m(o,a){z(o,e,a),I(t,e,null),i=!0,n||(s=x(e,"click",r[33]),n=!0)},p:j,i(o){i||(E(t.$$.fragment,o),i=!0)},o(o){S(t.$$.fragment,o),i=!1},d(o){o&&O(e),U(t),n=!1,s()}}}function jt(r){let e,t,i,n,s;return{c(){e=H("button"),e.textContent="Trim",t=Y(),i=H("button"),i.textContent="Cancel",d(e,"class","text-button svelte-ije4bl"),d(i,"class","text-button svelte-ije4bl")},m(o,a){z(o,e,a),z(o,t,a),z(o,i,a),n||(s=[x(e,"click",r[14]),x(i,"click",r[16])],n=!0)},p:j,i:j,o:j,d(o){o&&(O(e),O(t),O(i)),n=!1,Ee(s)}}}function Bt(r){let e,t,i,n,s;return t=new et({}),{c(){e=H("button"),B(t.$$.fragment),d(e,"class","action icon svelte-ije4bl"),d(e,"aria-label","Trim audio to selection")},m(o,a){z(o,e,a),I(t,e,null),i=!0,n||(s=x(e,"click",r[16]),n=!0)},p:j,i(o){i||(E(t.$$.fragment,o),i=!0)},o(o){S(t.$$.fragment,o),i=!1},d(o){o&&O(e),U(t),n=!1,s()}}}function It(r){let e,t,i,n,s,o,a,u,c,l,h,_,f,g,k,M,w,y,T,R,m,A,v,L,W,G,K,Z,re,oe;n=new Wt({props:{currentVolume:r[12]}});let V=r[1]&&He(r);k=new rt({});const ae=[Nt,Vt],Q=[];function ue(D,q){return D[5]?0:1}T=ue(r),R=Q[T]=ae[T](r),L=new lt({});let N=r[10]&&r[7]&&Ve(r);return{c(){e=H("div"),t=H("div"),i=H("button"),B(n.$$.fragment),s=Y(),V&&V.c(),o=Y(),a=H("button"),u=H("span"),c=me(r[11]),l=me("x"),_=Y(),f=H("div"),g=H("button"),B(k.$$.fragment),w=Y(),y=H("button"),R.c(),A=Y(),v=H("button"),B(L.$$.fragment),G=Y(),K=H("div"),N&&N.c(),d(i,"class","action icon volume svelte-ije4bl"),d(i,"aria-label","Adjust volume"),ye(i,"color",r[1]?"var(--color-accent)":"var(--neutral-400)"),d(a,"class","playback icon svelte-ije4bl"),d(a,"aria-label",h=`Adjust playback speed to ${r[13][(r[13].indexOf(r[11])+1)%r[13].length]}x`),ke(a,"hidden",r[1]),d(t,"class","control-wrapper svelte-ije4bl"),d(g,"class","rewind icon svelte-ije4bl"),d(g,"aria-label",M=`Skip backwards by ${fe(r[3],r[9].skip_length)} seconds`),d(y,"class","play-pause-button icon svelte-ije4bl"),d(y,"aria-label",m=r[5]?r[4]("audio.pause"):r[4]("audio.play")),d(v,"class","skip icon svelte-ije4bl"),d(v,"aria-label",W="Skip forward by "+fe(r[3],r[9].skip_length)+" seconds"),d(f,"class","play-pause-wrapper svelte-ije4bl"),d(K,"class","settings-wrapper svelte-ije4bl"),d(e,"class","controls svelte-ije4bl"),d(e,"data-testid","waveform-controls")},m(D,q){z(D,e,q),C(e,t),C(t,i),I(n,i,null),C(t,s),V&&V.m(t,null),C(t,o),C(t,a),C(a,u),C(u,c),C(u,l),C(e,_),C(e,f),C(f,g),I(k,g,null),C(f,w),C(f,y),Q[T].m(y,null),C(f,A),C(f,v),I(L,v,null),C(e,G),C(e,K),N&&N.m(K,null),Z=!0,re||(oe=[x(i,"click",r[26]),x(a,"click",r[29]),x(g,"click",r[30]),x(y,"click",r[31]),x(v,"click",r[32])],re=!0)},p(D,q){const de={};q[0]&4096&&(de.currentVolume=D[12]),n.$set(de),q[0]&2&&ye(i,"color",D[1]?"var(--color-accent)":"var(--neutral-400)"),D[1]?V?(V.p(D,q),q[0]&2&&E(V,1)):(V=He(D),V.c(),E(V,1),V.m(t,o)):V&&($(),S(V,1,1,()=>{V=null}),ee()),(!Z||q[0]&2048)&&Fe(c,D[11]),(!Z||q[0]&2048&&h!==(h=`Adjust playback speed to ${D[13][(D[13].indexOf(D[11])+1)%D[13].length]}x`))&&d(a,"aria-label",h),(!Z||q[0]&2)&&ke(a,"hidden",D[1]),(!Z||q[0]&520&&M!==(M=`Skip backwards by ${fe(D[3],D[9].skip_length)} seconds`))&&d(g,"aria-label",M);let b=T;T=ue(D),T!==b&&($(),S(Q[b],1,1,()=>{Q[b]=null}),ee(),R=Q[T],R||(R=Q[T]=ae[T](D),R.c()),E(R,1),R.m(y,null)),(!Z||q[0]&48&&m!==(m=D[5]?D[4]("audio.pause"):D[4]("audio.play")))&&d(y,"aria-label",m),(!Z||q[0]&520&&W!==(W="Skip forward by "+fe(D[3],D[9].skip_length)+" seconds"))&&d(v,"aria-label",W),D[10]&&D[7]?N?(N.p(D,q),q[0]&1152&&E(N,1)):(N=Ve(D),N.c(),E(N,1),N.m(K,null)):N&&($(),S(N,1,1,()=>{N=null}),ee())},i(D){Z||(E(n.$$.fragment,D),E(V),E(k.$$.fragment,D),E(R),E(L.$$.fragment,D),E(N),Z=!0)},o(D){S(n.$$.fragment,D),S(V),S(k.$$.fragment,D),S(R),S(L.$$.fragment,D),S(N),Z=!1},d(D){D&&O(e),U(n),V&&V.d(),U(k),Q[T].d(),U(L),N&&N.d(),re=!1,Ee(oe)}}}function Ut(r,e,t){let{waveform:i}=e,{audio_duration:n}=e,{i18n:s}=e,{playing:o}=e,{show_redo:a=!1}=e,{interactive:u=!1}=e,{handle_trim_audio:c}=e,{mode:l=""}=e,{container:h}=e,{handle_reset_value:_}=e,{waveform_options:f={}}=e,{trim_region_settings:g={}}=e,{show_volume_slider:k=!1}=e,{editable:M=!0}=e,{trimDuration:w=0}=e,y=[.5,1,1.5,2],T=y[1],R=null,m=null,A,v,L="",W=1;const G=()=>{R&&(t(22,m=R?.addRegion({start:n/4,end:n/2,...g})),t(17,w=m.end-m.start))},K=()=>{if(i&&R&&m){const b=m.start,se=m.end;c(b,se),t(0,l=""),t(22,m=null)}},Z=()=>{R?.getRegions().forEach(b=>{b.remove()}),R?.clearRegions()},re=()=>{Z(),l==="edit"?t(0,l=""):(t(0,l="edit"),G())},oe=(b,se)=>{let p,X;m&&(b==="left"?se==="ArrowLeft"?(p=m.start-.05,X=m.end):(p=m.start+.05,X=m.end):se==="ArrowLeft"?(p=m.start,X=m.end-.05):(p=m.start,X=m.end+.05),m.setOptions({start:p,end:X}),t(17,w=m.end-m.start))},V=()=>t(1,k=!k);function ae(b){W=b,t(12,W)}function Q(b){k=b,t(1,k)}const ue=()=>{t(11,T=y[(y.indexOf(T)+1)%y.length]),i?.setPlaybackRate(T)},N=()=>i?.skip(fe(n,f.skip_length)*-1),D=()=>i?.playPause(),q=()=>i?.skip(fe(n,f.skip_length)),de=()=>{_(),Z(),t(0,l="")};return r.$$set=b=>{"waveform"in b&&t(2,i=b.waveform),"audio_duration"in b&&t(3,n=b.audio_duration),"i18n"in b&&t(4,s=b.i18n),"playing"in b&&t(5,o=b.playing),"show_redo"in b&&t(6,a=b.show_redo),"interactive"in b&&t(7,u=b.interactive),"handle_trim_audio"in b&&t(18,c=b.handle_trim_audio),"mode"in b&&t(0,l=b.mode),"container"in b&&t(19,h=b.container),"handle_reset_value"in b&&t(8,_=b.handle_reset_value),"waveform_options"in b&&t(9,f=b.waveform_options),"trim_region_settings"in b&&t(20,g=b.trim_region_settings),"show_volume_slider"in b&&t(1,k=b.show_volume_slider),"editable"in b&&t(10,M=b.editable),"trimDuration"in b&&t(17,w=b.trimDuration)},r.$$.update=()=>{if(r.$$.dirty[0]&524292&&t(21,R=h&&i?i.registerPlugin(Te.create()):null),r.$$.dirty[0]&2097152&&R?.on("region-out",b=>{b.play()}),r.$$.dirty[0]&2097152&&R?.on("region-updated",b=>{t(17,w=b.end-b.start)}),r.$$.dirty[0]&2097152&&R?.on("region-clicked",(b,se)=>{se.stopPropagation(),t(22,m=b),b.play()}),r.$$.dirty[0]&31981568&&m){const b=h.children[0].shadowRoot;t(24,v=b.querySelector('[data-resize="right"]')),t(23,A=b.querySelector('[data-resize="left"]')),A&&v&&(A.setAttribute("role","button"),v.setAttribute("role","button"),A?.setAttribute("aria-label","Drag to adjust start time"),v?.setAttribute("aria-label","Drag to adjust end time"),A?.setAttribute("tabindex","0"),v?.setAttribute("tabindex","0"),A.addEventListener("focus",()=>{R&&t(25,L="left")}),v.addEventListener("focus",()=>{R&&t(25,L="right")}))}r.$$.dirty[0]&35651584&&R&&window.addEventListener("keydown",b=>{b.key==="ArrowLeft"?oe(L,"ArrowLeft"):b.key==="ArrowRight"&&oe(L,"ArrowRight")})},[l,k,i,n,s,o,a,u,_,f,M,T,W,y,K,Z,re,w,c,h,g,R,m,A,v,L,V,ae,Q,ue,N,D,q,de]}class Ft extends te{constructor(e){super(),ie(this,e,Ut,It,ne,{waveform:2,audio_duration:3,i18n:4,playing:5,show_redo:6,interactive:7,handle_trim_audio:18,mode:0,container:19,handle_reset_value:8,waveform_options:9,trim_region_settings:20,show_volume_slider:1,editable:10,trimDuration:17},null,[-1,-1])}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),P()}get audio_duration(){return this.$$.ctx[3]}set audio_duration(e){this.$$set({audio_duration:e}),P()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),P()}get playing(){return this.$$.ctx[5]}set playing(e){this.$$set({playing:e}),P()}get show_redo(){return this.$$.ctx[6]}set show_redo(e){this.$$set({show_redo:e}),P()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),P()}get handle_trim_audio(){return this.$$.ctx[18]}set handle_trim_audio(e){this.$$set({handle_trim_audio:e}),P()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),P()}get container(){return this.$$.ctx[19]}set container(e){this.$$set({container:e}),P()}get handle_reset_value(){return this.$$.ctx[8]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),P()}get waveform_options(){return this.$$.ctx[9]}set waveform_options(e){this.$$set({waveform_options:e}),P()}get trim_region_settings(){return this.$$.ctx[20]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),P()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),P()}get editable(){return this.$$.ctx[10]}set editable(e){this.$$set({editable:e}),P()}get trimDuration(){return this.$$.ctx[17]}set trimDuration(e){this.$$set({trimDuration:e}),P()}}function qt(r){let e,t,i,n,s,o,a,u,c,l,h,_,f,g,k,M,w,y=r[0]==="edit"&&r[16]>0&&je(r);function T(v){r[31](v)}function R(v){r[32](v)}function m(v){r[33](v)}let A={container:r[10],waveform:r[11],playing:r[14],audio_duration:r[15],i18n:r[3],interactive:r[4],handle_trim_audio:r[20],show_redo:r[4],handle_reset_value:r[9],waveform_options:r[8],trim_region_settings:r[6],editable:r[5]};return r[0]!==void 0&&(A.mode=r[0]),r[16]!==void 0&&(A.trimDuration=r[16]),r[17]!==void 0&&(A.show_volume_slider=r[17]),_=new Ft({props:A}),J.push(()=>pe(_,"mode",T)),J.push(()=>pe(_,"trimDuration",R)),J.push(()=>pe(_,"show_volume_slider",m)),{c(){e=H("div"),t=H("div"),i=H("div"),n=Y(),s=H("div"),o=H("time"),o.textContent="0:00",a=Y(),u=H("div"),y&&y.c(),c=Y(),l=H("time"),l.textContent="0:00",h=Y(),B(_.$$.fragment),d(i,"id","waveform"),d(i,"class","svelte-19usgod"),ye(i,"height",r[10]?null:"58px"),d(t,"class","waveform-container svelte-19usgod"),d(o,"id","time"),d(o,"class","svelte-19usgod"),d(l,"id","duration"),d(l,"class","svelte-19usgod"),d(s,"class","timestamps svelte-19usgod"),d(e,"class","component-wrapper svelte-19usgod"),d(e,"data-testid",M=r[2]?"waveform-"+r[2]:"unlabelled-audio")},m(v,L){z(v,e,L),C(e,t),C(t,i),r[28](i),C(e,n),C(e,s),C(s,o),r[29](o),C(s,a),C(s,u),y&&y.m(u,null),C(u,c),C(u,l),r[30](l),C(e,h),I(_,e,null),w=!0},p(v,L){L[0]&1024&&ye(i,"height",v[10]?null:"58px"),v[0]==="edit"&&v[16]>0?y?y.p(v,L):(y=je(v),y.c(),y.m(u,c)):y&&(y.d(1),y=null);const W={};L[0]&1024&&(W.container=v[10]),L[0]&2048&&(W.waveform=v[11]),L[0]&16384&&(W.playing=v[14]),L[0]&32768&&(W.audio_duration=v[15]),L[0]&8&&(W.i18n=v[3]),L[0]&16&&(W.interactive=v[4]),L[0]&16&&(W.show_redo=v[4]),L[0]&512&&(W.handle_reset_value=v[9]),L[0]&256&&(W.waveform_options=v[8]),L[0]&64&&(W.trim_region_settings=v[6]),L[0]&32&&(W.editable=v[5]),!f&&L[0]&1&&(f=!0,W.mode=v[0],_e(()=>f=!1)),!g&&L[0]&65536&&(g=!0,W.trimDuration=v[16],_e(()=>g=!1)),!k&&L[0]&131072&&(k=!0,W.show_volume_slider=v[17],_e(()=>k=!1)),_.$set(W),(!w||L[0]&4&&M!==(M=v[2]?"waveform-"+v[2]:"unlabelled-audio"))&&d(e,"data-testid",M)},i(v){w||(E(_.$$.fragment,v),w=!0)},o(v){S(_.$$.fragment,v),w=!1},d(v){v&&O(e),r[28](null),r[29](null),y&&y.d(),r[30](null),U(_)}}}function Xt(r){let e,t;return e=new Xe({props:{size:"small",$$slots:{default:[Gt]},$$scope:{ctx:r}}}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},p(i,n){const s={};n[1]&128&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function je(r){let e,t=Ce(r[16])+"",i;return{c(){e=H("time"),i=me(t),d(e,"id","trim-duration"),d(e,"class","svelte-19usgod")},m(n,s){z(n,e,s),C(e,i)},p(n,s){s[0]&65536&&t!==(t=Ce(n[16])+"")&&Fe(i,t)},d(n){n&&O(e)}}}function Gt(r){let e,t;return e=new Se({}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function Zt(r){let e,t,i,n,s,o,a,u,c;const l=[Xt,qt],h=[];function _(f,g){return f[1]===null?0:f[1].is_stream?-1:1}return~(n=_(r))&&(s=h[n]=l[n](r)),{c(){e=H("audio"),i=Y(),s&&s.c(),o=ve(),d(e,"class","standard-player svelte-19usgod"),e.controls=!0,e.autoplay=t=r[7].autoplay,ke(e,"hidden",!(r[1]&&r[1].is_stream))},m(f,g){z(f,e,g),r[25](e),z(f,i,g),~n&&h[n].m(f,g),z(f,o,g),a=!0,u||(c=[x(e,"load",r[24]),x(e,"ended",r[26]),x(e,"play",r[27])],u=!0)},p(f,g){(!a||g[0]&128&&t!==(t=f[7].autoplay))&&(e.autoplay=t),(!a||g[0]&2)&&ke(e,"hidden",!(f[1]&&f[1].is_stream));let k=n;n=_(f),n===k?~n&&h[n].p(f,g):(s&&($(),S(h[k],1,1,()=>{h[k]=null}),ee()),~n?(s=h[n],s?s.p(f,g):(s=h[n]=l[n](f),s.c()),E(s,1),s.m(o.parentNode,o)):s=null)},i(f){a||(E(s),a=!0)},o(f){S(s),a=!1},d(f){f&&(O(e),O(i),O(o)),r[25](null),~n&&h[n].d(f),u=!1,Ee(c)}}}function Yt(r,e,t){let i,{value:n=null}=e,{label:s}=e,{i18n:o}=e,{dispatch_blob:a=()=>Promise.resolve()}=e,{interactive:u=!1}=e,{editable:c=!0}=e,{trim_region_settings:l={}}=e,{waveform_settings:h}=e,{waveform_options:_}=e,{mode:f=""}=e,{loop:g}=e,{handle_reset_value:k=()=>{}}=e,M,w,y=!1,T,R,m,A=0,v=!1,L,W=!1;const G=qe(),K=()=>{t(11,w=Pe.create({container:M,...h})),Ae(n?.url).then(p=>{if(p&&w)return w.load(p)})},Z=async(p,X)=>{t(0,f="");const be=w?.getDecodedData();be&&await Rt(be,p,X,h.sampleRate).then(async ge=>{await a([ge],"change"),w?.destroy(),t(10,M.innerHTML="",M)}),G("edit")};async function re(p){W=!1,await Ae(p).then(X=>{if(!(!X||n?.is_stream))return w?.load(X)})}function oe(p){if(!(!p||!p.is_stream||!p.url)&&L)if(he.isSupported()&&!W){const X=new he({maxBufferLength:1,maxMaxBufferLength:1,lowLatencyMode:!0});X.loadSource(p.url),X.attachMedia(L),X.on(he.Events.MANIFEST_PARSED,function(){h.autoplay&&L.play()}),X.on(he.Events.ERROR,function(be,ge){if(console.error("HLS error:",be,ge),ge.fatal)switch(ge.type){case he.ErrorTypes.NETWORK_ERROR:console.error("Fatal network error encountered, trying to recover"),X.startLoad();break;case he.ErrorTypes.MEDIA_ERROR:console.error("Fatal media error encountered, trying to recover"),X.recoverMediaError();break;default:console.error("Fatal error, cannot recover"),X.destroy();break}}),W=!0}else W||(t(18,L.src=p.url,L),h.autoplay&&L.play(),W=!0)}Ue(()=>{window.addEventListener("keydown",p=>{!w||v||(p.key==="ArrowRight"&&f!=="edit"?ze(w,.1):p.key==="ArrowLeft"&&f!=="edit"&&ze(w,-.1))})});function V(p){le.call(this,r,p)}function ae(p){J[p?"unshift":"push"](()=>{L=p,t(18,L)})}const Q=()=>G("stop"),ue=()=>G("play");function N(p){J[p?"unshift":"push"](()=>{M=p,t(10,M),t(1,n),t(11,w)})}function D(p){J[p?"unshift":"push"](()=>{T=p,t(12,T),t(11,w)})}function q(p){J[p?"unshift":"push"](()=>{R=p,t(13,R),t(11,w)})}function de(p){f=p,t(0,f)}function b(p){A=p,t(16,A)}function se(p){v=p,t(17,v)}return r.$$set=p=>{"value"in p&&t(1,n=p.value),"label"in p&&t(2,s=p.label),"i18n"in p&&t(3,o=p.i18n),"dispatch_blob"in p&&t(21,a=p.dispatch_blob),"interactive"in p&&t(4,u=p.interactive),"editable"in p&&t(5,c=p.editable),"trim_region_settings"in p&&t(6,l=p.trim_region_settings),"waveform_settings"in p&&t(7,h=p.waveform_settings),"waveform_options"in p&&t(8,_=p.waveform_options),"mode"in p&&t(0,f=p.mode),"loop"in p&&t(22,g=p.loop),"handle_reset_value"in p&&t(9,k=p.handle_reset_value)},r.$$.update=()=>{r.$$.dirty[0]&2&&t(23,i=n?.url),r.$$.dirty[0]&3074&&!n?.is_stream&&M!==void 0&&M!==null&&(w!==void 0&&w.destroy(),t(10,M.innerHTML="",M),K(),t(14,y=!1)),r.$$.dirty[0]&10240&&w?.on("decode",p=>{t(15,m=p),R&&t(13,R.textContent=Ce(p),R)}),r.$$.dirty[0]&6144&&w?.on("timeupdate",p=>T&&t(12,T.textContent=Ce(p),T)),r.$$.dirty[0]&2176&&w?.on("ready",()=>{h.autoplay?w?.play():w?.stop()}),r.$$.dirty[0]&4196352&&w?.on("finish",()=>{g?w?.play():(t(14,y=!1),G("stop"))}),r.$$.dirty[0]&2048&&w?.on("pause",()=>{t(14,y=!1),G("pause")}),r.$$.dirty[0]&2048&&w?.on("play",()=>{t(14,y=!0),G("play")}),r.$$.dirty[0]&2048&&w?.on("load",()=>{G("load")}),r.$$.dirty[0]&8388608&&i&&re(i),r.$$.dirty[0]&2&&oe(n)},[f,n,s,o,u,c,l,h,_,k,M,w,T,R,y,m,A,v,L,G,Z,a,g,i,V,ae,Q,ue,N,D,q,de,b,se]}class xt extends te{constructor(e){super(),ie(this,e,Yt,Zt,ne,{value:1,label:2,i18n:3,dispatch_blob:21,interactive:4,editable:5,trim_region_settings:6,waveform_settings:7,waveform_options:8,mode:0,loop:22,handle_reset_value:9},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),P()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),P()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),P()}get dispatch_blob(){return this.$$.ctx[21]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),P()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),P()}get editable(){return this.$$.ctx[5]}set editable(e){this.$$set({editable:e}),P()}get trim_region_settings(){return this.$$.ctx[6]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),P()}get waveform_settings(){return this.$$.ctx[7]}set waveform_settings(e){this.$$set({waveform_settings:e}),P()}get waveform_options(){return this.$$.ctx[8]}set waveform_options(e){this.$$set({waveform_options:e}),P()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),P()}get loop(){return this.$$.ctx[22]}set loop(e){this.$$set({loop:e}),P()}get handle_reset_value(){return this.$$.ctx[9]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),P()}}const Kt=xt;function Qt(r){let e,t;return e=new Xe({props:{size:"small",$$slots:{default:[$t]},$$scope:{ctx:r}}}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},p(i,n){const s={};n&262144&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function Jt(r){let e,t,i,n;return e=new Je({props:{$$slots:{default:[ti]},$$scope:{ctx:r}}}),i=new Kt({props:{value:r[0],label:r[1],i18n:r[5],waveform_settings:r[6],waveform_options:r[7],editable:r[8],loop:r[9]}}),i.$on("pause",r[13]),i.$on("play",r[14]),i.$on("stop",r[15]),i.$on("load",r[16]),{c(){B(e.$$.fragment),t=Y(),B(i.$$.fragment)},m(s,o){I(e,s,o),z(s,t,o),I(i,s,o),n=!0},p(s,o){const a={};o&262201&&(a.$$scope={dirty:o,ctx:s}),e.$set(a);const u={};o&1&&(u.value=s[0]),o&2&&(u.label=s[1]),o&32&&(u.i18n=s[5]),o&64&&(u.waveform_settings=s[6]),o&128&&(u.waveform_options=s[7]),o&256&&(u.editable=s[8]),o&512&&(u.loop=s[9]),i.$set(u)},i(s){n||(E(e.$$.fragment,s),E(i.$$.fragment,s),n=!0)},o(s){S(e.$$.fragment,s),S(i.$$.fragment,s),n=!1},d(s){s&&O(t),U(e,s),U(i,s)}}}function $t(r){let e,t;return e=new Se({}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function Be(r){let e,t;return e=new nt({props:{href:r[0].is_stream?r[0].url?.replace("playlist.m3u8","playlist-file"):r[0].url,download:r[0].orig_name||r[0].path,$$slots:{default:[ei]},$$scope:{ctx:r}}}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},p(i,n){const s={};n&1&&(s.href=i[0].is_stream?i[0].url?.replace("playlist.m3u8","playlist-file"):i[0].url),n&1&&(s.download=i[0].orig_name||i[0].path),n&262176&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function ei(r){let e,t;return e=new Ze({props:{Icon:Qe,label:r[5]("common.download")}}),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},p(i,n){const s={};n&32&&(s.label=i[5]("common.download")),e.$set(s)},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function Ie(r){let e,t;return e=new Ke({props:{i18n:r[5],formatter:r[10],value:r[0]}}),e.$on("error",r[11]),e.$on("share",r[12]),{c(){B(e.$$.fragment)},m(i,n){I(e,i,n),t=!0},p(i,n){const s={};n&32&&(s.i18n=i[5]),n&1&&(s.value=i[0]),e.$set(s)},i(i){t||(E(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){U(e,i)}}}function ti(r){let e,t,i,n=r[3]&&Be(r),s=r[4]&&Ie(r);return{c(){n&&n.c(),e=Y(),s&&s.c(),t=ve()},m(o,a){n&&n.m(o,a),z(o,e,a),s&&s.m(o,a),z(o,t,a),i=!0},p(o,a){o[3]?n?(n.p(o,a),a&8&&E(n,1)):(n=Be(o),n.c(),E(n,1),n.m(e.parentNode,e)):n&&($(),S(n,1,1,()=>{n=null}),ee()),o[4]?s?(s.p(o,a),a&16&&E(s,1)):(s=Ie(o),s.c(),E(s,1),s.m(t.parentNode,t)):s&&($(),S(s,1,1,()=>{s=null}),ee())},i(o){i||(E(n),E(s),i=!0)},o(o){S(n),S(s),i=!1},d(o){o&&(O(e),O(t)),n&&n.d(o),s&&s.d(o)}}}function ii(r){let e,t,i,n,s,o;e=new xe({props:{show_label:r[2],Icon:Se,float:!1,label:r[1]||r[5]("audio.audio")}});const a=[Jt,Qt],u=[];function c(l,h){return l[0]!==null?0:1}return i=c(r),n=u[i]=a[i](r),{c(){B(e.$$.fragment),t=Y(),n.c(),s=ve()},m(l,h){I(e,l,h),z(l,t,h),u[i].m(l,h),z(l,s,h),o=!0},p(l,[h]){const _={};h&4&&(_.show_label=l[2]),h&34&&(_.label=l[1]||l[5]("audio.audio")),e.$set(_);let f=i;i=c(l),i===f?u[i].p(l,h):($(),S(u[f],1,1,()=>{u[f]=null}),ee(),n=u[i],n?n.p(l,h):(n=u[i]=a[i](l),n.c()),E(n,1),n.m(s.parentNode,s))},i(l){o||(E(e.$$.fragment,l),E(n),o=!0)},o(l){S(e.$$.fragment,l),S(n),o=!1},d(l){l&&(O(t),O(s)),U(e,l),u[i].d(l)}}}function ni(r,e,t){let{value:i=null}=e,{label:n}=e,{show_label:s=!0}=e,{show_download_button:o=!0}=e,{show_share_button:a=!1}=e,{i18n:u}=e,{waveform_settings:c}=e,{waveform_options:l}=e,{editable:h=!0}=e,{loop:_}=e;const f=qe(),g=async m=>m?`<audio controls src="${await Ye(m.url)}"></audio>`:"";function k(m){le.call(this,r,m)}function M(m){le.call(this,r,m)}function w(m){le.call(this,r,m)}function y(m){le.call(this,r,m)}function T(m){le.call(this,r,m)}function R(m){le.call(this,r,m)}return r.$$set=m=>{"value"in m&&t(0,i=m.value),"label"in m&&t(1,n=m.label),"show_label"in m&&t(2,s=m.show_label),"show_download_button"in m&&t(3,o=m.show_download_button),"show_share_button"in m&&t(4,a=m.show_share_button),"i18n"in m&&t(5,u=m.i18n),"waveform_settings"in m&&t(6,c=m.waveform_settings),"waveform_options"in m&&t(7,l=m.waveform_options),"editable"in m&&t(8,h=m.editable),"loop"in m&&t(9,_=m.loop)},r.$$.update=()=>{r.$$.dirty&1&&i&&f("change",i)},[i,n,s,o,a,u,c,l,h,_,g,k,M,w,y,T,R]}class si extends te{constructor(e){super(),ie(this,e,ni,ii,ne,{value:0,label:1,show_label:2,show_download_button:3,show_share_button:4,i18n:5,waveform_settings:6,waveform_options:7,editable:8,loop:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),P()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),P()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),P()}get show_download_button(){return this.$$.ctx[3]}set show_download_button(e){this.$$set({show_download_button:e}),P()}get show_share_button(){return this.$$.ctx[4]}set show_share_button(e){this.$$set({show_share_button:e}),P()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),P()}get waveform_settings(){return this.$$.ctx[6]}set waveform_settings(e){this.$$set({waveform_settings:e}),P()}get waveform_options(){return this.$$.ctx[7]}set waveform_options(e){this.$$set({waveform_options:e}),P()}get editable(){return this.$$.ctx[8]}set editable(e){this.$$set({editable:e}),P()}get loop(){return this.$$.ctx[9]}set loop(e){this.$$set({loop:e}),P()}}const bi=Object.freeze(Object.defineProperty({__proto__:null,default:si},Symbol.toStringTag,{value:"Module"}));export{Kt as A,si as S,Ft as W,Pe as a,bi as b,Rt as p,ze as s};
//# sourceMappingURL=StaticAudio-Bzja1Z2P.js.map
