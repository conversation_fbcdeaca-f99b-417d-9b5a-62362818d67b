<script context="module">export { default as BaseGallery } from "./shared/Gallery.svelte";
</script>

<script>import { Block, UploadText } from "@gradio/atoms";
import Gallery from "./shared/Gallery.svelte";
import { StatusTracker } from "@gradio/statustracker";
import { createEventDispatcher } from "svelte";
import { BaseFileUpload } from "@gradio/file";
export let loading_status;
export let show_label;
export let label;
export let root;
export let elem_id = "";
export let elem_classes = [];
export let visible = true;
export let value = null;
export let file_types = ["image", "video"];
export let container = true;
export let scale = null;
export let min_width = void 0;
export let columns = [2];
export let rows = void 0;
export let height = "auto";
export let preview;
export let allow_preview = true;
export let selected_index = null;
export let object_fit = "cover";
export let show_share_button = false;
export let interactive;
export let show_download_button = false;
export let gradio;
export let show_fullscreen_button = true;
const dispatch = createEventDispatcher();
$:
  no_value = value === null ? true : value.length === 0;
$:
  selected_index, dispatch("prop_change", { selected_index });
</script>

<Block
	{visible}
	variant="solid"
	padding={false}
	{elem_id}
	{elem_classes}
	{container}
	{scale}
	{min_width}
	allow_overflow={false}
	height={typeof height === "number" ? height : undefined}
>
	<StatusTracker
		autoscroll={gradio.autoscroll}
		i18n={gradio.i18n}
		{...loading_status}
		on:clear_status={() => gradio.dispatch("clear_status", loading_status)}
	/>
	{#if interactive && no_value}
		<BaseFileUpload
			value={null}
			{root}
			{label}
			max_file_size={gradio.max_file_size}
			file_count={"multiple"}
			{file_types}
			i18n={gradio.i18n}
			upload={(...args) => gradio.client.upload(...args)}
			stream_handler={(...args) => gradio.client.stream(...args)}
			on:upload={(e) => {
				const files = Array.isArray(e.detail) ? e.detail : [e.detail];
				value = files.map((x) =>
					x.mime_type?.includes("video")
						? { video: x, caption: null }
						: { image: x, caption: null }
				);
				gradio.dispatch("upload", value);
			}}
			on:error={({ detail }) => {
				loading_status = loading_status || {};
				loading_status.status = "error";
				gradio.dispatch("error", detail);
			}}
		>
			<UploadText i18n={gradio.i18n} type="gallery" />
		</BaseFileUpload>
	{:else}
		<Gallery
			on:change={() => gradio.dispatch("change", value)}
			on:select={(e) => gradio.dispatch("select", e.detail)}
			on:share={(e) => gradio.dispatch("share", e.detail)}
			on:error={(e) => gradio.dispatch("error", e.detail)}
			{label}
			{show_label}
			{columns}
			{rows}
			{height}
			{preview}
			{object_fit}
			{interactive}
			{allow_preview}
			bind:selected_index
			bind:value
			{show_share_button}
			{show_download_button}
			i18n={gradio.i18n}
			_fetch={(...args) => gradio.client.fetch(...args)}
			{show_fullscreen_button}
		/>
	{/if}
</Block>
