import{a as B,i as U,s as I,Q as g,q as s,l as v,v as b,w as k,o as $,f as q,E as V,t as d,y as S,b as p,z as y,p as M,x as j,c as N,r as C,m as P,F as T,d as W}from"../lite.js";import{U as D,I as G}from"./Upload-D_TzP4YC.js";function H(c){let e,l,t,i,a;return{c(){e=g("svg"),l=g("path"),t=g("path"),i=g("line"),a=g("line"),s(l,"d","M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"),s(t,"d","M19 10v2a7 7 0 0 1-14 0v-2"),s(i,"x1","12"),s(i,"y1","19"),s(i,"x2","12"),s(i,"y2","23"),s(a,"x1","8"),s(a,"y1","23"),s(a,"x2","16"),s(a,"y2","23"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"width","100%"),s(e,"height","100%"),s(e,"viewBox","0 0 24 24"),s(e,"fill","none"),s(e,"stroke","currentColor"),s(e,"stroke-width","2"),s(e,"stroke-linecap","round"),s(e,"stroke-linejoin","round"),s(e,"class","feather feather-mic")},m(n,o){v(n,e,o),b(e,l),b(e,t),b(e,i),b(e,a)},p:k,i:k,o:k,d(n){n&&$(e)}}}class J extends B{constructor(e){super(),U(this,e,null,H,I,{})}}function K(c){let e,l,t;return{c(){e=g("svg"),l=g("path"),t=g("path"),s(l,"fill","currentColor"),s(l,"d","M12 2c-4.963 0-9 4.038-9 9c0 3.328 1.82 6.232 4.513 7.79l-2.067 1.378A1 1 0 0 0 6 22h12a1 1 0 0 0 .555-1.832l-2.067-1.378C19.18 17.232 21 14.328 21 11c0-4.962-4.037-9-9-9zm0 16c-3.859 0-7-3.141-7-7c0-3.86 3.141-7 7-7s7 3.14 7 7c0 3.859-3.141 7-7 7z"),s(t,"fill","currentColor"),s(t,"d","M12 6c-2.757 0-5 2.243-5 5s2.243 5 5 5s5-2.243 5-5s-2.243-5-5-5zm0 8c-1.654 0-3-1.346-3-3s1.346-3 3-3s3 1.346 3 3s-1.346 3-3 3z"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"width","100%"),s(e,"height","100%"),s(e,"viewBox","0 0 24 24")},m(i,a){v(i,e,a),b(e,l),b(e,t)},p:k,i:k,o:k,d(i){i&&$(e)}}}class L extends B{constructor(e){super(),U(this,e,null,K,I,{})}}function O(c){let e,l,t;return{c(){e=g("svg"),l=g("circle"),t=g("animateTransform"),s(t,"attributeName","transform"),s(t,"type","rotate"),s(t,"from","0 25 25"),s(t,"to","360 25 25"),s(t,"repeatCount","indefinite"),s(l,"cx","25"),s(l,"cy","25"),s(l,"r","20"),s(l,"fill","none"),s(l,"stroke-width","3.0"),s(l,"stroke-linecap","round"),s(l,"stroke-dasharray","94.2477796076938 94.2477796076938"),s(l,"stroke-dashoffset","0"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"width","100%"),s(e,"height","100%"),s(e,"viewBox","0 0 50 50"),s(e,"class","svelte-184ngxt")},m(i,a){v(i,e,a),b(e,l),b(l,t)},p:k,i:k,o:k,d(i){i&&$(e)}}}class ee extends B{constructor(e){super(),U(this,e,null,O,I,{})}}function A(c){let e,l=c[1].includes("upload"),t,i=c[1].includes("microphone"),a,n=c[1].includes("webcam"),o,w=c[1].includes("clipboard"),z,f=l&&E(c),u=i&&F(c),m=n&&Q(c),r=w&&R(c);return{c(){e=M("span"),f&&f.c(),t=j(),u&&u.c(),a=j(),m&&m.c(),o=j(),r&&r.c(),s(e,"class","source-selection svelte-snayfm"),s(e,"data-testid","source-select")},m(h,_){v(h,e,_),f&&f.m(e,null),b(e,t),u&&u.m(e,null),b(e,a),m&&m.m(e,null),b(e,o),r&&r.m(e,null),z=!0},p(h,_){_&2&&(l=h[1].includes("upload")),l?f?(f.p(h,_),_&2&&d(f,1)):(f=E(h),f.c(),d(f,1),f.m(e,t)):f&&(S(),p(f,1,1,()=>{f=null}),y()),_&2&&(i=h[1].includes("microphone")),i?u?(u.p(h,_),_&2&&d(u,1)):(u=F(h),u.c(),d(u,1),u.m(e,a)):u&&(S(),p(u,1,1,()=>{u=null}),y()),_&2&&(n=h[1].includes("webcam")),n?m?(m.p(h,_),_&2&&d(m,1)):(m=Q(h),m.c(),d(m,1),m.m(e,o)):m&&(S(),p(m,1,1,()=>{m=null}),y()),_&2&&(w=h[1].includes("clipboard")),w?r?(r.p(h,_),_&2&&d(r,1)):(r=R(h),r.c(),d(r,1),r.m(e,null)):r&&(S(),p(r,1,1,()=>{r=null}),y())},i(h){z||(d(f),d(u),d(m),d(r),z=!0)},o(h){p(f),p(u),p(m),p(r),z=!1},d(h){h&&$(e),f&&f.d(),u&&u.d(),m&&m.d(),r&&r.d()}}}function E(c){let e,l,t,i,a;return l=new D({}),{c(){e=M("button"),N(l.$$.fragment),s(e,"class","icon svelte-snayfm"),s(e,"aria-label","Upload file"),C(e,"selected",c[0]==="upload"||!c[0])},m(n,o){v(n,e,o),P(l,e,null),t=!0,i||(a=T(e,"click",c[6]),i=!0)},p(n,o){(!t||o&1)&&C(e,"selected",n[0]==="upload"||!n[0])},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){p(l.$$.fragment,n),t=!1},d(n){n&&$(e),W(l),i=!1,a()}}}function F(c){let e,l,t,i,a;return l=new J({}),{c(){e=M("button"),N(l.$$.fragment),s(e,"class","icon svelte-snayfm"),s(e,"aria-label","Record audio"),C(e,"selected",c[0]==="microphone")},m(n,o){v(n,e,o),P(l,e,null),t=!0,i||(a=T(e,"click",c[7]),i=!0)},p(n,o){(!t||o&1)&&C(e,"selected",n[0]==="microphone")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){p(l.$$.fragment,n),t=!1},d(n){n&&$(e),W(l),i=!1,a()}}}function Q(c){let e,l,t,i,a;return l=new L({}),{c(){e=M("button"),N(l.$$.fragment),s(e,"class","icon svelte-snayfm"),s(e,"aria-label","Capture from camera"),C(e,"selected",c[0]==="webcam")},m(n,o){v(n,e,o),P(l,e,null),t=!0,i||(a=T(e,"click",c[8]),i=!0)},p(n,o){(!t||o&1)&&C(e,"selected",n[0]==="webcam")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){p(l.$$.fragment,n),t=!1},d(n){n&&$(e),W(l),i=!1,a()}}}function R(c){let e,l,t,i,a;return l=new G({}),{c(){e=M("button"),N(l.$$.fragment),s(e,"class","icon svelte-snayfm"),s(e,"aria-label","Paste from clipboard"),C(e,"selected",c[0]==="clipboard")},m(n,o){v(n,e,o),P(l,e,null),t=!0,i||(a=T(e,"click",c[9]),i=!0)},p(n,o){(!t||o&1)&&C(e,"selected",n[0]==="clipboard")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){p(l.$$.fragment,n),t=!1},d(n){n&&$(e),W(l),i=!1,a()}}}function X(c){let e,l,t=c[2].length>1&&A(c);return{c(){t&&t.c(),e=V()},m(i,a){t&&t.m(i,a),v(i,e,a),l=!0},p(i,[a]){i[2].length>1?t?(t.p(i,a),a&4&&d(t,1)):(t=A(i),t.c(),d(t,1),t.m(e.parentNode,e)):t&&(S(),p(t,1,1,()=>{t=null}),y())},i(i){l||(d(t),l=!0)},o(i){p(t),l=!1},d(i){i&&$(e),t&&t.d(i)}}}function Y(c,e,l){let t,{sources:i}=e,{active_source:a}=e,{handle_clear:n=()=>{}}=e,{handle_select:o=()=>{}}=e;async function w(r){n(),l(0,a=r),o(r)}const z=()=>w("upload"),f=()=>w("microphone"),u=()=>w("webcam"),m=()=>w("clipboard");return c.$$set=r=>{"sources"in r&&l(1,i=r.sources),"active_source"in r&&l(0,a=r.active_source),"handle_clear"in r&&l(4,n=r.handle_clear),"handle_select"in r&&l(5,o=r.handle_select)},c.$$.update=()=>{c.$$.dirty&2&&l(2,t=[...new Set(i)])},[a,i,t,w,n,o,z,f,u,m]}class te extends B{constructor(e){super(),U(this,e,Y,X,I,{sources:1,active_source:0,handle_clear:4,handle_select:5})}get sources(){return this.$$.ctx[1]}set sources(e){this.$$set({sources:e}),q()}get active_source(){return this.$$.ctx[0]}set active_source(e){this.$$set({active_source:e}),q()}get handle_clear(){return this.$$.ctx[4]}set handle_clear(e){this.$$set({handle_clear:e}),q()}get handle_select(){return this.$$.ctx[5]}set handle_select(e){this.$$set({handle_select:e}),q()}}export{te as S,L as W,ee as a};
//# sourceMappingURL=SelectSource-CfNi7fcQ.js.map
