import{a as m,i as g,s as b,f as d,e as h,p as f,q as _,$ as u,r as c,l as v,v as p,u as j,h as w,j as k,t as q,b as x,o as C}from"../lite.js";function I(n){let e,t,o,a;const r=n[4].default,i=h(r,n,n[3],null);return{c(){e=f("div"),t=f("div"),i&&i.c(),_(t,"class","styler svelte-1nguped"),u(t,"--block-radius","0px"),u(t,"--block-border-width","0px"),u(t,"--layout-gap","1px"),u(t,"--form-gap-width","1px"),u(t,"--button-border-width","0px"),u(t,"--button-large-radius","0px"),u(t,"--button-small-radius","0px"),_(e,"id",n[0]),_(e,"class",o="gr-group "+n[1].join(" ")+" svelte-1nguped"),c(e,"hide",!n[2])},m(s,l){v(s,e,l),p(e,t),i&&i.m(t,null),a=!0},p(s,[l]){i&&i.p&&(!a||l&8)&&j(i,r,s,s[3],a?k(r,s[3],l,null):w(s[3]),null),(!a||l&1)&&_(e,"id",s[0]),(!a||l&2&&o!==(o="gr-group "+s[1].join(" ")+" svelte-1nguped"))&&_(e,"class",o),(!a||l&6)&&c(e,"hide",!s[2])},i(s){a||(q(i,s),a=!0)},o(s){x(i,s),a=!1},d(s){s&&C(e),i&&i.d(s)}}}function S(n,e,t){let{$$slots:o={},$$scope:a}=e,{elem_id:r=""}=e,{elem_classes:i=[]}=e,{visible:s=!0}=e;return n.$$set=l=>{"elem_id"in l&&t(0,r=l.elem_id),"elem_classes"in l&&t(1,i=l.elem_classes),"visible"in l&&t(2,s=l.visible),"$$scope"in l&&t(3,a=l.$$scope)},[r,i,s,a,o]}class z extends m{constructor(e){super(),g(this,e,S,I,b,{elem_id:0,elem_classes:1,visible:2})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),d()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),d()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),d()}}export{z as default};
//# sourceMappingURL=Index-dGbp-6_G.js.map
