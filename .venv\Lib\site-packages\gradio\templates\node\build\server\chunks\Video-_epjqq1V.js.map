{"version": 3, "file": "Video-_epjqq1V.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Video.js"], "sourcesContent": ["import { create_ssr_component, add_attribute } from \"svelte/internal\";\nimport { createEventDispatcher } from \"svelte\";\nimport { r as resolve_wasm_src } from \"./DownloadLink.js\";\nimport { H as Hls } from \"./hls.js\";\nconst css = {\n  code: \".overlay.svelte-1y0s5gv{position:absolute;background-color:rgba(0, 0, 0, 0.4);width:100%;height:100%}.hidden.svelte-1y0s5gv{display:none}.load-wrap.svelte-1y0s5gv{display:flex;justify-content:center;align-items:center;height:100%}.loader.svelte-1y0s5gv{display:flex;position:relative;background-color:var(--border-color-accent-subdued);animation:svelte-1y0s5gv-shadowPulse 2s linear infinite;box-shadow:-24px 0 var(--border-color-accent-subdued),\\n\t\t\t24px 0 var(--border-color-accent-subdued);margin:var(--spacing-md);border-radius:50%;width:10px;height:10px;scale:0.5}@keyframes svelte-1y0s5gv-shadowPulse{33%{box-shadow:-24px 0 var(--border-color-accent-subdued),\\n\t\t\t\t24px 0 #fff;background:#fff}66%{box-shadow:-24px 0 #fff,\\n\t\t\t\t24px 0 #fff;background:var(--border-color-accent-subdued)}100%{box-shadow:-24px 0 #fff,\\n\t\t\t\t24px 0 var(--border-color-accent-subdued);background:#fff}}\",\n  map: '{\"version\":3,\"file\":\"Video.svelte\",\"sources\":[\"Video.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport { loaded } from \\\\\"./utils\\\\\";\\\\nimport { resolve_wasm_src } from \\\\\"@gradio/wasm/svelte\\\\\";\\\\nimport Hls from \\\\\"hls.js\\\\\";\\\\nexport let src = void 0;\\\\nexport let muted = void 0;\\\\nexport let playsinline = void 0;\\\\nexport let preload = void 0;\\\\nexport let autoplay = void 0;\\\\nexport let controls = void 0;\\\\nexport let currentTime = void 0;\\\\nexport let duration = void 0;\\\\nexport let paused = void 0;\\\\nexport let node = void 0;\\\\nexport let loop;\\\\nexport let is_stream;\\\\nexport let processingVideo = false;\\\\nlet resolved_src;\\\\nlet stream_active = false;\\\\nlet latest_src;\\\\n$: {\\\\n    resolved_src = src;\\\\n    latest_src = src;\\\\n    const resolving_src = src;\\\\n    resolve_wasm_src(resolving_src).then((s) => {\\\\n        if (latest_src === resolving_src) {\\\\n            resolved_src = s;\\\\n        }\\\\n    });\\\\n}\\\\nconst dispatch = createEventDispatcher();\\\\nfunction load_stream(src2, is_stream2, node2) {\\\\n    if (!src2 || !is_stream2)\\\\n        return;\\\\n    if (!node2)\\\\n        return;\\\\n    if (Hls.isSupported() && !stream_active) {\\\\n        const hls = new Hls({\\\\n            maxBufferLength: 1,\\\\n            // 0.5 seconds (500 ms)\\\\n            maxMaxBufferLength: 1,\\\\n            // Maximum max buffer length in seconds\\\\n            lowLatencyMode: true\\\\n            // Enable low latency mode\\\\n        });\\\\n        hls.loadSource(src2);\\\\n        hls.attachMedia(node2);\\\\n        hls.on(Hls.Events.MANIFEST_PARSED, function () {\\\\n            node2.play();\\\\n        });\\\\n        hls.on(Hls.Events.ERROR, function (event, data) {\\\\n            console.error(\\\\\"HLS error:\\\\\", event, data);\\\\n            if (data.fatal) {\\\\n                switch (data.type) {\\\\n                    case Hls.ErrorTypes.NETWORK_ERROR:\\\\n                        console.error(\\\\\"Fatal network error encountered, trying to recover\\\\\");\\\\n                        hls.startLoad();\\\\n                        break;\\\\n                    case Hls.ErrorTypes.MEDIA_ERROR:\\\\n                        console.error(\\\\\"Fatal media error encountered, trying to recover\\\\\");\\\\n                        hls.recoverMediaError();\\\\n                        break;\\\\n                    default:\\\\n                        console.error(\\\\\"Fatal error, cannot recover\\\\\");\\\\n                        hls.destroy();\\\\n                        break;\\\\n                }\\\\n            }\\\\n        });\\\\n        stream_active = true;\\\\n    }\\\\n}\\\\n$: src, stream_active = false;\\\\n$: load_stream(src, is_stream, node);\\\\n<\\/script>\\\\n\\\\n<!--\\\\nThe spread operator with `$$props` or `$$restProps` can\\'t be used here\\\\nto pass props from the parent component to the <video> element\\\\nbecause of its unexpected behavior: https://github.com/sveltejs/svelte/issues/7404\\\\nFor example, if we add {...$$props} or {...$$restProps}, the boolean props aside it like `controls` will be compiled as string \\\\\"true\\\\\" or \\\\\"false\\\\\" on the actual DOM.\\\\nThen, even when `controls` is false, the compiled DOM would be `<video controls=\\\\\"false\\\\\">` which is equivalent to `<video controls>` since the string \\\\\"false\\\\\" is even truthy.\\\\n-->\\\\n<div class:hidden={!processingVideo} class=\\\\\"overlay\\\\\">\\\\n\\\\t<span class=\\\\\"load-wrap\\\\\">\\\\n\\\\t\\\\t<span class=\\\\\"loader\\\\\" />\\\\n\\\\t</span>\\\\n</div>\\\\n<video\\\\n\\\\tsrc={resolved_src}\\\\n\\\\t{muted}\\\\n\\\\t{playsinline}\\\\n\\\\t{preload}\\\\n\\\\t{autoplay}\\\\n\\\\t{controls}\\\\n\\\\t{loop}\\\\n\\\\ton:loadeddata={dispatch.bind(null, \\\\\"loadeddata\\\\\")}\\\\n\\\\ton:click={dispatch.bind(null, \\\\\"click\\\\\")}\\\\n\\\\ton:play={dispatch.bind(null, \\\\\"play\\\\\")}\\\\n\\\\ton:pause={dispatch.bind(null, \\\\\"pause\\\\\")}\\\\n\\\\ton:ended={dispatch.bind(null, \\\\\"ended\\\\\")}\\\\n\\\\ton:mouseover={dispatch.bind(null, \\\\\"mouseover\\\\\")}\\\\n\\\\ton:mouseout={dispatch.bind(null, \\\\\"mouseout\\\\\")}\\\\n\\\\ton:focus={dispatch.bind(null, \\\\\"focus\\\\\")}\\\\n\\\\ton:blur={dispatch.bind(null, \\\\\"blur\\\\\")}\\\\n\\\\ton:loadstart\\\\n\\\\ton:loadeddata\\\\n\\\\ton:loadedmetadata\\\\n\\\\tbind:currentTime\\\\n\\\\tbind:duration\\\\n\\\\tbind:paused\\\\n\\\\tbind:this={node}\\\\n\\\\tuse:loaded={{ autoplay: autoplay ?? false }}\\\\n\\\\tdata-testid={$$props[\\\\\"data-testid\\\\\"]}\\\\n\\\\tcrossorigin=\\\\\"anonymous\\\\\"\\\\n>\\\\n\\\\t<slot />\\\\n</video>\\\\n\\\\n<style>\\\\n\\\\t.overlay {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbackground-color: rgba(0, 0, 0, 0.4);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.hidden {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.load-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.loader {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tbackground-color: var(--border-color-accent-subdued);\\\\n\\\\t\\\\tanimation: shadowPulse 2s linear infinite;\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t-24px 0 var(--border-color-accent-subdued),\\\\n\\\\t\\\\t\\\\t24px 0 var(--border-color-accent-subdued);\\\\n\\\\t\\\\tmargin: var(--spacing-md);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\twidth: 10px;\\\\n\\\\t\\\\theight: 10px;\\\\n\\\\t\\\\tscale: 0.5;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes shadowPulse {\\\\n\\\\t\\\\t33% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 var(--border-color-accent-subdued),\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 #fff;\\\\n\\\\t\\\\t\\\\tbackground: #fff;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t66% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 #fff,\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 #fff;\\\\n\\\\t\\\\t\\\\tbackground: var(--border-color-accent-subdued);\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 #fff,\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 var(--border-color-accent-subdued);\\\\n\\\\t\\\\t\\\\tbackground: #fff;\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAwHC,uBAAS,CACR,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CACpC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,sBAAQ,CACP,OAAO,CAAE,IACV,CAEA,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IACT,CAEA,sBAAQ,CACP,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,IAAI,6BAA6B,CAAC,CACpD,SAAS,CAAE,0BAAW,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CACzC,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAAC;AAC9C,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAC1C,MAAM,CAAE,IAAI,YAAY,CAAC,CACzB,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,GACR,CAEA,WAAW,0BAAY,CACtB,GAAI,CACH,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CACZ,UAAU,CAAE,IACb,CACA,GAAI,CACH,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CACZ,UAAU,CAAE,IAAI,6BAA6B,CAC9C,CACA,IAAK,CACJ,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAC1C,UAAU,CAAE,IACb,CACD\"}'\n};\nconst Video = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { src = void 0 } = $$props;\n  let { muted = void 0 } = $$props;\n  let { playsinline = void 0 } = $$props;\n  let { preload = void 0 } = $$props;\n  let { autoplay = void 0 } = $$props;\n  let { controls = void 0 } = $$props;\n  let { currentTime = void 0 } = $$props;\n  let { duration = void 0 } = $$props;\n  let { paused = void 0 } = $$props;\n  let { node = void 0 } = $$props;\n  let { loop } = $$props;\n  let { is_stream } = $$props;\n  let { processingVideo = false } = $$props;\n  let resolved_src;\n  let stream_active = false;\n  let latest_src;\n  createEventDispatcher();\n  function load_stream(src2, is_stream2, node2) {\n    if (!src2 || !is_stream2)\n      return;\n    if (!node2)\n      return;\n    if (Hls.isSupported() && !stream_active) {\n      const hls = new Hls({\n        maxBufferLength: 1,\n        // 0.5 seconds (500 ms)\n        maxMaxBufferLength: 1,\n        // Maximum max buffer length in seconds\n        lowLatencyMode: true\n      });\n      hls.loadSource(src2);\n      hls.attachMedia(node2);\n      hls.on(Hls.Events.MANIFEST_PARSED, function() {\n        node2.play();\n      });\n      hls.on(Hls.Events.ERROR, function(event, data) {\n        console.error(\"HLS error:\", event, data);\n        if (data.fatal) {\n          switch (data.type) {\n            case Hls.ErrorTypes.NETWORK_ERROR:\n              console.error(\"Fatal network error encountered, trying to recover\");\n              hls.startLoad();\n              break;\n            case Hls.ErrorTypes.MEDIA_ERROR:\n              console.error(\"Fatal media error encountered, trying to recover\");\n              hls.recoverMediaError();\n              break;\n            default:\n              console.error(\"Fatal error, cannot recover\");\n              hls.destroy();\n              break;\n          }\n        }\n      });\n      stream_active = true;\n    }\n  }\n  if ($$props.src === void 0 && $$bindings.src && src !== void 0)\n    $$bindings.src(src);\n  if ($$props.muted === void 0 && $$bindings.muted && muted !== void 0)\n    $$bindings.muted(muted);\n  if ($$props.playsinline === void 0 && $$bindings.playsinline && playsinline !== void 0)\n    $$bindings.playsinline(playsinline);\n  if ($$props.preload === void 0 && $$bindings.preload && preload !== void 0)\n    $$bindings.preload(preload);\n  if ($$props.autoplay === void 0 && $$bindings.autoplay && autoplay !== void 0)\n    $$bindings.autoplay(autoplay);\n  if ($$props.controls === void 0 && $$bindings.controls && controls !== void 0)\n    $$bindings.controls(controls);\n  if ($$props.currentTime === void 0 && $$bindings.currentTime && currentTime !== void 0)\n    $$bindings.currentTime(currentTime);\n  if ($$props.duration === void 0 && $$bindings.duration && duration !== void 0)\n    $$bindings.duration(duration);\n  if ($$props.paused === void 0 && $$bindings.paused && paused !== void 0)\n    $$bindings.paused(paused);\n  if ($$props.node === void 0 && $$bindings.node && node !== void 0)\n    $$bindings.node(node);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  if ($$props.is_stream === void 0 && $$bindings.is_stream && is_stream !== void 0)\n    $$bindings.is_stream(is_stream);\n  if ($$props.processingVideo === void 0 && $$bindings.processingVideo && processingVideo !== void 0)\n    $$bindings.processingVideo(processingVideo);\n  $$result.css.add(css);\n  {\n    {\n      resolved_src = src;\n      latest_src = src;\n      const resolving_src = src;\n      resolve_wasm_src(resolving_src).then((s) => {\n        if (latest_src === resolving_src) {\n          resolved_src = s;\n        }\n      });\n    }\n  }\n  {\n    stream_active = false;\n  }\n  {\n    load_stream(src, is_stream, node);\n  }\n  return ` <div class=\"${[\"overlay svelte-1y0s5gv\", !processingVideo ? \"hidden\" : \"\"].join(\" \").trim()}\" data-svelte-h=\"svelte-mez4j5\"><span class=\"load-wrap svelte-1y0s5gv\"><span class=\"loader svelte-1y0s5gv\"></span></span></div> <video${add_attribute(\"src\", resolved_src, 0)} ${muted ? \"muted\" : \"\"} ${playsinline ? \"playsinline\" : \"\"}${add_attribute(\"preload\", preload, 0)} ${autoplay ? \"autoplay\" : \"\"} ${controls ? \"controls\" : \"\"} ${loop ? \"loop\" : \"\"}${add_attribute(\"data-testid\", $$props[\"data-testid\"], 0)} crossorigin=\"anonymous\"${add_attribute(\"currentTime\", currentTime, 0)}${add_attribute(\"paused\", paused, 0)}${add_attribute(\"this\", node, 0)}>${slots.default ? slots.default({}) : ``}</video>`;\n});\nexport {\n  Video as V\n};\n"], "names": [], "mappings": ";;;;AAIA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,u3BAAu3B;AAC/3B,EAAE,GAAG,EAAE,uiNAAuiN;AAC9iN,CAAC,CAAC;AACG,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,OAAO,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC;AAC5B,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE;AAChD,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU;AAC5B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,OAAO;AACb,IAAI,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE;AAC7C,MAAM,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAC1B,QAAQ,eAAe,EAAE,CAAC;AAC1B;AACA,QAAQ,kBAAkB,EAAE,CAAC;AAC7B;AACA,QAAQ,cAAc,EAAE,IAAI;AAC5B,OAAO,CAAC,CAAC;AACT,MAAM,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC3B,MAAM,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC7B,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,WAAW;AACpD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE;AACrD,QAAQ,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACjD,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;AACxB,UAAU,QAAQ,IAAI,CAAC,IAAI;AAC3B,YAAY,KAAK,GAAG,CAAC,UAAU,CAAC,aAAa;AAC7C,cAAc,OAAO,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;AAClF,cAAc,GAAG,CAAC,SAAS,EAAE,CAAC;AAC9B,cAAc,MAAM;AACpB,YAAY,KAAK,GAAG,CAAC,UAAU,CAAC,WAAW;AAC3C,cAAc,OAAO,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;AAChF,cAAc,GAAG,CAAC,iBAAiB,EAAE,CAAC;AACtC,cAAc,MAAM;AACpB,YAAY;AACZ,cAAc,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;AAC3D,cAAc,GAAG,CAAC,OAAO,EAAE,CAAC;AAC5B,cAAc,MAAM;AACpB,WAAW;AACX,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,aAAa,GAAG,IAAI,CAAC;AAC3B,KAAK;AACL,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE;AACF,IAAI;AACJ,MAAM,YAAY,GAAG,GAAG,CAAC;AACzB,MAAM,UAAU,GAAG,GAAG,CAAC;AACvB,MAAM,MAAM,aAAa,GAAG,GAAG,CAAC;AAChC,MAAM,gBAAgB,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;AAClD,QAAQ,IAAI,UAAU,KAAK,aAAa,EAAE;AAC1C,UAAU,YAAY,GAAG,CAAC,CAAC;AAC3B,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE;AACF,IAAI,aAAa,GAAG,KAAK,CAAC;AAC1B,GAAG;AACH,EAAE;AACF,IAAI,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,wBAAwB,EAAE,CAAC,eAAe,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,sIAAsI,EAAE,aAAa,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC,EAAE,WAAW,GAAG,aAAa,GAAG,EAAE,CAAC,EAAE,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,EAAE,aAAa,CAAC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,wBAAwB,EAAE,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrsB,CAAC;;;;"}