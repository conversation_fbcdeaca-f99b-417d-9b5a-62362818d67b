{"version": 3, "file": "Index-CoduRn9X.js", "sources": ["../../../tabs/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as BaseTabs, TABS, type Tab } from \"./shared/Tabs.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport Tabs, { type Tab } from \"./shared/Tabs.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let visible = true;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let selected: number | string;\n\texport let initial_tabs: Tab[] = [];\n\texport let gradio:\n\t\t| Gradio<{\n\t\t\t\tchange: never;\n\t\t\t\tselect: SelectData;\n\t\t  }>\n\t\t| undefined;\n\n\t$: dispatch(\"prop_change\", { selected });\n</script>\n\n<Tabs\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tbind:selected\n\ton:change={() => gradio?.dispatch(\"change\")}\n\ton:select={(e) => gradio?.dispatch(\"select\", e.detail)}\n\t{initial_tabs}\n>\n\t<slot />\n</Tabs>\n"], "names": ["dispatch", "createEventDispatcher", "visible", "$$props", "elem_id", "elem_classes", "selected", "initial_tabs", "gradio", "e"], "mappings": "+mCASOA,EAAWC,QAEN,QAAAC,EAAU,EAAA,EAAAC,GACV,QAAAC,EAAU,EAAA,EAAAD,EACV,CAAA,aAAAE,EAAA,EAAA,EAAAF,EACA,CAAA,SAAAG,CAAA,EAAAH,EACA,CAAA,aAAAI,EAAA,EAAA,EAAAJ,EACA,CAAA,OAAAK,CAAA,EAAAL,uCAeMK,GAAQ,SAAS,QAAQ,IAC9BC,GAAMD,GAAQ,SAAS,SAAUC,EAAE,MAAM,0SATlDT,EAAS,cAAiB,CAAA,SAAAM,CAAA,CAAA"}