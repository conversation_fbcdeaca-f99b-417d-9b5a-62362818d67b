import{w as b,at as ut,bd as ct,be as ft,bf as dt,bg as ht,a as x,i as I,s as O,Q as A,q as i,l as L,v as m,o as R,O as gt,f as j,p as $,x as F,k as _t,r as tt,F as G,aw as et,y as lt,b as B,z as ot,t as H,n as mt,J as vt,K as pt,bh as wt,P as bt,bi as st,A as kt,a3 as yt,H as N,c as P,m as Q,d as U,ao as nt,b4 as $t,$ as Ct,bj as Mt}from"../lite.js";import{c as zt,f as it}from"./index-DCygRGWm.js";function jt(n,t,e,s){if(!t)return b;const r=n.getBoundingClientRect();if(t.left===r.left&&t.right===r.right&&t.top===r.top&&t.bottom===r.bottom)return b;const{delay:c=0,duration:h=300,easing:o=ut,start:a=ct()+c,end:l=a+h,tick:d=b,css:k}=e(n,{from:t,to:r},s);let w=!0,g=!1,v;function C(){k&&(v=dt(n,0,1,h,c,o,k)),c||(g=!0)}function u(){k&&ht(n,v),w=!1}return ft(y=>{if(!g&&y>=a&&(g=!0),g&&y>=l&&(d(1,0),u()),!w)return!1;if(g){const M=y-a,S=0+1*o(M/h);d(S,1-S)}return!0}),C(),d(0,1),u}function Bt(n){const t=getComputedStyle(n);if(t.position!=="absolute"&&t.position!=="fixed"){const{width:e,height:s}=t,r=n.getBoundingClientRect();n.style.position="absolute",n.style.width=e,n.style.height=s,Ht(n,r)}}function Ht(n,t){const e=n.getBoundingClientRect();if(t.left!==e.left||t.top!==e.top){const s=getComputedStyle(n),r=s.transform==="none"?"":s.transform;n.style.transform=`${r} translate(${t.left-e.left}px, ${t.top-e.top}px)`}}function Tt(n){let t,e;return{c(){t=A("svg"),e=A("path"),i(e,"stroke-linecap","round"),i(e,"stroke-linejoin","round"),i(e,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),i(t,"fill","none"),i(t,"stroke","currentColor"),i(t,"viewBox","0 0 24 24"),i(t,"width","100%"),i(t,"height","100%"),i(t,"xmlns","http://www.w3.org/2000/svg"),i(t,"aria-hidden","true"),i(t,"stroke-width","2"),i(t,"stroke-linecap","round"),i(t,"stroke-linejoin","round")},m(s,r){L(s,t,r),m(t,e)},p:b,i:b,o:b,d(s){s&&R(t)}}}class At extends x{constructor(t){super(),I(this,t,null,Tt,O,{})}}function Lt(n){let t,e;return{c(){t=A("svg"),e=A("path"),i(e,"stroke-linecap","round"),i(e,"stroke-linejoin","round"),i(e,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),i(t,"fill","none"),i(t,"stroke","currentColor"),i(t,"viewBox","0 0 24 24"),i(t,"width","100%"),i(t,"height","100%"),i(t,"xmlns","http://www.w3.org/2000/svg"),i(t,"aria-hidden","true"),i(t,"stroke-width","2"),i(t,"stroke-linecap","round"),i(t,"stroke-linejoin","round")},m(s,r){L(s,t,r),m(t,e)},p:b,i:b,o:b,d(s){s&&R(t)}}}class Rt extends x{constructor(t){super(),I(this,t,null,Lt,O,{})}}function St(n){let t,e;return{c(){t=A("svg"),e=A("path"),i(e,"stroke-linecap","round"),i(e,"stroke-linejoin","round"),i(e,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),i(t,"fill","none"),i(t,"stroke","currentColor"),i(t,"stroke-width","2"),i(t,"viewBox","0 0 24 24"),i(t,"width","100%"),i(t,"height","100%"),i(t,"xmlns","http://www.w3.org/2000/svg"),i(t,"aria-hidden","true"),i(t,"stroke-linecap","round"),i(t,"stroke-linejoin","round")},m(s,r){L(s,t,r),m(t,e)},p:b,i:b,o:b,d(s){s&&R(t)}}}class qt extends x{constructor(t){super(),I(this,t,null,St,O,{})}}function Ft(n,{from:t,to:e},s={}){const r=getComputedStyle(n),c=r.transform==="none"?"":r.transform,[h,o]=r.transformOrigin.split(" ").map(parseFloat),a=t.left+t.width*h/e.width-(e.left+h),l=t.top+t.height*o/e.height-(e.top+o),{delay:d=0,duration:k=g=>Math.sqrt(g)*120,easing:w=zt}=s;return{delay:d,duration:gt(k)?k(Math.sqrt(a*a+l*l)):k,easing:w,css:(g,v)=>{const C=v*a,u=v*l,y=g+v*t.width/e.width,M=g+v*t.height/e.height;return`transform: ${c} translate(${C}px, ${u}px) scale(${y}, ${M});`}}}function xt(n){let t,e;return t=new At({}),{c(){P(t.$$.fragment)},m(s,r){Q(t,s,r),e=!0},i(s){e||(H(t.$$.fragment,s),e=!0)},o(s){B(t.$$.fragment,s),e=!1},d(s){U(t,s)}}}function It(n){let t,e;return t=new Rt({}),{c(){P(t.$$.fragment)},m(s,r){Q(t,s,r),e=!0},i(s){e||(H(t.$$.fragment,s),e=!0)},o(s){B(t.$$.fragment,s),e=!1},d(s){U(t,s)}}}function Ot(n){let t,e;return t=new qt({}),{c(){P(t.$$.fragment)},m(s,r){Q(t,s,r),e=!0},i(s){e||(H(t.$$.fragment,s),e=!0)},o(s){B(t.$$.fragment,s),e=!1},d(s){U(t,s)}}}function Et(n){let t,e,s,r,c,h,o,a,l,d,k,w,g,v,C,u,y,M,S,T,E,D,J,K,q,_,V,X;const Y=[Ot,It,xt],z=[];function Z(f,p){return f[2]==="warning"?0:f[2]==="info"?1:f[2]==="error"?2:-1}return~(s=Z(n))&&(r=z[s]=Y[s](n)),{c(){t=$("div"),e=$("div"),r&&r.c(),h=F(),o=$("div"),a=$("div"),l=_t(n[1]),k=F(),w=$("div"),C=F(),u=$("button"),y=$("span"),y.textContent="×",S=F(),T=$("div"),i(e,"class",c="toast-icon "+n[2]+" svelte-utw7m1"),i(a,"class",d="toast-title "+n[2]+" svelte-utw7m1"),i(w,"class",g="toast-text "+n[2]+" svelte-utw7m1"),i(o,"class",v="toast-details "+n[2]+" svelte-utw7m1"),i(y,"aria-hidden","true"),i(u,"class",M="toast-close "+n[2]+" svelte-utw7m1"),i(u,"type","button"),i(u,"aria-label","Close"),i(u,"data-testid","toast-close"),i(T,"class",E="timer "+n[2]+" svelte-utw7m1"),i(T,"style",D=`animation-duration: ${n[3]};`),i(t,"class",J="toast-body "+n[2]+" svelte-utw7m1"),i(t,"role","alert"),i(t,"data-testid","toast-body"),tt(t,"hidden",!n[4])},m(f,p){L(f,t,p),m(t,e),~s&&z[s].m(e,null),m(t,h),m(t,o),m(o,a),m(a,l),m(o,k),m(o,w),w.innerHTML=n[0],m(t,C),m(t,u),m(u,y),m(t,S),m(t,T),_=!0,V||(X=[G(u,"click",n[5]),G(t,"click",et(n[9])),G(t,"keydown",et(n[10]))],V=!0)},p(f,[p]){let W=s;s=Z(f),s!==W&&(r&&(lt(),B(z[W],1,1,()=>{z[W]=null}),ot()),~s?(r=z[s],r||(r=z[s]=Y[s](f),r.c()),H(r,1),r.m(e,null)):r=null),(!_||p&4&&c!==(c="toast-icon "+f[2]+" svelte-utw7m1"))&&i(e,"class",c),(!_||p&2)&&mt(l,f[1]),(!_||p&4&&d!==(d="toast-title "+f[2]+" svelte-utw7m1"))&&i(a,"class",d),(!_||p&1)&&(w.innerHTML=f[0]),(!_||p&4&&g!==(g="toast-text "+f[2]+" svelte-utw7m1"))&&i(w,"class",g),(!_||p&4&&v!==(v="toast-details "+f[2]+" svelte-utw7m1"))&&i(o,"class",v),(!_||p&4&&M!==(M="toast-close "+f[2]+" svelte-utw7m1"))&&i(u,"class",M),(!_||p&4&&E!==(E="timer "+f[2]+" svelte-utw7m1"))&&i(T,"class",E),(!_||p&8&&D!==(D=`animation-duration: ${f[3]};`))&&i(T,"style",D),(!_||p&4&&J!==(J="toast-body "+f[2]+" svelte-utw7m1"))&&i(t,"class",J),(!_||p&20)&&tt(t,"hidden",!f[4])},i(f){_||(H(r),f&&vt(()=>{_&&(q&&q.end(1),K=pt(t,it,{duration:200,delay:100}),K.start())}),_=!0)},o(f){B(r),K&&K.invalidate(),f&&(q=wt(t,it,{duration:200})),_=!1},d(f){f&&R(t),~s&&z[s].d(),f&&q&&q.end(),V=!1,bt(X)}}}function Dt(n,t,e){let s,r,{title:c=""}=t,{message:h=""}=t,{type:o}=t,{id:a}=t,{duration:l=10}=t,{visible:d=!0}=t;const k=u=>{try{return!!u&&new URL(u,location.href).origin!==location.origin}catch{return!1}};st.addHook("afterSanitizeAttributes",function(u){"target"in u&&k(u.getAttribute("href"))&&(u.setAttribute("target","_blank"),u.setAttribute("rel","noopener noreferrer"))});const w=kt();function g(){w("close",a)}yt(()=>{l!==null&&setTimeout(()=>{g()},l*1e3)});function v(u){N.call(this,n,u)}function C(u){N.call(this,n,u)}return n.$$set=u=>{"title"in u&&e(1,c=u.title),"message"in u&&e(0,h=u.message),"type"in u&&e(2,o=u.type),"id"in u&&e(7,a=u.id),"duration"in u&&e(6,l=u.duration),"visible"in u&&e(8,d=u.visible)},n.$$.update=()=>{n.$$.dirty&1&&e(0,h=st.sanitize(h)),n.$$.dirty&256&&e(4,s=d),n.$$.dirty&64&&e(6,l=l||null),n.$$.dirty&64&&e(3,r=`${l||0}s`)},[h,c,o,r,s,g,l,a,d,v,C]}class Jt extends x{constructor(t){super(),I(this,t,Dt,Et,O,{title:1,message:0,type:2,id:7,duration:6,visible:8})}get title(){return this.$$.ctx[1]}set title(t){this.$$set({title:t}),j()}get message(){return this.$$.ctx[0]}set message(t){this.$$set({message:t}),j()}get type(){return this.$$.ctx[2]}set type(t){this.$$set({type:t}),j()}get id(){return this.$$.ctx[7]}set id(t){this.$$set({id:t}),j()}get duration(){return this.$$.ctx[6]}set duration(t){this.$$set({duration:t}),j()}get visible(){return this.$$.ctx[8]}set visible(t){this.$$set({visible:t}),j()}}function rt(n,t,e){const s=n.slice();return s[2]=t[e].type,s[3]=t[e].title,s[4]=t[e].message,s[5]=t[e].id,s[6]=t[e].duration,s[7]=t[e].visible,s}function at(n,t){let e,s,r,c,h=b,o;return s=new Jt({props:{type:t[2],title:t[3],message:t[4],duration:t[6],visible:t[7],id:t[5]}}),s.$on("close",t[1]),{key:n,first:null,c(){e=$("div"),P(s.$$.fragment),r=F(),Ct(e,"width","100%"),this.first=e},m(a,l){L(a,e,l),Q(s,e,null),m(e,r),o=!0},p(a,l){t=a;const d={};l&1&&(d.type=t[2]),l&1&&(d.title=t[3]),l&1&&(d.message=t[4]),l&1&&(d.duration=t[6]),l&1&&(d.visible=t[7]),l&1&&(d.id=t[5]),s.$set(d)},r(){c=e.getBoundingClientRect()},f(){Bt(e),h()},a(){h(),h=jt(e,c,Ft,{duration:300})},i(a){o||(H(s.$$.fragment,a),o=!0)},o(a){B(s.$$.fragment,a),o=!1},d(a){a&&R(e),U(s)}}}function Kt(n){let t,e=[],s=new Map,r,c=nt(n[0]);const h=o=>o[5];for(let o=0;o<c.length;o+=1){let a=rt(n,c,o),l=h(a);s.set(l,e[o]=at(l,a))}return{c(){t=$("div");for(let o=0;o<e.length;o+=1)e[o].c();i(t,"class","toast-wrap svelte-pu0yf1")},m(o,a){L(o,t,a);for(let l=0;l<e.length;l+=1)e[l]&&e[l].m(t,null);r=!0},p(o,[a]){if(a&1){c=nt(o[0]),lt();for(let l=0;l<e.length;l+=1)e[l].r();e=$t(e,a,h,1,o,c,s,t,Mt,at,null,rt);for(let l=0;l<e.length;l+=1)e[l].a();ot()}},i(o){if(!r){for(let a=0;a<c.length;a+=1)H(e[a]);r=!0}},o(o){for(let a=0;a<e.length;a+=1)B(e[a]);r=!1},d(o){o&&R(t);for(let a=0;a<e.length;a+=1)e[a].d()}}}function Pt(n){n.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function Qt(n,t,e){let{messages:s=[]}=t;function r(c){N.call(this,n,c)}return n.$$set=c=>{"messages"in c&&e(0,s=c.messages)},n.$$.update=()=>{n.$$.dirty&1&&Pt(s)},[s,r]}class Wt extends x{constructor(t){super(),I(this,t,Qt,Kt,O,{messages:0})}get messages(){return this.$$.ctx[0]}set messages(t){this.$$set({messages:t}),j()}}export{Wt as T};
//# sourceMappingURL=Toast-FkD9e-n8.js.map
