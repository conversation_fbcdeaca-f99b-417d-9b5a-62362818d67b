{"version": 3, "file": "MarkdownCode-C6RHM0m6.js", "sources": ["../../../../node_modules/.pnpm/katex@0.16.10/node_modules/katex/dist/contrib/auto-render.mjs", "../../../sanitize/browser.ts", "../../../markdown-code/MarkdownCode.svelte"], "sourcesContent": ["import katex from '../katex.mjs';\n\n/* eslint no-constant-condition:0 */\nvar findEndOfMath = function findEndOfMath(delimiter, text, startIndex) {\n  // Adapted from\n  // https://github.com/Khan/perseus/blob/master/src/perseus-markdown.jsx\n  var index = startIndex;\n  var braceLevel = 0;\n  var delimLength = delimiter.length;\n\n  while (index < text.length) {\n    var character = text[index];\n\n    if (braceLevel <= 0 && text.slice(index, index + delimLength) === delimiter) {\n      return index;\n    } else if (character === \"\\\\\") {\n      index++;\n    } else if (character === \"{\") {\n      braceLevel++;\n    } else if (character === \"}\") {\n      braceLevel--;\n    }\n\n    index++;\n  }\n\n  return -1;\n};\n\nvar escapeRegex = function escapeRegex(string) {\n  return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\n};\n\nvar amsRegex = /^\\\\begin{/;\n\nvar splitAtDelimiters = function splitAtDelimiters(text, delimiters) {\n  var index;\n  var data = [];\n  var regexLeft = new RegExp(\"(\" + delimiters.map(x => escapeRegex(x.left)).join(\"|\") + \")\");\n\n  while (true) {\n    index = text.search(regexLeft);\n\n    if (index === -1) {\n      break;\n    }\n\n    if (index > 0) {\n      data.push({\n        type: \"text\",\n        data: text.slice(0, index)\n      });\n      text = text.slice(index); // now text starts with delimiter\n    } // ... so this always succeeds:\n\n\n    var i = delimiters.findIndex(delim => text.startsWith(delim.left));\n    index = findEndOfMath(delimiters[i].right, text, delimiters[i].left.length);\n\n    if (index === -1) {\n      break;\n    }\n\n    var rawData = text.slice(0, index + delimiters[i].right.length);\n    var math = amsRegex.test(rawData) ? rawData : text.slice(delimiters[i].left.length, index);\n    data.push({\n      type: \"math\",\n      data: math,\n      rawData,\n      display: delimiters[i].display\n    });\n    text = text.slice(index + delimiters[i].right.length);\n  }\n\n  if (text !== \"\") {\n    data.push({\n      type: \"text\",\n      data: text\n    });\n  }\n\n  return data;\n};\n\n/* eslint no-console:0 */\n/* Note: optionsCopy is mutated by this method. If it is ever exposed in the\n * API, we should copy it before mutating.\n */\n\nvar renderMathInText = function renderMathInText(text, optionsCopy) {\n  var data = splitAtDelimiters(text, optionsCopy.delimiters);\n\n  if (data.length === 1 && data[0].type === 'text') {\n    // There is no formula in the text.\n    // Let's return null which means there is no need to replace\n    // the current text node with a new one.\n    return null;\n  }\n\n  var fragment = document.createDocumentFragment();\n\n  for (var i = 0; i < data.length; i++) {\n    if (data[i].type === \"text\") {\n      fragment.appendChild(document.createTextNode(data[i].data));\n    } else {\n      var span = document.createElement(\"span\");\n      var math = data[i].data; // Override any display mode defined in the settings with that\n      // defined by the text itself\n\n      optionsCopy.displayMode = data[i].display;\n\n      try {\n        if (optionsCopy.preProcess) {\n          math = optionsCopy.preProcess(math);\n        }\n\n        katex.render(math, span, optionsCopy);\n      } catch (e) {\n        if (!(e instanceof katex.ParseError)) {\n          throw e;\n        }\n\n        optionsCopy.errorCallback(\"KaTeX auto-render: Failed to parse `\" + data[i].data + \"` with \", e);\n        fragment.appendChild(document.createTextNode(data[i].rawData));\n        continue;\n      }\n\n      fragment.appendChild(span);\n    }\n  }\n\n  return fragment;\n};\n\nvar renderElem = function renderElem(elem, optionsCopy) {\n  for (var i = 0; i < elem.childNodes.length; i++) {\n    var childNode = elem.childNodes[i];\n\n    if (childNode.nodeType === 3) {\n      // Text node\n      // Concatenate all sibling text nodes.\n      // Webkit browsers split very large text nodes into smaller ones,\n      // so the delimiters may be split across different nodes.\n      var textContentConcat = childNode.textContent;\n      var sibling = childNode.nextSibling;\n      var nSiblings = 0;\n\n      while (sibling && sibling.nodeType === Node.TEXT_NODE) {\n        textContentConcat += sibling.textContent;\n        sibling = sibling.nextSibling;\n        nSiblings++;\n      }\n\n      var frag = renderMathInText(textContentConcat, optionsCopy);\n\n      if (frag) {\n        // Remove extra text nodes\n        for (var j = 0; j < nSiblings; j++) {\n          childNode.nextSibling.remove();\n        }\n\n        i += frag.childNodes.length - 1;\n        elem.replaceChild(frag, childNode);\n      } else {\n        // If the concatenated text does not contain math\n        // the siblings will not either\n        i += nSiblings;\n      }\n    } else if (childNode.nodeType === 1) {\n      (function () {\n        // Element node\n        var className = ' ' + childNode.className + ' ';\n        var shouldRender = optionsCopy.ignoredTags.indexOf(childNode.nodeName.toLowerCase()) === -1 && optionsCopy.ignoredClasses.every(x => className.indexOf(' ' + x + ' ') === -1);\n\n        if (shouldRender) {\n          renderElem(childNode, optionsCopy);\n        }\n      })();\n    } // Otherwise, it's something else, and ignore it.\n\n  }\n};\n\nvar renderMathInElement = function renderMathInElement(elem, options) {\n  if (!elem) {\n    throw new Error(\"No element provided to render\");\n  }\n\n  var optionsCopy = {}; // Object.assign(optionsCopy, option)\n\n  for (var option in options) {\n    if (options.hasOwnProperty(option)) {\n      optionsCopy[option] = options[option];\n    }\n  } // default options\n\n\n  optionsCopy.delimiters = optionsCopy.delimiters || [{\n    left: \"$$\",\n    right: \"$$\",\n    display: true\n  }, {\n    left: \"\\\\(\",\n    right: \"\\\\)\",\n    display: false\n  }, // LaTeX uses $…$, but it ruins the display of normal `$` in text:\n  // {left: \"$\", right: \"$\", display: false},\n  // $ must come after $$\n  // Render AMS environments even if outside $$…$$ delimiters.\n  {\n    left: \"\\\\begin{equation}\",\n    right: \"\\\\end{equation}\",\n    display: true\n  }, {\n    left: \"\\\\begin{align}\",\n    right: \"\\\\end{align}\",\n    display: true\n  }, {\n    left: \"\\\\begin{alignat}\",\n    right: \"\\\\end{alignat}\",\n    display: true\n  }, {\n    left: \"\\\\begin{gather}\",\n    right: \"\\\\end{gather}\",\n    display: true\n  }, {\n    left: \"\\\\begin{CD}\",\n    right: \"\\\\end{CD}\",\n    display: true\n  }, {\n    left: \"\\\\[\",\n    right: \"\\\\]\",\n    display: true\n  }];\n  optionsCopy.ignoredTags = optionsCopy.ignoredTags || [\"script\", \"noscript\", \"style\", \"textarea\", \"pre\", \"code\", \"option\"];\n  optionsCopy.ignoredClasses = optionsCopy.ignoredClasses || [];\n  optionsCopy.errorCallback = optionsCopy.errorCallback || console.error; // Enable sharing of global macros defined via `\\gdef` between different\n  // math elements within a single call to `renderMathInElement`.\n\n  optionsCopy.macros = optionsCopy.macros || {};\n  renderElem(elem, optionsCopy);\n};\n\nexport { renderMathInElement as default };\n", "import Amuchina from \"amuchina\";\n\nconst is_external_url = (link: string | null, root: string): boolean => {\n\ttry {\n\t\treturn !!link && new URL(link).origin !== new URL(root).origin;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\n\nexport function sanitize(source: string, root: string): string {\n\tconst amuchina = new Amuchina();\n\tconst node = new DOMParser().parseFromString(source, \"text/html\");\n\twalk_nodes(node.body, \"A\", (node) => {\n\t\tif (node instanceof HTMLElement && \"target\" in node) {\n\t\t\tif (is_external_url(node.getAttribute(\"href\"), root)) {\n\t\t\t\tnode.setAttribute(\"target\", \"_blank\");\n\t\t\t\tnode.setAttribute(\"rel\", \"noopener noreferrer\");\n\t\t\t}\n\t\t}\n\t});\n\n\treturn amuchina.sanitize(node).body.innerHTML;\n}\n\nfunction walk_nodes(\n\tnode: Node | null | HTMLElement,\n\ttest: string | ((node: Node | HTMLElement) => boolean),\n\tcallback: (node: Node | HTMLElement) => void\n): void {\n\tif (\n\t\tnode &&\n\t\t((typeof test === \"string\" && node.nodeName === test) ||\n\t\t\t(typeof test === \"function\" && test(node)))\n\t) {\n\t\tcallback(node);\n\t}\n\tconst children = node?.childNodes || [];\n\tfor (let i = 0; i < children.length; i++) {\n\t\t// @ts-ignore\n\t\twalk_nodes(children[i], test, callback);\n\t}\n}\n", "<script lang=\"ts\">\n\timport { afterUpdate } from \"svelte\";\n\timport render_math_in_element from \"katex/contrib/auto-render\";\n\timport \"katex/dist/katex.min.css\";\n\timport { create_marked } from \"./utils\";\n\timport { sanitize } from \"@gradio/sanitize\";\n\timport \"./prism.css\";\n\n\texport let chatbot = true;\n\texport let message: string;\n\texport let sanitize_html = true;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[] = [];\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let header_links = false;\n\texport let root: string;\n\n\tlet el: HTMLSpanElement;\n\tlet html: string;\n\n\tconst marked = create_marked({\n\t\theader_links,\n\t\tline_breaks,\n\t\tlatex_delimiters\n\t});\n\n\tfunction escapeRegExp(string: string): string {\n\t\treturn string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n\t}\n\n\tfunction process_message(value: string): string {\n\t\tlet parsedValue = value;\n\n\t\tif (render_markdown) {\n\t\t\tconst latexBlocks: string[] = [];\n\t\t\tlatex_delimiters.forEach((delimiter, index) => {\n\t\t\t\tconst leftDelimiter = escapeRegExp(delimiter.left);\n\t\t\t\tconst rightDelimiter = escapeRegExp(delimiter.right);\n\t\t\t\tconst regex = new RegExp(\n\t\t\t\t\t`${leftDelimiter}([\\\\s\\\\S]+?)${rightDelimiter}`,\n\t\t\t\t\t\"g\"\n\t\t\t\t);\n\t\t\t\tparsedValue = parsedValue.replace(regex, (match, p1) => {\n\t\t\t\t\tlatexBlocks.push(match);\n\t\t\t\t\treturn `%%%LATEX_BLOCK_${latexBlocks.length - 1}%%%`;\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tparsedValue = marked.parse(parsedValue) as string;\n\n\t\t\tparsedValue = parsedValue.replace(\n\t\t\t\t/%%%LATEX_BLOCK_(\\d+)%%%/g,\n\t\t\t\t(match, p1) => latexBlocks[parseInt(p1, 10)]\n\t\t\t);\n\t\t}\n\n\t\tif (sanitize_html && sanitize) {\n\t\t\tparsedValue = sanitize(parsedValue, root);\n\t\t}\n\n\t\treturn parsedValue;\n\t}\n\n\t$: if (message && message.trim()) {\n\t\thtml = process_message(message);\n\t} else {\n\t\thtml = \"\";\n\t}\n\n\tasync function render_html(value: string): Promise<void> {\n\t\tif (latex_delimiters.length > 0 && value) {\n\t\t\tconst containsDelimiter = latex_delimiters.some(\n\t\t\t\t(delimiter) =>\n\t\t\t\t\tvalue.includes(delimiter.left) && value.includes(delimiter.right)\n\t\t\t);\n\t\t\tif (containsDelimiter) {\n\t\t\t\trender_math_in_element(el, {\n\t\t\t\t\tdelimiters: latex_delimiters,\n\t\t\t\t\tthrowOnError: false\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(async () => {\n\t\tif (el && document.body.contains(el)) {\n\t\t\tawait render_html(message);\n\t\t} else {\n\t\t\tconsole.error(\"Element is not in the DOM\");\n\t\t}\n\t});\n</script>\n\n<span class:chatbot bind:this={el} class=\"md\" class:prose={render_markdown}>\n\t{@html html}\n</span>\n\n<style>\n\tspan :global(div[class*=\"code_wrap\"]) {\n\t\tposition: relative;\n\t}\n\n\t/* KaTeX */\n\tspan :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\tspan :global(div[class*=\"code_wrap\"] > button) {\n\t\tz-index: 1;\n\t\tcursor: pointer;\n\t\tborder-bottom-left-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-md);\n\t\twidth: 25px;\n\t\theight: 25px;\n\t\tposition: absolute;\n\t\tright: 0;\n\t}\n\n\tspan :global(.check) {\n\t\topacity: 0;\n\t\tz-index: var(--layer-top);\n\t\ttransition: opacity 0.2s;\n\t\tbackground: var(--code-background-fill);\n\t\tcolor: var(--body-text-color);\n\t\tposition: absolute;\n\t\ttop: var(--size-1-5);\n\t\tleft: var(--size-1-5);\n\t}\n\n\tspan :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\tspan :global(.md-header-anchor) {\n\t\t/* position: absolute; */\n\t\tmargin-left: -25px;\n\t\tpadding-right: 8px;\n\t\tline-height: 1;\n\t\tcolor: var(--body-text-color-subdued);\n\t\topacity: 0;\n\t}\n\n\tspan :global(h1:hover .md-header-anchor),\n\tspan :global(h2:hover .md-header-anchor),\n\tspan :global(h3:hover .md-header-anchor),\n\tspan :global(h4:hover .md-header-anchor),\n\tspan :global(h5:hover .md-header-anchor),\n\tspan :global(h6:hover .md-header-anchor) {\n\t\topacity: 1;\n\t}\n\n\tspan.md :global(.md-header-anchor > svg) {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tspan :global(table) {\n\t\tword-break: break-word;\n\t}\n</style>\n"], "names": ["findEndOfMath", "delimiter", "text", "startIndex", "index", "braceLevel", "delimLength", "character", "escapeRegex", "string", "amsRegex", "splitAtDelimiters", "delimiters", "data", "regexLeft", "x", "i", "delim", "rawData", "math", "renderMathInText", "optionsCopy", "fragment", "span", "katex", "e", "renderElem", "elem", "childNode", "textContentConcat", "sibling", "nSiblings", "frag", "j", "className", "shouldRender", "renderMathInElement", "options", "option", "is_external_url", "link", "root", "sanitize", "source", "<PERSON><PERSON>na", "<PERSON><PERSON><PERSON>", "node", "walk_nodes", "test", "callback", "children", "ctx", "insert", "target", "anchor", "escapeRegExp", "chatbot", "$$props", "message", "sanitize_html", "latex_delimiters", "render_markdown", "line_breaks", "header_links", "el", "html", "marked", "create_marked", "process_message", "value", "parsedValue", "latexBlocks", "leftDelimiter", "rightDelimiter", "regex", "match", "p1", "render_html", "render_math_in_element", "afterUpdate", "$$value", "$$invalidate"], "mappings": "+HAGA,IAAIA,EAAgB,SAAuBC,EAAWC,EAAMC,EAAY,CAOtE,QAJIC,EAAQD,EACRE,EAAa,EACbC,EAAcL,EAAU,OAErBG,EAAQF,EAAK,QAAQ,CAC1B,IAAIK,EAAYL,EAAKE,CAAK,EAE1B,GAAIC,GAAc,GAAKH,EAAK,MAAME,EAAOA,EAAQE,CAAW,IAAML,EAChE,OAAOG,EACEG,IAAc,KACvBH,IACSG,IAAc,IACvBF,IACSE,IAAc,KACvBF,IAGFD,GACD,CAED,MAAO,EACT,EAEII,EAAc,SAAqBC,EAAQ,CAC7C,OAAOA,EAAO,QAAQ,wBAAyB,MAAM,CACvD,EAEIC,EAAW,YAEXC,EAAoB,SAA2BT,EAAMU,EAAY,CAKnE,QAJIR,EACAS,EAAO,CAAA,EACPC,EAAY,IAAI,OAAO,IAAMF,EAAW,IAAIG,GAAKP,EAAYO,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,EAAI,GAAG,EAGvFX,EAAQF,EAAK,OAAOY,CAAS,EAEzBV,IAAU,IAHH,CAOPA,EAAQ,IACVS,EAAK,KAAK,CACR,KAAM,OACN,KAAMX,EAAK,MAAM,EAAGE,CAAK,CACjC,CAAO,EACDF,EAAOA,EAAK,MAAME,CAAK,GAIzB,IAAIY,EAAIJ,EAAW,UAAUK,GAASf,EAAK,WAAWe,EAAM,IAAI,CAAC,EAGjE,GAFAb,EAAQJ,EAAcY,EAAWI,CAAC,EAAE,MAAOd,EAAMU,EAAWI,CAAC,EAAE,KAAK,MAAM,EAEtEZ,IAAU,GACZ,MAGF,IAAIc,EAAUhB,EAAK,MAAM,EAAGE,EAAQQ,EAAWI,CAAC,EAAE,MAAM,MAAM,EAC1DG,EAAOT,EAAS,KAAKQ,CAAO,EAAIA,EAAUhB,EAAK,MAAMU,EAAWI,CAAC,EAAE,KAAK,OAAQZ,CAAK,EACzFS,EAAK,KAAK,CACR,KAAM,OACN,KAAMM,EACN,QAAAD,EACA,QAASN,EAAWI,CAAC,EAAE,OAC7B,CAAK,EACDd,EAAOA,EAAK,MAAME,EAAQQ,EAAWI,CAAC,EAAE,MAAM,MAAM,CACrD,CAED,OAAId,IAAS,IACXW,EAAK,KAAK,CACR,KAAM,OACN,KAAMX,CACZ,CAAK,EAGIW,CACT,EAOIO,EAAmB,SAA0BlB,EAAMmB,EAAa,CAClE,IAAIR,EAAOF,EAAkBT,EAAMmB,EAAY,UAAU,EAEzD,GAAIR,EAAK,SAAW,GAAKA,EAAK,CAAC,EAAE,OAAS,OAIxC,OAAO,KAKT,QAFIS,EAAW,SAAS,yBAEfN,EAAI,EAAGA,EAAIH,EAAK,OAAQG,IAC/B,GAAIH,EAAKG,CAAC,EAAE,OAAS,OACnBM,EAAS,YAAY,SAAS,eAAeT,EAAKG,CAAC,EAAE,IAAI,CAAC,MACrD,CACL,IAAIO,EAAO,SAAS,cAAc,MAAM,EACpCJ,EAAON,EAAKG,CAAC,EAAE,KAGnBK,EAAY,YAAcR,EAAKG,CAAC,EAAE,QAElC,GAAI,CACEK,EAAY,aACdF,EAAOE,EAAY,WAAWF,CAAI,GAGpCK,EAAM,OAAOL,EAAMI,EAAMF,CAAW,CACrC,OAAQI,EAAG,CACV,GAAI,EAAEA,aAAaD,EAAM,YACvB,MAAMC,EAGRJ,EAAY,cAAc,uCAAyCR,EAAKG,CAAC,EAAE,KAAO,UAAWS,CAAC,EAC9FH,EAAS,YAAY,SAAS,eAAeT,EAAKG,CAAC,EAAE,OAAO,CAAC,EAC7D,QACD,CAEDM,EAAS,YAAYC,CAAI,CAC1B,CAGH,OAAOD,CACT,EAEII,EAAa,SAASA,EAAWC,EAAMN,EAAa,CACtD,QAASL,EAAI,EAAGA,EAAIW,EAAK,WAAW,OAAQX,IAAK,CAC/C,IAAIY,EAAYD,EAAK,WAAWX,CAAC,EAEjC,GAAIY,EAAU,WAAa,EAAG,CAS5B,QAJIC,EAAoBD,EAAU,YAC9BE,EAAUF,EAAU,YACpBG,EAAY,EAETD,GAAWA,EAAQ,WAAa,KAAK,WAC1CD,GAAqBC,EAAQ,YAC7BA,EAAUA,EAAQ,YAClBC,IAGF,IAAIC,EAAOZ,EAAiBS,EAAmBR,CAAW,EAE1D,GAAIW,EAAM,CAER,QAASC,EAAI,EAAGA,EAAIF,EAAWE,IAC7BL,EAAU,YAAY,SAGxBZ,GAAKgB,EAAK,WAAW,OAAS,EAC9BL,EAAK,aAAaK,EAAMJ,CAAS,CACzC,MAGQZ,GAAKe,CAEb,MAAeH,EAAU,WAAa,GAC/B,UAAY,CAEX,IAAIM,EAAY,IAAMN,EAAU,UAAY,IACxCO,EAAed,EAAY,YAAY,QAAQO,EAAU,SAAS,YAAW,CAAE,IAAM,IAAMP,EAAY,eAAe,MAAMN,GAAKmB,EAAU,QAAQ,IAAMnB,EAAI,GAAG,IAAM,EAAE,EAExKoB,GACFT,EAAWE,EAAWP,CAAW,CAE3C,GAGG,CACH,EAEIe,EAAsB,SAA6BT,EAAMU,EAAS,CACpE,GAAI,CAACV,EACH,MAAM,IAAI,MAAM,+BAA+B,EAGjD,IAAIN,EAAc,CAAA,EAElB,QAASiB,KAAUD,EACbA,EAAQ,eAAeC,CAAM,IAC/BjB,EAAYiB,CAAM,EAAID,EAAQC,CAAM,GAKxCjB,EAAY,WAAaA,EAAY,YAAc,CAAC,CAClD,KAAM,KACN,MAAO,KACP,QAAS,EACb,EAAK,CACD,KAAM,MACN,MAAO,MACP,QAAS,EACV,EAID,CACE,KAAM,oBACN,MAAO,kBACP,QAAS,EACb,EAAK,CACD,KAAM,iBACN,MAAO,eACP,QAAS,EACb,EAAK,CACD,KAAM,mBACN,MAAO,iBACP,QAAS,EACb,EAAK,CACD,KAAM,kBACN,MAAO,gBACP,QAAS,EACb,EAAK,CACD,KAAM,cACN,MAAO,YACP,QAAS,EACb,EAAK,CACD,KAAM,MACN,MAAO,MACP,QAAS,EACb,CAAG,EACDA,EAAY,YAAcA,EAAY,aAAe,CAAC,SAAU,WAAY,QAAS,WAAY,MAAO,OAAQ,QAAQ,EACxHA,EAAY,eAAiBA,EAAY,gBAAkB,CAAA,EAC3DA,EAAY,cAAgBA,EAAY,eAAiB,QAAQ,MAGjEA,EAAY,OAASA,EAAY,QAAU,CAAA,EAC3CK,EAAWC,EAAMN,CAAW,CAC9B,EC/OA,MAAMkB,EAAkB,CAACC,EAAqBC,IAA0B,CACnE,GAAA,CACI,MAAA,CAAC,CAACD,GAAQ,IAAI,IAAIA,CAAI,EAAE,SAAW,IAAI,IAAIC,CAAI,EAAE,YAC7C,CACJ,MAAA,EACR,CACD,EAEgB,SAAAC,EAASC,EAAgBF,EAAsB,CACxD,MAAAG,EAAW,IAAIC,EACfC,EAAO,IAAI,UAAA,EAAY,gBAAgBH,EAAQ,WAAW,EAChE,OAAAI,EAAWD,EAAK,KAAM,IAAMA,GAAS,CAChCA,aAAgB,aAAe,WAAYA,GAC1CP,EAAgBO,EAAK,aAAa,MAAM,EAAGL,CAAI,IAClDK,EAAK,aAAa,SAAU,QAAQ,EACpCA,EAAK,aAAa,MAAO,qBAAqB,EAEhD,CACA,EAEMF,EAAS,SAASE,CAAI,EAAE,KAAK,SACrC,CAEA,SAASC,EACRD,EACAE,EACAC,EACO,CAENH,IAC8BA,EAAK,WAAaE,GAC9C,OAAOA,GAAS,aAElBC,EAASH,CAAI,EAER,MAAAI,EAAWJ,GAAM,YAAc,GACrC,QAAS9B,EAAI,EAAGA,EAAIkC,EAAS,OAAQlC,IAEpC+B,EAAWG,EAASlC,CAAC,EAAGgC,EAAMC,CAAQ,CAExC,4GCuD2DE,EAAe,CAAA,CAAA,UAA1EC,EAEMC,EAAA9B,EAAA+B,CAAA,cADEH,EAAI,CAAA,uCAAJA,EAAI,CAAA,6CAD+CA,EAAe,CAAA,CAAA,+CAnEhEI,EAAa9C,EAAA,QACdA,EAAO,QAAQ,sBAAuB,MAAM,wBAvBzC,QAAA+C,EAAU,EAAA,EAAAC,EACV,CAAA,QAAAC,CAAA,EAAAD,GACA,cAAAE,EAAgB,EAAA,EAAAF,EAChB,CAAA,iBAAAG,EAAA,EAAA,EAAAH,GAKA,gBAAAI,EAAkB,EAAA,EAAAJ,GAClB,YAAAK,EAAc,EAAA,EAAAL,GACd,aAAAM,EAAe,EAAA,EAAAN,EACf,CAAA,KAAAhB,CAAA,EAAAgB,EAEPO,EACAC,QAEEC,EAASC,EAAA,CACd,aAAAJ,EACA,YAAAD,EACA,iBAAAF,aAOQQ,EAAgBC,EAAA,KACpBC,EAAcD,EAEd,GAAAR,EAAA,CACG,MAAAU,EAAA,CAAA,EACNX,EAAiB,SAAS3D,EAAWG,IAAA,OAC9BoE,EAAgBjB,EAAatD,EAAU,IAAI,EAC3CwE,EAAiBlB,EAAatD,EAAU,KAAK,EAC7CyE,EAAA,IAAY,OACd,GAAAF,CAAa,eAAeC,CAAc,GAC7C,GAAA,EAEDH,EAAcA,EAAY,QAAQI,EAAA,CAAQC,EAAOC,MAChDL,EAAY,KAAKI,CAAK,EACG,kBAAAJ,EAAY,OAAS,CAAC,UAIjDD,EAAcJ,EAAO,MAAMI,CAAW,EAEtCA,EAAcA,EAAY,QACzB,2BACC,CAAAK,EAAOC,IAAOL,EAAY,SAASK,EAAI,EAAE,CAAA,CAAA,SAIxCjB,GAAiBjB,IACpB4B,EAAc5B,EAAS4B,EAAa7B,CAAI,GAGlC6B,iBASOO,EAAYR,EAAA,CACtBT,EAAiB,OAAS,GAAKS,GACRT,EAAiB,KACzC3D,GACAoE,EAAM,SAASpE,EAAU,IAAI,GAAKoE,EAAM,SAASpE,EAAU,KAAK,CAAA,GAGjE6E,EAAuBd,EAAA,CACtB,WAAYJ,EACZ,aAAc,KAMlBmB,EAAA,SAAA,CACKf,GAAM,SAAS,KAAK,SAASA,CAAE,EAC5B,MAAAa,EAAYnB,CAAO,EAEzB,QAAQ,MAAM,2BAA2B,6CAKbM,EAAEgB,0XA9BzBtB,GAAWA,EAAQ,OACzBuB,EAAA,EAAAhB,EAAOG,EAAgBV,CAAO,CAAA,MAE9BO,EAAO,EAAA", "x_google_ignoreList": [0]}