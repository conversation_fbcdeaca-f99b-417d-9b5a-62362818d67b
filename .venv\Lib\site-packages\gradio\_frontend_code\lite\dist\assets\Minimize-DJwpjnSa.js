import{a as l,i as h,s as c,Q as n,q as t,l as m,v as p,w as r,o as u}from"../lite.js";function v(o){let e,s;return{c(){e=n("svg"),s=n("path"),t(s,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","2"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-maximize"),t(e,"width","100%"),t(e,"height","100%")},m(a,i){m(a,e,i),p(e,s)},p:r,i:r,o:r,d(a){a&&u(e)}}}class f extends l{constructor(e){super(),h(this,e,null,v,c,{})}}function d(o){let e,s;return{c(){e=n("svg"),s=n("path"),t(s,"d","M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","2"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-minimize"),t(e,"width","100%"),t(e,"height","100%")},m(a,i){m(a,e,i),p(e,s)},p:r,i:r,o:r,d(a){a&&u(e)}}}class g extends l{constructor(e){super(),h(this,e,null,d,c,{})}}export{f as M,g as a};
//# sourceMappingURL=Minimize-DJwpjnSa.js.map
