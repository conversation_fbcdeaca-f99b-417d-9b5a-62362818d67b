{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACX,OAAO,EACP,OAAO,EACP,aAAa,EACb,MAAM,EACN,gBAAgB,EAChB,YAAY,EACZ,SAAS,EACT,aAAa,EACb,WAAW,EACX,MAAM,EACN,cAAc,EAEd,cAAc,EACd,WAAW,EACX,MAAM,SAAS,CAAC;AAGjB,OAAO,EAAU,QAAQ,EAAE,MAAM,UAAU,CAAC;AAuB5C,qBAAa,MAAM;IAClB,aAAa,EAAE,MAAM,CAAC;IACtB,OAAO,EAAE,aAAa,CAAC;IAEvB,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC;IAC3B,UAAU,SAAM;IAChB,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;IACzC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAM;IACrC,YAAY,EAAE,MAAM,CAA2C;IAC/D,GAAG,EAAE,MAAM,GAAG,KAAK,CAAS;IAC5B,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAM;IAElD,OAAO,CAAC,OAAO,CAAuB;IAGtC,aAAa;;MAAmB;IAChC,uBAAuB,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAM;IACtD,oBAAoB,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAM;IACnD,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAM;IACxE,eAAe,EAAE,GAAG,CAAC,MAAM,CAAC,CAAa;IACzC,eAAe,EAAE,WAAW,GAAG,IAAI,CAAQ;IAC3C,gBAAgB,EAAE,eAAe,GAAG,IAAI,CAAQ;IAChD,eAAe,EAAE,WAAW,GAAG,IAAI,CAAQ;IAC3C,eAAe,EAAE,GAAG,CAAC;IACrB,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,CAAC,CAAM;IAElD,KAAK,CAAC,KAAK,EAAE,WAAW,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC;IAStE,MAAM,CAAC,GAAG,EAAE,GAAG,GAAG,WAAW;IAiB7B,QAAQ,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;IAC5C,YAAY,EAAE,CACb,QAAQ,EAAE,MAAM,EAChB,KAAK,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,EACtB,SAAS,CAAC,EAAE,MAAM,KACd,OAAO,CAAC,cAAc,CAAC,CAAC;IAC7B,MAAM,EAAE,CACP,SAAS,EAAE,QAAQ,EAAE,EACrB,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,MAAM,EAClB,aAAa,CAAC,EAAE,MAAM,KAClB,OAAO,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IACzC,WAAW,EAAE,CACZ,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,OAAO,EAAE,EACf,aAAa,EAAE,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC,KAC5C,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IACxB,SAAS,EAAE,CACV,GAAG,EAAE,MAAM,EACX,IAAI,EAAE,OAAO,EACb,kBAAkB,CAAC,EAAE,GAAG,KACpB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IACxB,MAAM,EAAE,CACP,QAAQ,EAAE,MAAM,GAAG,MAAM,EACzB,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACrD,UAAU,CAAC,EAAE,OAAO,EACpB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EAC1B,UAAU,CAAC,EAAE,OAAO,KAChB,cAAc,CAAC,WAAW,CAAC,CAAC;IACjC,OAAO,EAAE,CACR,QAAQ,EAAE,MAAM,GAAG,MAAM,EACzB,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACrD,UAAU,CAAC,EAAE,OAAO,KAChB,OAAO,CAAC,aAAa,CAAC,CAAC;IAC5B,WAAW,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,OAAO,CAAC,cAAc,CAAoD;IAC1E,OAAO,CAAC,eAAe,CAAsB;gBAE5C,aAAa,EAAE,MAAM,EACrB,OAAO,GAAE,aAAoC;YAwBhC,IAAI;IAqBZ,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;WAsC1C,OAAO,CACnB,aAAa,EAAE,MAAM,EACrB,OAAO,GAAE,aAER,GACC,OAAO,CAAC,MAAM,CAAC;IAMlB,KAAK,IAAI,IAAI;IAIb,mBAAmB,CAAC,OAAO,EAAE,GAAG,GAAG,IAAI;WAI1B,SAAS,CACrB,aAAa,EAAE,MAAM,EACrB,OAAO,GAAE,gBAER,GACC,OAAO,CAAC,MAAM,CAAC;YAIJ,eAAe;YA0Cf,cAAc;IAyBtB,oBAAoB,CAAC,MAAM,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAgC1D,gBAAgB,CAC5B,YAAY,EAAE,MAAM,EACpB,OAAO,EAAE,MAAM,EACf,IAAI,EAAE,OAAO,EAAE,GAAG;QAAE,MAAM,EAAE,OAAO,CAAC;QAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;KAAE,GAC9D,OAAO,CAAC,OAAO,CAAC;IA6EZ,WAAW,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAI7C,OAAO,CAAC,kBAAkB;YAUZ,UAAU;IA+BlB,eAAe,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAatD,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAS1C;AAED;;;;;;;GAOG;AACH,wBAAsB,MAAM,CAC3B,aAAa,EAAE,MAAM,EACrB,OAAO,GAAE,aAER,GACC,OAAO,CAAC,MAAM,CAAC,CAEjB;AAED;;;;;;;GAOG;AACH,wBAAsB,eAAe,CACpC,aAAa,EAAE,MAAM,EACrB,OAAO,EAAE,gBAAgB,GACvB,OAAO,CAAC,MAAM,CAAC,CAEjB;AAED,MAAM,MAAM,cAAc,GAAG,MAAM,CAAC"}