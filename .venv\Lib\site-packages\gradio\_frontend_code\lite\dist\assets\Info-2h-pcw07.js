import{a as f,i as m,s as u,f as i,p as c,c as l,q as _,l as d,m as g,t as h,b as $,o as p,d as w}from"../lite.js";import{M as k}from"./MarkdownCode-C6RHM0m6.js";function q(r){let t,n,s;return n=new k({props:{root:r[1],message:r[0],sanitize_html:!0}}),{c(){t=c("div"),l(n.$$.fragment),_(t,"class","svelte-j9uq24")},m(e,o){d(e,t,o),g(n,t,null),s=!0},p(e,[o]){const a={};o&2&&(a.root=e[1]),o&1&&(a.message=e[0]),n.$set(a)},i(e){s||(h(n.$$.fragment,e),s=!0)},o(e){$(n.$$.fragment,e),s=!1},d(e){e&&p(t),w(n)}}}function v(r,t,n){let{info:s}=t,{root:e}=t;return r.$$set=o=>{"info"in o&&n(0,s=o.info),"root"in o&&n(1,e=o.root)},[s,e]}class I extends f{constructor(t){super(),m(this,t,v,q,u,{info:0,root:1})}get info(){return this.$$.ctx[0]}set info(t){this.$$set({info:t}),i()}get root(){return this.$$.ctx[1]}set root(t){this.$$set({root:t}),i()}}export{I};
//# sourceMappingURL=Info-2h-pcw07.js.map
