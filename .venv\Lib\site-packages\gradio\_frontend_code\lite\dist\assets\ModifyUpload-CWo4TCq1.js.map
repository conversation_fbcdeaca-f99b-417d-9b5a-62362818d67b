{"version": 3, "file": "ModifyUpload-CWo4TCq1.js", "sources": ["../../../icons/src/Edit.svelte", "../../../upload/src/ModifyUpload.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-edit-2\"\n>\n\t<path d=\"M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z\" />\n</svg>\n", "<script lang=\"ts\">\n\timport { IconButton, IconButtonWrapper } from \"@gradio/atoms\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { Edit, Clear, Undo, Download } from \"@gradio/icons\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let editable = false;\n\texport let undoable = false;\n\texport let download: string | null = null;\n\texport let i18n: I18nFormatter;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tedit?: never;\n\t\tclear?: never;\n\t\tundo?: never;\n\t}>();\n</script>\n\n<IconButtonWrapper>\n\t{#if editable}\n\t\t<IconButton\n\t\t\tIcon={Edit}\n\t\t\tlabel={i18n(\"common.edit\")}\n\t\t\ton:click={() => dispatch(\"edit\")}\n\t\t/>\n\t{/if}\n\n\t{#if undoable}\n\t\t<IconButton\n\t\t\tIcon={Undo}\n\t\t\tlabel={i18n(\"common.undo\")}\n\t\t\ton:click={() => dispatch(\"undo\")}\n\t\t/>\n\t{/if}\n\n\t{#if download}\n\t\t<DownloadLink href={download} download>\n\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t</DownloadLink>\n\t{/if}\n\n\t<slot />\n\n\t<IconButton\n\t\tIcon={Clear}\n\t\tlabel={i18n(\"common.clear\")}\n\t\ton:click={(event) => {\n\t\t\tdispatch(\"clear\");\n\t\t\tevent.stopPropagation();\n\t\t}}\n\t/>\n</IconButtonWrapper>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "Edit", "ctx", "dirty", "iconbutton_changes", "Undo", "Download", "create_if_block_2", "create_if_block_1", "create_if_block", "Clear", "editable", "$$props", "undoable", "download", "i18n", "dispatch", "createEventDispatcher", "click_handler", "click_handler_1", "event"], "mappings": "2xBAAAA,EAaKC,EAAAC,EAAAC,CAAA,EADJC,EAAmEF,EAAAG,CAAA,gJCW3DC,EACC,MAAAC,KAAK,aAAa,wFAAlBC,EAAA,IAAAC,EAAA,MAAAF,KAAK,aAAa,+IAOnBG,EACC,MAAAH,KAAK,aAAa,wFAAlBC,EAAA,IAAAC,EAAA,MAAAF,KAAK,aAAa,+IAMNA,EAAQ,CAAA,mIAARA,EAAQ,CAAA,kLACTI,EAAiB,MAAAJ,KAAK,iBAAiB,oEAAtBC,EAAA,IAAAC,EAAA,MAAAF,KAAK,iBAAiB,6HAlBtDA,EAAQ,CAAA,GAAAK,EAAAL,CAAA,IAQRA,EAAQ,CAAA,GAAAM,EAAAN,CAAA,IAQRA,EAAQ,CAAA,GAAAO,EAAAP,CAAA,sEASNQ,EACC,MAAAR,KAAK,cAAc,6NA1BtBA,EAAQ,CAAA,wGAQRA,EAAQ,CAAA,wGAQRA,EAAQ,CAAA,sLAULC,EAAA,IAAAC,EAAA,MAAAF,KAAK,cAAc,+gBAvChB,SAAAS,EAAW,EAAA,EAAAC,GACX,SAAAC,EAAW,EAAA,EAAAD,GACX,SAAAE,EAA0B,IAAA,EAAAF,EAC1B,CAAA,KAAAG,CAAA,EAAAH,QAELI,EAAWC,IAYCC,EAAA,IAAAF,EAAS,MAAM,EAQfG,EAAA,IAAAH,EAAS,MAAM,IAerBI,GAAK,CACfJ,EAAS,OAAO,EAChBI,EAAM,gBAAe"}