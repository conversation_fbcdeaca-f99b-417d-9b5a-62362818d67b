import{a as h,i as g,s as d,Q as o,q as e,l as m,v as i,w as l,o as u}from"../lite.js";function w(c){let t,r,s,n;return{c(){t=o("svg"),r=o("rect"),s=o("circle"),n=o("polyline"),e(r,"x","3"),e(r,"y","3"),e(r,"width","18"),e(r,"height","18"),e(r,"rx","2"),e(r,"ry","2"),e(s,"cx","8.5"),e(s,"cy","8.5"),e(s,"r","1.5"),e(n,"points","21 15 16 10 5 21"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-image")},m(a,p){m(a,t,p),i(t,r),i(t,s),i(t,n)},p:l,i:l,o:l,d(a){a&&u(t)}}}class x extends h{constructor(t){super(),g(this,t,null,w,d,{})}}export{x as I};
//# sourceMappingURL=Image-Cvn5Jo_E.js.map
