import{a as pe,i as ve,s as ze,Q as ue,q as b,l as W,v as V,w as O,o as Z,G as ke,f as h,I as te,a5 as se,p as N,c as P,x as ee,r as L,m as Y,M as Be,F as A,N as xe,a1 as $e,t as z,y as ie,b as S,z as ne,a6 as ae,O as et,d as j,P as tt,A as lt,C as it,D as nt,k as Te,n as Se,ao as Ce,$ as He,aq as st,ax as at,H as re,B as ut,Y as ot,S as ft,a0 as rt,a4 as _t}from"../lite.js";import{B as ct}from"./BlockTitle-BlPSRItZ.js";import{F as dt}from"./File-Kbo-bXuF.js";import{M as gt}from"./Music-FnJcUklZ.js";import{S as ht}from"./Send-DY8minAZ.js";import{S as mt}from"./Square-7hK0fZD1.js";import{V as bt}from"./Video-BIoWYjY-.js";import{U as kt}from"./Upload-TGDabKXH.js";import{I as wt}from"./Image-Bd-jnd8M.js";/* empty css                                                   */import{default as nl}from"./Example-EBq-hxw_.js";import"./Info-2h-pcw07.js";import"./MarkdownCode-C6RHM0m6.js";/* empty css                                             */import"./file-url-Co2ROWca.js";import"./Video-B2DSnbGg.js";import"./hls-CnVhpNcu.js";function pt(l){let e,t,i,a,s;return{c(){e=ue("svg"),t=ue("g"),i=ue("g"),a=ue("g"),s=ue("path"),b(t,"id","SVGRepo_bgCarrier"),b(t,"stroke-width","0"),b(i,"id","SVGRepo_tracerCarrier"),b(i,"stroke-linecap","round"),b(i,"stroke-linejoin","round"),b(s,"d","M1752.768 221.109C1532.646.986 1174.283.986 954.161 221.109l-838.588 838.588c-154.052 154.165-154.052 404.894 0 558.946 149.534 149.421 409.976 149.308 559.059 0l758.738-758.626c87.982-88.094 87.982-231.417 0-319.51-88.32-88.208-231.642-87.982-319.51 0l-638.796 638.908 79.85 79.849 638.795-638.908c43.934-43.821 115.539-43.934 159.812 0 43.934 44.047 43.934 115.877 0 159.812l-758.739 758.625c-110.23 110.118-289.355 110.005-399.36 0-110.118-110.117-110.005-289.242 0-399.247l838.588-838.588c175.963-175.962 462.382-176.188 638.909 0 176.075 176.188 176.075 462.833 0 638.908l-798.607 798.72 79.849 79.85 798.607-798.72c220.01-220.123 220.01-578.485 0-798.607"),b(s,"fill-rule","evenodd"),b(a,"id","SVGRepo_iconCarrier"),b(e,"fill","currentColor"),b(e,"width","100%"),b(e,"height","100%"),b(e,"viewBox","0 0 1920 1920"),b(e,"xmlns","http://www.w3.org/2000/svg")},m(d,u){W(d,e,u),V(e,t),V(e,i),V(e,a),V(a,s)},p:O,i:O,o:O,d(d){d&&Z(e)}}}class vt extends pe{constructor(e){super(),ve(this,e,null,pt,ze,{})}}async function we(l,e,t){if(await ke(),e===t)return;const i=window.getComputedStyle(l),a=parseFloat(i.paddingTop),s=parseFloat(i.paddingBottom),d=parseFloat(i.lineHeight);let u=t===void 0?!1:a+s+d*t,c=a+s+e*d;l.style.height="1px";let g;u&&l.scrollHeight>u?g=u:l.scrollHeight<c?g=c:g=l.scrollHeight,l.style.height=`${g}px`}function zt(l,e){if(e.lines===e.max_lines)return;l.style.overflowY="scroll";function t(i){we(i.target,e.lines,e.max_lines)}if(l.addEventListener("input",t),!!e.text.trim())return we(l,e.lines,e.max_lines),{destroy:()=>l.removeEventListener("input",t)}}function De(l,e,t){const i=l.slice();return i[59]=e[t],i[61]=t,i}function Tt(l){let e;return{c(){e=Te(l[5])},m(t,i){W(t,e,i)},p(t,i){i[0]&32&&Se(e,t[5])},d(t){t&&Z(e)}}}function Me(l){let e,t,i,a=Ce(l[0].files),s=[];for(let c=0;c<a.length;c+=1)s[c]=Fe(De(l,a,c));const d=c=>S(s[c],1,1,()=>{s[c]=null});let u=l[24]&&Ee();return{c(){e=N("div");for(let c=0;c<s.length;c+=1)s[c].c();t=ee(),u&&u.c(),b(e,"class","thumbnails scroll-hide svelte-1d7elt4"),b(e,"aria-label","Uploaded files"),b(e,"data-testid","container_el"),He(e,"display",l[0].files.length>0||l[24]?"flex":"none")},m(c,g){W(c,e,g);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(e,null);V(e,t),u&&u.m(e,null),i=!0},p(c,g){if(g[0]&1073741841){a=Ce(c[0].files);let f;for(f=0;f<a.length;f+=1){const p=De(c,a,f);s[f]?(s[f].p(p,g),z(s[f],1)):(s[f]=Fe(p),s[f].c(),z(s[f],1),s[f].m(e,t))}for(ie(),f=a.length;f<s.length;f+=1)d(f);ne()}c[24]?u||(u=Ee(),u.c(),u.m(e,null)):u&&(u.d(1),u=null),(!i||g[0]&16777217)&&He(e,"display",c[0].files.length>0||c[24]?"flex":"none")},i(c){if(!i){for(let g=0;g<a.length;g+=1)z(s[g]);i=!0}},o(c){s=s.filter(Boolean);for(let g=0;g<s.length;g+=1)S(s[g]);i=!1},d(c){c&&Z(e),st(s,c),u&&u.d()}}}function St(l){let e,t;return e=new dt({}),{c(){P(e.$$.fragment)},m(i,a){Y(e,i,a),t=!0},p:O,i(i){t||(z(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){j(e,i)}}}function Bt(l){let e,t;return e=new bt({}),{c(){P(e.$$.fragment)},m(i,a){Y(e,i,a),t=!0},p:O,i(i){t||(z(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){j(e,i)}}}function Ct(l){let e,t;return e=new gt({}),{c(){P(e.$$.fragment)},m(i,a){Y(e,i,a),t=!0},p:O,i(i){t||(z(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){j(e,i)}}}function Ht(l){let e,t;return e=new wt({props:{src:l[59].url,title:null,alt:"",loading:"lazy",class:"thumbnail-image"}}),{c(){P(e.$$.fragment)},m(i,a){Y(e,i,a),t=!0},p(i,a){const s={};a[0]&1&&(s.src=i[59].url),e.$set(s)},i(i){t||(z(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){j(e,i)}}}function Fe(l){let e,t,i,a,s,d,u,c,g,f,p,o,m;a=new at({});function v(...T){return l[44](l[61],...T)}const w=[Ht,Ct,Bt,St],F=[];function I(T,E){return E[0]&1&&(d=null),E[0]&1&&(u=null),E[0]&1&&(c=null),d==null&&(d=!!(T[59].mime_type&&T[59].mime_type.includes("image"))),d?0:(u==null&&(u=!!(T[59].mime_type&&T[59].mime_type.includes("audio"))),u?1:(c==null&&(c=!!(T[59].mime_type&&T[59].mime_type.includes("video"))),c?2:3))}return g=I(l,[-1,-1,-1]),f=F[g]=w[g](l),{c(){e=N("span"),t=N("button"),i=N("button"),P(a.$$.fragment),s=ee(),f.c(),b(i,"class","delete-button svelte-1d7elt4"),L(i,"disabled",l[4]),b(t,"class","thumbnail-item thumbnail-small svelte-1d7elt4"),b(e,"role","listitem"),b(e,"aria-label","File thumbnail")},m(T,E){W(T,e,E),V(e,t),V(t,i),Y(a,i,null),V(t,s),F[g].m(t,null),p=!0,o||(m=A(i,"click",v),o=!0)},p(T,E){l=T,(!p||E[0]&16)&&L(i,"disabled",l[4]);let G=g;g=I(l,E),g===G?F[g].p(l,E):(ie(),S(F[G],1,1,()=>{F[G]=null}),ne(),f=F[g],f?f.p(l,E):(f=F[g]=w[g](l),f.c()),z(f,1),f.m(t,null))},i(T){p||(z(a.$$.fragment,T),z(f),p=!0)},o(T){S(a.$$.fragment,T),S(f),p=!1},d(T){T&&Z(e),j(a),F[g].d(),o=!1,m()}}}function Ee(l){let e;return{c(){e=N("div"),b(e,"class","loader svelte-1d7elt4"),b(e,"role","status"),b(e,"aria-label","Uploading")},m(t,i){W(t,e,i)},d(t){t&&Z(e)}}}function Ue(l){let e,t,i,a,s,d;const u=[Mt,Dt],c=[];function g(f,p){return f[10]===!0?0:1}return t=g(l),i=c[t]=u[t](l),{c(){e=N("button"),i.c(),b(e,"class","submit-button svelte-1d7elt4"),L(e,"padded-button",l[10]!==!0)},m(f,p){W(f,e,p),c[t].m(e,null),a=!0,s||(d=A(e,"click",l[33]),s=!0)},p(f,p){let o=t;t=g(f),t===o?c[t].p(f,p):(ie(),S(c[o],1,1,()=>{c[o]=null}),ne(),i=c[t],i?i.p(f,p):(i=c[t]=u[t](f),i.c()),z(i,1),i.m(e,null)),(!a||p[0]&1024)&&L(e,"padded-button",f[10]!==!0)},i(f){a||(z(i),a=!0)},o(f){S(i),a=!1},d(f){f&&Z(e),c[t].d(),s=!1,d()}}}function Dt(l){let e;return{c(){e=Te(l[10])},m(t,i){W(t,e,i)},p(t,i){i[0]&1024&&Se(e,t[10])},i:O,o:O,d(t){t&&Z(e)}}}function Mt(l){let e,t;return e=new ht({}),{c(){P(e.$$.fragment)},m(i,a){Y(e,i,a),t=!0},p:O,i(i){t||(z(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){j(e,i)}}}function Ve(l){let e,t,i,a,s,d;const u=[Et,Ft],c=[];function g(f,p){return f[11]===!0?0:1}return t=g(l),i=c[t]=u[t](l),{c(){e=N("button"),i.c(),b(e,"class","stop-button svelte-1d7elt4"),L(e,"padded-button",l[11]!==!0)},m(f,p){W(f,e,p),c[t].m(e,null),a=!0,s||(d=A(e,"click",l[32]),s=!0)},p(f,p){let o=t;t=g(f),t===o?c[t].p(f,p):(ie(),S(c[o],1,1,()=>{c[o]=null}),ne(),i=c[t],i?i.p(f,p):(i=c[t]=u[t](f),i.c()),z(i,1),i.m(e,null)),(!a||p[0]&2048)&&L(e,"padded-button",f[11]!==!0)},i(f){a||(z(i),a=!0)},o(f){S(i),a=!1},d(f){f&&Z(e),c[t].d(),s=!1,d()}}}function Ft(l){let e;return{c(){e=Te(l[11])},m(t,i){W(t,e,i)},p(t,i){i[0]&2048&&Se(e,t[11])},i:O,o:O,d(t){t&&Z(e)}}}function Et(l){let e,t;return e=new mt({props:{fill:"none",stroke_width:2.5}}),{c(){P(e.$$.fragment)},m(i,a){Y(e,i,a),t=!0},p:O,i(i){t||(z(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){j(e,i)}}}function Ut(l){let e,t,i,a,s,d,u,c,g,f,p,o,m,v,w,F,I,T,E,G,D,$,le;i=new ct({props:{root:l[15],show_label:l[7],info:l[6],$$slots:{default:[Tt]},$$scope:{ctx:l}}});let C=(l[0].files.length>0||l[24])&&Me(l);function y(_){l[46](_)}function K(_){l[47](_)}function U(_){l[48](_)}let Q={file_count:l[20],filetype:l[16],root:l[15],max_file_size:l[17],show_progress:!1,disable_click:!0,hidden:!0,upload:l[18],stream_handler:l[19]};l[1]!==void 0&&(Q.dragging=l[1]),l[24]!==void 0&&(Q.uploading=l[24]),l[23]!==void 0&&(Q.hidden_upload=l[23]),u=new kt({props:Q}),l[45](u),te.push(()=>se(u,"dragging",y)),te.push(()=>se(u,"uploading",K)),te.push(()=>se(u,"hidden_upload",U)),u.$on("load",l[29]),u.$on("error",l[49]),m=new vt({});let B=l[10]&&Ue(l),H=l[11]&&Ve(l);return{c(){e=N("div"),t=N("label"),P(i.$$.fragment),a=ee(),C&&C.c(),s=ee(),d=N("div"),P(u.$$.fragment),p=ee(),o=N("button"),P(m.$$.fragment),v=ee(),w=N("textarea"),E=ee(),B&&B.c(),G=ee(),H&&H.c(),b(o,"data-testid","upload-button"),b(o,"class","upload-button svelte-1d7elt4"),b(w,"data-testid","textbox"),b(w,"class","scroll-hide svelte-1d7elt4"),b(w,"dir",F=l[12]?"rtl":"ltr"),b(w,"placeholder",l[3]),b(w,"rows",l[2]),w.disabled=l[4],w.autofocus=l[13],b(w,"style",I=l[14]?"text-align: "+l[14]:""),L(w,"no-label",!l[7]),b(d,"class","input-container svelte-1d7elt4"),L(t,"container",l[8]),b(e,"class","full-container svelte-1d7elt4"),b(e,"role","group"),b(e,"aria-label","Multimedia input field"),L(e,"dragging",l[1])},m(_,k){W(_,e,k),V(e,t),Y(i,t,null),V(t,a),C&&C.m(t,null),V(t,s),V(t,d),Y(u,d,null),V(d,p),V(d,o),Y(m,o,null),V(d,v),V(d,w),Be(w,l[0].text),l[51](w),V(d,E),B&&B.m(d,null),V(d,G),H&&H.m(d,null),l[52](e),D=!0,l[13]&&w.focus(),$||(le=[A(o,"click",l[31]),xe(T=zt.call(null,w,{text:l[0].text,lines:l[2],max_lines:l[9]})),A(w,"input",l[50]),A(w,"keypress",l[27]),A(w,"blur",l[42]),A(w,"select",l[26]),A(w,"focus",l[43]),A(w,"scroll",l[28]),A(w,"paste",l[34]),A(e,"dragenter",l[35]),A(e,"dragleave",l[36]),A(e,"dragover",$e(l[41])),A(e,"drop",l[37])],$=!0)},p(_,k){const X={};k[0]&32768&&(X.root=_[15]),k[0]&128&&(X.show_label=_[7]),k[0]&64&&(X.info=_[6]),k[0]&32|k[2]&1&&(X.$$scope={dirty:k,ctx:_}),i.$set(X),_[0].files.length>0||_[24]?C?(C.p(_,k),k[0]&16777217&&z(C,1)):(C=Me(_),C.c(),z(C,1),C.m(t,s)):C&&(ie(),S(C,1,1,()=>{C=null}),ne());const q={};k[0]&1048576&&(q.file_count=_[20]),k[0]&65536&&(q.filetype=_[16]),k[0]&32768&&(q.root=_[15]),k[0]&131072&&(q.max_file_size=_[17]),k[0]&262144&&(q.upload=_[18]),k[0]&524288&&(q.stream_handler=_[19]),!c&&k[0]&2&&(c=!0,q.dragging=_[1],ae(()=>c=!1)),!g&&k[0]&16777216&&(g=!0,q.uploading=_[24],ae(()=>g=!1)),!f&&k[0]&8388608&&(f=!0,q.hidden_upload=_[23],ae(()=>f=!1)),u.$set(q),(!D||k[0]&4096&&F!==(F=_[12]?"rtl":"ltr"))&&b(w,"dir",F),(!D||k[0]&8)&&b(w,"placeholder",_[3]),(!D||k[0]&4)&&b(w,"rows",_[2]),(!D||k[0]&16)&&(w.disabled=_[4]),(!D||k[0]&8192)&&(w.autofocus=_[13]),(!D||k[0]&16384&&I!==(I=_[14]?"text-align: "+_[14]:""))&&b(w,"style",I),T&&et(T.update)&&k[0]&517&&T.update.call(null,{text:_[0].text,lines:_[2],max_lines:_[9]}),k[0]&1&&Be(w,_[0].text),(!D||k[0]&128)&&L(w,"no-label",!_[7]),_[10]?B?(B.p(_,k),k[0]&1024&&z(B,1)):(B=Ue(_),B.c(),z(B,1),B.m(d,G)):B&&(ie(),S(B,1,1,()=>{B=null}),ne()),_[11]?H?(H.p(_,k),k[0]&2048&&z(H,1)):(H=Ve(_),H.c(),z(H,1),H.m(d,null)):H&&(ie(),S(H,1,1,()=>{H=null}),ne()),(!D||k[0]&256)&&L(t,"container",_[8]),(!D||k[0]&2)&&L(e,"dragging",_[1])},i(_){D||(z(i.$$.fragment,_),z(C),z(u.$$.fragment,_),z(m.$$.fragment,_),z(B),z(H),D=!0)},o(_){S(i.$$.fragment,_),S(C),S(u.$$.fragment,_),S(m.$$.fragment,_),S(B),S(H),D=!1},d(_){_&&Z(e),j(i),C&&C.d(),l[45](null),j(u),j(m),l[51](null),B&&B.d(),H&&H.d(),l[52](null),$=!1,tt(le)}}}function Vt(l,e,t){let{value:i={text:"",files:[]}}=e,{value_is_output:a=!1}=e,{lines:s=1}=e,{placeholder:d="Type here..."}=e,{disabled:u=!1}=e,{label:c}=e,{info:g=void 0}=e,{show_label:f=!0}=e,{container:p=!0}=e,{max_lines:o}=e,{submit_btn:m=null}=e,{stop_btn:v=null}=e,{rtl:w=!1}=e,{autofocus:F=!1}=e,{text_align:I=void 0}=e,{autoscroll:T=!0}=e,{root:E}=e,{file_types:G=null}=e,{max_file_size:D=null}=e,{upload:$}=e,{stream_handler:le}=e,{file_count:C="multiple"}=e,y,K,U,Q,B=0,H=!1,{dragging:_=!1}=e,k=!1,X=i.text,q;const R=lt();it(()=>{Q=U&&U.offsetHeight+U.scrollTop>U.scrollHeight-100});const _e=()=>{Q&&T&&!H&&U.scrollTo(0,U.scrollHeight)};async function oe(){R("change",i),a||R("input")}nt(()=>{F&&U!==null&&U.focus(),Q&&T&&_e(),t(38,a=!1)});function ce(n){const M=n.target,x=M.value,J=[M.selectionStart,M.selectionEnd];R("select",{value:x.substring(...J),index:J})}async function de(n){await ke(),(n.key==="Enter"&&n.shiftKey&&s>1||n.key==="Enter"&&!n.shiftKey&&s===1&&o>=1)&&(n.preventDefault(),R("submit"))}function ge(n){const M=n.target,x=M.scrollTop;x<B&&(H=!0),B=x;const J=M.scrollHeight-M.clientHeight;x>=J&&(H=!1)}async function he({detail:n}){if(oe(),Array.isArray(n)){for(let M of n)i.files.push(M);t(0,i)}else i.files.push(n),t(0,i);await ke(),R("change",i),R("upload",n)}function fe(n,M){oe(),n.stopPropagation(),i.files.splice(M,1),t(0,i)}function me(){K&&(t(23,K.value="",K),K.click())}function r(){R("stop")}function Ae(){R("submit")}function Ge(n){if(!n.clipboardData)return;const M=n.clipboardData.items;for(let x in M){const J=M[x];if(J.kind==="file"&&J.type.includes("image")){const be=J.getAsFile();be&&y.load_files([be])}}}function Ie(n){n.preventDefault(),t(1,_=!0)}function Re(n){n.preventDefault();const M=q.getBoundingClientRect(),{clientX:x,clientY:J}=n;(x<=M.left||x>=M.right||J<=M.top||J>=M.bottom)&&t(1,_=!1)}function Pe(n){n.preventDefault(),t(1,_=!1),n.dataTransfer&&n.dataTransfer.files&&y.load_files(Array.from(n.dataTransfer.files))}function Ye(n){re.call(this,l,n)}function je(n){re.call(this,l,n)}function Ke(n){re.call(this,l,n)}const Le=(n,M)=>fe(M,n);function Ne(n){te[n?"unshift":"push"](()=>{y=n,t(22,y)})}function Oe(n){_=n,t(1,_)}function Qe(n){k=n,t(24,k)}function Xe(n){K=n,t(23,K)}function Je(n){re.call(this,l,n)}function We(){i.text=this.value,t(0,i)}function Ze(n){te[n?"unshift":"push"](()=>{U=n,t(21,U)})}function ye(n){te[n?"unshift":"push"](()=>{q=n,t(25,q)})}return l.$$set=n=>{"value"in n&&t(0,i=n.value),"value_is_output"in n&&t(38,a=n.value_is_output),"lines"in n&&t(2,s=n.lines),"placeholder"in n&&t(3,d=n.placeholder),"disabled"in n&&t(4,u=n.disabled),"label"in n&&t(5,c=n.label),"info"in n&&t(6,g=n.info),"show_label"in n&&t(7,f=n.show_label),"container"in n&&t(8,p=n.container),"max_lines"in n&&t(9,o=n.max_lines),"submit_btn"in n&&t(10,m=n.submit_btn),"stop_btn"in n&&t(11,v=n.stop_btn),"rtl"in n&&t(12,w=n.rtl),"autofocus"in n&&t(13,F=n.autofocus),"text_align"in n&&t(14,I=n.text_align),"autoscroll"in n&&t(39,T=n.autoscroll),"root"in n&&t(15,E=n.root),"file_types"in n&&t(16,G=n.file_types),"max_file_size"in n&&t(17,D=n.max_file_size),"upload"in n&&t(18,$=n.upload),"stream_handler"in n&&t(19,le=n.stream_handler),"file_count"in n&&t(20,C=n.file_count),"dragging"in n&&t(1,_=n.dragging)},l.$$.update=()=>{l.$$.dirty[0]&2&&R("drag",_),l.$$.dirty[0]&1&&i===null&&t(0,i={text:"",files:[]}),l.$$.dirty[0]&1|l.$$.dirty[1]&512&&X!==i.text&&(R("change",i),t(40,X=i.text)),l.$$.dirty[0]&2097669&&U&&s!==o&&we(U,s,o)},[i,_,s,d,u,c,g,f,p,o,m,v,w,F,I,E,G,D,$,le,C,U,y,K,k,q,ce,de,ge,he,fe,me,r,Ae,Ge,Ie,Re,Pe,a,T,X,Ye,je,Ke,Le,Ne,Oe,Qe,Xe,Je,We,Ze,ye]}class qt extends pe{constructor(e){super(),ve(this,e,Vt,Ut,ze,{value:0,value_is_output:38,lines:2,placeholder:3,disabled:4,label:5,info:6,show_label:7,container:8,max_lines:9,submit_btn:10,stop_btn:11,rtl:12,autofocus:13,text_align:14,autoscroll:39,root:15,file_types:16,max_file_size:17,upload:18,stream_handler:19,file_count:20,dragging:1},null,[-1,-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get value_is_output(){return this.$$.ctx[38]}set value_is_output(e){this.$$set({value_is_output:e}),h()}get lines(){return this.$$.ctx[2]}set lines(e){this.$$set({lines:e}),h()}get placeholder(){return this.$$.ctx[3]}set placeholder(e){this.$$set({placeholder:e}),h()}get disabled(){return this.$$.ctx[4]}set disabled(e){this.$$set({disabled:e}),h()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),h()}get info(){return this.$$.ctx[6]}set info(e){this.$$set({info:e}),h()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),h()}get container(){return this.$$.ctx[8]}set container(e){this.$$set({container:e}),h()}get max_lines(){return this.$$.ctx[9]}set max_lines(e){this.$$set({max_lines:e}),h()}get submit_btn(){return this.$$.ctx[10]}set submit_btn(e){this.$$set({submit_btn:e}),h()}get stop_btn(){return this.$$.ctx[11]}set stop_btn(e){this.$$set({stop_btn:e}),h()}get rtl(){return this.$$.ctx[12]}set rtl(e){this.$$set({rtl:e}),h()}get autofocus(){return this.$$.ctx[13]}set autofocus(e){this.$$set({autofocus:e}),h()}get text_align(){return this.$$.ctx[14]}set text_align(e){this.$$set({text_align:e}),h()}get autoscroll(){return this.$$.ctx[39]}set autoscroll(e){this.$$set({autoscroll:e}),h()}get root(){return this.$$.ctx[15]}set root(e){this.$$set({root:e}),h()}get file_types(){return this.$$.ctx[16]}set file_types(e){this.$$set({file_types:e}),h()}get max_file_size(){return this.$$.ctx[17]}set max_file_size(e){this.$$set({max_file_size:e}),h()}get upload(){return this.$$.ctx[18]}set upload(e){this.$$set({upload:e}),h()}get stream_handler(){return this.$$.ctx[19]}set stream_handler(e){this.$$set({stream_handler:e}),h()}get file_count(){return this.$$.ctx[20]}set file_count(e){this.$$set({file_count:e}),h()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),h()}}const At=qt;function qe(l){let e,t;const i=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[18]];let a={};for(let s=0;s<i.length;s+=1)a=ot(a,i[s]);return e=new ft({props:a}),e.$on("clear_status",l[27]),{c(){P(e.$$.fragment)},m(s,d){Y(e,s,d),t=!0},p(s,d){const u=d[0]&262148?rt(i,[d[0]&4&&{autoscroll:s[2].autoscroll},d[0]&4&&{i18n:s[2].i18n},d[0]&262144&&_t(s[18])]):{};e.$set(u)},i(s){t||(z(e.$$.fragment,s),t=!0)},o(s){S(e.$$.fragment,s),t=!1},d(s){j(e,s)}}}function Gt(l){let e,t,i,a,s,d,u=l[18]&&qe(l);function c(o){l[30](o)}function g(o){l[31](o)}function f(o){l[32](o)}let p={file_types:l[6],root:l[24],label:l[9],info:l[10],show_label:l[11],lines:l[7],rtl:l[19],text_align:l[20],max_lines:l[12]?l[12]:l[7]+1,placeholder:l[8],submit_btn:l[16],stop_btn:l[17],autofocus:l[21],container:l[13],autoscroll:l[22],file_count:l[25],max_file_size:l[2].max_file_size,disabled:!l[23],upload:l[28],stream_handler:l[29]};return l[0]!==void 0&&(p.value=l[0]),l[1]!==void 0&&(p.value_is_output=l[1]),l[26]!==void 0&&(p.dragging=l[26]),t=new At({props:p}),te.push(()=>se(t,"value",c)),te.push(()=>se(t,"value_is_output",g)),te.push(()=>se(t,"dragging",f)),t.$on("change",l[33]),t.$on("input",l[34]),t.$on("submit",l[35]),t.$on("stop",l[36]),t.$on("blur",l[37]),t.$on("select",l[38]),t.$on("focus",l[39]),t.$on("error",l[40]),{c(){u&&u.c(),e=ee(),P(t.$$.fragment)},m(o,m){u&&u.m(o,m),W(o,e,m),Y(t,o,m),d=!0},p(o,m){o[18]?u?(u.p(o,m),m[0]&262144&&z(u,1)):(u=qe(o),u.c(),z(u,1),u.m(e.parentNode,e)):u&&(ie(),S(u,1,1,()=>{u=null}),ne());const v={};m[0]&64&&(v.file_types=o[6]),m[0]&16777216&&(v.root=o[24]),m[0]&512&&(v.label=o[9]),m[0]&1024&&(v.info=o[10]),m[0]&2048&&(v.show_label=o[11]),m[0]&128&&(v.lines=o[7]),m[0]&524288&&(v.rtl=o[19]),m[0]&1048576&&(v.text_align=o[20]),m[0]&4224&&(v.max_lines=o[12]?o[12]:o[7]+1),m[0]&256&&(v.placeholder=o[8]),m[0]&65536&&(v.submit_btn=o[16]),m[0]&131072&&(v.stop_btn=o[17]),m[0]&2097152&&(v.autofocus=o[21]),m[0]&8192&&(v.container=o[13]),m[0]&4194304&&(v.autoscroll=o[22]),m[0]&33554432&&(v.file_count=o[25]),m[0]&4&&(v.max_file_size=o[2].max_file_size),m[0]&8388608&&(v.disabled=!o[23]),m[0]&4&&(v.upload=o[28]),m[0]&4&&(v.stream_handler=o[29]),!i&&m[0]&1&&(i=!0,v.value=o[0],ae(()=>i=!1)),!a&&m[0]&2&&(a=!0,v.value_is_output=o[1],ae(()=>a=!1)),!s&&m[0]&67108864&&(s=!0,v.dragging=o[26],ae(()=>s=!1)),t.$set(v)},i(o){d||(z(u),z(t.$$.fragment,o),d=!0)},o(o){S(u),S(t.$$.fragment,o),d=!1},d(o){o&&Z(e),u&&u.d(o),j(t,o)}}}function It(l){let e,t;return e=new ut({props:{visible:l[5],elem_id:l[3],elem_classes:[...l[4],"multimodal-textbox"],scale:l[14],min_width:l[15],allow_overflow:!1,padding:l[13],border_mode:l[26]?"focus":"base",$$slots:{default:[Gt]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(i,a){Y(e,i,a),t=!0},p(i,a){const s={};a[0]&32&&(s.visible=i[5]),a[0]&8&&(s.elem_id=i[3]),a[0]&16&&(s.elem_classes=[...i[4],"multimodal-textbox"]),a[0]&16384&&(s.scale=i[14]),a[0]&32768&&(s.min_width=i[15]),a[0]&8192&&(s.padding=i[13]),a[0]&67108864&&(s.border_mode=i[26]?"focus":"base"),a[0]&134168519|a[1]&1024&&(s.$$scope={dirty:a,ctx:i}),e.$set(s)},i(i){t||(z(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){j(e,i)}}}function Rt(l,e,t){let{gradio:i}=e,{elem_id:a=""}=e,{elem_classes:s=[]}=e,{visible:d=!0}=e,{value:u={text:"",files:[]}}=e,{file_types:c=null}=e,{lines:g}=e,{placeholder:f=""}=e,{label:p="MultimodalTextbox"}=e,{info:o=void 0}=e,{show_label:m}=e,{max_lines:v}=e,{container:w=!0}=e,{scale:F=null}=e,{min_width:I=void 0}=e,{submit_btn:T=null}=e,{stop_btn:E=null}=e,{loading_status:G=void 0}=e,{value_is_output:D=!1}=e,{rtl:$=!1}=e,{text_align:le=void 0}=e,{autofocus:C=!1}=e,{autoscroll:y=!0}=e,{interactive:K}=e,{root:U}=e,{file_count:Q}=e,B;const H=()=>i.dispatch("clear_status",G),_=(...r)=>i.client.upload(...r),k=(...r)=>i.client.stream(...r);function X(r){u=r,t(0,u)}function q(r){D=r,t(1,D)}function R(r){B=r,t(26,B)}const _e=()=>i.dispatch("change",u),oe=()=>i.dispatch("input"),ce=()=>i.dispatch("submit"),de=()=>i.dispatch("stop"),ge=()=>i.dispatch("blur"),he=r=>i.dispatch("select",r.detail),fe=()=>i.dispatch("focus"),me=({detail:r})=>{i.dispatch("error",r)};return l.$$set=r=>{"gradio"in r&&t(2,i=r.gradio),"elem_id"in r&&t(3,a=r.elem_id),"elem_classes"in r&&t(4,s=r.elem_classes),"visible"in r&&t(5,d=r.visible),"value"in r&&t(0,u=r.value),"file_types"in r&&t(6,c=r.file_types),"lines"in r&&t(7,g=r.lines),"placeholder"in r&&t(8,f=r.placeholder),"label"in r&&t(9,p=r.label),"info"in r&&t(10,o=r.info),"show_label"in r&&t(11,m=r.show_label),"max_lines"in r&&t(12,v=r.max_lines),"container"in r&&t(13,w=r.container),"scale"in r&&t(14,F=r.scale),"min_width"in r&&t(15,I=r.min_width),"submit_btn"in r&&t(16,T=r.submit_btn),"stop_btn"in r&&t(17,E=r.stop_btn),"loading_status"in r&&t(18,G=r.loading_status),"value_is_output"in r&&t(1,D=r.value_is_output),"rtl"in r&&t(19,$=r.rtl),"text_align"in r&&t(20,le=r.text_align),"autofocus"in r&&t(21,C=r.autofocus),"autoscroll"in r&&t(22,y=r.autoscroll),"interactive"in r&&t(23,K=r.interactive),"root"in r&&t(24,U=r.root),"file_count"in r&&t(25,Q=r.file_count)},[u,D,i,a,s,d,c,g,f,p,o,m,v,w,F,I,T,E,G,$,le,C,y,K,U,Q,B,H,_,k,X,q,R,_e,oe,ce,de,ge,he,fe,me]}class tl extends pe{constructor(e){super(),ve(this,e,Rt,It,ze,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,file_types:6,lines:7,placeholder:8,label:9,info:10,show_label:11,max_lines:12,container:13,scale:14,min_width:15,submit_btn:16,stop_btn:17,loading_status:18,value_is_output:1,rtl:19,text_align:20,autofocus:21,autoscroll:22,interactive:23,root:24,file_count:25},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),h()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),h()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get file_types(){return this.$$.ctx[6]}set file_types(e){this.$$set({file_types:e}),h()}get lines(){return this.$$.ctx[7]}set lines(e){this.$$set({lines:e}),h()}get placeholder(){return this.$$.ctx[8]}set placeholder(e){this.$$set({placeholder:e}),h()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),h()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),h()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),h()}get max_lines(){return this.$$.ctx[12]}set max_lines(e){this.$$set({max_lines:e}),h()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),h()}get submit_btn(){return this.$$.ctx[16]}set submit_btn(e){this.$$set({submit_btn:e}),h()}get stop_btn(){return this.$$.ctx[17]}set stop_btn(e){this.$$set({stop_btn:e}),h()}get loading_status(){return this.$$.ctx[18]}set loading_status(e){this.$$set({loading_status:e}),h()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),h()}get rtl(){return this.$$.ctx[19]}set rtl(e){this.$$set({rtl:e}),h()}get text_align(){return this.$$.ctx[20]}set text_align(e){this.$$set({text_align:e}),h()}get autofocus(){return this.$$.ctx[21]}set autofocus(e){this.$$set({autofocus:e}),h()}get autoscroll(){return this.$$.ctx[22]}set autoscroll(e){this.$$set({autoscroll:e}),h()}get interactive(){return this.$$.ctx[23]}set interactive(e){this.$$set({interactive:e}),h()}get root(){return this.$$.ctx[24]}set root(e){this.$$set({root:e}),h()}get file_count(){return this.$$.ctx[25]}set file_count(e){this.$$set({file_count:e}),h()}}export{nl as BaseExample,At as BaseMultimodalTextbox,tl as default};
//# sourceMappingURL=Index-CDWXDux1.js.map
