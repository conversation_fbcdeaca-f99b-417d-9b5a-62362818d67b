import{a as c,i as g,s as d,f,p as h,k as o,q as v,r as u,l as m,v as y,n as _,w as r,o as p}from"../lite.js";function b(l){let e,a=(l[0]?l[0]:"")+"",n;return{c(){e=h("pre"),n=o(a),v(e,"class","svelte-agpzo2"),u(e,"table",l[1]==="table"),u(e,"gallery",l[1]==="gallery"),u(e,"selected",l[2])},m(t,s){m(t,e,s),y(e,n)},p(t,[s]){s&1&&a!==(a=(t[0]?t[0]:"")+"")&&_(n,a),s&2&&u(e,"table",t[1]==="table"),s&2&&u(e,"gallery",t[1]==="gallery"),s&4&&u(e,"selected",t[2])},i:r,o:r,d(t){t&&p(e)}}}function q(l,e,a){let{value:n}=e,{type:t}=e,{selected:s=!1}=e;return l.$$set=i=>{"value"in i&&a(0,n=i.value),"type"in i&&a(1,t=i.type),"selected"in i&&a(2,s=i.selected)},[n,t,s]}class w extends c{constructor(e){super(),g(this,e,q,b,d,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),f()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),f()}}export{w as default};
//# sourceMappingURL=Example-CSkYOt-V.js.map
