import{a as B,i as G,s as H,f as q,E as C,l as k,y as F,b as d,z as N,t as h,o as w,X as D,A as S,T as V,Y as m,Z as X,U as Y,e as y,p as O,_ as p,$ as W,r as b,F as P,u as E,h as U,j as L,a0 as T,a1 as Z}from"../lite.js";import{s as I}from"./file-url-Co2ROWca.js";function J(f){let e,t,o,r,u;const a=f[8].default,s=y(a,f,f[7],null);let i=[{class:"download-link"},{href:f[0]},{target:t=typeof window<"u"&&window.__is_colab__?"_blank":null},{rel:"noopener noreferrer"},{download:f[1]},f[6]],l={};for(let n=0;n<i.length;n+=1)l=m(l,i[n]);return{c(){e=O("a"),s&&s.c(),p(e,l),W(e,"position","relative"),b(e,"svelte-1s8vnbx",!0)},m(n,_){k(n,e,_),s&&s.m(e,null),o=!0,r||(u=P(e,"click",f[3].bind(null,"click")),r=!0)},p(n,_){s&&s.p&&(!o||_&128)&&E(s,a,n,n[7],o?L(a,n[7],_,null):U(n[7]),null),p(e,l=T(i,[{class:"download-link"},(!o||_&1)&&{href:n[0]},{target:t},{rel:"noopener noreferrer"},(!o||_&2)&&{download:n[1]},_&64&&n[6]])),W(e,"position","relative"),b(e,"svelte-1s8vnbx",!0)},i(n){o||(h(s,n),o=!0)},o(n){d(s,n),o=!1},d(n){n&&w(e),s&&s.d(n),r=!1,u()}}}function K(f){let e,t,o,r;const u=[Q,M],a=[];function s(i,l){return i[2]?0:1}return e=s(f),t=a[e]=u[e](f),{c(){t.c(),o=C()},m(i,l){a[e].m(i,l),k(i,o,l),r=!0},p(i,l){let n=e;e=s(i),e===n?a[e].p(i,l):(F(),d(a[n],1,1,()=>{a[n]=null}),N(),t=a[e],t?t.p(i,l):(t=a[e]=u[e](i),t.c()),h(t,1),t.m(o.parentNode,o))},i(i){r||(h(t),r=!0)},o(i){d(t),r=!1},d(i){i&&w(o),a[e].d(i)}}}function M(f){let e,t,o,r;const u=f[8].default,a=y(u,f,f[7],null);let s=[f[6],{href:f[0]}],i={};for(let l=0;l<s.length;l+=1)i=m(i,s[l]);return{c(){e=O("a"),a&&a.c(),p(e,i),b(e,"svelte-1s8vnbx",!0)},m(l,n){k(l,e,n),a&&a.m(e,null),t=!0,o||(r=P(e,"click",Z(f[5])),o=!0)},p(l,n){a&&a.p&&(!t||n&128)&&E(a,u,l,l[7],t?L(u,l[7],n,null):U(l[7]),null),p(e,i=T(s,[n&64&&l[6],(!t||n&1)&&{href:l[0]}])),b(e,"svelte-1s8vnbx",!0)},i(l){t||(h(a,l),t=!0)},o(l){d(a,l),t=!1},d(l){l&&w(e),a&&a.d(l),o=!1,r()}}}function Q(f){let e;const t=f[8].default,o=y(t,f,f[7],null);return{c(){o&&o.c()},m(r,u){o&&o.m(r,u),e=!0},p(r,u){o&&o.p&&(!e||u&128)&&E(o,t,r,r[7],e?L(t,r[7],u,null):U(r[7]),null)},i(r){e||(h(o,r),e=!0)},o(r){d(o,r),e=!1},d(r){o&&o.d(r)}}}function $(f){let e,t,o,r,u;const a=[K,J],s=[];function i(l,n){return n&1&&(e=null),e==null&&(e=!!(l[4]&&I(l[0]))),e?0:1}return t=i(f,-1),o=s[t]=a[t](f),{c(){o.c(),r=C()},m(l,n){s[t].m(l,n),k(l,r,n),u=!0},p(l,[n]){let _=t;t=i(l,n),t===_?s[t].p(l,n):(F(),d(s[_],1,1,()=>{s[_]=null}),N(),o=s[t],o?o.p(l,n):(o=s[t]=a[t](l),o.c()),h(o,1),o.m(r.parentNode,r))},i(l){u||(h(o),u=!0)},o(l){d(o),u=!1},d(l){l&&w(r),s[t].d(l)}}}function x(f,e,t){const o=["href","download"];let r=D(e,o),{$$slots:u={},$$scope:a}=e,{href:s=void 0}=e,{download:i}=e;const l=S();let n=!1;const _=V();async function z(){if(n)return;if(l("click"),s==null)throw new Error("href is not defined.");if(_==null)throw new Error("Wasm worker proxy is not available.");const R=new URL(s,window.location.href).pathname;t(2,n=!0),_.httpRequest({method:"GET",path:R,headers:{},query_string:""}).then(v=>{if(v.status!==200)throw new Error(`Failed to get file ${R} from the Wasm worker.`);const A=new Blob([v.body],{type:Y(v.headers,"content-type")}),j=URL.createObjectURL(A),g=document.createElement("a");g.href=j,g.download=i,g.click(),URL.revokeObjectURL(j)}).finally(()=>{t(2,n=!1)})}return f.$$set=c=>{e=m(m({},e),X(c)),t(6,r=D(e,o)),"href"in c&&t(0,s=c.href),"download"in c&&t(1,i=c.download),"$$scope"in c&&t(7,a=c.$$scope)},[s,i,n,l,_,z,r,a,u]}class te extends B{constructor(e){super(),G(this,e,x,$,H,{href:0,download:1})}get href(){return this.$$.ctx[0]}set href(e){this.$$set({href:e}),q()}get download(){return this.$$.ctx[1]}set download(e){this.$$set({download:e}),q()}}export{te as D};
//# sourceMappingURL=DownloadLink-B8hI46-W.js.map
