{"version": 3, "file": "Index56-B8Q3Lwup.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index56.js"], "sourcesContent": ["import { create_ssr_component, validate_component, missing_component, escape, add_styles, each, add_attribute, null_to_empty } from \"svelte/internal\";\nimport { g as <PERSON><PERSON><PERSON><PERSON><PERSON>, r as <PERSON><PERSON>, f as IconButtonWrapper, a8 as <PERSON><PERSON>, N as Undo, l as Markdown<PERSON>ode, a9 as Community, aa as Trash, ab as ScrollDownArrow, B as Block, S as Static, e as BlockLabel, ac as Chat } from \"./client.js\";\nimport { I as Image } from \"./Image.js\";\nimport \"./ImagePreview.js\";\nimport { onDestroy, onMount, createEventDispatcher, afterUpdate } from \"svelte\";\nimport { D as DownloadLink } from \"./DownloadLink.js\";\nimport { d as dequal } from \"./index6.js\";\nconst redirect_src_url = (src, root) => src.replace('src=\"/file', `src=\"${root}file`);\nfunction get_component_for_mime_type(mime_type) {\n  if (!mime_type)\n    return \"file\";\n  if (mime_type.includes(\"audio\"))\n    return \"audio\";\n  if (mime_type.includes(\"video\"))\n    return \"video\";\n  if (mime_type.includes(\"image\"))\n    return \"image\";\n  return \"file\";\n}\nfunction convert_file_message_to_component_message(message) {\n  const _file = Array.isArray(message.file) ? message.file[0] : message.file;\n  return {\n    component: get_component_for_mime_type(_file?.mime_type),\n    value: message.file,\n    alt_text: message.alt_text,\n    constructor_args: {},\n    props: {}\n  };\n}\nfunction normalise_messages(messages, root) {\n  if (messages === null)\n    return messages;\n  return messages.map((message, i) => {\n    if (typeof message.content === \"string\") {\n      return {\n        role: message.role,\n        metadata: message.metadata,\n        content: redirect_src_url(message.content, root),\n        type: \"text\",\n        index: i\n      };\n    } else if (\"file\" in message.content) {\n      return {\n        content: convert_file_message_to_component_message(message.content),\n        metadata: message.metadata,\n        role: message.role,\n        type: \"component\",\n        index: i\n      };\n    }\n    return { type: \"component\", ...message };\n  });\n}\nfunction normalise_tuples(messages, root) {\n  if (messages === null)\n    return messages;\n  const msg = messages.flatMap((message_pair, i) => {\n    return message_pair.map((message, index) => {\n      if (message == null)\n        return null;\n      const role = index == 0 ? \"user\" : \"assistant\";\n      if (typeof message === \"string\") {\n        return {\n          role,\n          type: \"text\",\n          content: redirect_src_url(message, root),\n          metadata: { title: null },\n          index: [i, index]\n        };\n      }\n      if (\"file\" in message) {\n        return {\n          content: convert_file_message_to_component_message(message),\n          role,\n          type: \"component\",\n          index: [i, index]\n        };\n      }\n      return {\n        role,\n        content: message,\n        type: \"component\",\n        index: [i, index]\n      };\n    });\n  });\n  return msg.filter((message) => message != null);\n}\nfunction is_component_message(message) {\n  return message.type === \"component\";\n}\nfunction is_last_bot_message(messages, all_messages) {\n  const is_bot = messages[messages.length - 1].role === \"assistant\";\n  const last_index = messages[messages.length - 1].index;\n  const is_last = JSON.stringify(last_index) === JSON.stringify(all_messages[all_messages.length - 1].index);\n  return is_last && is_bot;\n}\nfunction group_messages(messages, msg_format) {\n  const groupedMessages = [];\n  let currentGroup = [];\n  let currentRole = null;\n  for (const message of messages) {\n    if (msg_format === \"tuples\") {\n      currentRole = null;\n    }\n    if (!(message.role === \"assistant\" || message.role === \"user\")) {\n      continue;\n    }\n    if (message.role === currentRole) {\n      currentGroup.push(message);\n    } else {\n      if (currentGroup.length > 0) {\n        groupedMessages.push(currentGroup);\n      }\n      currentGroup = [message];\n      currentRole = message.role;\n    }\n  }\n  if (currentGroup.length > 0) {\n    groupedMessages.push(currentGroup);\n  }\n  return groupedMessages;\n}\nasync function load_components(component_names, _components, load_component) {\n  let names = [];\n  let components = [];\n  component_names.forEach((component_name) => {\n    if (_components[component_name] || component_name === \"file\") {\n      return;\n    }\n    const { name, component } = load_component(component_name, \"base\");\n    names.push(name);\n    components.push(component);\n  });\n  const loaded_components = await Promise.all(components);\n  loaded_components.forEach((component, i) => {\n    _components[names[i]] = component.default;\n  });\n  return _components;\n}\nfunction get_components_from_messages(messages) {\n  if (!messages)\n    return [];\n  let components = /* @__PURE__ */ new Set();\n  messages.forEach((message) => {\n    if (message.type === \"component\") {\n      components.add(message.content.component);\n    }\n  });\n  return Array.from(components);\n}\nconst Component = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { type } = $$props;\n  let { components } = $$props;\n  let { value } = $$props;\n  let { target } = $$props;\n  let { theme_mode } = $$props;\n  let { props } = $$props;\n  let { i18n } = $$props;\n  let { upload } = $$props;\n  let { _fetch } = $$props;\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.components === void 0 && $$bindings.components && components !== void 0)\n    $$bindings.components(components);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.target === void 0 && $$bindings.target && target !== void 0)\n    $$bindings.target(target);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.props === void 0 && $$bindings.props && props !== void 0)\n    $$bindings.props(props);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  return `${type === \"gallery\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      show_label: false,\n      i18n,\n      label: \"\",\n      _fetch,\n      allow_preview: false,\n      interactive: false,\n      mode: \"minimal\",\n      fixed_height: 1\n    },\n    {},\n    {}\n  )}` : `${type === \"plot\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      target,\n      theme_mode,\n      bokeh_version: props.bokeh_version,\n      caption: \"\",\n      show_actions_button: true\n    },\n    {},\n    {}\n  )}` : `${type === \"audio\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      show_label: false,\n      show_share_button: true,\n      i18n,\n      label: \"\",\n      waveform_settings: {},\n      waveform_options: {},\n      show_download_button: false\n    },\n    {},\n    {}\n  )}` : `${type === \"video\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      autoplay: true,\n      value: value.video || value,\n      show_label: false,\n      show_share_button: true,\n      i18n,\n      upload,\n      show_download_button: false\n    },\n    {},\n    {\n      default: () => {\n        return `<track kind=\"captions\">`;\n      }\n    }\n  )}` : `${type === \"image\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      show_label: false,\n      label: \"chatbot-image\",\n      show_download_button: false,\n      i18n\n    },\n    {},\n    {}\n  )}` : `${type === \"html\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      show_label: false,\n      label: \"chatbot-image\",\n      show_share_button: true,\n      i18n,\n      gradio: {\n        dispatch: () => {\n        }\n      }\n    },\n    {},\n    {}\n  )}` : ``}`}`}`}`}`}`;\n});\nconst css$5 = {\n  code: \".box.svelte-1e60bn1{border-radius:4px;cursor:pointer;max-width:max-content;background:var(--color-accent-soft);border:1px solid var(--border-color-accent-subdued);font-size:0.8em}.title.svelte-1e60bn1{display:flex;align-items:center;padding:3px 6px;color:var(--body-text-color);opacity:0.8}.content.svelte-1e60bn1{padding:4px 8px}.content.svelte-1e60bn1 *{font-size:0.8em}.title-text.svelte-1e60bn1{padding-right:var(--spacing-lg)}.arrow.svelte-1e60bn1{margin-left:auto;opacity:0.8}\",\n  map: '{\"version\":3,\"file\":\"MessageBox.svelte\",\"sources\":[\"MessageBox.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let expanded = false;\\\\nexport let title;\\\\nfunction toggleExpanded() {\\\\n    expanded = !expanded;\\\\n}\\\\n<\\/script>\\\\n\\\\n<button class=\\\\\"box\\\\\" on:click={toggleExpanded}>\\\\n\\\\t<div class=\\\\\"title\\\\\">\\\\n\\\\t\\\\t<span class=\\\\\"title-text\\\\\">{title}</span>\\\\n\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\tstyle:transform={expanded ? \\\\\"rotate(0)\\\\\" : \\\\\"rotate(90deg)\\\\\"}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"arrow\\\\\"\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t▼\\\\n\\\\t\\\\t</span>\\\\n\\\\t</div>\\\\n\\\\t{#if expanded}\\\\n\\\\t\\\\t<div class=\\\\\"content\\\\\">\\\\n\\\\t\\\\t\\\\t<slot></slot>\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</button>\\\\n\\\\n<style>\\\\n\\\\t.box {\\\\n\\\\t\\\\tborder-radius: 4px;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tmax-width: max-content;\\\\n\\\\t\\\\tbackground: var(--color-accent-soft);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-accent-subdued);\\\\n\\\\t\\\\tfont-size: 0.8em;\\\\n\\\\t}\\\\n\\\\n\\\\t.title {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tpadding: 3px 6px;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t}\\\\n\\\\n\\\\t.content {\\\\n\\\\t\\\\tpadding: 4px 8px;\\\\n\\\\t}\\\\n\\\\n\\\\t.content :global(*) {\\\\n\\\\t\\\\tfont-size: 0.8em;\\\\n\\\\t}\\\\n\\\\n\\\\t.title-text {\\\\n\\\\t\\\\tpadding-right: var(--spacing-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.arrow {\\\\n\\\\t\\\\tmargin-left: auto;\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAyBC,mBAAK,CACJ,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,OAAO,CACf,SAAS,CAAE,WAAW,CACtB,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,6BAA6B,CAAC,CACpD,SAAS,CAAE,KACZ,CAEA,qBAAO,CACN,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,OAAO,CAAE,GACV,CAEA,uBAAS,CACR,OAAO,CAAE,GAAG,CAAC,GACd,CAEA,uBAAQ,CAAS,CAAG,CACnB,SAAS,CAAE,KACZ,CAEA,0BAAY,CACX,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,qBAAO,CACN,WAAW,CAAE,IAAI,CACjB,OAAO,CAAE,GACV\"}'\n};\nconst MessageBox = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { expanded = false } = $$props;\n  let { title } = $$props;\n  if ($$props.expanded === void 0 && $$bindings.expanded && expanded !== void 0)\n    $$bindings.expanded(expanded);\n  if ($$props.title === void 0 && $$bindings.title && title !== void 0)\n    $$bindings.title(title);\n  $$result.css.add(css$5);\n  return `<button class=\"box svelte-1e60bn1\"><div class=\"title svelte-1e60bn1\"><span class=\"title-text svelte-1e60bn1\">${escape(title)}</span> <span class=\"arrow svelte-1e60bn1\"${add_styles({\n    \"transform\": expanded ? \"rotate(0)\" : \"rotate(90deg)\"\n  })} data-svelte-h=\"svelte-15ydlzc\">▼</span></div> ${expanded ? `<div class=\"content svelte-1e60bn1\">${slots.default ? slots.default({}) : ``}</div>` : ``} </button>`;\n});\ncreate_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z\" fill=\"currentColor\"></path></svg>`;\n});\nconst ThumbDownDefault = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z\" fill=\"currentColor\"></path></svg>`;\n});\ncreate_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z\" fill=\"currentColor\"></path></svg>`;\n});\nconst ThumbUpDefault = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z\" fill=\"currentColor\"></path></svg>`;\n});\nconst LikeDislike = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { handle_action } = $$props;\n  if ($$props.handle_action === void 0 && $$bindings.handle_action && handle_action !== void 0)\n    $$bindings.handle_action(handle_action);\n  return `${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      Icon: ThumbDownDefault,\n      label: \"dislike\",\n      color: \"var(--block-label-text-color)\"\n    },\n    {},\n    {}\n  )} ${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      Icon: ThumbUpDefault,\n      label: \"like\",\n      color: \"var(--block-label-text-color)\"\n    },\n    {},\n    {}\n  )}`;\n});\nconst Copy_1 = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  onDestroy(() => {\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  return `${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      label: \"Copy message\",\n      Icon: Copy\n    },\n    {},\n    {}\n  )}`;\n});\nconst Download = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"16\" height=\"16\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M6.27701 8.253C6.24187 8.29143 6.19912 8.32212 6.15147 8.34311C6.10383 8.36411 6.05233 8.37495 6.00026 8.37495C5.94819 8.37495 5.89669 8.36411 5.84905 8.34311C5.8014 8.32212 5.75865 8.29143 5.72351 8.253L3.72351 6.0655C3.65798 5.99185 3.62408 5.89536 3.62916 5.79691C3.63424 5.69846 3.67788 5.60596 3.75064 5.53945C3.8234 5.47293 3.91943 5.43774 4.01794 5.44149C4.11645 5.44525 4.20952 5.48764 4.27701 5.5595L5.62501 7.0345V1.5C5.62501 1.40054 5.66452 1.30516 5.73485 1.23483C5.80517 1.16451 5.90055 1.125 6.00001 1.125C6.09947 1.125 6.19485 1.16451 6.26517 1.23483C6.3355 1.30516 6.37501 1.40054 6.37501 1.5V7.034L7.72351 5.559C7.79068 5.4856 7.88425 5.44189 7.98364 5.43748C8.08304 5.43308 8.18011 5.46833 8.25351 5.5355C8.32691 5.60267 8.37062 5.69624 8.37503 5.79563C8.37943 5.89503 8.34418 5.9921 8.27701 6.0655L6.27701 8.253Z\" fill=\"currentColor\"></path><path d=\"M1.875 7.39258C1.875 7.29312 1.83549 7.19774 1.76517 7.12741C1.69484 7.05709 1.59946 7.01758 1.5 7.01758C1.40054 7.01758 1.30516 7.05709 1.23483 7.12741C1.16451 7.19774 1.125 7.29312 1.125 7.39258V7.42008C1.125 8.10358 1.125 8.65508 1.1835 9.08858C1.2435 9.53858 1.3735 9.91758 1.674 10.2186C1.975 10.5196 2.354 10.6486 2.804 10.7096C3.2375 10.7676 3.789 10.7676 4.4725 10.7676H7.5275C8.211 10.7676 8.7625 10.7676 9.196 10.7096C9.646 10.6486 10.025 10.5196 10.326 10.2186C10.627 9.91758 10.756 9.53858 10.817 9.08858C10.875 8.65508 10.875 8.10358 10.875 7.42008V7.39258C10.875 7.29312 10.8355 7.19774 10.7652 7.12741C10.6948 7.05709 10.5995 7.01758 10.5 7.01758C10.4005 7.01758 10.3052 7.05709 10.2348 7.12741C10.1645 7.19774 10.125 7.29312 10.125 7.39258C10.125 8.11008 10.124 8.61058 10.0735 8.98858C10.024 9.35558 9.9335 9.54958 9.7955 9.68808C9.657 9.82658 9.463 9.91658 9.0955 9.96608C8.718 10.0166 8.2175 10.0176 7.5 10.0176H4.5C3.7825 10.0176 3.2815 10.0166 2.904 9.96608C2.537 9.91658 2.343 9.82608 2.2045 9.68808C2.066 9.54958 1.976 9.35558 1.9265 8.98808C1.876 8.61058 1.875 8.11008 1.875 7.39258Z\" fill=\"currentColor\"></path></svg>`;\n});\nconst css$4 = {\n  code: \".bubble.svelte-1ibfe7l .icon-button-wrapper{margin:0px calc(var(--spacing-xl) * 2)}.message-buttons-left.svelte-1ibfe7l{align-self:flex-start}.bubble.message-buttons-right.svelte-1ibfe7l{align-self:flex-end}.message-buttons-right.svelte-1ibfe7l .icon-button-wrapper{margin-left:auto}.bubble.with-avatar.svelte-1ibfe7l{margin-left:calc(var(--spacing-xl) * 5);margin-right:calc(var(--spacing-xl) * 5)}.panel.svelte-1ibfe7l{display:flex;align-self:flex-start;padding:0 var(--spacing-xl);z-index:var(--layer-1)}\",\n  map: `{\"version\":3,\"file\":\"ButtonPanel.svelte\",\"sources\":[\"ButtonPanel.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import LikeDislike from \\\\\"./LikeDislike.svelte\\\\\";\\\\nimport Copy from \\\\\"./Copy.svelte\\\\\";\\\\nimport DownloadIcon from \\\\\"./Download.svelte\\\\\";\\\\nimport { DownloadLink } from \\\\\"@gradio/wasm/svelte\\\\\";\\\\nimport { is_component_message } from \\\\\"./utils\\\\\";\\\\nimport { Retry, Undo } from \\\\\"@gradio/icons\\\\\";\\\\nimport { IconButtonWrapper, IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nexport let likeable;\\\\nexport let show_retry;\\\\nexport let show_undo;\\\\nexport let show_copy_button;\\\\nexport let show;\\\\nexport let message;\\\\nexport let position;\\\\nexport let avatar;\\\\nexport let generating;\\\\nexport let handle_action;\\\\nexport let layout;\\\\nfunction is_all_text(message2) {\\\\n    return Array.isArray(message2) && message2.every((m) => typeof m.content === \\\\\"string\\\\\") || !Array.isArray(message2) && typeof message2.content === \\\\\"string\\\\\";\\\\n}\\\\nfunction all_text(message2) {\\\\n    if (Array.isArray(message2)) {\\\\n        return message2.map((m) => m.content).join(\\\\\"\\\\\\\\n\\\\\");\\\\n    }\\\\n    return message2.content;\\\\n}\\\\n$: message_text = is_all_text(message) ? all_text(message) : \\\\\"\\\\\";\\\\n$: show_copy = show_copy_button && message && is_all_text(message);\\\\n$: show_download = !Array.isArray(message) && is_component_message(message) && message.content.value?.url;\\\\n<\\/script>\\\\n\\\\n{#if show}\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"message-buttons-{position} {layout} message-buttons {avatar !==\\\\n\\\\t\\\\t\\\\tnull && 'with-avatar'}\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<IconButtonWrapper top_panel={false}>\\\\n\\\\t\\\\t\\\\t{#if show_copy}\\\\n\\\\t\\\\t\\\\t\\\\t<Copy value={message_text} />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{#if show_download && !Array.isArray(message) && is_component_message(message)}\\\\n\\\\t\\\\t\\\\t\\\\t<DownloadLink\\\\n\\\\t\\\\t\\\\t\\\\t\\\\thref={message?.content?.value.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdownload={message.content.value.orig_name || \\\\\"image\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton Icon={DownloadIcon} />\\\\n\\\\t\\\\t\\\\t\\\\t</DownloadLink>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{#if show_retry}\\\\n\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Retry}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tlabel=\\\\\"Retry\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"retry\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{#if show_undo}\\\\n\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tlabel=\\\\\"Undo\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Undo}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"undo\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{#if likeable}\\\\n\\\\t\\\\t\\\\t\\\\t<LikeDislike {handle_action} />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.bubble :global(.icon-button-wrapper) {\\\\n\\\\t\\\\tmargin: 0px calc(var(--spacing-xl) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-buttons-left {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.message-buttons-right {\\\\n\\\\t\\\\talign-self: flex-end;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-buttons-right :global(.icon-button-wrapper) {\\\\n\\\\t\\\\tmargin-left: auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.with-avatar {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xl) * 5);\\\\n\\\\t\\\\tmargin-right: calc(var(--spacing-xl) * 5);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tpadding: 0 var(--spacing-xl);\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAyEC,sBAAO,CAAS,oBAAsB,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,oCAAsB,CACrB,UAAU,CAAE,UACb,CAEA,OAAO,qCAAuB,CAC7B,UAAU,CAAE,QACb,CAEA,qCAAsB,CAAS,oBAAsB,CACpD,WAAW,CAAE,IACd,CAEA,OAAO,2BAAa,CACnB,WAAW,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACxC,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CACzC,CAEA,qBAAO,CACN,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,UAAU,CACtB,OAAO,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC5B,OAAO,CAAE,IAAI,SAAS,CACvB\"}`\n};\nfunction is_all_text(message2) {\n  return Array.isArray(message2) && message2.every((m) => typeof m.content === \"string\") || !Array.isArray(message2) && typeof message2.content === \"string\";\n}\nfunction all_text(message2) {\n  if (Array.isArray(message2)) {\n    return message2.map((m) => m.content).join(\"\\n\");\n  }\n  return message2.content;\n}\nconst ButtonPanel = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let message_text;\n  let show_copy;\n  let show_download;\n  let { likeable } = $$props;\n  let { show_retry } = $$props;\n  let { show_undo } = $$props;\n  let { show_copy_button } = $$props;\n  let { show } = $$props;\n  let { message } = $$props;\n  let { position } = $$props;\n  let { avatar } = $$props;\n  let { generating } = $$props;\n  let { handle_action } = $$props;\n  let { layout } = $$props;\n  if ($$props.likeable === void 0 && $$bindings.likeable && likeable !== void 0)\n    $$bindings.likeable(likeable);\n  if ($$props.show_retry === void 0 && $$bindings.show_retry && show_retry !== void 0)\n    $$bindings.show_retry(show_retry);\n  if ($$props.show_undo === void 0 && $$bindings.show_undo && show_undo !== void 0)\n    $$bindings.show_undo(show_undo);\n  if ($$props.show_copy_button === void 0 && $$bindings.show_copy_button && show_copy_button !== void 0)\n    $$bindings.show_copy_button(show_copy_button);\n  if ($$props.show === void 0 && $$bindings.show && show !== void 0)\n    $$bindings.show(show);\n  if ($$props.message === void 0 && $$bindings.message && message !== void 0)\n    $$bindings.message(message);\n  if ($$props.position === void 0 && $$bindings.position && position !== void 0)\n    $$bindings.position(position);\n  if ($$props.avatar === void 0 && $$bindings.avatar && avatar !== void 0)\n    $$bindings.avatar(avatar);\n  if ($$props.generating === void 0 && $$bindings.generating && generating !== void 0)\n    $$bindings.generating(generating);\n  if ($$props.handle_action === void 0 && $$bindings.handle_action && handle_action !== void 0)\n    $$bindings.handle_action(handle_action);\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  $$result.css.add(css$4);\n  message_text = is_all_text(message) ? all_text(message) : \"\";\n  show_copy = show_copy_button && message && is_all_text(message);\n  show_download = !Array.isArray(message) && is_component_message(message) && message.content.value?.url;\n  return `${show ? `<div class=\"${\"message-buttons-\" + escape(position, true) + \" \" + escape(layout, true) + \" message-buttons \" + escape(avatar !== null && \"with-avatar\", true) + \" svelte-1ibfe7l\"}\">${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, { top_panel: false }, {}, {\n    default: () => {\n      return `${show_copy ? `${validate_component(Copy_1, \"Copy\").$$render($$result, { value: message_text }, {}, {})}` : ``} ${show_download && !Array.isArray(message) && is_component_message(message) ? `${validate_component(DownloadLink, \"DownloadLink\").$$render(\n        $$result,\n        {\n          href: message?.content?.value.url,\n          download: message.content.value.orig_name || \"image\"\n        },\n        {},\n        {\n          default: () => {\n            return `${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Download }, {}, {})}`;\n          }\n        }\n      )}` : ``} ${show_retry ? `${validate_component(IconButton, \"IconButton\").$$render(\n        $$result,\n        {\n          Icon: Retry,\n          label: \"Retry\",\n          disabled: generating\n        },\n        {},\n        {}\n      )}` : ``} ${show_undo ? `${validate_component(IconButton, \"IconButton\").$$render(\n        $$result,\n        {\n          label: \"Undo\",\n          Icon: Undo,\n          disabled: generating\n        },\n        {},\n        {}\n      )}` : ``} ${likeable ? `${validate_component(LikeDislike, \"LikeDislike\").$$render($$result, { handle_action }, {}, {})}` : ``}`;\n    }\n  })}</div>` : ``}`;\n});\nconst css$3 = {\n  code: \".message.svelte-5ng3n.svelte-5ng3n{position:relative;width:100%}.avatar-container.svelte-5ng3n.svelte-5ng3n{flex-shrink:0;border-radius:50%;border:1px solid var(--border-color-primary);overflow:hidden}.avatar-container.svelte-5ng3n img{object-fit:cover}.flex-wrap.svelte-5ng3n.svelte-5ng3n{display:flex;flex-direction:column;width:calc(100% - var(--spacing-xxl));max-width:100%;color:var(--body-text-color);font-size:var(--chatbot-text-size);overflow-wrap:break-word;width:100%;height:100%}.component.svelte-5ng3n.svelte-5ng3n{padding:0;border-radius:var(--radius-md);width:fit-content;overflow:hidden}.component.gallery.svelte-5ng3n.svelte-5ng3n{border:none}.message-row.svelte-5ng3n .svelte-5ng3n:not(.avatar-container) img{margin:var(--size-2);max-height:300px}.file-pil.svelte-5ng3n.svelte-5ng3n{display:block;width:fit-content;padding:var(--spacing-sm) var(--spacing-lg);border-radius:var(--radius-md);background:var(--background-fill-secondary);color:var(--body-text-color);text-decoration:none;margin:0;font-family:var(--font-mono);font-size:var(--text-sm)}.file.svelte-5ng3n.svelte-5ng3n{width:auto !important;max-width:fit-content !important}@media(max-width: 600px) or (max-width: 480px){.component.svelte-5ng3n.svelte-5ng3n{width:100%}}.message.svelte-5ng3n .prose{font-size:var(--chatbot-text-size)}.message-bubble-border.svelte-5ng3n.svelte-5ng3n{border-width:1px;border-radius:var(--radius-md)}.message-fit.svelte-5ng3n.svelte-5ng3n{width:fit-content !important}.panel-full-width.svelte-5ng3n.svelte-5ng3n{width:100%}.message-markdown-disabled.svelte-5ng3n.svelte-5ng3n{white-space:pre-line}.user.svelte-5ng3n.svelte-5ng3n{border-width:1px;border-radius:var(--radius-md);align-self:flex-start;border-bottom-right-radius:0;box-shadow:var(--shadow-drop);align-self:flex-start;text-align:right;padding:var(--spacing-sm) var(--spacing-xl);border-color:var(--border-color-accent-subdued);background-color:var(--color-accent-soft)}.bot.svelte-5ng3n.svelte-5ng3n{border-width:1px;border-radius:var(--radius-lg);border-bottom-left-radius:0;border-color:var(--border-color-primary);background-color:var(--background-fill-secondary);box-shadow:var(--shadow-drop);align-self:flex-start;text-align:right;padding:var(--spacing-sm) var(--spacing-xl)}.panel.svelte-5ng3n .user.svelte-5ng3n *{text-align:right}.bubble.svelte-5ng3n .bot.svelte-5ng3n{border-color:var(--border-color-primary)}.message-row.svelte-5ng3n.svelte-5ng3n{display:flex;position:relative}.bubble.svelte-5ng3n.svelte-5ng3n{margin:calc(var(--spacing-xl) * 2);margin-bottom:var(--spacing-xl)}.bubble.user-row.svelte-5ng3n.svelte-5ng3n{align-self:flex-end;max-width:calc(100% - var(--spacing-xl) * 6)}.bubble.bot-row.svelte-5ng3n.svelte-5ng3n{align-self:flex-start;max-width:calc(100% - var(--spacing-xl) * 6)}.bubble.svelte-5ng3n .user-row.svelte-5ng3n{flex-direction:row;justify-content:flex-end}.bubble.svelte-5ng3n .with_avatar.user-row.svelte-5ng3n{margin-right:calc(var(--spacing-xl) * 2) !important}.bubble.svelte-5ng3n .with_avatar.bot-row.svelte-5ng3n{margin-left:calc(var(--spacing-xl) * 2) !important}.bubble.svelte-5ng3n .with_opposite_avatar.user-row.svelte-5ng3n{margin-left:calc(var(--spacing-xxl) + 35px + var(--spacing-xxl))}.bubble.svelte-5ng3n .message-fit.svelte-5ng3n{width:fit-content !important}.panel.svelte-5ng3n.svelte-5ng3n{margin:0;padding:calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2)}.panel.bot-row.svelte-5ng3n.svelte-5ng3n{background:var(--background-fill-secondary)}.panel.svelte-5ng3n .with_avatar.svelte-5ng3n{padding-left:calc(var(--spacing-xl) * 2) !important;padding-right:calc(var(--spacing-xl) * 2) !important}.panel.svelte-5ng3n .panel-full-width.svelte-5ng3n{width:100%}.panel.svelte-5ng3n .user.svelte-5ng3n *{text-align:right}.flex-wrap.svelte-5ng3n.svelte-5ng3n{display:flex;flex-direction:column;max-width:100%;color:var(--body-text-color);font-size:var(--chatbot-text-size);overflow-wrap:break-word}.user.svelte-5ng3n.svelte-5ng3n{border-width:1px;border-radius:var(--radius-md);align-self:flex-start;border-bottom-right-radius:0;box-shadow:var(--shadow-drop);text-align:right;padding:var(--spacing-sm) var(--spacing-xl);border-color:var(--border-color-accent-subdued);background-color:var(--color-accent-soft)}@media(max-width: 480px){.user-row.bubble.svelte-5ng3n.svelte-5ng3n{align-self:flex-end}.bot-row.bubble.svelte-5ng3n.svelte-5ng3n{align-self:flex-start}.message.svelte-5ng3n.svelte-5ng3n{width:100%}}.avatar-container.svelte-5ng3n.svelte-5ng3n{align-self:flex-start;position:relative;display:flex;justify-content:flex-start;align-items:flex-start;width:35px;height:35px;flex-shrink:0;bottom:0;border-radius:50%;border:1px solid var(--border-color-primary)}.user-row.svelte-5ng3n>.avatar-container.svelte-5ng3n{order:2}.user-row.bubble.svelte-5ng3n>.avatar-container.svelte-5ng3n{margin-left:var(--spacing-xxl)}.bot-row.bubble.svelte-5ng3n>.avatar-container.svelte-5ng3n{margin-left:var(--spacing-xxl)}.panel.user-row.svelte-5ng3n>.avatar-container.svelte-5ng3n{order:0}.bot-row.bubble.svelte-5ng3n>.avatar-container.svelte-5ng3n{margin-right:var(--spacing-xxl);margin-left:0}.avatar-container.svelte-5ng3n:not(.thumbnail-item) img{width:100%;height:100%;object-fit:cover;border-radius:50%;padding:6px}.selectable.svelte-5ng3n.svelte-5ng3n{cursor:pointer}@keyframes svelte-5ng3n-dot-flashing{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}.message.svelte-5ng3n .preview{object-fit:contain;width:95%;max-height:93%}.image-preview.svelte-5ng3n.svelte-5ng3n{position:absolute;z-index:999;left:0;top:0;width:100%;height:100%;overflow:auto;background-color:rgba(0, 0, 0, 0.9);display:flex;justify-content:center;align-items:center}.image-preview.svelte-5ng3n svg{stroke:white}.image-preview-close-button.svelte-5ng3n.svelte-5ng3n{position:absolute;top:10px;right:10px;background:none;border:none;font-size:1.5em;cursor:pointer;height:30px;width:30px;padding:3px;background:var(--bg-color);box-shadow:var(--shadow-drop);border:1px solid var(--button-secondary-border-color);border-radius:var(--radius-lg)}.message.svelte-5ng3n>button.svelte-5ng3n{width:100%}.html.svelte-5ng3n.svelte-5ng3n{padding:0;border:none;background:none}.thought.svelte-5ng3n.svelte-5ng3n{margin-top:var(--spacing-xxl)}.panel.svelte-5ng3n .bot.svelte-5ng3n,.panel.svelte-5ng3n .user.svelte-5ng3n{border:none;box-shadow:none;background-color:var(--background-fill-secondary)}.panel.user-row.svelte-5ng3n.svelte-5ng3n{background-color:var(--color-accent-soft)}.panel.svelte-5ng3n .user-row.svelte-5ng3n,.panel.svelte-5ng3n .bot-row.svelte-5ng3n{align-self:flex-start}.panel.svelte-5ng3n .user.svelte-5ng3n *,.panel.svelte-5ng3n .bot.svelte-5ng3n *{text-align:left}.panel.svelte-5ng3n .user.svelte-5ng3n{background-color:var(--color-accent-soft)}.panel.svelte-5ng3n .user-row.svelte-5ng3n{background-color:var(--color-accent-soft);align-self:flex-start}.panel.svelte-5ng3n .message.svelte-5ng3n{margin-bottom:var(--spacing-md)}\",\n  map: '{\"version\":3,\"file\":\"Message.svelte\",\"sources\":[\"Message.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { is_component_message, is_last_bot_message } from \\\\\"../shared/utils\\\\\";\\\\nimport { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport Component from \\\\\"./Component.svelte\\\\\";\\\\nimport MessageBox from \\\\\"./MessageBox.svelte\\\\\";\\\\nimport { MarkdownCode as Markdown } from \\\\\"@gradio/markdown-code\\\\\";\\\\nimport ButtonPanel from \\\\\"./ButtonPanel.svelte\\\\\";\\\\nexport let value;\\\\nexport let avatar_img;\\\\nexport let opposite_avatar_img = null;\\\\nexport let role = \\\\\"user\\\\\";\\\\nexport let messages = [];\\\\nexport let layout;\\\\nexport let bubble_full_width;\\\\nexport let render_markdown;\\\\nexport let latex_delimiters;\\\\nexport let sanitize_html;\\\\nexport let selectable;\\\\nexport let _fetch;\\\\nexport let rtl;\\\\nexport let dispatch;\\\\nexport let i18n;\\\\nexport let line_breaks;\\\\nexport let upload;\\\\nexport let target;\\\\nexport let root;\\\\nexport let theme_mode;\\\\nexport let _components;\\\\nexport let i;\\\\nexport let show_copy_button;\\\\nexport let generating;\\\\nexport let show_like;\\\\nexport let show_retry;\\\\nexport let show_undo;\\\\nexport let msg_format;\\\\nexport let handle_action;\\\\nexport let scroll;\\\\nfunction handle_select(i2, message) {\\\\n    dispatch(\\\\\"select\\\\\", {\\\\n        index: message.index,\\\\n        value: message.content\\\\n    });\\\\n}\\\\nfunction get_message_label_data(message) {\\\\n    if (message.type === \\\\\"text\\\\\") {\\\\n        return message.content;\\\\n    }\\\\n    else if (message.type === \\\\\"component\\\\\" && message.content.component === \\\\\"file\\\\\") {\\\\n        if (Array.isArray(message.content.value)) {\\\\n            return `file of extension type: ${message.content.value[0].orig_name?.split(\\\\\".\\\\\").pop()}`;\\\\n        }\\\\n        return `file of extension type: ${message.content.value?.orig_name?.split(\\\\\".\\\\\").pop()}` + (message.content.value?.orig_name ?? \\\\\"\\\\\");\\\\n    }\\\\n    return `a component of type ${message.content.component ?? \\\\\"unknown\\\\\"}`;\\\\n}\\\\nlet button_panel_props;\\\\n$: button_panel_props = {\\\\n    show: show_like || show_retry || show_undo || show_copy_button,\\\\n    handle_action,\\\\n    likeable: show_like,\\\\n    show_retry,\\\\n    show_undo,\\\\n    generating,\\\\n    show_copy_button,\\\\n    message: msg_format === \\\\\"tuples\\\\\" ? messages[0] : messages,\\\\n    position: role === \\\\\"user\\\\\" ? \\\\\"right\\\\\" : \\\\\"left\\\\\",\\\\n    avatar: avatar_img,\\\\n    layout\\\\n};\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"message-row {layout} {role}-row\\\\\"\\\\n\\\\tclass:with_avatar={avatar_img !== null}\\\\n\\\\tclass:with_opposite_avatar={opposite_avatar_img !== null}\\\\n>\\\\n\\\\t{#if avatar_img !== null}\\\\n\\\\t\\\\t<div class=\\\\\"avatar-container\\\\\">\\\\n\\\\t\\\\t\\\\t<Image class=\\\\\"avatar-image\\\\\" src={avatar_img?.url} alt=\\\\\"{role} avatar\\\\\" />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\t<div\\\\n\\\\t\\\\tclass:role\\\\n\\\\t\\\\tclass=\\\\\"flex-wrap\\\\\"\\\\n\\\\t\\\\tclass:component-wrap={messages[0].type === \\\\\"component\\\\\"}\\\\n\\\\t>\\\\n\\\\t\\\\t{#each messages as message, thought_index}\\\\n\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"message {role} {is_component_message(message)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t? message?.content.component\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t: \\'\\'}\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:message-fit={layout === \\\\\"bubble\\\\\" && !bubble_full_width}\\\\n\\\\t\\\\t\\\\t\\\\tclass:panel-full-width={true}\\\\n\\\\t\\\\t\\\\t\\\\tclass:message-markdown-disabled={!render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\tstyle:text-align={rtl && role === \\\\\"user\\\\\" ? \\\\\"left\\\\\" : \\\\\"right\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\tclass:component={message.type === \\\\\"component\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\tclass:html={is_component_message(message) &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.component === \\\\\"html\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\tclass:thought={thought_index > 0}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid={role}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:latest={i === value.length - 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:message-markdown-disabled={!render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstyle:user-select=\\\\\"text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:selectable\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstyle:cursor={selectable ? \\\\\"pointer\\\\\" : \\\\\"default\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstyle:text-align={rtl ? \\\\\"right\\\\\" : \\\\\"left\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_select(i, message)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (e.key === \\\\\"Enter\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_select(i, message);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdir={rtl ? \\\\\"rtl\\\\\" : \\\\\"ltr\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label={role + \\\\\"\\'s message: \\\\\" + get_message_label_data(message)}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if message.type === \\\\\"text\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if message.metadata.title}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<MessageBox\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttitle={message.metadata.title}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\texpanded={is_last_bot_message([message], value)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Markdown\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage={message.content}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:load={scroll}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</MessageBox>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Markdown\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage={message.content}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:load={scroll}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else if message.type === \\\\\"component\\\\\" && message.content.component in _components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Component\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tprops={message.content.props}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttype={message.content.component}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tcomponents={_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue={message.content.value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:load={() => scroll()}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else if message.type === \\\\\"component\\\\\" && message.content.component === \\\\\"file\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<a\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"chatbot-file\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"file-pil\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thref={message.content.value.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttarget=\\\\\"_blank\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdownload={window.__is_colab__\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? null\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: message.content.value?.orig_name ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.path.split(\\\\\"/\\\\\").pop() ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"file\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{message.content.value?.orig_name ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.path.split(\\\\\"/\\\\\").pop() ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"file\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</a>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\n\\\\t\\\\t\\\\t{#if layout === \\\\\"panel\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t<ButtonPanel {...button_panel_props} />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{/each}\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n{#if layout === \\\\\"bubble\\\\\"}\\\\n\\\\t<ButtonPanel {...button_panel_props} />\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.message {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t/* avatar styles */\\\\n\\\\t.avatar-container {\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container :global(img) {\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t/* message wrapper */\\\\n\\\\t.flex-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\twidth: calc(100% - var(--spacing-xxl));\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.component {\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.component.gallery {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-row :not(.avatar-container) :global(img) {\\\\n\\\\t\\\\tmargin: var(--size-2);\\\\n\\\\t\\\\tmax-height: 300px;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-pil {\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tfont-family: var(--font-mono);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.file {\\\\n\\\\t\\\\twidth: auto !important;\\\\n\\\\t\\\\tmax-width: fit-content !important;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 600px) or (max-width: 480px) {\\\\n\\\\t\\\\t.component {\\\\n\\\\t\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.message :global(.prose) {\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-bubble-border {\\\\n\\\\t\\\\tborder-width: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-fit {\\\\n\\\\t\\\\twidth: fit-content !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel-full-width {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\t.message-markdown-disabled {\\\\n\\\\t\\\\twhite-space: pre-line;\\\\n\\\\t}\\\\n\\\\n\\\\t.user {\\\\n\\\\t\\\\tborder-width: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tborder-bottom-right-radius: 0;\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t\\\\tborder-color: var(--border-color-accent-subdued);\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.bot {\\\\n\\\\t\\\\tborder-width: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tborder-bottom-left-radius: 0;\\\\n\\\\t\\\\tborder-color: var(--border-color-primary);\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*) {\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t}\\\\n\\\\n\\\\t/* Colors */\\\\n\\\\t.bubble .bot {\\\\n\\\\t\\\\tborder-color: var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-row {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t/* bubble mode styles */\\\\n\\\\t.bubble {\\\\n\\\\t\\\\tmargin: calc(var(--spacing-xl) * 2);\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.user-row {\\\\n\\\\t\\\\talign-self: flex-end;\\\\n\\\\t\\\\tmax-width: calc(100% - var(--spacing-xl) * 6);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.bot-row {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tmax-width: calc(100% - var(--spacing-xl) * 6);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .user-row {\\\\n\\\\t\\\\tflex-direction: row;\\\\n\\\\t\\\\tjustify-content: flex-end;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_avatar.user-row {\\\\n\\\\t\\\\tmargin-right: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_avatar.bot-row {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_opposite_avatar.user-row {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .message-fit {\\\\n\\\\t\\\\twidth: fit-content !important;\\\\n\\\\t}\\\\n\\\\n\\\\t/* panel mode styles */\\\\n\\\\t.panel {\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.bot-row {\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .with_avatar {\\\\n\\\\t\\\\tpadding-left: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t\\\\tpadding-right: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .panel-full-width {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*) {\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t}\\\\n\\\\n\\\\t/* message content */\\\\n\\\\t.flex-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t}\\\\n\\\\n\\\\t.user {\\\\n\\\\t\\\\tborder-width: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tborder-bottom-right-radius: 0;\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t\\\\tborder-color: var(--border-color-accent-subdued);\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\t@media (max-width: 480px) {\\\\n\\\\t\\\\t.user-row.bubble {\\\\n\\\\t\\\\t\\\\talign-self: flex-end;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.bot-row.bubble {\\\\n\\\\t\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t.message {\\\\n\\\\t\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: flex-start;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\twidth: 35px;\\\\n\\\\t\\\\theight: 35px;\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\t.user-row > .avatar-container {\\\\n\\\\t\\\\torder: 2;\\\\n\\\\t}\\\\n\\\\n\\\\t.user-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-left: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bot-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-left: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.user-row > .avatar-container {\\\\n\\\\t\\\\torder: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.bot-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-right: var(--spacing-xxl);\\\\n\\\\t\\\\tmargin-left: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container:not(.thumbnail-item) :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tpadding: 6px;\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes dot-flashing {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\topacity: 0.5;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t/* Image preview */\\\\n\\\\t.message :global(.preview) {\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t\\\\twidth: 95%;\\\\n\\\\t\\\\tmax-height: 93%;\\\\n\\\\t}\\\\n\\\\t.image-preview {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tz-index: 999;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\toverflow: auto;\\\\n\\\\t\\\\tbackground-color: rgba(0, 0, 0, 0.9);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\t.image-preview :global(svg) {\\\\n\\\\t\\\\tstroke: white;\\\\n\\\\t}\\\\n\\\\t.image-preview-close-button {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 10px;\\\\n\\\\t\\\\tright: 10px;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tfont-size: 1.5em;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\theight: 30px;\\\\n\\\\t\\\\twidth: 30px;\\\\n\\\\t\\\\tpadding: 3px;\\\\n\\\\t\\\\tbackground: var(--bg-color);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\tborder: 1px solid var(--button-secondary-border-color);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.message > button {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\t.html {\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.thought {\\\\n\\\\t\\\\tmargin-top: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .bot,\\\\n\\\\t.panel .user {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbox-shadow: none;\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.user-row {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user-row,\\\\n\\\\t.panel .bot-row {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*),\\\\n\\\\t.panel .bot :global(*) {\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user-row {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .message {\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-md);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA4LC,kCAAS,CACR,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IACR,CAGA,2CAAkB,CACjB,WAAW,CAAE,CAAC,CACd,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,QAAQ,CAAE,MACX,CAEA,8BAAiB,CAAS,GAAK,CAC9B,UAAU,CAAE,KACb,CAGA,oCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,KAAK,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CACtC,SAAS,CAAE,IAAI,CACf,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,mBAAmB,CAAC,CACnC,aAAa,CAAE,UAAU,CACzB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,oCAAW,CACV,OAAO,CAAE,CAAC,CACV,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,WAAW,CAClB,QAAQ,CAAE,MACX,CAEA,UAAU,kCAAS,CAClB,MAAM,CAAE,IACT,CAEA,yBAAY,cAAC,KAAK,iBAAiB,CAAC,CAAS,GAAK,CACjD,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,UAAU,CAAE,KACb,CAEA,mCAAU,CACT,OAAO,CAAE,KAAK,CACd,KAAK,CAAE,WAAW,CAClB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAC5C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,eAAe,CAAE,IAAI,CACrB,MAAM,CAAE,CAAC,CACT,WAAW,CAAE,IAAI,WAAW,CAAC,CAC7B,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,+BAAM,CACL,KAAK,CAAE,IAAI,CAAC,UAAU,CACtB,SAAS,CAAE,WAAW,CAAC,UACxB,CAEA,MAAO,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC,YAAY,KAAK,CAAE,CAC/C,oCAAW,CACV,KAAK,CAAE,IACR,CACD,CAEA,qBAAQ,CAAS,MAAQ,CACxB,SAAS,CAAE,IAAI,mBAAmB,CACnC,CAEA,gDAAuB,CACtB,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,sCAAa,CACZ,KAAK,CAAE,WAAW,CAAC,UACpB,CAEA,2CAAkB,CACjB,KAAK,CAAE,IACR,CACA,oDAA2B,CAC1B,WAAW,CAAE,QACd,CAEA,+BAAM,CACL,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,UAAU,CACtB,0BAA0B,CAAE,CAAC,CAC7B,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CAAE,UAAU,CACtB,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAC5C,YAAY,CAAE,IAAI,6BAA6B,CAAC,CAChD,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,8BAAK,CACJ,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,yBAAyB,CAAE,CAAC,CAC5B,YAAY,CAAE,IAAI,sBAAsB,CAAC,CACzC,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CAAE,UAAU,CACtB,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAC5C,CAEA,mBAAM,CAAC,kBAAK,CAAS,CAAG,CACvB,UAAU,CAAE,KACb,CAGA,oBAAO,CAAC,iBAAK,CACZ,YAAY,CAAE,IAAI,sBAAsB,CACzC,CAEA,sCAAa,CACZ,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QACX,CAGA,iCAAQ,CACP,MAAM,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACnC,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,OAAO,mCAAU,CAChB,UAAU,CAAE,QAAQ,CACpB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7C,CAEA,OAAO,kCAAS,CACf,UAAU,CAAE,UAAU,CACtB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7C,CAEA,oBAAO,CAAC,sBAAU,CACjB,cAAc,CAAE,GAAG,CACnB,eAAe,CAAE,QAClB,CAEA,oBAAO,CAAC,YAAY,sBAAU,CAC7B,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC3C,CAEA,oBAAO,CAAC,YAAY,qBAAS,CAC5B,WAAW,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC1C,CAEA,oBAAO,CAAC,qBAAqB,sBAAU,CACtC,WAAW,CAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CACjE,CAEA,oBAAO,CAAC,yBAAa,CACpB,KAAK,CAAE,WAAW,CAAC,UACpB,CAGA,gCAAO,CACN,MAAM,CAAE,CAAC,CACT,OAAO,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAChE,CAEA,MAAM,kCAAS,CACd,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,mBAAM,CAAC,yBAAa,CACnB,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CACpD,aAAa,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC5C,CAEA,mBAAM,CAAC,8BAAkB,CACxB,KAAK,CAAE,IACR,CAEA,mBAAM,CAAC,kBAAK,CAAS,CAAG,CACvB,UAAU,CAAE,KACb,CAGA,oCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,SAAS,CAAE,IAAI,CACf,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,mBAAmB,CAAC,CACnC,aAAa,CAAE,UAChB,CAEA,+BAAM,CACL,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,UAAU,CACtB,0BAA0B,CAAE,CAAC,CAC7B,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAC5C,YAAY,CAAE,IAAI,6BAA6B,CAAC,CAChD,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CACA,MAAO,YAAY,KAAK,CAAE,CACzB,SAAS,iCAAQ,CAChB,UAAU,CAAE,QACb,CAEA,QAAQ,iCAAQ,CACf,UAAU,CAAE,UACb,CACA,kCAAS,CACR,KAAK,CAAE,IACR,CACD,CAEA,2CAAkB,CACjB,UAAU,CAAE,UAAU,CACtB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,UAAU,CAC3B,WAAW,CAAE,UAAU,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,MAAM,CAAE,CAAC,CACT,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CACA,sBAAS,CAAG,8BAAkB,CAC7B,KAAK,CAAE,CACR,CAEA,SAAS,oBAAO,CAAG,8BAAkB,CACpC,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,QAAQ,oBAAO,CAAG,8BAAkB,CACnC,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,MAAM,sBAAS,CAAG,8BAAkB,CACnC,KAAK,CAAE,CACR,CAEA,QAAQ,oBAAO,CAAG,8BAAkB,CACnC,YAAY,CAAE,IAAI,aAAa,CAAC,CAChC,WAAW,CAAE,CACd,CAEA,8BAAiB,KAAK,eAAe,CAAC,CAAS,GAAK,CACnD,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,GAAG,CAClB,OAAO,CAAE,GACV,CAEA,qCAAY,CACX,MAAM,CAAE,OACT,CAEA,WAAW,yBAAa,CACvB,EAAG,CACF,OAAO,CAAE,GACV,CACA,GAAI,CACH,OAAO,CAAE,GACV,CACA,IAAK,CACJ,OAAO,CAAE,GACV,CACD,CAGA,qBAAQ,CAAS,QAAU,CAC1B,UAAU,CAAE,OAAO,CACnB,KAAK,CAAE,GAAG,CACV,UAAU,CAAE,GACb,CACA,wCAAe,CACd,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,GAAG,CACZ,IAAI,CAAE,CAAC,CACP,GAAG,CAAE,CAAC,CACN,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,IAAI,CACd,gBAAgB,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CACpC,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd,CACA,2BAAc,CAAS,GAAK,CAC3B,MAAM,CAAE,KACT,CACA,qDAA4B,CAC3B,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,IAAI,CACT,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,OAAO,CACf,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,GAAG,CACZ,UAAU,CAAE,IAAI,UAAU,CAAC,CAC3B,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,+BAA+B,CAAC,CACtD,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,qBAAQ,CAAG,mBAAO,CACjB,KAAK,CAAE,IACR,CACA,+BAAM,CACL,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IACb,CAEA,kCAAS,CACR,UAAU,CAAE,IAAI,aAAa,CAC9B,CAEA,mBAAM,CAAC,iBAAI,CACX,mBAAM,CAAC,kBAAM,CACZ,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAChB,gBAAgB,CAAE,IAAI,2BAA2B,CAClD,CAEA,MAAM,mCAAU,CACf,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,mBAAM,CAAC,sBAAS,CAChB,mBAAM,CAAC,qBAAS,CACf,UAAU,CAAE,UACb,CAEA,mBAAM,CAAC,kBAAK,CAAS,CAAE,CACvB,mBAAM,CAAC,iBAAI,CAAS,CAAG,CACtB,UAAU,CAAE,IACb,CAEA,mBAAM,CAAC,kBAAM,CACZ,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,mBAAM,CAAC,sBAAU,CAChB,gBAAgB,CAAE,IAAI,mBAAmB,CAAC,CAC1C,UAAU,CAAE,UACb,CAEA,mBAAM,CAAC,qBAAS,CACf,aAAa,CAAE,IAAI,YAAY,CAChC\"}'\n};\nfunction get_message_label_data(message) {\n  if (message.type === \"text\") {\n    return message.content;\n  } else if (message.type === \"component\" && message.content.component === \"file\") {\n    if (Array.isArray(message.content.value)) {\n      return `file of extension type: ${message.content.value[0].orig_name?.split(\".\").pop()}`;\n    }\n    return `file of extension type: ${message.content.value?.orig_name?.split(\".\").pop()}` + (message.content.value?.orig_name ?? \"\");\n  }\n  return `a component of type ${message.content.component ?? \"unknown\"}`;\n}\nconst Message = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { avatar_img } = $$props;\n  let { opposite_avatar_img = null } = $$props;\n  let { role = \"user\" } = $$props;\n  let { messages = [] } = $$props;\n  let { layout } = $$props;\n  let { bubble_full_width } = $$props;\n  let { render_markdown } = $$props;\n  let { latex_delimiters } = $$props;\n  let { sanitize_html } = $$props;\n  let { selectable } = $$props;\n  let { _fetch } = $$props;\n  let { rtl } = $$props;\n  let { dispatch } = $$props;\n  let { i18n } = $$props;\n  let { line_breaks } = $$props;\n  let { upload } = $$props;\n  let { target } = $$props;\n  let { root } = $$props;\n  let { theme_mode } = $$props;\n  let { _components } = $$props;\n  let { i } = $$props;\n  let { show_copy_button } = $$props;\n  let { generating } = $$props;\n  let { show_like } = $$props;\n  let { show_retry } = $$props;\n  let { show_undo } = $$props;\n  let { msg_format } = $$props;\n  let { handle_action } = $$props;\n  let { scroll: scroll2 } = $$props;\n  let button_panel_props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.avatar_img === void 0 && $$bindings.avatar_img && avatar_img !== void 0)\n    $$bindings.avatar_img(avatar_img);\n  if ($$props.opposite_avatar_img === void 0 && $$bindings.opposite_avatar_img && opposite_avatar_img !== void 0)\n    $$bindings.opposite_avatar_img(opposite_avatar_img);\n  if ($$props.role === void 0 && $$bindings.role && role !== void 0)\n    $$bindings.role(role);\n  if ($$props.messages === void 0 && $$bindings.messages && messages !== void 0)\n    $$bindings.messages(messages);\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  if ($$props.bubble_full_width === void 0 && $$bindings.bubble_full_width && bubble_full_width !== void 0)\n    $$bindings.bubble_full_width(bubble_full_width);\n  if ($$props.render_markdown === void 0 && $$bindings.render_markdown && render_markdown !== void 0)\n    $$bindings.render_markdown(render_markdown);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props.selectable === void 0 && $$bindings.selectable && selectable !== void 0)\n    $$bindings.selectable(selectable);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.dispatch === void 0 && $$bindings.dispatch && dispatch !== void 0)\n    $$bindings.dispatch(dispatch);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.target === void 0 && $$bindings.target && target !== void 0)\n    $$bindings.target(target);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props._components === void 0 && $$bindings._components && _components !== void 0)\n    $$bindings._components(_components);\n  if ($$props.i === void 0 && $$bindings.i && i !== void 0)\n    $$bindings.i(i);\n  if ($$props.show_copy_button === void 0 && $$bindings.show_copy_button && show_copy_button !== void 0)\n    $$bindings.show_copy_button(show_copy_button);\n  if ($$props.generating === void 0 && $$bindings.generating && generating !== void 0)\n    $$bindings.generating(generating);\n  if ($$props.show_like === void 0 && $$bindings.show_like && show_like !== void 0)\n    $$bindings.show_like(show_like);\n  if ($$props.show_retry === void 0 && $$bindings.show_retry && show_retry !== void 0)\n    $$bindings.show_retry(show_retry);\n  if ($$props.show_undo === void 0 && $$bindings.show_undo && show_undo !== void 0)\n    $$bindings.show_undo(show_undo);\n  if ($$props.msg_format === void 0 && $$bindings.msg_format && msg_format !== void 0)\n    $$bindings.msg_format(msg_format);\n  if ($$props.handle_action === void 0 && $$bindings.handle_action && handle_action !== void 0)\n    $$bindings.handle_action(handle_action);\n  if ($$props.scroll === void 0 && $$bindings.scroll && scroll2 !== void 0)\n    $$bindings.scroll(scroll2);\n  $$result.css.add(css$3);\n  button_panel_props = {\n    show: show_like || show_retry || show_undo || show_copy_button,\n    handle_action,\n    likeable: show_like,\n    show_retry,\n    show_undo,\n    generating,\n    show_copy_button,\n    message: msg_format === \"tuples\" ? messages[0] : messages,\n    position: role === \"user\" ? \"right\" : \"left\",\n    avatar: avatar_img,\n    layout\n  };\n  return `<div class=\"${[\n    \"message-row \" + escape(layout, true) + \" \" + escape(role, true) + \"-row svelte-5ng3n\",\n    (avatar_img !== null ? \"with_avatar\" : \"\") + \" \" + (opposite_avatar_img !== null ? \"with_opposite_avatar\" : \"\")\n  ].join(\" \").trim()}\">${avatar_img !== null ? `<div class=\"avatar-container svelte-5ng3n\">${validate_component(Image, \"Image\").$$render(\n    $$result,\n    {\n      class: \"avatar-image\",\n      src: avatar_img?.url,\n      alt: role + \" avatar\"\n    },\n    {},\n    {}\n  )}</div>` : ``} <div class=\"${[\n    \"flex-wrap svelte-5ng3n\",\n    (role ? \"role\" : \"\") + \" \" + (messages[0].type === \"component\" ? \"component-wrap\" : \"\")\n  ].join(\" \").trim()}\">${each(messages, (message, thought_index) => {\n    return `<div class=\"${[\n      \"message \" + escape(role, true) + \" \" + escape(\n        is_component_message(message) ? message?.content.component : \"\",\n        true\n      ) + \" svelte-5ng3n\",\n      (layout === \"bubble\" && !bubble_full_width ? \"message-fit\" : \"\") + \" panel-full-width \" + (!render_markdown ? \"message-markdown-disabled\" : \"\") + \" \" + (message.type === \"component\" ? \"component\" : \"\") + \" \" + (is_component_message(message) && message.content.component === \"html\" ? \"html\" : \"\") + \" \" + (thought_index > 0 ? \"thought\" : \"\")\n    ].join(\" \").trim()}\"${add_styles({\n      \"text-align\": rtl && role === \"user\" ? \"left\" : \"right\"\n    })}><button${add_attribute(\"data-testid\", role, 0)}${add_attribute(\"dir\", rtl ? \"rtl\" : \"ltr\", 0)}${add_attribute(\"aria-label\", role + \"'s message: \" + get_message_label_data(message), 0)} class=\"${[\n      \"svelte-5ng3n\",\n      (i === value.length - 1 ? \"latest\" : \"\") + \" \" + (!render_markdown ? \"message-markdown-disabled\" : \"\") + \" \" + (selectable ? \"selectable\" : \"\")\n    ].join(\" \").trim()}\"${add_styles({\n      \"user-select\": `text`,\n      \"cursor\": selectable ? \"pointer\" : \"default\",\n      \"text-align\": rtl ? \"right\" : \"left\"\n    })}>${message.type === \"text\" ? `${message.metadata.title ? `${validate_component(MessageBox, \"MessageBox\").$$render(\n      $$result,\n      {\n        title: message.metadata.title,\n        expanded: is_last_bot_message([message], value)\n      },\n      {},\n      {\n        default: () => {\n          return `${validate_component(MarkdownCode, \"Markdown\").$$render(\n            $$result,\n            {\n              message: message.content,\n              latex_delimiters,\n              sanitize_html,\n              render_markdown,\n              line_breaks,\n              root\n            },\n            {},\n            {}\n          )} `;\n        }\n      }\n    )}` : `${validate_component(MarkdownCode, \"Markdown\").$$render(\n      $$result,\n      {\n        message: message.content,\n        latex_delimiters,\n        sanitize_html,\n        render_markdown,\n        line_breaks,\n        root\n      },\n      {},\n      {}\n    )}`}` : `${message.type === \"component\" && message.content.component in _components ? `${validate_component(Component, \"Component\").$$render(\n      $$result,\n      {\n        target,\n        theme_mode,\n        props: message.content.props,\n        type: message.content.component,\n        components: _components,\n        value: message.content.value,\n        i18n,\n        upload,\n        _fetch\n      },\n      {},\n      {}\n    )}` : `${message.type === \"component\" && message.content.component === \"file\" ? `<a data-testid=\"chatbot-file\" class=\"file-pil svelte-5ng3n\"${add_attribute(\"href\", message.content.value.url, 0)} target=\"_blank\"${add_attribute(\n      \"download\",\n      window.__is_colab__ ? null : message.content.value?.orig_name || message.content.value?.path.split(\"/\").pop() || \"file\",\n      0\n    )}>${escape(message.content.value?.orig_name || message.content.value?.path.split(\"/\").pop() || \"file\")} </a>` : ``}`}`} </button></div> ${layout === \"panel\" ? `${validate_component(ButtonPanel, \"ButtonPanel\").$$render($$result, Object.assign({}, button_panel_props), {}, {})}` : ``}`;\n  })}</div></div> ${layout === \"bubble\" ? `${validate_component(ButtonPanel, \"ButtonPanel\").$$render($$result, Object.assign({}, button_panel_props), {}, {})}` : ``}`;\n});\nconst css$2 = {\n  code: \".pending.svelte-1gpwetz{background:var(--color-accent-soft);display:flex;flex-direction:row;justify-content:center;align-items:center;align-self:center;gap:2px;width:100%;height:var(--size-16)}.dot-flashing.svelte-1gpwetz{animation:svelte-1gpwetz-flash 1s infinite ease-in-out;border-radius:5px;background-color:var(--body-text-color);width:7px;height:7px;color:var(--body-text-color)}@keyframes svelte-1gpwetz-flash{0%,100%{opacity:0}50%{opacity:1}}.dot-flashing.svelte-1gpwetz:nth-child(1){animation-delay:0s}.dot-flashing.svelte-1gpwetz:nth-child(2){animation-delay:0.33s}.dot-flashing.svelte-1gpwetz:nth-child(3){animation-delay:0.66s}\",\n  map: '{\"version\":3,\"file\":\"Pending.svelte\",\"sources\":[\"Pending.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let layout = \\\\\"bubble\\\\\";\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"message pending\\\\\"\\\\n\\\\trole=\\\\\"status\\\\\"\\\\n\\\\taria-label=\\\\\"Loading response\\\\\"\\\\n\\\\taria-live=\\\\\"polite\\\\\"\\\\n\\\\tstyle:border-radius={layout === \\\\\"bubble\\\\\" ? \\\\\"var(--radius-xxl)\\\\\" : \\\\\"none\\\\\"}\\\\n>\\\\n\\\\t<span class=\\\\\"sr-only\\\\\">Loading content</span>\\\\n\\\\t<div class=\\\\\"dot-flashing\\\\\" />\\\\n\\\\t&nbsp;\\\\n\\\\t<div class=\\\\\"dot-flashing\\\\\" />\\\\n\\\\t&nbsp;\\\\n\\\\t<div class=\\\\\"dot-flashing\\\\\" />\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.pending {\\\\n\\\\t\\\\tbackground: var(--color-accent-soft);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: row;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\talign-self: center;\\\\n\\\\t\\\\tgap: 2px;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: var(--size-16);\\\\n\\\\t}\\\\n\\\\t.dot-flashing {\\\\n\\\\t\\\\tanimation: flash 1s infinite ease-in-out;\\\\n\\\\t\\\\tborder-radius: 5px;\\\\n\\\\t\\\\tbackground-color: var(--body-text-color);\\\\n\\\\t\\\\twidth: 7px;\\\\n\\\\t\\\\theight: 7px;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\t@keyframes flash {\\\\n\\\\t\\\\t0%,\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\topacity: 0;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\topacity: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.dot-flashing:nth-child(1) {\\\\n\\\\t\\\\tanimation-delay: 0s;\\\\n\\\\t}\\\\n\\\\n\\\\t.dot-flashing:nth-child(2) {\\\\n\\\\t\\\\tanimation-delay: 0.33s;\\\\n\\\\t}\\\\n\\\\t.dot-flashing:nth-child(3) {\\\\n\\\\t\\\\tanimation-delay: 0.66s;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAmBC,uBAAS,CACR,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,GAAG,CACnB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,MAAM,CAClB,GAAG,CAAE,GAAG,CACR,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,SAAS,CACtB,CACA,4BAAc,CACb,SAAS,CAAE,oBAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CACxC,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,GAAG,CACX,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CACA,WAAW,oBAAM,CAChB,EAAE,CACF,IAAK,CACJ,OAAO,CAAE,CACV,CACA,GAAI,CACH,OAAO,CAAE,CACV,CACD,CAEA,4BAAa,WAAW,CAAC,CAAE,CAC1B,eAAe,CAAE,EAClB,CAEA,4BAAa,WAAW,CAAC,CAAE,CAC1B,eAAe,CAAE,KAClB,CACA,4BAAa,WAAW,CAAC,CAAE,CAC1B,eAAe,CAAE,KAClB\"}'\n};\nconst Pending = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { layout = \"bubble\" } = $$props;\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  $$result.css.add(css$2);\n  return `<div class=\"message pending svelte-1gpwetz\" role=\"status\" aria-label=\"Loading response\" aria-live=\"polite\"${add_styles({\n    \"border-radius\": layout === \"bubble\" ? \"var(--radius-xxl)\" : \"none\"\n  })} data-svelte-h=\"svelte-exuub1\"><span class=\"sr-only\">Loading content</span> <div class=\"dot-flashing svelte-1gpwetz\"></div>\n\t \n\t<div class=\"dot-flashing svelte-1gpwetz\"></div>\n\t \n\t<div class=\"dot-flashing svelte-1gpwetz\"></div> </div>`;\n});\nconst CopyAll = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  onDestroy(() => {\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  return `${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      Icon: Copy,\n      label: \"Copy conversation\"\n    },\n    {},\n    {}\n  )}`;\n});\nconst css$1 = {\n  code: \".placeholder-content.svelte-vxn3uw.svelte-vxn3uw{display:flex;flex-direction:column;height:100%}.placeholder.svelte-vxn3uw.svelte-vxn3uw{align-items:center;display:flex;justify-content:center;height:100%;flex-grow:1}.examples.svelte-vxn3uw img{pointer-events:none}.examples.svelte-vxn3uw.svelte-vxn3uw{margin:auto;padding:var(--spacing-xxl);display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:var(--spacing-xxl);max-width:calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%))}.example.svelte-vxn3uw.svelte-vxn3uw{display:flex;flex-direction:column;align-items:center;padding:var(--spacing-xl);border:0.05px solid var(--border-color-primary);border-radius:var(--radius-xl);background-color:var(--background-fill-secondary);cursor:pointer;transition:var(--button-transition);max-width:var(--size-56);width:100%;justify-content:center}.example.svelte-vxn3uw.svelte-vxn3uw:hover{background-color:var(--color-accent-soft);border-color:var(--border-color-accent)}.example-icon-container.svelte-vxn3uw.svelte-vxn3uw{display:flex;align-self:flex-start;margin-left:var(--spacing-md);width:var(--size-6);height:var(--size-6)}.example-display-text.svelte-vxn3uw.svelte-vxn3uw,.example-text.svelte-vxn3uw.svelte-vxn3uw,.example-file.svelte-vxn3uw.svelte-vxn3uw{font-size:var(--text-md);width:100%;text-align:center;overflow:hidden;text-overflow:ellipsis}.example-display-text.svelte-vxn3uw.svelte-vxn3uw,.example-file.svelte-vxn3uw.svelte-vxn3uw{margin-top:var(--spacing-md)}.example-image-container.svelte-vxn3uw.svelte-vxn3uw{flex-grow:1;display:flex;justify-content:center;align-items:center;margin-top:var(--spacing-xl)}.example-image-container.svelte-vxn3uw img{max-height:100%;max-width:100%;height:var(--size-32);width:100%;object-fit:cover;border-radius:var(--radius-xl)}.panel-wrap.svelte-vxn3uw.svelte-vxn3uw{width:100%;overflow-y:auto}.bubble-wrap.svelte-vxn3uw.svelte-vxn3uw{width:100%;overflow-y:auto;height:100%;padding-top:var(--spacing-xxl)}@media(prefers-color-scheme: dark){.bubble-wrap.svelte-vxn3uw.svelte-vxn3uw{background:var(--background-fill-secondary)}}.message-wrap.svelte-vxn3uw.svelte-vxn3uw{display:flex;flex-direction:column;justify-content:space-between;margin-bottom:var(--spacing-xxl)}.message-wrap.svelte-vxn3uw .prose.chatbot.md{opacity:0.8;overflow-wrap:break-word}.message-wrap.svelte-vxn3uw .message-row .md img{border-radius:var(--radius-xl);margin:var(--size-2);width:400px;max-width:30vw;max-height:30vw}.message-wrap.svelte-vxn3uw .message a{color:var(--color-text-link);text-decoration:underline}.message-wrap.svelte-vxn3uw .bot table,.message-wrap.svelte-vxn3uw .bot tr,.message-wrap.svelte-vxn3uw .bot td,.message-wrap.svelte-vxn3uw .bot th{border:1px solid var(--border-color-primary)}.message-wrap.svelte-vxn3uw .user table,.message-wrap.svelte-vxn3uw .user tr,.message-wrap.svelte-vxn3uw .user td,.message-wrap.svelte-vxn3uw .user th{border:1px solid var(--border-color-accent)}.message-wrap.svelte-vxn3uw span.katex{font-size:var(--text-lg);direction:ltr}.message-wrap.svelte-vxn3uw span.katex-display{margin-top:0}.message-wrap.svelte-vxn3uw pre{position:relative}.message-wrap.svelte-vxn3uw .grid-wrap{max-height:80% !important;max-width:600px;object-fit:contain}.message-wrap.svelte-vxn3uw>div.svelte-vxn3uw p:not(:first-child){margin-top:var(--spacing-xxl)}.message-wrap.svelte-vxn3uw.svelte-vxn3uw{display:flex;flex-direction:column;justify-content:space-between;margin-bottom:var(--spacing-xxl)}.panel-wrap.svelte-vxn3uw .message-row:first-child{padding-top:calc(var(--spacing-xxl) * 2)}.scroll-down-button-container.svelte-vxn3uw.svelte-vxn3uw{position:absolute;bottom:10px;left:50%;transform:translateX(-50%);z-index:var(--layer-top)}.scroll-down-button-container.svelte-vxn3uw button{border-radius:50%;box-shadow:var(--shadow-drop);transition:box-shadow 0.2s ease-in-out,\\n\t\t\ttransform 0.2s ease-in-out}.scroll-down-button-container.svelte-vxn3uw button:hover{box-shadow:var(--shadow-drop),\\n\t\t\t0 2px 2px rgba(0, 0, 0, 0.05);transform:translateY(-2px)}.image-preview.svelte-vxn3uw.svelte-vxn3uw{position:absolute;z-index:999;left:0;top:0;width:100%;height:100%;overflow:auto;background-color:var(--background-fill-secondary);display:flex;justify-content:center;align-items:center}\",\n  map: '{\"version\":3,\"file\":\"ChatBot.svelte\",\"sources\":[\"ChatBot.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { format_chat_for_sharing, is_last_bot_message, group_messages, load_components, get_components_from_messages } from \\\\\"./utils\\\\\";\\\\nimport { copy } from \\\\\"@gradio/utils\\\\\";\\\\nimport Message from \\\\\"./Message.svelte\\\\\";\\\\nimport { dequal } from \\\\\"dequal/lite\\\\\";\\\\nimport { afterUpdate, createEventDispatcher, tick, onMount } from \\\\\"svelte\\\\\";\\\\nimport { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport { Clear, Trash, Community, ScrollDownArrow } from \\\\\"@gradio/icons\\\\\";\\\\nimport { IconButtonWrapper, IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { MarkdownCode as Markdown } from \\\\\"@gradio/markdown-code\\\\\";\\\\nimport Pending from \\\\\"./Pending.svelte\\\\\";\\\\nimport { ShareError } from \\\\\"@gradio/utils\\\\\";\\\\nimport { Gradio } from \\\\\"@gradio/utils\\\\\";\\\\nexport let value = [];\\\\nlet old_value = null;\\\\nimport CopyAll from \\\\\"./CopyAll.svelte\\\\\";\\\\nexport let _fetch;\\\\nexport let load_component;\\\\nlet _components = {};\\\\nconst is_browser = typeof window !== \\\\\"undefined\\\\\";\\\\nasync function update_components() {\\\\n    _components = await load_components(get_components_from_messages(value), _components, load_component);\\\\n}\\\\n$: value, update_components();\\\\nexport let latex_delimiters;\\\\nexport let pending_message = false;\\\\nexport let generating = false;\\\\nexport let selectable = false;\\\\nexport let likeable = false;\\\\nexport let show_share_button = false;\\\\nexport let show_copy_all_button = false;\\\\nexport let rtl = false;\\\\nexport let show_copy_button = false;\\\\nexport let avatar_images = [null, null];\\\\nexport let sanitize_html = true;\\\\nexport let bubble_full_width = true;\\\\nexport let render_markdown = true;\\\\nexport let line_breaks = true;\\\\nexport let autoscroll = true;\\\\nexport let theme_mode;\\\\nexport let i18n;\\\\nexport let layout = \\\\\"bubble\\\\\";\\\\nexport let placeholder = null;\\\\nexport let upload;\\\\nexport let msg_format = \\\\\"tuples\\\\\";\\\\nexport let examples = null;\\\\nexport let _retryable = false;\\\\nexport let _undoable = false;\\\\nexport let like_user_message = false;\\\\nexport let root;\\\\nlet target = null;\\\\nonMount(() => {\\\\n    target = document.querySelector(\\\\\"div.gradio-container\\\\\");\\\\n});\\\\nlet div;\\\\nlet show_scroll_button = false;\\\\nconst dispatch = createEventDispatcher();\\\\nfunction is_at_bottom() {\\\\n    return div && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\\\\n}\\\\nfunction scroll_to_bottom() {\\\\n    if (!div)\\\\n        return;\\\\n    div.scrollTo(0, div.scrollHeight);\\\\n    show_scroll_button = false;\\\\n}\\\\nlet scroll_after_component_load = false;\\\\nfunction on_child_component_load() {\\\\n    if (scroll_after_component_load) {\\\\n        scroll_to_bottom();\\\\n        scroll_after_component_load = false;\\\\n    }\\\\n}\\\\nasync function scroll_on_value_update() {\\\\n    if (!autoscroll)\\\\n        return;\\\\n    if (is_at_bottom()) {\\\\n        scroll_after_component_load = true;\\\\n        await tick();\\\\n        scroll_to_bottom();\\\\n    }\\\\n    else {\\\\n        show_scroll_button = true;\\\\n    }\\\\n}\\\\nonMount(() => {\\\\n    scroll_on_value_update();\\\\n});\\\\n$: if (value || pending_message || _components) {\\\\n    scroll_on_value_update();\\\\n}\\\\nonMount(() => {\\\\n    function handle_scroll() {\\\\n        if (is_at_bottom()) {\\\\n            show_scroll_button = false;\\\\n        }\\\\n        else {\\\\n            scroll_after_component_load = false;\\\\n        }\\\\n    }\\\\n    div?.addEventListener(\\\\\"scroll\\\\\", handle_scroll);\\\\n    return () => {\\\\n        div?.removeEventListener(\\\\\"scroll\\\\\", handle_scroll);\\\\n    };\\\\n});\\\\nlet image_preview_source;\\\\nlet image_preview_source_alt;\\\\nlet is_image_preview_open = false;\\\\nafterUpdate(() => {\\\\n    if (!div)\\\\n        return;\\\\n    div.querySelectorAll(\\\\\"img\\\\\").forEach((n) => {\\\\n        n.addEventListener(\\\\\"click\\\\\", (e) => {\\\\n            const target2 = e.target;\\\\n            if (target2) {\\\\n                image_preview_source = target2.src;\\\\n                image_preview_source_alt = target2.alt;\\\\n                is_image_preview_open = true;\\\\n            }\\\\n        });\\\\n    });\\\\n});\\\\n$: {\\\\n    if (!dequal(value, old_value)) {\\\\n        old_value = value;\\\\n        dispatch(\\\\\"change\\\\\");\\\\n    }\\\\n}\\\\n$: groupedMessages = value && group_messages(value, msg_format);\\\\nfunction handle_example_select(i, example) {\\\\n    dispatch(\\\\\"example_select\\\\\", {\\\\n        index: i,\\\\n        value: { text: example.text, files: example.files }\\\\n    });\\\\n}\\\\nfunction handle_like(i, message, selected) {\\\\n    if (selected === \\\\\"undo\\\\\" || selected === \\\\\"retry\\\\\") {\\\\n        const val_ = value;\\\\n        let last_index = val_.length - 1;\\\\n        while (val_[last_index].role === \\\\\"assistant\\\\\") {\\\\n            last_index--;\\\\n        }\\\\n        dispatch(selected, {\\\\n            index: val_[last_index].index,\\\\n            value: val_[last_index].content\\\\n        });\\\\n        return;\\\\n    }\\\\n    if (msg_format === \\\\\"tuples\\\\\") {\\\\n        dispatch(\\\\\"like\\\\\", {\\\\n            index: message.index,\\\\n            value: message.content,\\\\n            liked: selected === \\\\\"like\\\\\"\\\\n        });\\\\n    }\\\\n    else {\\\\n        if (!groupedMessages)\\\\n            return;\\\\n        const message_group = groupedMessages[i];\\\\n        const [first, last] = [\\\\n            message_group[0],\\\\n            message_group[message_group.length - 1]\\\\n        ];\\\\n        dispatch(\\\\\"like\\\\\", {\\\\n            index: [first.index, last.index],\\\\n            value: message_group.map((m) => m.content),\\\\n            liked: selected === \\\\\"like\\\\\"\\\\n        });\\\\n    }\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if value !== null && value.length > 0}\\\\n\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t{#if show_share_button}\\\\n\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\tIcon={Community}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={async () => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ttry {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t// @ts-ignore\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tconst formatted = await format_chat_for_sharing(value);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"share\\\\\", {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdescription: formatted\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t});\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t} catch (e) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tconsole.error(e);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlet message = e instanceof ShareError ? e.message : \\\\\"Share failed.\\\\\";\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"error\\\\\", message);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<Community />\\\\n\\\\t\\\\t\\\\t</IconButton>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<IconButton Icon={Trash} on:click={() => dispatch(\\\\\"clear\\\\\")} label={\\\\\"Clear\\\\\"}\\\\n\\\\t\\\\t></IconButton>\\\\n\\\\t\\\\t{#if show_copy_all_button}\\\\n\\\\t\\\\t\\\\t<CopyAll {value} />\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</IconButtonWrapper>\\\\n{/if}\\\\n\\\\n<div\\\\n\\\\tclass={layout === \\\\\"bubble\\\\\" ? \\\\\"bubble-wrap\\\\\" : \\\\\"panel-wrap\\\\\"}\\\\n\\\\tbind:this={div}\\\\n\\\\trole=\\\\\"log\\\\\"\\\\n\\\\taria-label=\\\\\"chatbot conversation\\\\\"\\\\n\\\\taria-live=\\\\\"polite\\\\\"\\\\n>\\\\n\\\\t{#if value !== null && value.length > 0 && groupedMessages !== null}\\\\n\\\\t\\\\t<div class=\\\\\"message-wrap\\\\\" use:copy>\\\\n\\\\t\\\\t\\\\t{#each groupedMessages as messages, i}\\\\n\\\\t\\\\t\\\\t\\\\t{@const role = messages[0].role === \\\\\"user\\\\\" ? \\\\\"user\\\\\" : \\\\\"bot\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t{@const avatar_img = avatar_images[role === \\\\\"user\\\\\" ? 0 : 1]}\\\\n\\\\t\\\\t\\\\t\\\\t{@const opposite_avatar_img = avatar_images[role === \\\\\"user\\\\\" ? 0 : 1]}\\\\n\\\\t\\\\t\\\\t\\\\t{#if is_image_preview_open}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"image-preview\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<img src={image_preview_source} alt={image_preview_source_alt} />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Clear}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => (is_image_preview_open = false)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel={\\\\\"Clear\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t<Message\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{messages}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{opposite_avatar_img}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{avatar_img}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{role}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{layout}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{dispatch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{selectable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{bubble_full_width}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{msg_format}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_like={role === \\\\\"user\\\\\" ? likeable && like_user_message : likeable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_retry={_retryable && is_last_bot_message(messages, value)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_undo={_undoable && is_last_bot_message(messages, value)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{show_copy_button}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\thandle_action={(selected) => handle_like(i, messages[0], selected)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tscroll={is_browser ? scroll : () => {}}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t{#if pending_message}\\\\n\\\\t\\\\t\\\\t\\\\t<Pending {layout} />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<div class=\\\\\"placeholder-content\\\\\">\\\\n\\\\t\\\\t\\\\t{#if placeholder !== null}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"placeholder\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Markdown message={placeholder} {latex_delimiters} {root} />\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{#if examples !== null}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"examples\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each examples as example, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_example_select(i, example)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example.icon !== undefined}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-icon-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={example.icon.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"example-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example.display_text !== undefined}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"example-display-text\\\\\">{example.display_text}</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"example-text\\\\\">{example.text}</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example.files !== undefined && example.files.length > 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"example-file\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t><em>{example.files.length} Files</em></span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files !== undefined && example.files[0] !== undefined && example.files[0].mime_type?.includes(\\\\\"image\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={example.files[0].url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files !== undefined && example.files[0] !== undefined}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"example-file\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t><em>{example.files[0].orig_name}</em></span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n{#if show_scroll_button}\\\\n\\\\t<div class=\\\\\"scroll-down-button-container\\\\\">\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={ScrollDownArrow}\\\\n\\\\t\\\\t\\\\tlabel=\\\\\"Scroll down\\\\\"\\\\n\\\\t\\\\t\\\\tsize=\\\\\"large\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={scroll_to_bottom}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.placeholder-content {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.placeholder {\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t.examples :global(img) {\\\\n\\\\t\\\\tpointer-events: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.examples {\\\\n\\\\t\\\\tmargin: auto;\\\\n\\\\t\\\\tpadding: var(--spacing-xxl);\\\\n\\\\t\\\\tdisplay: grid;\\\\n\\\\t\\\\tgrid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\\\n\\\\t\\\\tgap: var(--spacing-xxl);\\\\n\\\\t\\\\tmax-width: calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));\\\\n\\\\t}\\\\n\\\\n\\\\t.example {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tpadding: var(--spacing-xl);\\\\n\\\\t\\\\tborder: 0.05px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\ttransition: var(--button-transition);\\\\n\\\\t\\\\tmax-width: var(--size-56);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.example:hover {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.example-icon-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tmargin-left: var(--spacing-md);\\\\n\\\\t\\\\twidth: var(--size-6);\\\\n\\\\t\\\\theight: var(--size-6);\\\\n\\\\t}\\\\n\\\\n\\\\t.example-display-text,\\\\n\\\\t.example-text,\\\\n\\\\t.example-file {\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-display-text,\\\\n\\\\t.example-file {\\\\n\\\\t\\\\tmargin-top: var(--spacing-md);\\\\n\\\\t}\\\\n\\\\n\\\\t.example-image-container {\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmargin-top: var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.example-image-container :global(img) {\\\\n\\\\t\\\\tmax-height: 100%;\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\theight: var(--size-32);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel-wrap {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble-wrap {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tpadding-top: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (prefers-color-scheme: dark) {\\\\n\\\\t\\\\t.bubble-wrap {\\\\n\\\\t\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.prose.chatbot.md) {\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.message-row .md img) {\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tmargin: var(--size-2);\\\\n\\\\t\\\\twidth: 400px;\\\\n\\\\t\\\\tmax-width: 30vw;\\\\n\\\\t\\\\tmax-height: 30vw;\\\\n\\\\t}\\\\n\\\\n\\\\t/* link styles */\\\\n\\\\t.message-wrap :global(.message a) {\\\\n\\\\t\\\\tcolor: var(--color-text-link);\\\\n\\\\t\\\\ttext-decoration: underline;\\\\n\\\\t}\\\\n\\\\n\\\\t/* table styles */\\\\n\\\\t.message-wrap :global(.bot table),\\\\n\\\\t.message-wrap :global(.bot tr),\\\\n\\\\t.message-wrap :global(.bot td),\\\\n\\\\t.message-wrap :global(.bot th) {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.user table),\\\\n\\\\t.message-wrap :global(.user tr),\\\\n\\\\t.message-wrap :global(.user td),\\\\n\\\\t.message-wrap :global(.user th) {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t/* KaTeX */\\\\n\\\\t.message-wrap :global(span.katex) {\\\\n\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\tdirection: ltr;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(span.katex-display) {\\\\n\\\\t\\\\tmargin-top: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(pre) {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.grid-wrap) {\\\\n\\\\t\\\\tmax-height: 80% !important;\\\\n\\\\t\\\\tmax-width: 600px;\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap > div :global(p:not(:first-child)) {\\\\n\\\\t\\\\tmargin-top: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel-wrap :global(.message-row:first-child) {\\\\n\\\\t\\\\tpadding-top: calc(var(--spacing-xxl) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.scroll-down-button-container {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: 10px;\\\\n\\\\t\\\\tleft: 50%;\\\\n\\\\t\\\\ttransform: translateX(-50%);\\\\n\\\\t\\\\tz-index: var(--layer-top);\\\\n\\\\t}\\\\n\\\\t.scroll-down-button-container :global(button) {\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\ttransition:\\\\n\\\\t\\\\t\\\\tbox-shadow 0.2s ease-in-out,\\\\n\\\\t\\\\t\\\\ttransform 0.2s ease-in-out;\\\\n\\\\t}\\\\n\\\\t.scroll-down-button-container :global(button:hover) {\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\tvar(--shadow-drop),\\\\n\\\\t\\\\t\\\\t0 2px 2px rgba(0, 0, 0, 0.05);\\\\n\\\\t\\\\ttransform: translateY(-2px);\\\\n\\\\t}\\\\n\\\\n\\\\t.image-preview {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tz-index: 999;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\toverflow: auto;\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAwUC,gDAAqB,CACpB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,MAAM,CAAE,IACT,CAEA,wCAAa,CACZ,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,CACZ,CAEA,uBAAS,CAAS,GAAK,CACtB,cAAc,CAAE,IACjB,CAEA,qCAAU,CACT,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,aAAa,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,aAAa,CAAC,CACvB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9D,CAEA,oCAAS,CACR,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,MAAM,CAAE,MAAM,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAChD,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,CACX,eAAe,CAAE,MAClB,CAEA,oCAAQ,MAAO,CACd,gBAAgB,CAAE,IAAI,mBAAmB,CAAC,CAC1C,YAAY,CAAE,IAAI,qBAAqB,CACxC,CAEA,mDAAwB,CACvB,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,UAAU,CACtB,WAAW,CAAE,IAAI,YAAY,CAAC,CAC9B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CACrB,CAEA,iDAAqB,CACrB,yCAAa,CACb,yCAAc,CACb,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,MAAM,CAClB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,iDAAqB,CACrB,yCAAc,CACb,UAAU,CAAE,IAAI,YAAY,CAC7B,CAEA,oDAAyB,CACxB,SAAS,CAAE,CAAC,CACZ,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,YAAY,CAC7B,CAEA,sCAAwB,CAAS,GAAK,CACrC,UAAU,CAAE,IAAI,CAChB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,uCAAY,CACX,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IACb,CAEA,wCAAa,CACZ,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,MAAO,uBAAuB,IAAI,CAAE,CACnC,wCAAa,CACZ,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CACD,CAEA,yCAAc,CACb,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,aAAa,CAC9B,aAAa,CAAE,IAAI,aAAa,CACjC,CAEA,2BAAa,CAAS,iBAAmB,CACxC,OAAO,CAAE,GAAG,CACZ,aAAa,CAAE,UAChB,CAEA,2BAAa,CAAS,oBAAsB,CAC3C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,KAAK,CACZ,SAAS,CAAE,IAAI,CACf,UAAU,CAAE,IACb,CAGA,2BAAa,CAAS,UAAY,CACjC,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,eAAe,CAAE,SAClB,CAGA,2BAAa,CAAS,UAAW,CACjC,2BAAa,CAAS,OAAQ,CAC9B,2BAAa,CAAS,OAAQ,CAC9B,2BAAa,CAAS,OAAS,CAC9B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CAEA,2BAAa,CAAS,WAAY,CAClC,2BAAa,CAAS,QAAS,CAC/B,2BAAa,CAAS,QAAS,CAC/B,2BAAa,CAAS,QAAU,CAC/B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB,CAC5C,CAGA,2BAAa,CAAS,UAAY,CACjC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,SAAS,CAAE,GACZ,CAEA,2BAAa,CAAS,kBAAoB,CACzC,UAAU,CAAE,CACb,CAEA,2BAAa,CAAS,GAAK,CAC1B,QAAQ,CAAE,QACX,CAEA,2BAAa,CAAS,UAAY,CACjC,UAAU,CAAE,GAAG,CAAC,UAAU,CAC1B,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,OACb,CAEA,2BAAa,CAAG,iBAAG,CAAS,mBAAqB,CAChD,UAAU,CAAE,IAAI,aAAa,CAC9B,CAEA,yCAAc,CACb,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,aAAa,CAC9B,aAAa,CAAE,IAAI,aAAa,CACjC,CAEA,yBAAW,CAAS,wBAA0B,CAC7C,WAAW,CAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CACzC,CAEA,yDAA8B,CAC7B,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,OAAO,CAAE,IAAI,WAAW,CACzB,CACA,2CAA6B,CAAS,MAAQ,CAC7C,aAAa,CAAE,GAAG,CAClB,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CACT,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC;AAC/B,GAAG,SAAS,CAAC,IAAI,CAAC,WACjB,CACA,2CAA6B,CAAS,YAAc,CACnD,UAAU,CACT,IAAI,aAAa,CAAC,CAAC;AACtB,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9B,SAAS,CAAE,WAAW,IAAI,CAC3B,CAEA,0CAAe,CACd,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,GAAG,CACZ,IAAI,CAAE,CAAC,CACP,GAAG,CAAE,CAAC,CACN,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,IAAI,CACd,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd\"}'\n};\nconst ChatBot = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let groupedMessages;\n  let { value = [] } = $$props;\n  let old_value = null;\n  let { _fetch } = $$props;\n  let { load_component } = $$props;\n  let _components = {};\n  const is_browser = typeof window !== \"undefined\";\n  async function update_components() {\n    _components = await load_components(get_components_from_messages(value), _components, load_component);\n  }\n  let { latex_delimiters } = $$props;\n  let { pending_message = false } = $$props;\n  let { generating = false } = $$props;\n  let { selectable = false } = $$props;\n  let { likeable = false } = $$props;\n  let { show_share_button = false } = $$props;\n  let { show_copy_all_button = false } = $$props;\n  let { rtl = false } = $$props;\n  let { show_copy_button = false } = $$props;\n  let { avatar_images = [null, null] } = $$props;\n  let { sanitize_html = true } = $$props;\n  let { bubble_full_width = true } = $$props;\n  let { render_markdown = true } = $$props;\n  let { line_breaks = true } = $$props;\n  let { autoscroll = true } = $$props;\n  let { theme_mode } = $$props;\n  let { i18n } = $$props;\n  let { layout = \"bubble\" } = $$props;\n  let { placeholder = null } = $$props;\n  let { upload } = $$props;\n  let { msg_format = \"tuples\" } = $$props;\n  let { examples = null } = $$props;\n  let { _retryable = false } = $$props;\n  let { _undoable = false } = $$props;\n  let { like_user_message = false } = $$props;\n  let { root } = $$props;\n  let target = null;\n  onMount(() => {\n    target = document.querySelector(\"div.gradio-container\");\n  });\n  let div;\n  let show_scroll_button = false;\n  const dispatch = createEventDispatcher();\n  async function scroll_on_value_update() {\n    if (!autoscroll)\n      return;\n    {\n      show_scroll_button = true;\n    }\n  }\n  onMount(() => {\n    scroll_on_value_update();\n  });\n  onMount(() => {\n    return () => {\n    };\n  });\n  afterUpdate(() => {\n    return;\n  });\n  function handle_like(i, message, selected) {\n    if (selected === \"undo\" || selected === \"retry\") {\n      const val_ = value;\n      let last_index = val_.length - 1;\n      while (val_[last_index].role === \"assistant\") {\n        last_index--;\n      }\n      dispatch(selected, {\n        index: val_[last_index].index,\n        value: val_[last_index].content\n      });\n      return;\n    }\n    if (msg_format === \"tuples\") {\n      dispatch(\"like\", {\n        index: message.index,\n        value: message.content,\n        liked: selected === \"like\"\n      });\n    } else {\n      if (!groupedMessages)\n        return;\n      const message_group = groupedMessages[i];\n      const [first, last] = [message_group[0], message_group[message_group.length - 1]];\n      dispatch(\"like\", {\n        index: [first.index, last.index],\n        value: message_group.map((m) => m.content),\n        liked: selected === \"like\"\n      });\n    }\n  }\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  if ($$props.load_component === void 0 && $$bindings.load_component && load_component !== void 0)\n    $$bindings.load_component(load_component);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.pending_message === void 0 && $$bindings.pending_message && pending_message !== void 0)\n    $$bindings.pending_message(pending_message);\n  if ($$props.generating === void 0 && $$bindings.generating && generating !== void 0)\n    $$bindings.generating(generating);\n  if ($$props.selectable === void 0 && $$bindings.selectable && selectable !== void 0)\n    $$bindings.selectable(selectable);\n  if ($$props.likeable === void 0 && $$bindings.likeable && likeable !== void 0)\n    $$bindings.likeable(likeable);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.show_copy_all_button === void 0 && $$bindings.show_copy_all_button && show_copy_all_button !== void 0)\n    $$bindings.show_copy_all_button(show_copy_all_button);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.show_copy_button === void 0 && $$bindings.show_copy_button && show_copy_button !== void 0)\n    $$bindings.show_copy_button(show_copy_button);\n  if ($$props.avatar_images === void 0 && $$bindings.avatar_images && avatar_images !== void 0)\n    $$bindings.avatar_images(avatar_images);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props.bubble_full_width === void 0 && $$bindings.bubble_full_width && bubble_full_width !== void 0)\n    $$bindings.bubble_full_width(bubble_full_width);\n  if ($$props.render_markdown === void 0 && $$bindings.render_markdown && render_markdown !== void 0)\n    $$bindings.render_markdown(render_markdown);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.autoscroll === void 0 && $$bindings.autoscroll && autoscroll !== void 0)\n    $$bindings.autoscroll(autoscroll);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  if ($$props.placeholder === void 0 && $$bindings.placeholder && placeholder !== void 0)\n    $$bindings.placeholder(placeholder);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.msg_format === void 0 && $$bindings.msg_format && msg_format !== void 0)\n    $$bindings.msg_format(msg_format);\n  if ($$props.examples === void 0 && $$bindings.examples && examples !== void 0)\n    $$bindings.examples(examples);\n  if ($$props._retryable === void 0 && $$bindings._retryable && _retryable !== void 0)\n    $$bindings._retryable(_retryable);\n  if ($$props._undoable === void 0 && $$bindings._undoable && _undoable !== void 0)\n    $$bindings._undoable(_undoable);\n  if ($$props.like_user_message === void 0 && $$bindings.like_user_message && like_user_message !== void 0)\n    $$bindings.like_user_message(like_user_message);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  $$result.css.add(css$1);\n  {\n    update_components();\n  }\n  {\n    if (value || pending_message || _components) {\n      scroll_on_value_update();\n    }\n  }\n  {\n    {\n      if (!dequal(value, old_value)) {\n        old_value = value;\n        dispatch(\"change\");\n      }\n    }\n  }\n  groupedMessages = value && group_messages(value, msg_format);\n  return `${value !== null && value.length > 0 ? `${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, {}, {}, {\n    default: () => {\n      return `${show_share_button ? `${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Community }, {}, {\n        default: () => {\n          return `${validate_component(Community, \"Community\").$$render($$result, {}, {}, {})}`;\n        }\n      })}` : ``} ${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Trash, label: \"Clear\" }, {}, {})} ${show_copy_all_button ? `${validate_component(CopyAll, \"CopyAll\").$$render($$result, { value }, {}, {})}` : ``}`;\n    }\n  })}` : ``} <div class=\"${escape(null_to_empty(layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"), true) + \" svelte-vxn3uw\"}\" role=\"log\" aria-label=\"chatbot conversation\" aria-live=\"polite\"${add_attribute(\"this\", div, 0)}>${value !== null && value.length > 0 && groupedMessages !== null ? `<div class=\"message-wrap svelte-vxn3uw\">${each(groupedMessages, (messages, i) => {\n    let role = messages[0].role === \"user\" ? \"user\" : \"bot\", avatar_img = avatar_images[role === \"user\" ? 0 : 1], opposite_avatar_img = avatar_images[role === \"user\" ? 0 : 1];\n    return `   ${``} ${validate_component(Message, \"Message\").$$render(\n      $$result,\n      {\n        messages,\n        opposite_avatar_img,\n        avatar_img,\n        role,\n        layout,\n        dispatch,\n        i18n,\n        _fetch,\n        line_breaks,\n        theme_mode,\n        target,\n        root,\n        upload,\n        selectable,\n        sanitize_html,\n        bubble_full_width,\n        render_markdown,\n        rtl,\n        i,\n        value,\n        latex_delimiters,\n        _components,\n        generating,\n        msg_format,\n        show_like: role === \"user\" ? likeable && like_user_message : likeable,\n        show_retry: _retryable && is_last_bot_message(messages, value),\n        show_undo: _undoable && is_last_bot_message(messages, value),\n        show_copy_button,\n        handle_action: (selected) => handle_like(i, messages[0], selected),\n        scroll: is_browser ? scroll : () => {\n        }\n      },\n      {},\n      {}\n    )}`;\n  })} ${pending_message ? `${validate_component(Pending, \"Pending\").$$render($$result, { layout }, {}, {})}` : ``}</div>` : `<div class=\"placeholder-content svelte-vxn3uw\">${placeholder !== null ? `<div class=\"placeholder svelte-vxn3uw\">${validate_component(MarkdownCode, \"Markdown\").$$render(\n    $$result,\n    {\n      message: placeholder,\n      latex_delimiters,\n      root\n    },\n    {},\n    {}\n  )}</div>` : ``} ${examples !== null ? `<div class=\"examples svelte-vxn3uw\">${each(examples, (example, i) => {\n    return `<button class=\"example svelte-vxn3uw\">${example.icon !== void 0 ? `<div class=\"example-icon-container svelte-vxn3uw\">${validate_component(Image, \"Image\").$$render(\n      $$result,\n      {\n        class: \"example-icon\",\n        src: example.icon.url,\n        alt: \"example-icon\"\n      },\n      {},\n      {}\n    )} </div>` : ``} ${example.display_text !== void 0 ? `<span class=\"example-display-text svelte-vxn3uw\">${escape(example.display_text)}</span>` : `<span class=\"example-text svelte-vxn3uw\">${escape(example.text)}</span>`} ${example.files !== void 0 && example.files.length > 1 ? `<span class=\"example-file svelte-vxn3uw\"><em>${escape(example.files.length)} Files</em></span>` : `${example.files !== void 0 && example.files[0] !== void 0 && example.files[0].mime_type?.includes(\"image\") ? `<div class=\"example-image-container svelte-vxn3uw\">${validate_component(Image, \"Image\").$$render(\n      $$result,\n      {\n        class: \"example-image\",\n        src: example.files[0].url,\n        alt: \"example-image\"\n      },\n      {},\n      {}\n    )} </div>` : `${example.files !== void 0 && example.files[0] !== void 0 ? `<span class=\"example-file svelte-vxn3uw\"><em>${escape(example.files[0].orig_name)}</em></span>` : ``}`}`} </button>`;\n  })}</div>` : ``}</div>`}</div> ${show_scroll_button ? `<div class=\"scroll-down-button-container svelte-vxn3uw\">${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      Icon: ScrollDownArrow,\n      label: \"Scroll down\",\n      size: \"large\"\n    },\n    {},\n    {}\n  )}</div>` : ``}`;\n});\nconst ChatBot$1 = ChatBot;\nconst css = {\n  code: \".wrapper.svelte-g3p8na{display:flex;position:relative;flex-direction:column;align-items:start;width:100%;height:100%;flex-grow:1}.progress-text{right:auto}\",\n  map: '{\"version\":3,\"file\":\"Index.svelte\",\"sources\":[\"Index.svelte\"],\"sourcesContent\":[\"<script context=\\\\\"module\\\\\" lang=\\\\\"ts\\\\\">export { default as BaseChatBot } from \\\\\"./shared/ChatBot.svelte\\\\\";\\\\n<\\/script>\\\\n\\\\n<script lang=\\\\\"ts\\\\\">import ChatBot from \\\\\"./shared/ChatBot.svelte\\\\\";\\\\nimport { Block, BlockLabel } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Chat } from \\\\\"@gradio/icons\\\\\";\\\\nimport { StatusTracker } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport { normalise_tuples, normalise_messages } from \\\\\"./shared/utils\\\\\";\\\\nexport let elem_id = \\\\\"\\\\\";\\\\nexport let elem_classes = [];\\\\nexport let visible = true;\\\\nexport let value = [];\\\\nexport let scale = null;\\\\nexport let min_width = void 0;\\\\nexport let label;\\\\nexport let show_label = true;\\\\nexport let root;\\\\nexport let _selectable = false;\\\\nexport let likeable = false;\\\\nexport let show_share_button = false;\\\\nexport let rtl = false;\\\\nexport let show_copy_button = true;\\\\nexport let show_copy_all_button = false;\\\\nexport let sanitize_html = true;\\\\nexport let bubble_full_width = true;\\\\nexport let layout = \\\\\"bubble\\\\\";\\\\nexport let type = \\\\\"tuples\\\\\";\\\\nexport let render_markdown = true;\\\\nexport let line_breaks = true;\\\\nexport let autoscroll = true;\\\\nexport let _retryable = false;\\\\nexport let _undoable = false;\\\\nexport let latex_delimiters;\\\\nexport let gradio;\\\\nlet _value = [];\\\\n$: _value = type === \\\\\"tuples\\\\\" ? normalise_tuples(value, root) : normalise_messages(value, root);\\\\nexport let avatar_images = [null, null];\\\\nexport let like_user_message = false;\\\\nexport let loading_status = void 0;\\\\nexport let height;\\\\nexport let min_height;\\\\nexport let max_height;\\\\nexport let placeholder = null;\\\\nexport let examples = null;\\\\nexport let theme_mode;\\\\n<\\/script>\\\\n\\\\n<Block\\\\n\\\\t{elem_id}\\\\n\\\\t{elem_classes}\\\\n\\\\t{visible}\\\\n\\\\tpadding={false}\\\\n\\\\t{scale}\\\\n\\\\t{min_width}\\\\n\\\\t{height}\\\\n\\\\t{min_height}\\\\n\\\\t{max_height}\\\\n\\\\tallow_overflow={true}\\\\n\\\\tflex={true}\\\\n\\\\toverflow_behavior=\\\\\"auto\\\\\"\\\\n>\\\\n\\\\t{#if loading_status}\\\\n\\\\t\\\\t<StatusTracker\\\\n\\\\t\\\\t\\\\tautoscroll={gradio.autoscroll}\\\\n\\\\t\\\\t\\\\ti18n={gradio.i18n}\\\\n\\\\t\\\\t\\\\t{...loading_status}\\\\n\\\\t\\\\t\\\\tshow_progress={loading_status.show_progress === \\\\\"hidden\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"hidden\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"minimal\\\\\"}\\\\n\\\\t\\\\t\\\\ton:clear_status={() => gradio.dispatch(\\\\\"clear_status\\\\\", loading_status)}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<div class=\\\\\"wrapper\\\\\">\\\\n\\\\t\\\\t{#if show_label}\\\\n\\\\t\\\\t\\\\t<BlockLabel\\\\n\\\\t\\\\t\\\\t\\\\t{show_label}\\\\n\\\\t\\\\t\\\\t\\\\tIcon={Chat}\\\\n\\\\t\\\\t\\\\t\\\\tfloat={true}\\\\n\\\\t\\\\t\\\\t\\\\tlabel={label || \\\\\"Chatbot\\\\\"}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<ChatBot\\\\n\\\\t\\\\t\\\\ti18n={gradio.i18n}\\\\n\\\\t\\\\t\\\\tselectable={_selectable}\\\\n\\\\t\\\\t\\\\t{likeable}\\\\n\\\\t\\\\t\\\\t{show_share_button}\\\\n\\\\t\\\\t\\\\t{show_copy_all_button}\\\\n\\\\t\\\\t\\\\tvalue={_value}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\tpending_message={loading_status?.status === \\\\\"pending\\\\\"}\\\\n\\\\t\\\\t\\\\tgenerating={loading_status?.status === \\\\\"generating\\\\\"}\\\\n\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t{show_copy_button}\\\\n\\\\t\\\\t\\\\t{like_user_message}\\\\n\\\\t\\\\t\\\\ton:change={() => gradio.dispatch(\\\\\"change\\\\\", value)}\\\\n\\\\t\\\\t\\\\ton:select={(e) => gradio.dispatch(\\\\\"select\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:like={(e) => gradio.dispatch(\\\\\"like\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:share={(e) => gradio.dispatch(\\\\\"share\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:error={(e) => gradio.dispatch(\\\\\"error\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:example_select={(e) => gradio.dispatch(\\\\\"example_select\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:retry={(e) => gradio.dispatch(\\\\\"retry\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:undo={(e) => gradio.dispatch(\\\\\"undo\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:clear={() => {\\\\n\\\\t\\\\t\\\\t\\\\tvalue = [];\\\\n\\\\t\\\\t\\\\t\\\\tgradio.dispatch(\\\\\"clear\\\\\");\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t{avatar_images}\\\\n\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t{bubble_full_width}\\\\n\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t{autoscroll}\\\\n\\\\t\\\\t\\\\t{layout}\\\\n\\\\t\\\\t\\\\t{placeholder}\\\\n\\\\t\\\\t\\\\t{examples}\\\\n\\\\t\\\\t\\\\t{_retryable}\\\\n\\\\t\\\\t\\\\t{_undoable}\\\\n\\\\t\\\\t\\\\tupload={(...args) => gradio.client.upload(...args)}\\\\n\\\\t\\\\t\\\\t_fetch={(...args) => gradio.client.fetch(...args)}\\\\n\\\\t\\\\t\\\\tload_component={gradio.load_component}\\\\n\\\\t\\\\t\\\\tmsg_format={type}\\\\n\\\\t\\\\t\\\\troot={gradio.root}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n</Block>\\\\n\\\\n<style>\\\\n\\\\t.wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: start;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.progress-text) {\\\\n\\\\t\\\\tright: auto;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAgIC,sBAAS,CACR,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,KAAK,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,CACZ,CAEQ,cAAgB,CACvB,KAAK,CAAE,IACR\"}'\n};\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value = [] } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { label } = $$props;\n  let { show_label = true } = $$props;\n  let { root } = $$props;\n  let { _selectable = false } = $$props;\n  let { likeable = false } = $$props;\n  let { show_share_button = false } = $$props;\n  let { rtl = false } = $$props;\n  let { show_copy_button = true } = $$props;\n  let { show_copy_all_button = false } = $$props;\n  let { sanitize_html = true } = $$props;\n  let { bubble_full_width = true } = $$props;\n  let { layout = \"bubble\" } = $$props;\n  let { type = \"tuples\" } = $$props;\n  let { render_markdown = true } = $$props;\n  let { line_breaks = true } = $$props;\n  let { autoscroll = true } = $$props;\n  let { _retryable = false } = $$props;\n  let { _undoable = false } = $$props;\n  let { latex_delimiters } = $$props;\n  let { gradio } = $$props;\n  let _value = [];\n  let { avatar_images = [null, null] } = $$props;\n  let { like_user_message = false } = $$props;\n  let { loading_status = void 0 } = $$props;\n  let { height } = $$props;\n  let { min_height } = $$props;\n  let { max_height } = $$props;\n  let { placeholder = null } = $$props;\n  let { examples = null } = $$props;\n  let { theme_mode } = $$props;\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props._selectable === void 0 && $$bindings._selectable && _selectable !== void 0)\n    $$bindings._selectable(_selectable);\n  if ($$props.likeable === void 0 && $$bindings.likeable && likeable !== void 0)\n    $$bindings.likeable(likeable);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.show_copy_button === void 0 && $$bindings.show_copy_button && show_copy_button !== void 0)\n    $$bindings.show_copy_button(show_copy_button);\n  if ($$props.show_copy_all_button === void 0 && $$bindings.show_copy_all_button && show_copy_all_button !== void 0)\n    $$bindings.show_copy_all_button(show_copy_all_button);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props.bubble_full_width === void 0 && $$bindings.bubble_full_width && bubble_full_width !== void 0)\n    $$bindings.bubble_full_width(bubble_full_width);\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.render_markdown === void 0 && $$bindings.render_markdown && render_markdown !== void 0)\n    $$bindings.render_markdown(render_markdown);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.autoscroll === void 0 && $$bindings.autoscroll && autoscroll !== void 0)\n    $$bindings.autoscroll(autoscroll);\n  if ($$props._retryable === void 0 && $$bindings._retryable && _retryable !== void 0)\n    $$bindings._retryable(_retryable);\n  if ($$props._undoable === void 0 && $$bindings._undoable && _undoable !== void 0)\n    $$bindings._undoable(_undoable);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.avatar_images === void 0 && $$bindings.avatar_images && avatar_images !== void 0)\n    $$bindings.avatar_images(avatar_images);\n  if ($$props.like_user_message === void 0 && $$bindings.like_user_message && like_user_message !== void 0)\n    $$bindings.like_user_message(like_user_message);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  if ($$props.min_height === void 0 && $$bindings.min_height && min_height !== void 0)\n    $$bindings.min_height(min_height);\n  if ($$props.max_height === void 0 && $$bindings.max_height && max_height !== void 0)\n    $$bindings.max_height(max_height);\n  if ($$props.placeholder === void 0 && $$bindings.placeholder && placeholder !== void 0)\n    $$bindings.placeholder(placeholder);\n  if ($$props.examples === void 0 && $$bindings.examples && examples !== void 0)\n    $$bindings.examples(examples);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  $$result.css.add(css);\n  _value = type === \"tuples\" ? normalise_tuples(value, root) : normalise_messages(value, root);\n  return `${validate_component(Block, \"Block\").$$render(\n    $$result,\n    {\n      elem_id,\n      elem_classes,\n      visible,\n      padding: false,\n      scale,\n      min_width,\n      height,\n      min_height,\n      max_height,\n      allow_overflow: true,\n      flex: true,\n      overflow_behavior: \"auto\"\n    },\n    {},\n    {\n      default: () => {\n        return `${loading_status ? `${validate_component(Static, \"StatusTracker\").$$render(\n          $$result,\n          Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status, {\n            show_progress: loading_status.show_progress === \"hidden\" ? \"hidden\" : \"minimal\"\n          }),\n          {},\n          {}\n        )}` : ``} <div class=\"wrapper svelte-g3p8na\">${show_label ? `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n          $$result,\n          {\n            show_label,\n            Icon: Chat,\n            float: true,\n            label: label || \"Chatbot\"\n          },\n          {},\n          {}\n        )}` : ``} ${validate_component(ChatBot$1, \"ChatBot\").$$render(\n          $$result,\n          {\n            i18n: gradio.i18n,\n            selectable: _selectable,\n            likeable,\n            show_share_button,\n            show_copy_all_button,\n            value: _value,\n            latex_delimiters,\n            render_markdown,\n            theme_mode,\n            pending_message: loading_status?.status === \"pending\",\n            generating: loading_status?.status === \"generating\",\n            rtl,\n            show_copy_button,\n            like_user_message,\n            avatar_images,\n            sanitize_html,\n            bubble_full_width,\n            line_breaks,\n            autoscroll,\n            layout,\n            placeholder,\n            examples,\n            _retryable,\n            _undoable,\n            upload: (...args) => gradio.client.upload(...args),\n            _fetch: (...args) => gradio.client.fetch(...args),\n            load_component: gradio.load_component,\n            msg_format: type,\n            root: gradio.root\n          },\n          {},\n          {}\n        )}</div>`;\n      }\n    }\n  )}`;\n});\nexport {\n  ChatBot$1 as BaseChatBot,\n  Index as default\n};\n"], "names": ["Image"], "mappings": ";;;;;;;;;;;;AAOA,MAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtF,SAAS,2BAA2B,CAAC,SAAS,EAAE;AAChD,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,OAAO,MAAM,CAAC;AAClB,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,SAAS,yCAAyC,CAAC,OAAO,EAAE;AAC5D,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7E,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,2BAA2B,CAAC,KAAK,EAAE,SAAS,CAAC;AAC5D,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI;AACvB,IAAI,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAC9B,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,KAAK,EAAE,EAAE;AACb,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,kBAAkB,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC5C,EAAE,IAAI,QAAQ,KAAK,IAAI;AACvB,IAAI,OAAO,QAAQ,CAAC;AACpB,EAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK;AACtC,IAAI,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;AAC7C,MAAM,OAAO;AACb,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAClC,QAAQ,OAAO,EAAE,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;AACxD,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,KAAK,EAAE,CAAC;AAChB,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;AAC1C,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,yCAAyC,CAAC,OAAO,CAAC,OAAO,CAAC;AAC3E,QAAQ,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAClC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,IAAI,EAAE,WAAW;AACzB,QAAQ,KAAK,EAAE,CAAC;AAChB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,EAAE,CAAC;AAC7C,GAAG,CAAC,CAAC;AACL,CAAC;AACD,SAAS,gBAAgB,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC1C,EAAE,IAAI,QAAQ,KAAK,IAAI;AACvB,IAAI,OAAO,QAAQ,CAAC;AACpB,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,KAAK;AACpD,IAAI,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK;AAChD,MAAM,IAAI,OAAO,IAAI,IAAI;AACzB,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,MAAM,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,WAAW,CAAC;AACrD,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACvC,QAAQ,OAAO;AACf,UAAU,IAAI;AACd,UAAU,IAAI,EAAE,MAAM;AACtB,UAAU,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC;AAClD,UAAU,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;AACnC,UAAU,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3B,SAAS,CAAC;AACV,OAAO;AACP,MAAM,IAAI,MAAM,IAAI,OAAO,EAAE;AAC7B,QAAQ,OAAO;AACf,UAAU,OAAO,EAAE,yCAAyC,CAAC,OAAO,CAAC;AACrE,UAAU,IAAI;AACd,UAAU,IAAI,EAAE,WAAW;AAC3B,UAAU,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3B,SAAS,CAAC;AACV,OAAO;AACP,MAAM,OAAO;AACb,QAAQ,IAAI;AACZ,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,WAAW;AACzB,QAAQ,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AACzB,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,CAAC;AAClD,CAAC;AACD,SAAS,oBAAoB,CAAC,OAAO,EAAE;AACvC,EAAE,OAAO,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC;AACtC,CAAC;AACD,SAAS,mBAAmB,CAAC,QAAQ,EAAE,YAAY,EAAE;AACrD,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC;AACpE,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;AACzD,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7G,EAAE,OAAO,OAAO,IAAI,MAAM,CAAC;AAC3B,CAAC;AACD,SAAS,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE;AAC9C,EAAE,MAAM,eAAe,GAAG,EAAE,CAAC;AAC7B,EAAE,IAAI,YAAY,GAAG,EAAE,CAAC;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC;AACzB,EAAE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAClC,IAAI,IAAI,UAAU,KAAK,QAAQ,EAAE;AACjC,MAAM,WAAW,GAAG,IAAI,CAAC;AACzB,KAAK;AACL,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE;AACpE,MAAM,SAAS;AACf,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE;AACtC,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC,KAAK,MAAM;AACX,MAAM,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,QAAQ,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC3C,OAAO;AACP,MAAM,YAAY,GAAG,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,IAAI,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,OAAO,eAAe,CAAC;AACzB,CAAC;AACD,eAAe,eAAe,CAAC,eAAe,EAAE,WAAW,EAAE,cAAc,EAAE;AAC7E,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;AACjB,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;AACtB,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,KAAK;AAC9C,IAAI,IAAI,WAAW,CAAC,cAAc,CAAC,IAAI,cAAc,KAAK,MAAM,EAAE;AAClE,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AACvE,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrB,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAC1D,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,KAAK;AAC9C,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;AAC9C,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC;AACD,SAAS,4BAA4B,CAAC,QAAQ,EAAE;AAChD,EAAE,IAAI,CAAC,QAAQ;AACf,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,IAAI,UAAU,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC7C,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AAChC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAChD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC,CAAC;AACD,MAAM,SAAS,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACjF,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,OAAO,CAAC,EAAE,IAAI,KAAK,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC1H,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,MAAM;AACZ,MAAM,aAAa,EAAE,KAAK;AAC1B,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,YAAY,EAAE,CAAC;AACrB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,MAAM,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACtH,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,aAAa,EAAE,KAAK,CAAC,aAAa;AACxC,MAAM,OAAO,EAAE,EAAE;AACjB,MAAM,mBAAmB,EAAE,IAAI;AAC/B,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACvH,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,iBAAiB,EAAE,EAAE;AAC3B,MAAM,gBAAgB,EAAE,EAAE;AAC1B,MAAM,oBAAoB,EAAE,KAAK;AACjC,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACvH,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,KAAK;AACjC,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,oBAAoB,EAAE,KAAK;AACjC,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACzC,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACvH,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,oBAAoB,EAAE,KAAK;AACjC,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,MAAM,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACtH,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,IAAI;AACV,MAAM,MAAM,EAAE;AACd,QAAQ,QAAQ,EAAE,MAAM;AACxB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,oeAAoe;AAC5e,EAAE,GAAG,EAAE,40DAA40D;AACn1D,CAAC,CAAC;AACF,MAAM,UAAU,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAClF,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,6GAA6G,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,0CAA0C,EAAE,UAAU,CAAC;AAC9L,IAAI,WAAW,EAAE,QAAQ,GAAG,WAAW,GAAG,eAAe;AACzD,GAAG,CAAC,CAAC,+CAA+C,EAAE,QAAQ,GAAG,CAAC,oCAAoC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AACxK,CAAC,CAAC,CAAC;AAIH,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACxF,EAAE,OAAO,CAAC,2+BAA2+B,CAAC,CAAC;AACv/B,CAAC,CAAC,CAAC;AAIH,MAAM,cAAc,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACtF,EAAE,OAAO,CAAC,u+BAAu+B,CAAC,CAAC;AACn/B,CAAC,CAAC,CAAC;AACH,MAAM,WAAW,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACnF,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,gBAAgB;AAC5B,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,KAAK,EAAE,+BAA+B;AAC5C,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC5D,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,KAAK,EAAE,+BAA+B;AAC5C,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC;AACH,MAAM,MAAM,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC9E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,SAAS,CAAC,MAAM;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,IAAI,EAAE,IAAI;AAChB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC;AACH,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAChF,EAAE,OAAO,CAAC,mkEAAmkE,CAAC,CAAC;AAC/kE,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,6fAA6f;AACrgB,EAAE,GAAG,EAAE,CAAC,ywHAAywH,CAAC;AAClxH,CAAC,CAAC;AACF,SAAS,WAAW,CAAC,QAAQ,EAAE;AAC/B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,QAAQ,CAAC;AAC7J,CAAC;AACD,SAAS,QAAQ,CAAC,QAAQ,EAAE;AAC5B,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC/B,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC,OAAO,CAAC;AAC1B,CAAC;AACD,MAAM,WAAW,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACnF,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;AAC/D,EAAE,SAAS,GAAG,gBAAgB,IAAI,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AAClE,EAAE,aAAa,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,oBAAoB,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACzG,EAAE,OAAO,CAAC,EAAE,IAAI,GAAG,CAAC,YAAY,EAAE,kBAAkB,GAAG,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,mBAAmB,GAAG,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,aAAa,EAAE,IAAI,CAAC,GAAG,iBAAiB,CAAC,EAAE,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;AAClT,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AACxQ,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG;AAC3C,UAAU,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,IAAI,OAAO;AAC9D,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ;AACR,UAAU,OAAO,EAAE,MAAM;AACzB,YAAY,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACpH,WAAW;AACX,SAAS;AACT,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACvF,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,KAAK,EAAE,OAAO;AACxB,UAAU,QAAQ,EAAE,UAAU;AAC9B,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACtF,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,KAAK,EAAE,MAAM;AACvB,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,QAAQ,EAAE,UAAU;AAC9B,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACtI,KAAK;AACL,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,gxNAAgxN;AACxxN,EAAE,GAAG,EAAE,ulrBAAulrB;AAC9lrB,CAAC,CAAC;AACF,SAAS,sBAAsB,CAAC,OAAO,EAAE;AACzC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;AAC/B,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC;AAC3B,GAAG,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,MAAM,EAAE;AACnF,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC9C,MAAM,OAAO,CAAC,wBAAwB,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/F,KAAK;AACL,IAAI,OAAO,CAAC,wBAAwB,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,IAAI,EAAE,CAAC,CAAC;AACtI,GAAG;AACH,EAAE,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC;AACzE,CAAC;AACD,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,mBAAmB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;AACxB,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC;AACtB,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,kBAAkB,CAAC;AACzB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,mBAAmB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,mBAAmB,IAAI,mBAAmB,KAAK,KAAK,CAAC;AAChH,IAAI,UAAU,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AACxD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;AAC1D,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,KAAK,KAAK,CAAC;AAC1E,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,EAAE,SAAS,IAAI,UAAU,IAAI,SAAS,IAAI,gBAAgB;AAClE,IAAI,aAAa;AACjB,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,gBAAgB;AACpB,IAAI,OAAO,EAAE,UAAU,KAAK,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ;AAC7D,IAAI,QAAQ,EAAE,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;AAChD,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,MAAM;AACV,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,mBAAmB;AAC1F,IAAI,CAAC,UAAU,KAAK,IAAI,GAAG,aAAa,GAAG,EAAE,IAAI,GAAG,IAAI,mBAAmB,KAAK,IAAI,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACnH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,UAAU,KAAK,IAAI,GAAG,CAAC,2CAA2C,EAAE,kBAAkB,CAACA,OAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACxI,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,GAAG,EAAE,UAAU,EAAE,GAAG;AAC1B,MAAM,GAAG,EAAE,IAAI,GAAG,SAAS;AAC3B,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE;AAChC,IAAI,wBAAwB;AAC5B,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,EAAE,IAAI,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,GAAG,gBAAgB,GAAG,EAAE,CAAC;AAC3F,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,aAAa,KAAK;AACpE,IAAI,OAAO,CAAC,YAAY,EAAE;AAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM;AACpD,QAAQ,oBAAoB,CAAC,OAAO,CAAC,GAAG,OAAO,EAAE,OAAO,CAAC,SAAS,GAAG,EAAE;AACvE,QAAQ,IAAI;AACZ,OAAO,GAAG,eAAe;AACzB,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,iBAAiB,GAAG,aAAa,GAAG,EAAE,IAAI,oBAAoB,IAAI,CAAC,eAAe,GAAG,2BAA2B,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,GAAG,WAAW,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,oBAAoB,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC;AAC1V,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC;AACrC,MAAM,YAAY,EAAE,GAAG,IAAI,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO;AAC7D,KAAK,CAAC,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,YAAY,EAAE,IAAI,GAAG,cAAc,GAAG,sBAAsB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE;AAC1M,MAAM,cAAc;AACpB,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,eAAe,GAAG,2BAA2B,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,UAAU,GAAG,YAAY,GAAG,EAAE,CAAC;AACrJ,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC;AACrC,MAAM,aAAa,EAAE,CAAC,IAAI,CAAC;AAC3B,MAAM,QAAQ,EAAE,UAAU,GAAG,SAAS,GAAG,SAAS;AAClD,MAAM,YAAY,EAAE,GAAG,GAAG,OAAO,GAAG,MAAM;AAC1C,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACxH,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK;AACrC,QAAQ,QAAQ,EAAE,mBAAmB,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC;AACvD,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ;AACzE,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,OAAO,EAAE,OAAO,CAAC,OAAO;AACtC,cAAc,gBAAgB;AAC9B,cAAc,aAAa;AAC3B,cAAc,eAAe;AAC7B,cAAc,WAAW;AACzB,cAAc,IAAI;AAClB,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,CAAC,CAAC;AACf,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ;AAClE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO,EAAE,OAAO,CAAC,OAAO;AAChC,QAAQ,gBAAgB;AACxB,QAAQ,aAAa;AACrB,QAAQ,eAAe;AACvB,QAAQ,WAAW;AACnB,QAAQ,IAAI;AACZ,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,QAAQ;AAChJ,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,MAAM;AACd,QAAQ,UAAU;AAClB,QAAQ,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;AACpC,QAAQ,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS;AACvC,QAAQ,UAAU,EAAE,WAAW;AAC/B,QAAQ,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;AACpC,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,CAAC,2DAA2D,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,aAAa;AACrO,MAAM,UAAU;AAChB,MAAM,MAAM,CAAC,YAAY,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,MAAM;AAC7H,MAAM,CAAC;AACP,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,MAAM,KAAK,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,kBAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjS,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,KAAK,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,kBAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvK,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,ioBAAioB;AACzoB,EAAE,GAAG,EAAE,gnEAAgnE;AACvnE,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,0GAA0G,EAAE,UAAU,CAAC;AACjI,IAAI,eAAe,EAAE,MAAM,KAAK,QAAQ,GAAG,mBAAmB,GAAG,MAAM;AACvE,GAAG,CAAC,CAAC;AACL;AACA;AACA;AACA,uDAAuD,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC;AACH,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,SAAS,CAAC,MAAM;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,0pIAA0pI;AAClqI,EAAE,GAAG,EAAE,u+oBAAu+oB;AAC9+oB,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,WAAW,GAAG,EAAE,CAAC;AACvB,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,eAAe,iBAAiB,GAAG;AACrC,IAAI,WAAW,GAAG,MAAM,eAAe,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AAC1G,GAAG;AACH,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,gBAAgB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,iBAAiB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AAIpB,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,kBAAkB,GAAG,KAAK,CAAC;AACjC,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,eAAe,sBAAsB,GAAG;AAC1C,IAAI,IAAI,CAAC,UAAU;AACnB,MAAM,OAAO;AACb,IAAI;AACJ,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,KAAK;AACL,GAAG;AAWH,EAAE,SAAS,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC7C,IAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;AACrD,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC;AACzB,MAAM,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK,WAAW,EAAE;AACpD,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO;AACP,MAAM,QAAQ,CAAC,QAAQ,EAAE;AACzB,QAAQ,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK;AACrC,QAAQ,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO;AACvC,OAAO,CAAC,CAAC;AACT,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,UAAU,KAAK,QAAQ,EAAE;AACjC,MAAM,QAAQ,CAAC,MAAM,EAAE;AACvB,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK;AAC5B,QAAQ,KAAK,EAAE,OAAO,CAAC,OAAO;AAC9B,QAAQ,KAAK,EAAE,QAAQ,KAAK,MAAM;AAClC,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,eAAe;AAC1B,QAAQ,OAAO;AACf,MAAM,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAM,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACxF,MAAM,QAAQ,CAAC,MAAM,EAAE;AACvB,QAAQ,KAAK,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;AACxC,QAAQ,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AAClD,QAAQ,KAAK,EAAE,QAAQ,KAAK,MAAM;AAClC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE;AACF,IAAI,iBAAiB,EAAE,CAAC;AACxB,GAAG;AACH,EAAE;AACF,IAAI,IAAI,KAAK,IAAI,eAAe,IAAI,WAAW,EAAE;AACjD,MAAM,sBAAsB,EAAE,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE;AACF,IAAI;AACJ,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;AACrC,QAAQ,SAAS,GAAG,KAAK,CAAC;AAC1B,QAAQ,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC3B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,eAAe,GAAG,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AAC/D,EAAE,OAAO,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1I,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,iBAAiB,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE;AAChI,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAChG,SAAS;AACT,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,oBAAoB,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/O,KAAK;AACL,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,MAAM,KAAK,QAAQ,GAAG,aAAa,GAAG,YAAY,CAAC,EAAE,IAAI,CAAC,GAAG,gBAAgB,CAAC,iEAAiE,EAAE,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,KAAK,IAAI,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK;AACrX,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,EAAE,UAAU,GAAG,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,GAAG,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/K,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ;AACtE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,QAAQ;AAChB,QAAQ,mBAAmB;AAC3B,QAAQ,UAAU;AAClB,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,QAAQ;AAChB,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,WAAW;AACnB,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,UAAU;AAClB,QAAQ,aAAa;AACrB,QAAQ,iBAAiB;AACzB,QAAQ,eAAe;AACvB,QAAQ,GAAG;AACX,QAAQ,CAAC;AACT,QAAQ,KAAK;AACb,QAAQ,gBAAgB;AACxB,QAAQ,WAAW;AACnB,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,SAAS,EAAE,IAAI,KAAK,MAAM,GAAG,QAAQ,IAAI,iBAAiB,GAAG,QAAQ;AAC7E,QAAQ,UAAU,EAAE,UAAU,IAAI,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC;AACtE,QAAQ,SAAS,EAAE,SAAS,IAAI,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC;AACpE,QAAQ,gBAAgB;AACxB,QAAQ,aAAa,EAAE,CAAC,QAAQ,KAAK,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;AAC1E,QAAQ,MAAM,EAAE,UAAU,GAAG,MAAM,GAAG,MAAM;AAC5C,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,+CAA+C,EAAE,WAAW,KAAK,IAAI,GAAG,CAAC,uCAAuC,EAAE,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ;AACpS,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,gBAAgB;AACtB,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,KAAK,IAAI,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK;AAC9G,IAAI,OAAO,CAAC,sCAAsC,EAAE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,kDAAkD,EAAE,kBAAkB,CAACA,OAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC9K,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG;AAC7B,QAAQ,GAAG,EAAE,cAAc;AAC3B,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,iDAAiD,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,6CAA6C,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mDAAmD,EAAE,kBAAkB,CAACA,OAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC3kB,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;AACjC,QAAQ,GAAG,EAAE,eAAe;AAC5B,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,6CAA6C,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AACpM,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,kBAAkB,GAAG,CAAC,wDAAwD,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACxK,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,eAAe;AAC3B,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,IAAI,EAAE,OAAO;AACnB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC,CAAC;AACE,MAAC,SAAS,GAAG,QAAQ;AAC1B,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,6JAA6J;AACrK,EAAE,GAAG,EAAE,8sJAA8sJ;AACrtJ,CAAC,CAAC;AACG,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,gBAAgB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,iBAAiB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,IAAI,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,cAAc,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,MAAM,GAAG,IAAI,KAAK,QAAQ,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/F,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACvD,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,cAAc,EAAE,IAAI;AAC1B,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,iBAAiB,EAAE,MAAM;AAC/B,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,cAAc,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ;AAC1F,UAAU,QAAQ;AAClB,UAAU,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,EAAE;AACtG,YAAY,aAAa,EAAE,cAAc,CAAC,aAAa,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;AAC3F,WAAW,CAAC;AACZ,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,oCAAoC,EAAE,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC5H,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,UAAU;AACtB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,KAAK,EAAE,KAAK,IAAI,SAAS;AACrC,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,QAAQ;AACrE,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;AAC7B,YAAY,UAAU,EAAE,WAAW;AACnC,YAAY,QAAQ;AACpB,YAAY,iBAAiB;AAC7B,YAAY,oBAAoB;AAChC,YAAY,KAAK,EAAE,MAAM;AACzB,YAAY,gBAAgB;AAC5B,YAAY,eAAe;AAC3B,YAAY,UAAU;AACtB,YAAY,eAAe,EAAE,cAAc,EAAE,MAAM,KAAK,SAAS;AACjE,YAAY,UAAU,EAAE,cAAc,EAAE,MAAM,KAAK,YAAY;AAC/D,YAAY,GAAG;AACf,YAAY,gBAAgB;AAC5B,YAAY,iBAAiB;AAC7B,YAAY,aAAa;AACzB,YAAY,aAAa;AACzB,YAAY,iBAAiB;AAC7B,YAAY,WAAW;AACvB,YAAY,UAAU;AACtB,YAAY,MAAM;AAClB,YAAY,WAAW;AACvB,YAAY,QAAQ;AACpB,YAAY,UAAU;AACtB,YAAY,SAAS;AACrB,YAAY,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC9D,YAAY,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC7D,YAAY,cAAc,EAAE,MAAM,CAAC,cAAc;AACjD,YAAY,UAAU,EAAE,IAAI;AAC5B,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;AAC7B,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,MAAM,CAAC,CAAC;AAClB,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN,CAAC;;;;"}