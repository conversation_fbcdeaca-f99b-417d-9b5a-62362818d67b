import{a as Je,i as Oe,s as We,f,E as x,l as A,y as ee,b,z as te,t as w,o as G,D as je,I as D,B as se,c as k,m as I,d as z,Y as ie,S as ne,a5 as N,x as ae,a0 as le,a4 as re,a6 as P}from"../lite.js";import qe from"./ImagePreview-2NMpgMNU.js";import{I as Ce}from"./ImageUploader-DjVwxXyF.js";import{W as Ut}from"./ImageUploader-DjVwxXyF.js";import{E as Te}from"./Empty-C76eC2zW.js";import{I as Ye}from"./Image-Cvn5Jo_E.js";import{U as oe}from"./UploadText-CW3z6Ye_.js";import{I as Et}from"./Image-Bd-jnd8M.js";import{default as Pt}from"./Example-B3LHr6Wt.js";import"./utils-BsGrhMNe.js";import"./BlockLabel-B0HN-MOU.js";import"./ShareButton-Bn_rnIUK.js";import"./Community-_qL8Iyvr.js";import"./Download-CgLP-Xl6.js";import"./Minimize-DJwpjnSa.js";import"./IconButtonWrapper-Ck50MZwX.js";import"./utils-Gtzs_Zla.js";import"./DownloadLink-B8hI46-W.js";import"./file-url-Co2ROWca.js";/* empty css                                                   */import"./SelectSource-CfNi7fcQ.js";import"./Upload-D_TzP4YC.js";import"./DropdownArrow-B85Vabzi.js";import"./Square-7hK0fZD1.js";import"./index-DCygRGWm.js";import"./StreamingBar-C8nPcBp-.js";import"./Upload-TGDabKXH.js";/* empty css                                             *//* empty css                                              */function Ae(s){let e,i;return e=new se({props:{visible:s[5],variant:s[0]===null?"dashed":"solid",border_mode:s[28]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],height:s[10]||void 0,width:s[11],allow_overflow:!1,container:s[14],scale:s[15],min_width:s[16],$$slots:{default:[Re]},$$scope:{ctx:s}}}),e.$on("dragenter",s[31]),e.$on("dragleave",s[31]),e.$on("dragover",s[31]),e.$on("drop",s[32]),{c(){k(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[0]&32&&(o.visible=t[5]),a[0]&1&&(o.variant=t[0]===null?"dashed":"solid"),a[0]&268435456&&(o.border_mode=t[28]?"focus":"base"),a[0]&8&&(o.elem_id=t[3]),a[0]&16&&(o.elem_classes=t[4]),a[0]&1024&&(o.height=t[10]||void 0),a[0]&2048&&(o.width=t[11]),a[0]&16384&&(o.container=t[14]),a[0]&32768&&(o.scale=t[15]),a[0]&65536&&(o.min_width=t[16]),a[0]&2129932999|a[1]&536870912&&(o.$$scope={dirty:a,ctx:t}),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){z(e,t)}}}function Ge(s){let e,i;return e=new se({props:{visible:s[5],variant:"solid",border_mode:s[28]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],height:s[10]||void 0,width:s[11],allow_overflow:!1,container:s[14],scale:s[15],min_width:s[16],$$slots:{default:[Ve]},$$scope:{ctx:s}}}),{c(){k(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[0]&32&&(o.visible=t[5]),a[0]&268435456&&(o.border_mode=t[28]?"focus":"base"),a[0]&8&&(o.elem_id=t[3]),a[0]&16&&(o.elem_classes=t[4]),a[0]&1024&&(o.height=t[10]||void 0),a[0]&2048&&(o.width=t[11]),a[0]&16384&&(o.container=t[14]),a[0]&32768&&(o.scale=t[15]),a[0]&65536&&(o.min_width=t[16]),a[0]&50471365|a[1]&536870912&&(o.$$scope={dirty:a,ctx:t}),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){z(e,t)}}}function He(s){let e,i;return e=new Te({props:{unpadded_box:!0,size:"large",$$slots:{default:[Me]},$$scope:{ctx:s}}}),{c(){k(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[1]&536870912&&(o.$$scope={dirty:a,ctx:t}),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){z(e,t)}}}function Ke(s){let e,i;return e=new oe({props:{i18n:s[25].i18n,type:"clipboard",mode:"short"}}),{c(){k(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[0]&33554432&&(o.i18n=t[25].i18n),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){z(e,t)}}}function Le(s){let e,i;return e=new oe({props:{i18n:s[25].i18n,type:"image",placeholder:s[23]}}),{c(){k(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[0]&33554432&&(o.i18n=t[25].i18n),a[0]&8388608&&(o.placeholder=t[23]),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){z(e,t)}}}function Me(s){let e,i;return e=new Ye({}),{c(){k(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){z(e,t)}}}function Qe(s){let e,i,t,a;const o=[Le,Ke,He],m=[];function _(l,c){return l[29]==="upload"||!l[29]?0:l[29]==="clipboard"?1:2}return e=_(s),i=m[e]=o[e](s),{c(){i.c(),t=x()},m(l,c){m[e].m(l,c),A(l,t,c),a=!0},p(l,c){let h=e;e=_(l),e===h?m[e].p(l,c):(ee(),b(m[h],1,1,()=>{m[h]=null}),te(),i=m[e],i?i.p(l,c):(i=m[e]=o[e](l),i.c()),w(i,1),i.m(t.parentNode,t))},i(l){a||(w(i),a=!0)},o(l){b(i),a=!1},d(l){l&&G(t),m[e].d(l)}}}function Re(s){let e,i,t,a,o,m,_,l,c,h;const E=[{autoscroll:s[25].autoscroll},{i18n:s[25].i18n},s[2]];let p={};for(let r=0;r<E.length;r+=1)p=ie(p,E[r]);e=new ne({props:p}),e.$on("clear_status",s[41]);function F(r){s[44](r)}function O(r){s[45](r)}function W(r){s[46](r)}function j(r){s[47](r)}function q(r){s[48](r)}function C(r){s[49](r)}let v={selectable:s[13],root:s[9],sources:s[18],label:s[6],show_label:s[7],pending:s[21],streaming:s[20],mirror_webcam:s[22],stream_every:s[12],max_file_size:s[25].max_file_size,i18n:s[25].i18n,upload:s[42],stream_handler:s[25].client?.stream,$$slots:{default:[Qe]},$$scope:{ctx:s}};return s[26]!==void 0&&(v.uploading=s[26]),s[29]!==void 0&&(v.active_source=s[29]),s[0]!==void 0&&(v.value=s[0]),s[28]!==void 0&&(v.dragging=s[28]),s[27]!==void 0&&(v.modify_stream=s[27]),s[1]!==void 0&&(v.set_time_limit=s[1]),t=new Ce({props:v}),s[43](t),D.push(()=>N(t,"uploading",F)),D.push(()=>N(t,"active_source",O)),D.push(()=>N(t,"value",W)),D.push(()=>N(t,"dragging",j)),D.push(()=>N(t,"modify_stream",q)),D.push(()=>N(t,"set_time_limit",C)),t.$on("edit",s[50]),t.$on("clear",s[51]),t.$on("stream",s[52]),t.$on("drag",s[53]),t.$on("upload",s[54]),t.$on("select",s[55]),t.$on("share",s[56]),t.$on("error",s[57]),t.$on("close_stream",s[58]),{c(){k(e.$$.fragment),i=ae(),k(t.$$.fragment)},m(r,u){I(e,r,u),A(r,i,u),I(t,r,u),h=!0},p(r,u){const T=u[0]&33554436?le(E,[u[0]&33554432&&{autoscroll:r[25].autoscroll},u[0]&33554432&&{i18n:r[25].i18n},u[0]&4&&re(r[2])]):{};e.$set(T);const g={};u[0]&8192&&(g.selectable=r[13]),u[0]&512&&(g.root=r[9]),u[0]&262144&&(g.sources=r[18]),u[0]&64&&(g.label=r[6]),u[0]&128&&(g.show_label=r[7]),u[0]&2097152&&(g.pending=r[21]),u[0]&1048576&&(g.streaming=r[20]),u[0]&4194304&&(g.mirror_webcam=r[22]),u[0]&4096&&(g.stream_every=r[12]),u[0]&33554432&&(g.max_file_size=r[25].max_file_size),u[0]&33554432&&(g.i18n=r[25].i18n),u[0]&33554432&&(g.upload=r[42]),u[0]&33554432&&(g.stream_handler=r[25].client?.stream),u[0]&578813952|u[1]&536870912&&(g.$$scope={dirty:u,ctx:r}),!a&&u[0]&67108864&&(a=!0,g.uploading=r[26],P(()=>a=!1)),!o&&u[0]&536870912&&(o=!0,g.active_source=r[29],P(()=>o=!1)),!m&&u[0]&1&&(m=!0,g.value=r[0],P(()=>m=!1)),!_&&u[0]&268435456&&(_=!0,g.dragging=r[28],P(()=>_=!1)),!l&&u[0]&134217728&&(l=!0,g.modify_stream=r[27],P(()=>l=!1)),!c&&u[0]&2&&(c=!0,g.set_time_limit=r[1],P(()=>c=!1)),t.$set(g)},i(r){h||(w(e.$$.fragment,r),w(t.$$.fragment,r),h=!0)},o(r){b(e.$$.fragment,r),b(t.$$.fragment,r),h=!1},d(r){r&&G(i),z(e,r),s[43](null),z(t,r)}}}function Ve(s){let e,i,t,a;const o=[{autoscroll:s[25].autoscroll},{i18n:s[25].i18n},s[2]];let m={};for(let _=0;_<o.length;_+=1)m=ie(m,o[_]);return e=new ne({props:m}),t=new qe({props:{value:s[0],label:s[6],show_label:s[7],show_download_button:s[8],selectable:s[13],show_share_button:s[17],i18n:s[25].i18n,show_fullscreen_button:s[24]}}),t.$on("select",s[38]),t.$on("share",s[39]),t.$on("error",s[40]),{c(){k(e.$$.fragment),i=ae(),k(t.$$.fragment)},m(_,l){I(e,_,l),A(_,i,l),I(t,_,l),a=!0},p(_,l){const c=l[0]&33554436?le(o,[l[0]&33554432&&{autoscroll:_[25].autoscroll},l[0]&33554432&&{i18n:_[25].i18n},l[0]&4&&re(_[2])]):{};e.$set(c);const h={};l[0]&1&&(h.value=_[0]),l[0]&64&&(h.label=_[6]),l[0]&128&&(h.show_label=_[7]),l[0]&256&&(h.show_download_button=_[8]),l[0]&8192&&(h.selectable=_[13]),l[0]&131072&&(h.show_share_button=_[17]),l[0]&33554432&&(h.i18n=_[25].i18n),l[0]&16777216&&(h.show_fullscreen_button=_[24]),t.$set(h)},i(_){a||(w(e.$$.fragment,_),w(t.$$.fragment,_),a=!0)},o(_){b(e.$$.fragment,_),b(t.$$.fragment,_),a=!1},d(_){_&&G(i),z(e,_),z(t,_)}}}function Xe(s){let e,i,t,a;const o=[Ge,Ae],m=[];function _(l,c){return l[19]?1:0}return e=_(s),i=m[e]=o[e](s),{c(){i.c(),t=x()},m(l,c){m[e].m(l,c),A(l,t,c),a=!0},p(l,c){let h=e;e=_(l),e===h?m[e].p(l,c):(ee(),b(m[h],1,1,()=>{m[h]=null}),te(),i=m[e],i?i.p(l,c):(i=m[e]=o[e](l),i.c()),w(i,1),i.m(t.parentNode,t))},i(l){a||(w(i),a=!0)},o(l){b(i),a=!1},d(l){l&&G(t),m[e].d(l)}}}function Ze(s,e,i){let t="closed",a=()=>{};function o(n){t=n,a(n)}const m=()=>t;let{set_time_limit:_}=e,{value_is_output:l=!1}=e,{elem_id:c=""}=e,{elem_classes:h=[]}=e,{visible:E=!0}=e,{value:p=null}=e,F=null,{label:O}=e,{show_label:W}=e,{show_download_button:j}=e,{root:q}=e,{height:C}=e,{width:v}=e,{stream_every:r}=e,{_selectable:u=!1}=e,{container:T=!0}=e,{scale:g=null}=e,{min_width:M=void 0}=e,{loading_status:S}=e,{show_share_button:Q=!1}=e,{sources:R=["upload","clipboard","webcam"]}=e,{interactive:H}=e,{streaming:V}=e,{pending:X}=e,{mirror_webcam:Z}=e,{placeholder:y=void 0}=e,{show_fullscreen_button:$}=e,{input_ready:K}=e,Y=!1,{gradio:d}=e;je(()=>{i(33,l=!1)});let U,L=null,J;const _e=n=>{const B=n;B.preventDefault(),B.stopPropagation(),B.type==="dragenter"||B.type==="dragover"?i(28,U=!0):B.type==="dragleave"&&i(28,U=!1)},ue=n=>{if(H){const B=n;B.preventDefault(),B.stopPropagation(),i(28,U=!1),J&&J.loadFilesFromDrop(B)}},fe=({detail:n})=>d.dispatch("select",n),me=({detail:n})=>d.dispatch("share",n),he=({detail:n})=>d.dispatch("error",n),ge=()=>d.dispatch("clear_status",S),ce=(...n)=>d.client.upload(...n);function de(n){D[n?"unshift":"push"](()=>{J=n,i(30,J)})}function be(n){Y=n,i(26,Y)}function we(n){L=n,i(29,L)}function pe(n){p=n,i(0,p)}function ve(n){U=n,i(28,U)}function ke(n){a=n,i(27,a)}function Ie(n){_=n,i(1,_)}const ze=()=>d.dispatch("edit"),Be=()=>{d.dispatch("clear")},Se=({detail:n})=>d.dispatch("stream",n),Ue=({detail:n})=>i(28,U=n),De=()=>d.dispatch("upload"),Ee=({detail:n})=>d.dispatch("select",n),Ne=({detail:n})=>d.dispatch("share",n),Pe=({detail:n})=>{i(2,S=S||{}),i(2,S.status="error",S),d.dispatch("error",n)},Fe=()=>{d.dispatch("close_stream","stream")};return s.$$set=n=>{"set_time_limit"in n&&i(1,_=n.set_time_limit),"value_is_output"in n&&i(33,l=n.value_is_output),"elem_id"in n&&i(3,c=n.elem_id),"elem_classes"in n&&i(4,h=n.elem_classes),"visible"in n&&i(5,E=n.visible),"value"in n&&i(0,p=n.value),"label"in n&&i(6,O=n.label),"show_label"in n&&i(7,W=n.show_label),"show_download_button"in n&&i(8,j=n.show_download_button),"root"in n&&i(9,q=n.root),"height"in n&&i(10,C=n.height),"width"in n&&i(11,v=n.width),"stream_every"in n&&i(12,r=n.stream_every),"_selectable"in n&&i(13,u=n._selectable),"container"in n&&i(14,T=n.container),"scale"in n&&i(15,g=n.scale),"min_width"in n&&i(16,M=n.min_width),"loading_status"in n&&i(2,S=n.loading_status),"show_share_button"in n&&i(17,Q=n.show_share_button),"sources"in n&&i(18,R=n.sources),"interactive"in n&&i(19,H=n.interactive),"streaming"in n&&i(20,V=n.streaming),"pending"in n&&i(21,X=n.pending),"mirror_webcam"in n&&i(22,Z=n.mirror_webcam),"placeholder"in n&&i(23,y=n.placeholder),"show_fullscreen_button"in n&&i(24,$=n.show_fullscreen_button),"input_ready"in n&&i(34,K=n.input_ready),"gradio"in n&&i(25,d=n.gradio)},s.$$.update=()=>{s.$$.dirty[0]&67108864&&i(34,K=!Y),s.$$.dirty[0]&33554433|s.$$.dirty[1]&68&&JSON.stringify(p)!==JSON.stringify(F)&&(i(37,F=p),d.dispatch("change"),l||d.dispatch("input"))},[p,_,S,c,h,E,O,W,j,q,C,v,r,u,T,g,M,Q,R,H,V,X,Z,y,$,d,Y,a,U,L,J,_e,ue,l,K,o,m,F,fe,me,he,ge,ce,de,be,we,pe,ve,ke,Ie,ze,Be,Se,Ue,De,Ee,Ne,Pe,Fe]}class zt extends Je{constructor(e){super(),Oe(this,e,Ze,Xe,We,{modify_stream_state:35,get_stream_state:36,set_time_limit:1,value_is_output:33,elem_id:3,elem_classes:4,visible:5,value:0,label:6,show_label:7,show_download_button:8,root:9,height:10,width:11,stream_every:12,_selectable:13,container:14,scale:15,min_width:16,loading_status:2,show_share_button:17,sources:18,interactive:19,streaming:20,pending:21,mirror_webcam:22,placeholder:23,show_fullscreen_button:24,input_ready:34,gradio:25},null,[-1,-1])}get modify_stream_state(){return this.$$.ctx[35]}get get_stream_state(){return this.$$.ctx[36]}get set_time_limit(){return this.$$.ctx[1]}set set_time_limit(e){this.$$set({set_time_limit:e}),f()}get value_is_output(){return this.$$.ctx[33]}set value_is_output(e){this.$$set({value_is_output:e}),f()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),f()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),f()}get show_download_button(){return this.$$.ctx[8]}set show_download_button(e){this.$$set({show_download_button:e}),f()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),f()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),f()}get width(){return this.$$.ctx[11]}set width(e){this.$$set({width:e}),f()}get stream_every(){return this.$$.ctx[12]}set stream_every(e){this.$$set({stream_every:e}),f()}get _selectable(){return this.$$.ctx[13]}set _selectable(e){this.$$set({_selectable:e}),f()}get container(){return this.$$.ctx[14]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[15]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[16]}set min_width(e){this.$$set({min_width:e}),f()}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),f()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),f()}get sources(){return this.$$.ctx[18]}set sources(e){this.$$set({sources:e}),f()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),f()}get streaming(){return this.$$.ctx[20]}set streaming(e){this.$$set({streaming:e}),f()}get pending(){return this.$$.ctx[21]}set pending(e){this.$$set({pending:e}),f()}get mirror_webcam(){return this.$$.ctx[22]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),f()}get placeholder(){return this.$$.ctx[23]}set placeholder(e){this.$$set({placeholder:e}),f()}get show_fullscreen_button(){return this.$$.ctx[24]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),f()}get input_ready(){return this.$$.ctx[34]}set input_ready(e){this.$$set({input_ready:e}),f()}get gradio(){return this.$$.ctx[25]}set gradio(e){this.$$set({gradio:e}),f()}}export{Pt as BaseExample,Et as BaseImage,Ce as BaseImageUploader,qe as BaseStaticImage,Ut as Webcam,zt as default};
//# sourceMappingURL=Index-Cy0g7pnb.js.map
