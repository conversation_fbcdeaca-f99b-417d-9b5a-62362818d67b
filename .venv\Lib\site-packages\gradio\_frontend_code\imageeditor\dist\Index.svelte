<svelte:options accessors={true} immutable={true} />

<script>import { BaseStaticImage as StaticImage } from "@gradio/image";
import InteractiveImageEditor from "./shared/InteractiveImageEditor.svelte";
import { Block } from "@gradio/atoms";
import { StatusTracker } from "@gradio/statustracker";
import { tick } from "svelte";
export let elem_id = "";
export let elem_classes = [];
export let visible = true;
export let value = {
  background: null,
  layers: [],
  composite: null
};
export let label;
export let show_label;
export let show_download_button;
export let root;
export let value_is_output = false;
export let height = 450;
export let width;
export let _selectable = false;
export let container = true;
export let scale = null;
export let min_width = void 0;
export let loading_status;
export let show_share_button = false;
export let sources = [
  "upload",
  "clipboard",
  "webcam"
];
export let interactive;
export let placeholder;
export let brush;
export let eraser;
export let crop_size = null;
export let transforms = ["crop"];
export let layers = true;
export let attached_events = [];
export let server;
export let canvas_size;
export let show_fullscreen_button = true;
export let gradio;
let editor_instance;
let image_id = null;
export async function get_value() {
  if (image_id) {
    const val = { id: image_id };
    image_id = null;
    return val;
  }
  const blobs = await editor_instance.get_data();
  return blobs;
}
let dragging;
$:
  value && handle_change();
const is_browser = typeof window !== "undefined";
const raf = is_browser ? window.requestAnimationFrame : (cb) => cb();
function wait_for_next_frame() {
  return new Promise((resolve) => {
    raf(() => raf(() => resolve()));
  });
}
async function handle_change() {
  await wait_for_next_frame();
  if (value && (value.background || value.layers?.length || value.composite)) {
    gradio.dispatch("change");
  }
}
function handle_save() {
  gradio.dispatch("apply");
}
function handle_history_change() {
  gradio.dispatch("change");
  if (!value_is_output) {
    gradio.dispatch("input");
    tick().then((_) => value_is_output = false);
  }
}
$:
  has_value = value?.background || value?.layers?.length || value?.composite;
</script>

{#if !interactive}
	<Block
		{visible}
		variant={"solid"}
		border_mode={dragging ? "focus" : "base"}
		padding={false}
		{elem_id}
		{elem_classes}
		height={height || undefined}
		{width}
		allow_overflow={false}
		{container}
		{scale}
		{min_width}
	>
		<StatusTracker
			autoscroll={gradio.autoscroll}
			i18n={gradio.i18n}
			{...loading_status}
			on:clear_status={() => gradio.dispatch("clear_status", loading_status)}
		/>
		<StaticImage
			on:select={({ detail }) => gradio.dispatch("select", detail)}
			on:share={({ detail }) => gradio.dispatch("share", detail)}
			on:error={({ detail }) => gradio.dispatch("error", detail)}
			value={value?.composite || null}
			{label}
			{show_label}
			{show_download_button}
			selectable={_selectable}
			{show_share_button}
			i18n={gradio.i18n}
			{show_fullscreen_button}
		/>
	</Block>
{:else}
	<Block
		{visible}
		variant={has_value ? "solid" : "dashed"}
		border_mode={dragging ? "focus" : "base"}
		padding={false}
		{elem_id}
		{elem_classes}
		height={height || undefined}
		{width}
		allow_overflow={false}
		{container}
		{scale}
		{min_width}
	>
		<StatusTracker
			autoscroll={gradio.autoscroll}
			i18n={gradio.i18n}
			{...loading_status}
			on:clear_status={() => gradio.dispatch("clear_status", loading_status)}
		/>

		<InteractiveImageEditor
			bind:dragging
			{canvas_size}
			on:change={() => handle_history_change()}
			bind:image_id
			{crop_size}
			{value}
			bind:this={editor_instance}
			{root}
			{sources}
			{label}
			{show_label}
			{height}
			on:save={(e) => handle_save()}
			on:edit={() => gradio.dispatch("edit")}
			on:clear={() => gradio.dispatch("clear")}
			on:drag={({ detail }) => (dragging = detail)}
			on:upload={() => gradio.dispatch("upload")}
			on:share={({ detail }) => gradio.dispatch("share", detail)}
			on:error={({ detail }) => {
				loading_status = loading_status || {};
				loading_status.status = "error";
				gradio.dispatch("error", detail);
			}}
			on:error
			{brush}
			{eraser}
			changeable={attached_events.includes("apply")}
			realtime={attached_events.includes("change")}
			i18n={gradio.i18n}
			{transforms}
			accept_blobs={server.accept_blobs}
			{layers}
			status={loading_status?.status}
			upload={(...args) => gradio.client.upload(...args)}
			stream_handler={(...args) => gradio.client.stream(...args)}
			{placeholder}
		></InteractiveImageEditor>
	</Block>
{/if}
