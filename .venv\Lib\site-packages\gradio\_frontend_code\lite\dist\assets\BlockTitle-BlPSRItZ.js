import{a as k,i as p,s as w,f as m,e as $,p as j,x as q,E as B,q as g,r as u,l as h,u as I,h as z,j as C,t as _,y as E,b as c,z as N,o as b,c as S,m as T,d as v}from"../lite.js";import{I as A}from"./Info-2h-pcw07.js";function d(l){let e,i;return e=new A({props:{root:l[2],info:l[1]}}),{c(){S(e.$$.fragment)},m(o,n){T(e,o,n),i=!0},p(o,n){const r={};n&4&&(r.root=o[2]),n&2&&(r.info=o[1]),e.$set(r)},i(o){i||(_(e.$$.fragment,o),i=!0)},o(o){c(e.$$.fragment,o),i=!1},d(o){v(e,o)}}}function D(l){let e,i,o,n;const r=l[4].default,f=$(r,l,l[3],null);let t=l[1]&&d(l);return{c(){e=j("span"),f&&f.c(),i=q(),t&&t.c(),o=B(),g(e,"data-testid","block-info"),g(e,"class","svelte-1gfkn6j"),u(e,"sr-only",!l[0]),u(e,"hide",!l[0]),u(e,"has-info",l[1]!=null)},m(s,a){h(s,e,a),f&&f.m(e,null),h(s,i,a),t&&t.m(s,a),h(s,o,a),n=!0},p(s,[a]){f&&f.p&&(!n||a&8)&&I(f,r,s,s[3],n?C(r,s[3],a,null):z(s[3]),null),(!n||a&1)&&u(e,"sr-only",!s[0]),(!n||a&1)&&u(e,"hide",!s[0]),(!n||a&2)&&u(e,"has-info",s[1]!=null),s[1]?t?(t.p(s,a),a&2&&_(t,1)):(t=d(s),t.c(),_(t,1),t.m(o.parentNode,o)):t&&(E(),c(t,1,1,()=>{t=null}),N())},i(s){n||(_(f,s),_(t),n=!0)},o(s){c(f,s),c(t),n=!1},d(s){s&&(b(e),b(i),b(o)),f&&f.d(s),t&&t.d(s)}}}function F(l,e,i){let{$$slots:o={},$$scope:n}=e,{show_label:r=!0}=e,{info:f=void 0}=e,{root:t}=e;return l.$$set=s=>{"show_label"in s&&i(0,r=s.show_label),"info"in s&&i(1,f=s.info),"root"in s&&i(2,t=s.root),"$$scope"in s&&i(3,n=s.$$scope)},[r,f,t,n,o]}class J extends k{constructor(e){super(),p(this,e,F,D,w,{show_label:0,info:1,root:2})}get show_label(){return this.$$.ctx[0]}set show_label(e){this.$$set({show_label:e}),m()}get info(){return this.$$.ctx[1]}set info(e){this.$$set({info:e}),m()}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),m()}}export{J as B};
//# sourceMappingURL=BlockTitle-BlPSRItZ.js.map
