import{a as p,i as h,s as v,f as b,Y as _,p as I,_ as u,r as m,l as j,F as q,a0 as y,ap as x,w as g,o as C,X as f,Z as F,H}from"../lite.js";import{r as P}from"./file-url-Co2ROWca.js";function S(t){let s,r,o,n,a=[{src:r=t[0]},t[1]],l={};for(let e=0;e<a.length;e+=1)l=_(l,a[e]);return{c(){s=I("img"),u(s,l),m(s,"svelte-1pijsyv",!0)},m(e,i){j(e,s,i),o||(n=q(s,"load",t[4]),o=!0)},p(e,[i]){u(s,l=y(a,[i&1&&!x(s.src,r=e[0])&&{src:r},i&2&&e[1]])),m(s,"svelte-1pijsyv",!0)},i:g,o:g,d(e){e&&C(s),o=!1,n()}}}function X(t,s,r){const o=["src"];let n=f(s,o),{src:a=void 0}=s,l,e;function i(c){H.call(this,t,c)}return t.$$set=c=>{s=_(_({},s),F(c)),r(1,n=f(s,o)),"src"in c&&r(2,a=c.src)},t.$$.update=()=>{if(t.$$.dirty&12){r(0,l=a),r(3,e=a);const c=a;P(c).then(d=>{e===c&&r(0,l=d)})}},[l,n,a,e,i]}class Y extends p{constructor(s){super(),h(this,s,X,S,v,{src:2})}get src(){return this.$$.ctx[2]}set src(s){this.$$set({src:s}),b()}}const w=Y;export{w as I};
//# sourceMappingURL=Image-Bd-jnd8M.js.map
