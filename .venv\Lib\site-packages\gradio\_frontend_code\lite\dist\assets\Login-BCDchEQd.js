import{a as G,i as J,s as K,f as L,p as B,c as v,q as I,r as S,l as c,m as h,t as k,b as w,o as $,d as x,am as Q,an as R,k as N,x as T,v as D,n as q,B as z,I as P,a5 as j,a6 as A}from"../lite.js";import U from"./Index-zXxI_7EJ.js";import{T as E}from"./Textbox-GhQQJgUa.js";/* empty css                                              */import{B as V}from"./Button-kP2IUsdy.js";import W from"./Index-DdV7UDNf.js";import"./BlockTitle-BlPSRItZ.js";import"./Info-2h-pcw07.js";import"./MarkdownCode-C6RHM0m6.js";import"./Check-ChD9RrF6.js";import"./Copy-DOOc0VFX.js";import"./Send-DY8minAZ.js";import"./Square-7hK0fZD1.js";import"./index-DCygRGWm.js";/* empty css                                              */function C(a){let e;return{c(){e=B("p"),I(e,"class","auth svelte-1ogxbi0")},m(t,s){c(t,e,s),e.innerHTML=a[1]},p(t,s){s&2&&(e.innerHTML=t[1])},d(t){t&&$(e)}}}function F(a){let e,t=a[7]("login.enable_cookies")+"",s;return{c(){e=B("p"),s=N(t),I(e,"class","auth svelte-1ogxbi0")},m(l,o){c(l,e,o),D(e,s)},p(l,o){o&128&&t!==(t=l[7]("login.enable_cookies")+"")&&q(s,t)},d(l){l&&$(e)}}}function O(a){let e,t=a[7]("login.incorrect_credentials")+"",s;return{c(){e=B("p"),s=N(t),I(e,"class","creds svelte-1ogxbi0")},m(l,o){c(l,e,o),D(e,s)},p(l,o){o&128&&t!==(t=l[7]("login.incorrect_credentials")+"")&&q(s,t)},d(l){l&&$(e)}}}function X(a){let e,t,s;function l(n){a[9](n)}let o={root:a[0],label:"username",lines:1,show_label:!0,max_lines:1};return a[4]!==void 0&&(o.value=a[4]),e=new E({props:o}),P.push(()=>j(e,"value",l)),e.$on("submit",a[8]),{c(){v(e.$$.fragment)},m(n,u){h(e,n,u),s=!0},p(n,u){const f={};u&1&&(f.root=n[0]),!t&&u&16&&(t=!0,f.value=n[4],A(()=>t=!1)),e.$set(f)},i(n){s||(k(e.$$.fragment,n),s=!0)},o(n){w(e.$$.fragment,n),s=!1},d(n){x(e,n)}}}function Y(a){let e,t,s;function l(n){a[10](n)}let o={root:a[0],label:"password",lines:1,show_label:!0,max_lines:1,type:"password"};return a[5]!==void 0&&(o.value=a[5]),e=new E({props:o}),P.push(()=>j(e,"value",l)),e.$on("submit",a[8]),{c(){v(e.$$.fragment)},m(n,u){h(e,n,u),s=!0},p(n,u){const f={};u&1&&(f.root=n[0]),!t&&u&32&&(t=!0,f.value=n[5],A(()=>t=!1)),e.$set(f)},i(n){s||(k(e.$$.fragment,n),s=!0)},o(n){w(e.$$.fragment,n),s=!1},d(n){x(e,n)}}}function Z(a){let e,t,s,l;return e=new z({props:{$$slots:{default:[X]},$$scope:{ctx:a}}}),s=new z({props:{$$slots:{default:[Y]},$$scope:{ctx:a}}}),{c(){v(e.$$.fragment),t=T(),v(s.$$.fragment)},m(o,n){h(e,o,n),c(o,t,n),h(s,o,n),l=!0},p(o,n){const u={};n&2065&&(u.$$scope={dirty:n,ctx:o}),e.$set(u);const f={};n&2081&&(f.$$scope={dirty:n,ctx:o}),s.$set(f)},i(o){l||(k(e.$$.fragment,o),k(s.$$.fragment,o),l=!0)},o(o){w(e.$$.fragment,o),w(s.$$.fragment,o),l=!1},d(o){o&&$(t),x(e,o),x(s,o)}}}function y(a){let e=a[7]("login.login")+"",t;return{c(){t=N(e)},m(s,l){c(s,t,l)},p(s,l){l&128&&e!==(e=s[7]("login.login")+"")&&q(t,e)},d(s){s&&$(t)}}}function ee(a){let e,t=a[7]("login.login")+"",s,l,o,n,u,f,g,b,d,_=a[1]&&C(a),p=a[3]&&F(a),r=a[6]&&O(a);return f=new U({props:{$$slots:{default:[Z]},$$scope:{ctx:a}}}),b=new V({props:{size:"lg",variant:"primary",$$slots:{default:[y]},$$scope:{ctx:a}}}),b.$on("click",a[8]),{c(){e=B("h2"),s=N(t),l=T(),_&&_.c(),o=T(),p&&p.c(),n=T(),r&&r.c(),u=T(),v(f.$$.fragment),g=T(),v(b.$$.fragment),I(e,"class","svelte-1ogxbi0")},m(i,m){c(i,e,m),D(e,s),c(i,l,m),_&&_.m(i,m),c(i,o,m),p&&p.m(i,m),c(i,n,m),r&&r.m(i,m),c(i,u,m),h(f,i,m),c(i,g,m),h(b,i,m),d=!0},p(i,m){(!d||m&128)&&t!==(t=i[7]("login.login")+"")&&q(s,t),i[1]?_?_.p(i,m):(_=C(i),_.c(),_.m(o.parentNode,o)):_&&(_.d(1),_=null),i[3]?p?p.p(i,m):(p=F(i),p.c(),p.m(n.parentNode,n)):p&&(p.d(1),p=null),i[6]?r?r.p(i,m):(r=O(i),r.c(),r.m(u.parentNode,u)):r&&(r.d(1),r=null);const H={};m&2097&&(H.$$scope={dirty:m,ctx:i}),f.$set(H);const M={};m&2176&&(M.$$scope={dirty:m,ctx:i}),b.$set(M)},i(i){d||(k(f.$$.fragment,i),k(b.$$.fragment,i),d=!0)},o(i){w(f.$$.fragment,i),w(b.$$.fragment,i),d=!1},d(i){i&&($(e),$(l),$(o),$(n),$(u),$(g)),_&&_.d(i),p&&p.d(i),r&&r.d(i),x(f,i),x(b,i)}}}function te(a){let e,t,s;return t=new W({props:{variant:"panel",min_width:480,$$slots:{default:[ee]},$$scope:{ctx:a}}}),{c(){e=B("div"),v(t.$$.fragment),I(e,"class","wrap svelte-1ogxbi0"),S(e,"min-h-screen",a[2])},m(l,o){c(l,e,o),h(t,e,null),s=!0},p(l,[o]){const n={};o&2299&&(n.$$scope={dirty:o,ctx:l}),t.$set(n),(!s||o&4)&&S(e,"min-h-screen",l[2])},i(l){s||(k(t.$$.fragment,l),s=!0)},o(l){w(t.$$.fragment,l),s=!1},d(l){l&&$(e),x(t)}}}function se(a,e,t){let s;Q(a,R,r=>t(7,s=r));let{root:l}=e,{auth_message:o}=e,{app_mode:n}=e,{space_id:u}=e,f="",g="",b=!1;const d=async()=>{const r=new FormData;r.append("username",f),r.append("password",g);let i=await fetch(l+"/login",{method:"POST",body:r});i.status===400?(t(6,b=!0),t(4,f=""),t(5,g="")):i.status==200&&location.reload()};function _(r){f=r,t(4,f)}function p(r){g=r,t(5,g)}return a.$$set=r=>{"root"in r&&t(0,l=r.root),"auth_message"in r&&t(1,o=r.auth_message),"app_mode"in r&&t(2,n=r.app_mode),"space_id"in r&&t(3,u=r.space_id)},[l,o,n,u,f,g,b,s,d,_,p]}class de extends G{constructor(e){super(),J(this,e,se,te,K,{root:0,auth_message:1,app_mode:2,space_id:3})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),L()}get auth_message(){return this.$$.ctx[1]}set auth_message(e){this.$$set({auth_message:e}),L()}get app_mode(){return this.$$.ctx[2]}set app_mode(e){this.$$set({app_mode:e}),L()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),L()}}export{de as default};
//# sourceMappingURL=Login-BCDchEQd.js.map
