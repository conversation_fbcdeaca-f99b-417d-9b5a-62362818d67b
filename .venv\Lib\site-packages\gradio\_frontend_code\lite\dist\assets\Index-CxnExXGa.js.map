{"version": 3, "file": "Index-CxnExXGa.js", "sources": ["../../../icons/src/Remove.svelte", "../../../dropdown/shared/DropdownOptions.svelte", "../../../dropdown/shared/utils.ts", "../../../dropdown/shared/Multiselect.svelte", "../../../dropdown/shared/Dropdown.svelte", "../../../dropdown/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n>\n\t<path\n\t\td=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { fly } from \"svelte/transition\";\n\timport { createEventDispatcher } from \"svelte\";\n\texport let choices: [string, string | number][];\n\texport let filtered_indices: number[];\n\texport let show_options = false;\n\texport let disabled = false;\n\texport let selected_indices: (string | number)[] = [];\n\texport let active_index: number | null = null;\n\n\tlet distance_from_top: number;\n\tlet distance_from_bottom: number;\n\tlet input_height: number;\n\tlet input_width: number;\n\tlet refElement: HTMLDivElement;\n\tlet listElement: HTMLUListElement;\n\tlet top: string | null, bottom: string | null, max_height: number;\n\tlet innerHeight: number;\n\n\tfunction calculate_window_distance(): void {\n\t\tconst { top: ref_top, bottom: ref_bottom } =\n\t\t\trefElement.getBoundingClientRect();\n\t\tdistance_from_top = ref_top;\n\t\tdistance_from_bottom = innerHeight - ref_bottom;\n\t}\n\n\tlet scroll_timeout: NodeJS.Timeout | null = null;\n\tfunction scroll_listener(): void {\n\t\tif (!show_options) return;\n\t\tif (scroll_timeout !== null) {\n\t\t\tclearTimeout(scroll_timeout);\n\t\t}\n\n\t\tscroll_timeout = setTimeout(() => {\n\t\t\tcalculate_window_distance();\n\t\t\tscroll_timeout = null;\n\t\t}, 10);\n\t}\n\n\t$: {\n\t\tif (show_options && refElement) {\n\t\t\tif (listElement && selected_indices.length > 0) {\n\t\t\t\tlet elements = listElement.querySelectorAll(\"li\");\n\t\t\t\tfor (const element of Array.from(elements)) {\n\t\t\t\t\tif (\n\t\t\t\t\t\telement.getAttribute(\"data-index\") ===\n\t\t\t\t\t\tselected_indices[0].toString()\n\t\t\t\t\t) {\n\t\t\t\t\t\tlistElement?.scrollTo?.(0, (element as HTMLLIElement).offsetTop);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcalculate_window_distance();\n\t\t\tconst rect = refElement.parentElement?.getBoundingClientRect();\n\t\t\tinput_height = rect?.height || 0;\n\t\t\tinput_width = rect?.width || 0;\n\t\t}\n\t\tif (distance_from_bottom > distance_from_top) {\n\t\t\ttop = `${distance_from_top}px`;\n\t\t\tmax_height = distance_from_bottom;\n\t\t\tbottom = null;\n\t\t} else {\n\t\t\tbottom = `${distance_from_bottom + input_height}px`;\n\t\t\tmax_height = distance_from_top - input_height;\n\t\t\ttop = null;\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<svelte:window on:scroll={scroll_listener} bind:innerHeight />\n\n<div class=\"reference\" bind:this={refElement} />\n{#if show_options && !disabled}\n\t<ul\n\t\tclass=\"options\"\n\t\ttransition:fly={{ duration: 200, y: 5 }}\n\t\ton:mousedown|preventDefault={(e) => dispatch(\"change\", e)}\n\t\tstyle:top\n\t\tstyle:bottom\n\t\tstyle:max-height={`calc(${max_height}px - var(--window-padding))`}\n\t\tstyle:width={input_width + \"px\"}\n\t\tbind:this={listElement}\n\t\trole=\"listbox\"\n\t>\n\t\t{#each filtered_indices as index}\n\t\t\t<li\n\t\t\t\tclass=\"item\"\n\t\t\t\tclass:selected={selected_indices.includes(index)}\n\t\t\t\tclass:active={index === active_index}\n\t\t\t\tclass:bg-gray-100={index === active_index}\n\t\t\t\tclass:dark:bg-gray-600={index === active_index}\n\t\t\t\tstyle:width={input_width + \"px\"}\n\t\t\t\tdata-index={index}\n\t\t\t\taria-label={choices[index][0]}\n\t\t\t\tdata-testid=\"dropdown-option\"\n\t\t\t\trole=\"option\"\n\t\t\t\taria-selected={selected_indices.includes(index)}\n\t\t\t>\n\t\t\t\t<span class:hide={!selected_indices.includes(index)} class=\"inner-item\">\n\t\t\t\t\t✓\n\t\t\t\t</span>\n\t\t\t\t{choices[index][0]}\n\t\t\t</li>\n\t\t{/each}\n\t</ul>\n{/if}\n\n<style>\n\t.options {\n\t\t--window-padding: var(--size-8);\n\t\tposition: fixed;\n\t\tz-index: var(--layer-top);\n\t\tmargin-left: 0;\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--background-fill-primary);\n\t\tmin-width: fit-content;\n\t\tmax-width: inherit;\n\t\toverflow: auto;\n\t\tcolor: var(--body-text-color);\n\t\tlist-style: none;\n\t}\n\n\t.item {\n\t\tdisplay: flex;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t\tword-break: break-word;\n\t}\n\n\t.item:hover,\n\t.active {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.inner-item {\n\t\tpadding-right: var(--size-1);\n\t}\n\n\t.hide {\n\t\tvisibility: hidden;\n\t}\n</style>\n", "function positive_mod(n: number, m: number): number {\n\treturn ((n % m) + m) % m;\n}\n\nexport function handle_filter(\n\tchoices: [string, string | number][],\n\tinput_text: string\n): number[] {\n\treturn choices.reduce((filtered_indices, o, index) => {\n\t\tif (\n\t\t\tinput_text ? o[0].toLowerCase().includes(input_text.toLowerCase()) : true\n\t\t) {\n\t\t\tfiltered_indices.push(index);\n\t\t}\n\t\treturn filtered_indices;\n\t}, [] as number[]);\n}\n\nexport function handle_change(\n\tdispatch: any,\n\tvalue: string | number | (string | number)[] | undefined,\n\tvalue_is_output: boolean\n): void {\n\tdispatch(\"change\", value);\n\tif (!value_is_output) {\n\t\tdispatch(\"input\");\n\t}\n}\n\nexport function handle_shared_keys(\n\te: KeyboardEvent,\n\tactive_index: number | null,\n\tfiltered_indices: number[]\n): [boolean, number | null] {\n\tif (e.key === \"Escape\") {\n\t\treturn [false, active_index];\n\t}\n\tif (e.key === \"ArrowDown\" || e.key === \"ArrowUp\") {\n\t\tif (filtered_indices.length >= 0) {\n\t\t\tif (active_index === null) {\n\t\t\t\tactive_index =\n\t\t\t\t\te.key === \"ArrowDown\"\n\t\t\t\t\t\t? filtered_indices[0]\n\t\t\t\t\t\t: filtered_indices[filtered_indices.length - 1];\n\t\t\t} else {\n\t\t\t\tconst index_in_filtered = filtered_indices.indexOf(active_index);\n\t\t\t\tconst increment = e.key === \"ArrowUp\" ? -1 : 1;\n\t\t\t\tactive_index =\n\t\t\t\t\tfiltered_indices[\n\t\t\t\t\t\tpositive_mod(index_in_filtered + increment, filtered_indices.length)\n\t\t\t\t\t];\n\t\t\t}\n\t\t}\n\t}\n\treturn [true, active_index];\n}\n", "<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Remove, DropdownArrow } from \"@gradio/icons\";\n\timport type { KeyUp<PERSON><PERSON>, SelectData, I18nFormatter } from \"@gradio/utils\";\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { handle_filter, handle_change, handle_shared_keys } from \"./utils\";\n\n\ttype Item = string | number;\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: Item | Item[] | undefined = [];\n\tlet old_value: typeof value = [];\n\texport let value_is_output = false;\n\texport let max_choices: number | null = null;\n\texport let choices: [string, Item][];\n\tlet old_choices: typeof choices;\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\texport let filterable = true;\n\texport let i18n: I18nFormatter;\n\texport let root: string;\n\n\tlet filter_input: HTMLElement;\n\tlet input_text = \"\";\n\tlet old_input_text = \"\";\n\tlet show_options = false;\n\tlet choices_names: string[];\n\tlet choices_values: (string | number)[];\n\n\t// All of these are indices with respect to the choices array\n\tlet filtered_indices: number[] = [];\n\tlet active_index: number | null = null;\n\t// selected_index consists of indices from choices or strings if allow_custom_value is true and user types in a custom value\n\tlet selected_indices: (number | string)[] = [];\n\tlet old_selected_index: (number | string)[] = [];\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | string[] | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t\tkey_up: KeyUpData;\n\t}>();\n\n\t// Setting the initial value of the multiselect dropdown\n\tif (Array.isArray(value)) {\n\t\tvalue.forEach((element) => {\n\t\t\tconst index = choices.map((c) => c[1]).indexOf(element);\n\t\t\tif (index !== -1) {\n\t\t\t\tselected_indices.push(index);\n\t\t\t} else {\n\t\t\t\tselected_indices.push(element);\n\t\t\t}\n\t\t});\n\t}\n\n\t$: {\n\t\tchoices_names = choices.map((c) => c[0]);\n\t\tchoices_values = choices.map((c) => c[1]);\n\t}\n\n\t$: {\n\t\tif (choices !== old_choices || input_text !== old_input_text) {\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\told_choices = choices;\n\t\t\told_input_text = input_text;\n\t\t\tif (!allow_custom_value) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t}\n\t}\n\n\t$: {\n\t\tif (JSON.stringify(value) != JSON.stringify(old_value)) {\n\t\t\thandle_change(dispatch, value, value_is_output);\n\t\t\told_value = Array.isArray(value) ? value.slice() : value;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (\n\t\t\tJSON.stringify(selected_indices) != JSON.stringify(old_selected_index)\n\t\t) {\n\t\t\tvalue = selected_indices.map((index) =>\n\t\t\t\ttypeof index === \"number\" ? choices_values[index] : index\n\t\t\t);\n\t\t\told_selected_index = selected_indices.slice();\n\t\t}\n\t}\n\n\tfunction handle_blur(): void {\n\t\tif (!allow_custom_value) {\n\t\t\tinput_text = \"\";\n\t\t}\n\n\t\tif (allow_custom_value && input_text !== \"\") {\n\t\t\tadd_selected_choice(input_text);\n\t\t\tinput_text = \"\";\n\t\t}\n\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction remove_selected_choice(option_index: number | string): void {\n\t\tselected_indices = selected_indices.filter((v) => v !== option_index);\n\t\tdispatch(\"select\", {\n\t\t\tindex: typeof option_index === \"number\" ? option_index : -1,\n\t\t\tvalue:\n\t\t\t\ttypeof option_index === \"number\"\n\t\t\t\t\t? choices_values[option_index]\n\t\t\t\t\t: option_index,\n\t\t\tselected: false\n\t\t});\n\t}\n\n\tfunction add_selected_choice(option_index: number | string): void {\n\t\tif (max_choices === null || selected_indices.length < max_choices) {\n\t\t\tselected_indices = [...selected_indices, option_index];\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: typeof option_index === \"number\" ? option_index : -1,\n\t\t\t\tvalue:\n\t\t\t\t\ttypeof option_index === \"number\"\n\t\t\t\t\t\t? choices_values[option_index]\n\t\t\t\t\t\t: option_index,\n\t\t\t\tselected: true\n\t\t\t});\n\t\t}\n\t\tif (selected_indices.length === max_choices) {\n\t\t\tshow_options = false;\n\t\t\tactive_index = null;\n\t\t\tfilter_input.blur();\n\t\t}\n\t}\n\n\tfunction handle_option_selected(e: any): void {\n\t\tconst option_index = parseInt(e.detail.target.dataset.index);\n\t\tadd_or_remove_index(option_index);\n\t}\n\n\tfunction add_or_remove_index(option_index: number): void {\n\t\tif (selected_indices.includes(option_index)) {\n\t\t\tremove_selected_choice(option_index);\n\t\t} else {\n\t\t\tadd_selected_choice(option_index);\n\t\t}\n\t\tinput_text = \"\";\n\t}\n\n\tfunction remove_all(e: any): void {\n\t\tselected_indices = [];\n\t\tinput_text = \"\";\n\t\te.preventDefault();\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tfiltered_indices = choices.map((_, i) => i);\n\t\tif (max_choices === null || selected_indices.length < max_choices) {\n\t\t\tshow_options = true;\n\t\t}\n\t\tdispatch(\"focus\");\n\t}\n\n\tfunction handle_key_down(e: KeyboardEvent): void {\n\t\t[show_options, active_index] = handle_shared_keys(\n\t\t\te,\n\t\t\tactive_index,\n\t\t\tfiltered_indices\n\t\t);\n\t\tif (e.key === \"Enter\") {\n\t\t\tif (active_index !== null) {\n\t\t\t\tadd_or_remove_index(active_index);\n\t\t\t} else {\n\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\tadd_selected_choice(input_text);\n\t\t\t\t\tinput_text = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (e.key === \"Backspace\" && input_text === \"\") {\n\t\t\tselected_indices = [...selected_indices.slice(0, -1)];\n\t\t}\n\t\tif (selected_indices.length === max_choices) {\n\t\t\tshow_options = false;\n\t\t\tactive_index = null;\n\t\t}\n\t}\n\n\tfunction set_selected_indices(): void {\n\t\tif (value === undefined) {\n\t\t\tselected_indices = [];\n\t\t} else if (Array.isArray(value)) {\n\t\t\tselected_indices = value\n\t\t\t\t.map((v) => {\n\t\t\t\t\tconst index = choices_values.indexOf(v);\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\treturn index;\n\t\t\t\t\t}\n\t\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\t\treturn v;\n\t\t\t\t\t}\n\t\t\t\t\t// Instead of returning null, skip this iteration\n\t\t\t\t\treturn undefined;\n\t\t\t\t})\n\t\t\t\t.filter((val): val is string | number => val !== undefined);\n\t\t}\n\t}\n\n\t$: value, set_selected_indices();\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n</script>\n\n<label class:container>\n\t<BlockTitle {root} {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:show_options>\n\t\t\t{#each selected_indices as s}\n\t\t\t\t<div class=\"token\">\n\t\t\t\t\t<span>\n\t\t\t\t\t\t{#if typeof s === \"number\"}\n\t\t\t\t\t\t\t{choices_names[s]}\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{s}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</span>\n\t\t\t\t\t{#if !disabled}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tclass=\"token-remove\"\n\t\t\t\t\t\t\ton:click|preventDefault={() => remove_selected_choice(s)}\n\t\t\t\t\t\t\ton:keydown|preventDefault={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremove_selected_choice(s);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ttitle={i18n(\"common.remove\") + \" \" + s}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\t\t\t{/each}\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={(!choices_names.includes(input_text) &&\n\t\t\t\t\t\t!allow_custom_value) ||\n\t\t\t\t\t\tselected_indices.length === max_choices}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={input_text}\n\t\t\t\t\tbind:this={filter_input}\n\t\t\t\t\ton:keydown={handle_key_down}\n\t\t\t\t\ton:keyup={(e) =>\n\t\t\t\t\t\tdispatch(\"key_up\", {\n\t\t\t\t\t\t\tkey: e.key,\n\t\t\t\t\t\t\tinput_value: input_text\n\t\t\t\t\t\t})}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t\treadonly={!filterable}\n\t\t\t\t/>\n\n\t\t\t\t{#if !disabled}\n\t\t\t\t\t{#if selected_indices.length > 0}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\tclass=\"token-remove remove-all\"\n\t\t\t\t\t\t\ttitle={i18n(\"common.clear\")}\n\t\t\t\t\t\t\ton:click={remove_all}\n\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremove_all(event);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<span class=\"icon-wrap\"> <DropdownArrow /></span>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\t{show_options}\n\t\t\t{choices}\n\t\t\t{filtered_indices}\n\t\t\t{disabled}\n\t\t\t{selected_indices}\n\t\t\t{active_index}\n\t\t\ton:change={handle_option_selected}\n\t\t/>\n\t</div>\n</label>\n\n<style>\n\t.icon-wrap {\n\t\tcolor: var(--body-text-color);\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-5);\n\t}\n\tlabel:not(.container),\n\tlabel:not(.container) .wrap,\n\tlabel:not(.container) .wrap-inner,\n\tlabel:not(.container) .secondary-wrap,\n\tlabel:not(.container) .token,\n\tlabel:not(.container) input {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t}\n\n\t.token {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t\tword-break: break-word;\n\t}\n\n\t.token > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.token-remove {\n\t\tfill: var(--body-text-color);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tborder: var(--checkbox-border-width) solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-0-5);\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\tflex-shrink: 0;\n\t}\n\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.remove-all {\n\t\tmargin-left: var(--size-1);\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\tinput[readonly] {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { createEventDispatcher, afterUpdate } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { DropdownArrow } from \"@gradio/icons\";\n\timport type { SelectData, KeyUpData } from \"@gradio/utils\";\n\timport { handle_filter, handle_change, handle_shared_keys } from \"./utils\";\n\n\ttype Item = string | number;\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: Item | Item[] | undefined = undefined;\n\tlet old_value: typeof value = undefined;\n\texport let value_is_output = false;\n\texport let choices: [string, Item][];\n\tlet old_choices: typeof choices;\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\texport let filterable = true;\n\texport let root: string;\n\n\tlet filter_input: HTMLElement;\n\n\tlet show_options = false;\n\tlet choices_names: string[];\n\tlet choices_values: (string | number)[];\n\tlet input_text = \"\";\n\tlet old_input_text = \"\";\n\tlet initialized = false;\n\n\t// All of these are indices with respect to the choices array\n\tlet filtered_indices: number[] = [];\n\tlet active_index: number | null = null;\n\t// selected_index is null if allow_custom_value is true and the input_text is not in choices_names\n\tlet selected_index: number | null = null;\n\tlet old_selected_index: number | null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t\tkey_up: KeyUpData;\n\t}>();\n\n\t// Setting the initial value of the dropdown\n\tif (value) {\n\t\told_selected_index = choices.map((c) => c[1]).indexOf(value as string);\n\t\tselected_index = old_selected_index;\n\t\tif (selected_index === -1) {\n\t\t\told_value = value;\n\t\t\tselected_index = null;\n\t\t} else {\n\t\t\t[input_text, old_value] = choices[selected_index];\n\t\t\told_input_text = input_text;\n\t\t}\n\t\tset_input_text();\n\t}\n\n\t$: {\n\t\tif (\n\t\t\tselected_index !== old_selected_index &&\n\t\t\tselected_index !== null &&\n\t\t\tinitialized\n\t\t) {\n\t\t\t[input_text, value] = choices[selected_index];\n\t\t\told_selected_index = selected_index;\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: selected_index,\n\t\t\t\tvalue: choices_values[selected_index],\n\t\t\t\tselected: true\n\t\t\t});\n\t\t}\n\t}\n\n\t$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\n\t\tset_input_text();\n\t\thandle_change(dispatch, value, value_is_output);\n\t\told_value = value;\n\t}\n\n\tfunction set_choice_names_values(): void {\n\t\tchoices_names = choices.map((c) => c[0]);\n\t\tchoices_values = choices.map((c) => c[1]);\n\t}\n\n\t$: choices, set_choice_names_values();\n\n\tconst is_browser = typeof window !== \"undefined\";\n\n\t$: {\n\t\tif (choices !== old_choices) {\n\t\t\tif (!allow_custom_value) {\n\t\t\t\tset_input_text();\n\t\t\t}\n\t\t\told_choices = choices;\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\tif (!allow_custom_value && filtered_indices.length > 0) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t\tif (is_browser && filter_input === document.activeElement) {\n\t\t\t\tshow_options = true;\n\t\t\t}\n\t\t}\n\t}\n\n\t$: {\n\t\tif (input_text !== old_input_text) {\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\told_input_text = input_text;\n\t\t\tif (!allow_custom_value && filtered_indices.length > 0) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction set_input_text(): void {\n\t\tset_choice_names_values();\n\t\tif (value === undefined || (Array.isArray(value) && value.length === 0)) {\n\t\t\tinput_text = \"\";\n\t\t\tselected_index = null;\n\t\t} else if (choices_values.includes(value as string)) {\n\t\t\tinput_text = choices_names[choices_values.indexOf(value as string)];\n\t\t\tselected_index = choices_values.indexOf(value as string);\n\t\t} else if (allow_custom_value) {\n\t\t\tinput_text = value as string;\n\t\t\tselected_index = null;\n\t\t} else {\n\t\t\tinput_text = \"\";\n\t\t\tselected_index = null;\n\t\t}\n\t\told_selected_index = selected_index;\n\t}\n\n\tfunction handle_option_selected(e: any): void {\n\t\tselected_index = parseInt(e.detail.target.dataset.index);\n\t\tif (isNaN(selected_index)) {\n\t\t\t// This is the case when the user clicks on the scrollbar\n\t\t\tselected_index = null;\n\t\t\treturn;\n\t\t}\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tfilter_input.blur();\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tfiltered_indices = choices.map((_, i) => i);\n\t\tshow_options = true;\n\t\tdispatch(\"focus\");\n\t}\n\n\tfunction handle_blur(): void {\n\t\tif (!allow_custom_value) {\n\t\t\tinput_text = choices_names[choices_values.indexOf(value as string)];\n\t\t} else {\n\t\t\tvalue = input_text;\n\t\t}\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction handle_key_down(e: KeyboardEvent): void {\n\t\t[show_options, active_index] = handle_shared_keys(\n\t\t\te,\n\t\t\tactive_index,\n\t\t\tfiltered_indices\n\t\t);\n\t\tif (e.key === \"Enter\") {\n\t\t\tif (active_index !== null) {\n\t\t\t\tselected_index = active_index;\n\t\t\t\tshow_options = false;\n\t\t\t\tfilter_input.blur();\n\t\t\t\tactive_index = null;\n\t\t\t} else if (choices_names.includes(input_text)) {\n\t\t\t\tselected_index = choices_names.indexOf(input_text);\n\t\t\t\tshow_options = false;\n\t\t\t\tactive_index = null;\n\t\t\t\tfilter_input.blur();\n\t\t\t} else if (allow_custom_value) {\n\t\t\t\tvalue = input_text;\n\t\t\t\tselected_index = null;\n\t\t\t\tshow_options = false;\n\t\t\t\tactive_index = null;\n\t\t\t\tfilter_input.blur();\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t\tinitialized = true;\n\t});\n</script>\n\n<div class:container>\n\t<BlockTitle {root} {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:show_options>\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\trole=\"listbox\"\n\t\t\t\t\taria-controls=\"dropdown-options\"\n\t\t\t\t\taria-expanded={show_options}\n\t\t\t\t\taria-label={label}\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={!choices_names.includes(input_text) &&\n\t\t\t\t\t\t!allow_custom_value}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={input_text}\n\t\t\t\t\tbind:this={filter_input}\n\t\t\t\t\ton:keydown={handle_key_down}\n\t\t\t\t\ton:keyup={(e) =>\n\t\t\t\t\t\tdispatch(\"key_up\", {\n\t\t\t\t\t\t\tkey: e.key,\n\t\t\t\t\t\t\tinput_value: input_text\n\t\t\t\t\t\t})}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t\treadonly={!filterable}\n\t\t\t\t/>\n\t\t\t\t{#if !disabled}\n\t\t\t\t\t<div class=\"icon-wrap\">\n\t\t\t\t\t\t<DropdownArrow />\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\t{show_options}\n\t\t\t{choices}\n\t\t\t{filtered_indices}\n\t\t\t{disabled}\n\t\t\tselected_indices={selected_index === null ? [] : [selected_index]}\n\t\t\t{active_index}\n\t\t\ton:change={handle_option_selected}\n\t\t/>\n\t</div>\n</div>\n\n<style>\n\t.icon-wrap {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\tright: var(--size-5);\n\t\tcolor: var(--body-text-color);\n\t\twidth: var(--size-5);\n\t\tpointer-events: none;\n\t}\n\t.container {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t\tbackground: var(--input-background-fill-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t\theight: 100%;\n\t}\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t\theight: 100%;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\theight: 100%;\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tinput[readonly] {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseDropdown } from \"./shared/Dropdown.svelte\";\n\texport { default as BaseMultiselect } from \"./shared/Multiselect.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, KeyUpData, SelectData } from \"@gradio/utils\";\n\timport Multiselect from \"./shared/Multiselect.svelte\";\n\timport Dropdown from \"./shared/Dropdown.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype Item = string | number;\n\n\texport let label = \"Dropdown\";\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let multiselect = false;\n\texport let value: Item | Item[] | undefined = multiselect ? [] : undefined;\n\texport let value_is_output = false;\n\texport let max_choices: number | null = null;\n\texport let choices: [string, Item][];\n\texport let show_label: boolean;\n\texport let filterable: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let allow_custom_value = false;\n\texport let root: string;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\tselect: SelectData;\n\t\tblur: never;\n\t\tfocus: never;\n\t\tkey_up: KeyUpData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let interactive: boolean;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={container}\n\tallow_overflow={false}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\n\t{#if multiselect}\n\t\t<Multiselect\n\t\t\tbind:value\n\t\t\tbind:value_is_output\n\t\t\t{choices}\n\t\t\t{max_choices}\n\t\t\t{root}\n\t\t\t{label}\n\t\t\t{info}\n\t\t\t{show_label}\n\t\t\t{allow_custom_value}\n\t\t\t{filterable}\n\t\t\t{container}\n\t\t\ti18n={gradio.i18n}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\t\ton:key_up={() => gradio.dispatch(\"key_up\")}\n\t\t\tdisabled={!interactive}\n\t\t/>\n\t{:else}\n\t\t<Dropdown\n\t\t\tbind:value\n\t\t\tbind:value_is_output\n\t\t\t{choices}\n\t\t\t{label}\n\t\t\t{root}\n\t\t\t{info}\n\t\t\t{show_label}\n\t\t\t{filterable}\n\t\t\t{allow_custom_value}\n\t\t\t{container}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\t\ton:key_up={(e) => gradio.dispatch(\"key_up\", e.detail)}\n\t\t\tdisabled={!interactive}\n\t\t/>\n\t{/if}\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "ctx", "i", "set_style", "ul", "ul_transition", "create_bidirectional_transition", "fly", "toggle_class", "li", "span", "set_data", "t2", "t2_value", "if_block", "create_if_block", "div", "choices", "$$props", "filtered_indices", "show_options", "disabled", "selected_indices", "active_index", "distance_from_top", "distance_from_bottom", "input_height", "input_width", "refElement", "listElement", "top", "bottom", "max_height", "innerHeight", "calculate_window_distance", "ref_top", "ref_bottom", "$$invalidate", "scroll_timeout", "scroll_listener", "dispatch", "createEventDispatcher", "$$value", "mousedown_handler", "e", "elements", "element", "rect", "positive_mod", "n", "m", "handle_filter", "input_text", "o", "index", "handle_change", "value", "value_is_output", "handle_shared_keys", "index_in_filtered", "increment", "t_value", "dirty", "attr", "div_title_value", "current", "create_if_block_3", "create_if_block_2", "create_if_block_1", "label_1", "div2", "div1", "div0", "input", "each_blocks", "label", "info", "old_value", "max_choices", "old_choices", "show_label", "container", "allow_custom_value", "filterable", "i18n", "root", "filter_input", "old_input_text", "choices_names", "choices_values", "old_selected_index", "c", "handle_blur", "add_selected_choice", "remove_selected_choice", "option_index", "v", "handle_option_selected", "add_or_remove_index", "remove_all", "handle_focus", "_", "handle_key_down", "set_selected_indices", "val", "afterUpdate", "click_handler", "s", "event", "keyup_handler", "div3", "dropdownoptions_changes", "initialized", "selected_index", "set_input_text", "set_choice_names_values", "is_browser", "multiselect_1_changes", "elem_id", "elem_classes", "visible", "multiselect", "scale", "min_width", "loading_status", "gradio", "interactive", "clear_status_handler"], "mappings": "u3BAAAA,EASKC,EAAAC,EAAAC,CAAA,EAHJC,EAECF,EAAAG,CAAA,iMC+EOC,EAAgB,CAAA,CAAA,uBAArB,OAAIC,GAAA,uMALoBD,EAAU,EAAA,CAAA,6BAAA,EACvBE,EAAAC,EAAA,QAAAH,KAAc,IAAI,UAPhCN,EA+BIC,EAAAQ,EAAAN,CAAA,+HApBIG,EAAgB,CAAA,CAAA,oBAArB,OAAIC,GAAA,EAAA,mHAAJ,2FALwBD,EAAU,EAAA,CAAA,6BAAA,SACvBE,EAAAC,EAAA,QAAAH,KAAc,IAAI,2BALbI,IAAAA,EAAAC,GAAAF,EAAAG,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,+BAAnBF,IAAAA,EAAAC,GAAAF,EAAAG,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,wGA0BlCN,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,EAAA,wIAHEA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,uDANtCA,EAAK,EAAA,CAAA,qBACLA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,CAAA,kFAGbA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,iBAT9BA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,EACjCO,EAAAC,EAAA,SAAAR,QAAUA,EAAY,CAAA,CAAA,EACjBO,EAAAC,EAAA,cAAAR,QAAUA,EAAY,CAAA,CAAA,EACjBO,EAAAC,EAAA,mBAAAR,QAAUA,EAAY,CAAA,CAAA,EACjCE,EAAAM,EAAA,QAAAR,KAAc,IAAI,UANhCN,EAiBIC,EAAAa,EAAAX,CAAA,EAJHC,EAEMU,EAAAC,CAAA,iDAFaT,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,cAGjDA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,EAAA,KAAAU,GAAAC,EAAAC,CAAA,cATLZ,EAAK,EAAA,oCACLA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,qCAGbA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,gDAT9BA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,QACjCO,EAAAC,EAAA,SAAAR,QAAUA,EAAY,CAAA,CAAA,QACjBO,EAAAC,EAAA,cAAAR,QAAUA,EAAY,CAAA,CAAA,QACjBO,EAAAC,EAAA,mBAAAR,QAAUA,EAAY,CAAA,CAAA,SACjCE,EAAAM,EAAA,QAAAR,KAAc,IAAI,yDAnB9B,IAAAa,EAAAb,OAAiBA,EAAQ,CAAA,GAAAc,GAAAd,CAAA,+EAD9BN,EAA+CC,EAAAoB,EAAAlB,CAAA,kEAFrBG,EAAe,EAAA,CAAA,4CAGpCA,OAAiBA,EAAQ,CAAA,gNAxElB,GAAA,CAAA,QAAAgB,CAAA,EAAAC,EACA,CAAA,iBAAAC,CAAA,EAAAD,GACA,aAAAE,EAAe,EAAA,EAAAF,GACf,SAAAG,EAAW,EAAA,EAAAH,EACX,CAAA,iBAAAI,EAAA,EAAA,EAAAJ,GACA,aAAAK,EAA8B,IAAA,EAAAL,EAErCM,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAoBC,EAAuBC,EAC3CC,EAEK,SAAAC,GAAA,OACA,IAAKC,EAAS,OAAQC,GAC7BR,EAAW,6BACZJ,EAAoBW,CAAA,EACpBE,EAAA,GAAAZ,EAAuBQ,EAAcG,CAAA,MAGlCE,EAAwC,KACnC,SAAAC,GAAA,CACHnB,IACDkB,IAAmB,MACtB,aAAaA,CAAc,EAG5BA,EAAiB,gBAChBJ,IACAI,EAAiB,MACf,WAiCEE,EAAWC,sFAKgBb,EAAUc,WAKZ,MAAAC,EAAAC,GAAMJ,EAAS,SAAUI,CAAC,2CAK7Cf,EAAWa,+TA7CvB,IACKtB,GAAgBQ,EAAA,CACf,GAAAC,GAAeP,EAAiB,OAAS,EAAA,KACxCuB,EAAWhB,EAAY,iBAAiB,IAAI,YACrCiB,KAAW,MAAM,KAAKD,CAAQ,EAEvC,GAAAC,EAAQ,aAAa,YAAY,IACjCxB,EAAiB,CAAC,EAAE,WAAA,CAEpBO,GAAa,WAAW,EAAIiB,EAA0B,SAAS,SAKlEZ,IACM,MAAAa,EAAOnB,EAAW,eAAe,sBAAA,EACvCS,EAAA,GAAAX,EAAeqB,GAAM,QAAU,CAAA,EAC/BV,EAAA,EAAAV,EAAcoB,GAAM,OAAS,CAAA,EAE1BtB,EAAuBD,GAC1Ba,EAAA,EAAAP,EAAA,GAASN,CAAiB,IAAA,OAC1BQ,EAAaP,CAAA,OACbM,EAAS,IAAA,IAETM,EAAA,GAAAN,EAAA,GAAYN,EAAuBC,CAAY,IAAA,EAC/CW,EAAA,GAAAL,EAAaR,EAAoBE,CAAA,MACjCI,EAAM,IAAA,+vBCjET,SAASkB,GAAaC,EAAWC,EAAmB,CAC1C,OAAAD,EAAIC,EAAKA,GAAKA,CACxB,CAEgB,SAAAC,GACflC,EACAmC,EACW,CACX,OAAOnC,EAAQ,OAAO,CAACE,EAAkBkC,EAAGC,MAE1C,CAAAF,GAAaC,EAAE,CAAC,EAAE,YAAA,EAAc,SAASD,EAAW,aAAa,IAEjEjC,EAAiB,KAAKmC,CAAK,EAErBnC,GACL,CAAc,CAAA,CAClB,CAEgB,SAAAoC,GACff,EACAgB,EACAC,EACO,CACPjB,EAAS,SAAUgB,CAAK,EACnBC,GACJjB,EAAS,OAAO,CAElB,CAEgB,SAAAkB,GACfd,EACArB,EACAJ,EAC2B,CACvB,GAAAyB,EAAE,MAAQ,SACN,MAAA,CAAC,GAAOrB,CAAY,EAE5B,IAAIqB,EAAE,MAAQ,aAAeA,EAAE,MAAQ,YAClCzB,EAAiB,QAAU,EAC9B,GAAII,IAAiB,KAEnBA,EAAAqB,EAAE,MAAQ,YACPzB,EAAiB,CAAC,EAClBA,EAAiBA,EAAiB,OAAS,CAAC,MAC1C,CACA,MAAAwC,EAAoBxC,EAAiB,QAAQI,CAAY,EACzDqC,EAAYhB,EAAE,MAAQ,UAAY,GAAK,EAC7CrB,EACCJ,EACC6B,GAAaW,EAAoBC,EAAWzC,EAAiB,MAAM,CACpE,CACF,CAGK,MAAA,CAAC,GAAMI,CAAY,CAC3B,+FCwKyCtB,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,uCAUtCA,EAAC,EAAA,EAAA,mEAADA,EAAC,EAAA,EAAA,KAAAU,GAAA,EAAAkD,CAAA,iCAFD,IAAAA,EAAA5D,MAAcA,EAAC,EAAA,CAAA,EAAA,iDAAf6D,EAAA,CAAA,EAAA,OAAAD,KAAAA,EAAA5D,MAAcA,EAAC,EAAA,CAAA,EAAA,KAAAU,GAAA,EAAAkD,CAAA,oQAgBTE,EAAA/C,EAAA,QAAAgD,EAAA/D,EAAK,CAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,CAAA,UAVvCN,EAaKC,EAAAoB,EAAAlB,CAAA,sFAHG,CAAAmE,GAAAH,EAAA,CAAA,EAAA,MAAAE,KAAAA,EAAA/D,EAAK,CAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,gKAjB3B,OAAA,OAAAA,OAAM,SAAQiE,2BAMrBjE,EAAQ,CAAA,GAAAkE,GAAAlE,CAAA,mIARfN,EAwBKC,EAAAoB,EAAAlB,CAAA,EAvBJC,EAMMiB,EAAAN,CAAA,iHACAT,EAAQ,CAAA,sMAwCTA,EAAgB,EAAA,EAAC,OAAS,GAACmE,GAAAnE,CAAA,4IAgBhCN,EAAgDC,EAAAc,EAAAZ,CAAA,2BAhB3CG,EAAgB,EAAA,EAAC,OAAS,mZAKtB8D,EAAA/C,EAAA,QAAAgD,EAAA/D,KAAK,cAAc,CAAA,UAJ3BN,EAaKC,EAAAoB,EAAAlB,CAAA,sCARMG,EAAU,EAAA,CAAA,uCADb,CAAAgE,GAAAH,EAAA,CAAA,EAAA,KAAAE,KAAAA,EAAA/D,KAAK,cAAc,+QAtDvBA,EAAgB,EAAA,CAAA,uBAArB,OAAIC,GAAA,mEAgDCD,EAAQ,CAAA,GAAAc,GAAAd,CAAA,2JA4BJA,EAAsB,EAAA,CAAA,4QA/BpBA,EAAU,CAAA,iBAfJA,EAAa,EAAA,EAAC,SAASA,EAAU,EAAA,CAAA,GAAA,CAChDA,EAAkB,CAAA,GACnBA,EAAgB,EAAA,EAAC,SAAWA,EAAW,CAAA,CAAA,iNArC7CN,EAoFOC,EAAAyE,EAAAvE,CAAA,qBAjFNC,EAgFKsE,EAAAC,CAAA,EA/EJvE,EAqEKuE,EAAAC,CAAA,0DAzCJxE,EAwCKwE,EAAAC,CAAA,EAvCJzE,EAkBCyE,EAAAC,CAAA,OAXYxE,EAAU,EAAA,CAAA,iGAEVA,EAAe,EAAA,CAAA,gCAMlBA,EAAW,EAAA,CAAA,cACVA,EAAY,EAAA,CAAA,uLA5CjBA,EAAgB,EAAA,CAAA,oBAArB,OAAIC,GAAA,EAAA,wGAAJ,OAAIA,EAAAwE,EAAA,OAAAxE,GAAA,mEA6COD,EAAU,CAAA,yCAVTA,EAAU,EAAA,QAAVA,EAAU,EAAA,CAAA,mCALLA,EAAa,EAAA,EAAC,SAASA,EAAU,EAAA,CAAA,GAAA,CAChDA,EAAkB,CAAA,GACnBA,EAAgB,EAAA,EAAC,SAAWA,EAAW,CAAA,CAAA,EAgBnCA,EAAQ,CAAA,kcAhDb,OAAIC,GAAA,2OAxNG,GAAA,CAAA,MAAAyE,CAAA,EAAAzD,GACA,KAAA0D,EAA2B,MAAA,EAAA1D,EAC3B,CAAA,MAAAsC,EAAA,EAAA,EAAAtC,EACP2D,EAAA,CAAA,GACO,gBAAApB,EAAkB,EAAA,EAAAvC,GAClB,YAAA4D,EAA6B,IAAA,EAAA5D,EAC7B,CAAA,QAAAD,CAAA,EAAAC,EACP6D,GACO,SAAA1D,EAAW,EAAA,EAAAH,EACX,CAAA,WAAA8D,CAAA,EAAA9D,GACA,UAAA+D,EAAY,EAAA,EAAA/D,GACZ,mBAAAgE,EAAqB,EAAA,EAAAhE,GACrB,WAAAiE,EAAa,EAAA,EAAAjE,EACb,CAAA,KAAAkE,CAAA,EAAAlE,EACA,CAAA,KAAAmE,CAAA,EAAAnE,EAEPoE,EACAlC,EAAa,GACbmC,EAAiB,GACjBnE,EAAe,GACfoE,EACAC,EAGAtE,EAAA,CAAA,EACAI,EAA8B,KAE9BD,EAAA,CAAA,EACAoE,EAAA,CAAA,QAEElD,EAAWC,KAUb,MAAM,QAAQe,CAAK,GACtBA,EAAM,QAASV,GAAA,CACR,MAAAQ,EAAQrC,EAAQ,IAAK0E,IAAMA,GAAE,CAAC,CAAA,EAAG,QAAQ7C,CAAO,EAClDQ,IAAU,GACbhC,EAAiB,KAAKgC,CAAK,EAE3BhC,EAAiB,KAAKwB,CAAO,IAuCvB,SAAA8C,GAAA,CACHV,QACJ9B,EAAa,EAAA,EAGV8B,GAAsB9B,IAAe,KACxCyC,EAAoBzC,CAAU,OAC9BA,EAAa,EAAA,QAGdhC,EAAe,EAAA,OACfG,EAAe,IAAA,EACfiB,EAAS,MAAM,WAGPsD,EAAuBC,EAAA,CAC/B1D,EAAA,GAAAf,EAAmBA,EAAiB,OAAQ0E,GAAMA,IAAMD,CAAY,CAAA,EACpEvD,EAAS,SAAA,CACR,MAAc,OAAAuD,GAAiB,SAAWA,EAAe,GACzD,MAAA,OACQA,GAAiB,SACrBN,EAAeM,CAAY,EAC3BA,EACJ,SAAU,cAIHF,EAAoBE,EAAA,EACxBjB,IAAgB,MAAQxD,EAAiB,OAASwD,KACrDzC,EAAA,GAAAf,EAAA,CAAA,GAAuBA,EAAkByE,CAAY,CAAA,EACrDvD,EAAS,SAAA,CACR,MAAc,OAAAuD,GAAiB,SAAWA,EAAe,GACzD,MAAA,OACQA,GAAiB,SACrBN,EAAeM,CAAY,EAC3BA,EACJ,SAAU,MAGRzE,EAAiB,SAAWwD,SAC/B1D,EAAe,EAAA,OACfG,EAAe,IAAA,EACf+D,EAAa,KAAA,YAINW,GAAuBrD,EAAA,CACzB,MAAAmD,EAAe,SAASnD,EAAE,OAAO,OAAO,QAAQ,KAAK,EAC3DsD,GAAoBH,CAAY,WAGxBG,GAAoBH,EAAA,CACxBzE,EAAiB,SAASyE,CAAY,EACzCD,EAAuBC,CAAY,EAEnCF,EAAoBE,CAAY,OAEjC3C,EAAa,EAAA,WAGL+C,GAAWvD,EAAA,CACnBP,EAAA,GAAAf,EAAA,CAAA,CAAA,OACA8B,EAAa,EAAA,EACbR,EAAE,eAAA,WAGMwD,GAAaxD,EAAA,CACrBP,EAAA,GAAAlB,EAAmBF,EAAQ,IAAA,CAAKoF,EAAGnG,KAAMA,EAAC,CAAA,GACtC4E,IAAgB,MAAQxD,EAAiB,OAASwD,SACrD1D,EAAe,EAAA,EAEhBoB,EAAS,OAAO,WAGR8D,GAAgB1D,EAAA,CACvBP,EAAA,GAAA,CAAAjB,EAAcG,CAAY,EAAImC,GAC9Bd,EACArB,EACAJ,CAAA,EAAAC,GAAAiB,EAAA,GAAAd,CAAA,EAAAc,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAA0C,CAAA,EAAA1C,EAAA,GAAAe,CAAA,EAAAf,EAAA,GAAAkD,CAAA,EAAAlD,EAAA,EAAA6C,CAAA,EAAA7C,EAAA,GAAAlB,CAAA,IAEGyB,EAAE,MAAQ,UACTrB,IAAiB,KACpB2E,GAAoB3E,CAAY,EAE5B2D,IACHW,EAAoBzC,CAAU,OAC9BA,EAAa,EAAA,IAIZR,EAAE,MAAQ,aAAeQ,IAAe,SAC3C9B,EAAuB,CAAA,GAAAA,EAAiB,MAAM,EAAA,EAAK,CAAA,CAAA,EAEhDA,EAAiB,SAAWwD,SAC/B1D,EAAe,EAAA,OACfG,EAAe,IAAA,GAIR,SAAAgF,IAAA,CACJ/C,IAAU,OACbnB,EAAA,GAAAf,EAAA,CAAA,CAAA,EACU,MAAM,QAAQkC,CAAK,GAC7BnB,EAAA,GAAAf,EAAmBkC,EACjB,IAAKwC,GAAA,OACC1C,EAAQmC,EAAe,QAAQO,CAAC,KAClC1C,IAAU,GACN,OAAAA,EAEJ,GAAA4B,EACI,OAAAc,IAKR,OAAQQ,GAAgCA,UAAiB,CAAA,EAM7DC,GAAA,IAAA,MACChD,EAAkB,EAAA,IAqBkB,MAAAiD,EAAAC,GAAAb,EAAuBa,CAAC,QAC3BC,IAAK,CAC5BA,EAAM,MAAQ,SACjBd,EAAuBa,CAAC,gBAoBhBvD,EAAU,KAAA,wDACXkC,EAAY5C,YAEZ,MAAAmE,GAAAjE,GACVJ,EAAS,SACR,CAAA,IAAKI,EAAE,IACP,YAAaQ,CAAA,CAAA,KAeAwD,GAAK,CACbA,EAAM,MAAQ,SACjBT,GAAWS,CAAK,mgBA9NvBvE,EAAA,GAAAmD,EAAgBvE,EAAQ,IAAK0E,GAAMA,EAAE,CAAC,CAAA,CAAA,EACtCtD,EAAA,GAAAoD,EAAiBxE,EAAQ,IAAK0E,GAAMA,EAAE,CAAC,CAAA,CAAA,6BAInC1E,IAAY8D,GAAe3B,IAAemC,UAC7CpE,EAAmBgC,GAAclC,EAASmC,CAAU,CAAA,OACpD2B,EAAc9D,CAAA,OACdsE,EAAiBnC,CAAA,EACZ8B,GACJ7C,EAAA,GAAAd,EAAeJ,EAAiB,CAAC,CAAA,6CAclC,KAAK,UAAUG,CAAgB,GAAK,KAAK,UAAUoE,CAAkB,SAErElC,EAAQlC,EAAiB,IAAKgC,GAAA,OACtBA,GAAU,SAAWmC,EAAenC,CAAK,EAAIA,CAAA,CAAA,EAErDjB,EAAA,GAAAqD,EAAqBpE,EAAiB,MAAA,CAAA,4BAbnC,KAAK,UAAUkC,CAAK,GAAK,KAAK,UAAUqB,CAAS,IACpDtB,GAAcf,EAAUgB,EAAOC,CAAe,EAC9CpB,EAAA,GAAAwC,EAAY,MAAM,QAAQrB,CAAK,EAAIA,EAAM,QAAUA,CAAA,2BAsI3C+C,GAAA,g7CCd8BtG,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,gJA4BzCN,EAEKC,EAAAoB,EAAAlB,CAAA,8PAHAG,EAAQ,CAAA,GAAAc,GAAA,+FAYG,iBAAAd,QAAmB,SAAaA,EAAc,EAAA,CAAA,uCAErDA,EAAsB,EAAA,CAAA,+MAjChBA,EAAY,EAAA,CAAA,mBACfA,EAAK,CAAA,CAAA,oGAgBNA,EAAU,CAAA,EAdLO,EAAAiE,EAAA,UAAA,CAAAxE,EAAc,EAAA,EAAA,SAASA,SACrCA,EAAkB,CAAA,CAAA,iNAbzBN,EA6CKC,EAAAkH,EAAAhH,CAAA,qBA1CJC,EAyCK+G,EAAAxC,CAAA,EAxCJvE,EA8BKuE,EAAAC,CAAA,EA7BJxE,EA4BKwE,EAAAC,CAAA,EA3BJzE,EAqBCyE,EAAAC,CAAA,OAXYxE,EAAU,EAAA,CAAA,iGAEVA,EAAe,EAAA,CAAA,gCAMlBA,EAAW,EAAA,CAAA,cACVA,EAAY,EAAA,CAAA,kMAhBPA,EAAY,EAAA,CAAA,iCACfA,EAAK,CAAA,CAAA,yDAgBNA,EAAU,CAAA,yCAVTA,EAAU,EAAA,QAAVA,EAAU,EAAA,CAAA,oBAJNO,EAAAiE,EAAA,UAAA,CAAAxE,EAAc,EAAA,EAAA,SAASA,SACrCA,EAAkB,CAAA,CAAA,EAefA,EAAQ,CAAA,yQAYG6D,EAAA,CAAA,EAAA,OAAAiD,EAAA,iBAAA9G,QAAmB,SAAaA,EAAc,EAAA,CAAA,mRAtOvD,GAAA,CAAA,MAAA0E,CAAA,EAAAzD,GACA,KAAA0D,EAA2B,MAAA,EAAA1D,GAC3B,MAAAsC,EAAmC,MAAA,EAAAtC,EAC1C2D,GACO,gBAAApB,EAAkB,EAAA,EAAAvC,EAClB,CAAA,QAAAD,CAAA,EAAAC,EACP6D,GACO,SAAA1D,EAAW,EAAA,EAAAH,EACX,CAAA,WAAA8D,CAAA,EAAA9D,GACA,UAAA+D,EAAY,EAAA,EAAA/D,GACZ,mBAAAgE,EAAqB,EAAA,EAAAhE,GACrB,WAAAiE,EAAa,EAAA,EAAAjE,EACb,CAAA,KAAAmE,CAAA,EAAAnE,EAEPoE,EAEAlE,EAAe,GACfoE,EACAC,EACArC,EAAa,GACbmC,EAAiB,GACjByB,EAAc,GAGd7F,EAAA,CAAA,EACAI,EAA8B,KAE9B0F,EAAgC,KAChCvB,QAEElD,EAAWC,KAUbe,IACHkC,EAAqBzE,EAAQ,IAAK0E,GAAMA,EAAE,CAAC,CAAA,EAAG,QAAQnC,CAAe,EACrEyD,EAAiBvB,EACbuB,IAAmB,IACtBpC,EAAYrB,EACZyD,EAAiB,QAEhB7D,EAAYyB,CAAS,EAAI5D,EAAQgG,CAAc,EAChD1B,EAAiBnC,GAElB8D,KAyBQ,SAAAC,GAAA,CACR9E,EAAA,GAAAmD,EAAgBvE,EAAQ,IAAK0E,GAAMA,EAAE,CAAC,CAAA,CAAA,EACtCtD,EAAA,GAAAoD,EAAiBxE,EAAQ,IAAK0E,GAAMA,EAAE,CAAC,CAAA,CAAA,EAKlC,MAAAyB,EAAA,OAAoB,OAAW,IA4B5B,SAAAF,GAAA,CACRC,IACI3D,IAAA,QAAwB,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,QACpEJ,EAAa,EAAA,OACb6D,EAAiB,IAAA,GACPxB,EAAe,SAASjC,CAAe,QACjDJ,EAAaoC,EAAcC,EAAe,QAAQjC,CAAe,CAAA,CAAA,OACjEyD,EAAiBxB,EAAe,QAAQjC,CAAe,CAAA,GAC7C0B,QACV9B,EAAaI,CAAA,OACbyD,EAAiB,IAAA,SAEjB7D,EAAa,EAAA,OACb6D,EAAiB,IAAA,QAElBvB,EAAqBuB,CAAA,WAGbhB,EAAuBrD,EAAA,CAE3B,GADJP,EAAA,GAAA4E,EAAiB,SAASrE,EAAE,OAAO,OAAO,QAAQ,KAAK,CAAA,EACnD,MAAMqE,CAAc,EAAA,MAEvBA,EAAiB,IAAA,cAGlB7F,EAAe,EAAA,OACfG,EAAe,IAAA,EACf+D,EAAa,KAAA,WAGLc,GAAaxD,EAAA,CACrBP,EAAA,GAAAlB,EAAmBF,EAAQ,IAAA,CAAKoF,GAAGnG,IAAMA,CAAC,CAAA,OAC1CkB,EAAe,EAAA,EACfoB,EAAS,OAAO,EAGR,SAAAoD,IAAA,CACHV,OAGJ1B,EAAQJ,CAAA,OAFRA,EAAaoC,EAAcC,EAAe,QAAQjC,CAAe,CAAA,CAAA,OAIlEpC,EAAe,EAAA,OACfG,EAAe,IAAA,EACfiB,EAAS,MAAM,WAGP8D,GAAgB1D,EAAA,CACvBP,EAAA,GAAA,CAAAjB,EAAcG,CAAY,EAAImC,GAC9Bd,EACArB,EACAJ,CAAA,EAAAC,GAAAiB,EAAA,GAAAd,CAAA,EAAAc,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAA0C,CAAA,EAAA1C,EAAA,EAAA6C,CAAA,EAAA7C,EAAA,GAAAe,CAAA,EAAAf,EAAA,GAAAlB,CAAA,EAAAkB,EAAA,EAAAiD,CAAA,EAAAjD,EAAA,GAAAkD,CAAA,EAAAlD,EAAA,GAAA4E,CAAA,EAAA5E,EAAA,GAAAqD,CAAA,EAAArD,EAAA,GAAA2E,CAAA,EAAA3E,EAAA,GAAAoD,CAAA,EAAA,EAEG7C,EAAE,MAAQ,UACTrB,IAAiB,WACpB0F,EAAiB1F,CAAA,OACjBH,EAAe,EAAA,EACfkE,EAAa,KAAA,OACb/D,EAAe,IAAA,GACLiE,EAAc,SAASpC,CAAU,QAC3C6D,EAAiBzB,EAAc,QAAQpC,CAAU,CAAA,OACjDhC,EAAe,EAAA,OACfG,EAAe,IAAA,EACf+D,EAAa,KAAA,GACHJ,SACV1B,EAAQJ,CAAA,OACR6D,EAAiB,IAAA,OACjB7F,EAAe,EAAA,OACfG,EAAe,IAAA,EACf+D,EAAa,KAAA,IAKhBmB,GAAA,IAAA,MACChD,EAAkB,EAAA,OAClBuD,EAAc,EAAA,kBAoBC5D,EAAU,KAAA,+FACXkC,EAAY5C,WAEZ,MAAAmE,GAAAjE,GACVJ,EAAS,SACR,CAAA,IAAKI,EAAE,IACP,YAAaQ,CAAA,CAAA,scA7JjB6D,IAAmBvB,GACnBuB,IAAmB,MACnBD,UAEC5D,EAAYI,CAAK,EAAIvC,EAAQgG,CAAc,EAAA7D,GAAAf,EAAA,GAAAmB,CAAA,EAAAnB,EAAA,GAAA4E,CAAA,EAAA5E,EAAA,GAAAqD,CAAA,EAAArD,EAAA,GAAA2E,CAAA,EAAA3E,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAAoD,CAAA,SAC5CC,EAAqBuB,CAAA,EACrBzE,EAAS,SAAA,CACR,MAAOyE,EACP,MAAOxB,EAAewB,CAAc,EACpC,SAAU,8BAKN,KAAK,UAAUpC,CAAS,IAAM,KAAK,UAAUrB,CAAK,IACxD0D,IACA3D,GAAcf,EAAUgB,EAAOC,CAAe,OAC9CoB,EAAYrB,CAAA,oBAQD2D,EAAA,0BAKPlG,IAAY8D,IACVG,GACJgC,SAEDnC,EAAc9D,CAAA,OACdE,EAAmBgC,GAAclC,EAASmC,CAAU,CAAA,EAC/C,CAAA8B,GAAsB/D,EAAiB,OAAS,GACpDkB,EAAA,GAAAd,EAAeJ,EAAiB,CAAC,CAAA,EAE9BiG,GAAc9B,IAAiB,SAAS,oBAC3ClE,EAAe,EAAA,2BAMbgC,IAAemC,SAClBpE,EAAmBgC,GAAclC,EAASmC,CAAU,CAAA,OACpDmC,EAAiBnC,CAAA,EACZ,CAAA8B,GAAsB/D,EAAiB,OAAS,GACpDkB,EAAA,GAAAd,EAAeJ,EAAiB,CAAC,CAAA,w5CCbvBlB,EAAW,EAAA,2mBAAXA,EAAW,EAAA,0ZA3BhB,KAAAA,MAAO,eAOFA,EAAW,EAAA,gnBAPhB6D,EAAA,CAAA,EAAA,SAAAuD,EAAA,KAAApH,MAAO,gCAOFA,EAAW,EAAA,0OA1BX,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,iJAIdA,EAAW,CAAA,EAAA,8KANH,WAAAA,MAAO,YACb6D,EAAA,CAAA,EAAA,QAAA,CAAA,KAAA7D,MAAO,IAAI,iBACbA,EAAc,EAAA,CAAA,kXARVA,EAAS,EAAA,iBACF,iPADPA,EAAS,EAAA,iOAlCP,MAAA0E,EAAQ,UAAA,EAAAzD,GACR,KAAA0D,EAA2B,MAAA,EAAA1D,GAC3B,QAAAoG,EAAU,EAAA,EAAApG,EACV,CAAA,aAAAqG,EAAA,EAAA,EAAArG,GACA,QAAAsG,EAAU,EAAA,EAAAtG,GACV,YAAAuG,EAAc,EAAA,EAAAvG,EACd,CAAA,MAAAsC,EAAmCiE,EAAmB,CAAA,EAAA,MAAA,EAAAvG,GACtD,gBAAAuC,EAAkB,EAAA,EAAAvC,GAClB,YAAA4D,EAA6B,IAAA,EAAA5D,EAC7B,CAAA,QAAAD,CAAA,EAAAC,EACA,CAAA,WAAA8D,CAAA,EAAA9D,EACA,CAAA,WAAAiE,CAAA,EAAAjE,GACA,UAAA+D,EAAY,EAAA,EAAA/D,GACZ,MAAAwG,EAAuB,IAAA,EAAAxG,GACvB,UAAAyG,EAAgC,MAAA,EAAAzG,EAChC,CAAA,eAAA0G,CAAA,EAAA1G,GACA,mBAAAgE,EAAqB,EAAA,EAAAhE,EACrB,CAAA,KAAAmE,CAAA,EAAAnE,EACA,CAAA,OAAA2G,CAAA,EAAA3G,EASA,CAAA,YAAA4G,CAAA,EAAA5G,EAgBa,MAAA6G,EAAA,IAAAF,EAAO,SAAS,eAAgBD,CAAc,gEAiBnDC,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO,IAC3BjF,GAAMiF,EAAO,SAAS,SAAUjF,EAAE,MAAM,QACrCiF,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,mEAexBA,EAAO,SAAS,QAAQ,SACzBA,EAAO,SAAS,OAAO,KAC3BjF,GAAMiF,EAAO,SAAS,SAAUjF,EAAE,MAAM,SACrCiF,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,KAC3BjF,GAAMiF,EAAO,SAAS,SAAUjF,EAAE,MAAM"}