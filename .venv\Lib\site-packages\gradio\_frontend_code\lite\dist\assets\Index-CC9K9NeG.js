import{a as f,i as o,s as d,f as v,a8 as g}from"../lite.js";function h(r,t,e){let{gradio:u}=t,{value:s=1}=t,{active:l=!0}=t,c,n,i;return g(()=>{i&&clearInterval(i)}),r.$$set=a=>{"gradio"in a&&e(0,u=a.gradio),"value"in a&&e(1,s=a.value),"active"in a&&e(2,l=a.active)},r.$$.update=()=>{r.$$.dirty&63&&(c!==s||l!==n)&&(i&&clearInterval(i),l&&e(5,i=setInterval(()=>{document.visibilityState==="visible"&&u.dispatch("tick")},s*1e3)),e(3,c=s),e(4,n=l))},[u,s,l,c,n,i]}class m extends f{constructor(t){super(),o(this,t,h,null,d,{gradio:0,value:1,active:2})}get gradio(){return this.$$.ctx[0]}set gradio(t){this.$$set({gradio:t}),v()}get value(){return this.$$.ctx[1]}set value(t){this.$$set({value:t}),v()}get active(){return this.$$.ctx[2]}set active(t){this.$$set({active:t}),v()}}export{m as default};
//# sourceMappingURL=Index-CC9K9NeG.js.map
