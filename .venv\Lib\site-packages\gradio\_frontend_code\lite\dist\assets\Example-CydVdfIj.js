import{a as m,i as x,s as y,f as a,E as d,l as c,w as _,o as r,k as u,n as o}from"../lite.js";function k(f){let t,s,n;return{c(){t=u(f[1]),s=u(" x "),n=u(f[2])},m(e,i){c(e,t,i),c(e,s,i),c(e,n,i)},p(e,i){i&2&&o(t,e[1]),i&4&&o(n,e[2])},d(e){e&&(r(t),r(s),r(n))}}}function b(f){let t;return{c(){t=u(f[0])},m(s,n){c(s,t,n)},p(s,n){n&1&&o(t,s[0])},d(s){s&&r(t)}}}function h(f){let t;function s(i,l){return i[0]?b:k}let n=s(f),e=n(f);return{c(){e.c(),t=d()},m(i,l){e.m(i,l),c(i,t,l)},p(i,[l]){n===(n=s(i))&&e?e.p(i,l):(e.d(1),e=n(i),e&&(e.c(),e.m(t.parentNode,t)))},i:_,o:_,d(i){i&&r(t),e.d(i)}}}function E(f,t,s){let{title:n}=t,{x:e}=t,{y:i}=t;return f.$$set=l=>{"title"in l&&s(0,n=l.title),"x"in l&&s(1,e=l.x),"y"in l&&s(2,i=l.y)},[n,e,i]}class w extends m{constructor(t){super(),x(this,t,E,h,y,{title:0,x:1,y:2})}get title(){return this.$$.ctx[0]}set title(t){this.$$set({title:t}),a()}get x(){return this.$$.ctx[1]}set x(t){this.$$set({x:t}),a()}get y(){return this.$$.ctx[2]}set y(t){this.$$set({y:t}),a()}}export{w as default};
//# sourceMappingURL=Example-CydVdfIj.js.map
