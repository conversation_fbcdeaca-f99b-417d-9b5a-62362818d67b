import{a as p,i as h,s as d,Q as n,q as t,l as g,v as i,w as a,o as u}from"../lite.js";function w(l){let e,s,o;return{c(){e=n("svg"),s=n("polygon"),o=n("rect"),t(s,"points","23 7 16 12 23 17 23 7"),t(o,"x","1"),t(o,"y","5"),t(o,"width","15"),t(o,"height","14"),t(o,"rx","2"),t(o,"ry","2"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-video")},m(r,c){g(r,e,c),i(e,s),i(e,o)},p:a,i:a,o:a,d(r){r&&u(e)}}}class v extends p{constructor(e){super(),h(this,e,null,w,d,{})}}export{v as V};
//# sourceMappingURL=Video-BIoWYjY-.js.map
