{"version": 3, "file": "frontmatter-DT_8_6QC.js", "sources": ["../../../code/shared/frontmatter.ts"], "sourcesContent": ["import type {\n\tElement,\n\tMarkdownExtension,\n\tBlockContext,\n\tLine\n} from \"@lezer/markdown\";\nimport { parseMixed } from \"@lezer/common\";\nimport { yaml } from \"@codemirror/legacy-modes/mode/yaml\";\nimport { foldInside, foldNodeProp, StreamLanguage } from \"@codemirror/language\";\nimport { styleTags, tags } from \"@lezer/highlight\";\n\nconst frontMatterFence = /^---\\s*$/m;\n\nexport const frontmatter: MarkdownExtension = {\n\tdefineNodes: [{ name: \"Frontmatter\", block: true }, \"FrontmatterMark\"],\n\tprops: [\n\t\tstyleTags({\n\t\t\tFrontmatter: [tags.documentMeta, tags.monospace],\n\t\t\tFrontmatterMark: tags.processingInstruction\n\t\t}),\n\t\tfoldNodeProp.add({\n\t\t\tFrontmatter: foldInside,\n\t\t\tFrontmatterMark: () => null\n\t\t})\n\t],\n\twrap: parseMixed((node) => {\n\t\tconst { parser } = StreamLanguage.define(yaml);\n\t\tif (node.type.name === \"Frontmatter\") {\n\t\t\treturn {\n\t\t\t\tparser,\n\t\t\t\toverlay: [{ from: node.from + 4, to: node.to - 4 }]\n\t\t\t};\n\t\t}\n\t\treturn null;\n\t}),\n\tparseBlock: [\n\t\t{\n\t\t\tname: \"Frontmatter\",\n\t\t\tbefore: \"HorizontalRule\",\n\t\t\tparse: (cx: BlockContext, line: Line): boolean => {\n\t\t\t\tlet end: number | undefined = undefined;\n\t\t\t\tconst children = new Array<Element>();\n\t\t\t\tif (cx.lineStart === 0 && frontMatterFence.test(line.text)) {\n\t\t\t\t\tchildren.push(cx.elt(\"FrontmatterMark\", 0, 4));\n\t\t\t\t\twhile (cx.nextLine()) {\n\t\t\t\t\t\tif (frontMatterFence.test(line.text)) {\n\t\t\t\t\t\t\tend = cx.lineStart + 4;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (end !== undefined) {\n\t\t\t\t\t\tchildren.push(cx.elt(\"FrontmatterMark\", end - 4, end));\n\t\t\t\t\t\tcx.addElement(cx.elt(\"Frontmatter\", 0, end, children));\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\t]\n};\n"], "names": ["frontMatterFence", "frontmatter", "styleTags", "tags", "foldNodeProp", "foldInside", "parseMixed", "node", "parser", "StreamLanguage", "yaml", "cx", "line", "end", "children"], "mappings": "kHAWA,MAAMA,EAAmB,YAEZC,EAAiC,CAC7C,YAAa,CAAC,CAAE,KAAM,cAAe,MAAO,IAAQ,iBAAiB,EACrE,MAAO,CACNC,EAAU,CACT,YAAa,CAACC,EAAK,aAAcA,EAAK,SAAS,EAC/C,gBAAiBA,EAAK,qBAAA,CACtB,EACDC,EAAa,IAAI,CAChB,YAAaC,EACb,gBAAiB,IAAM,IAAA,CACvB,CACF,EACA,KAAMC,EAAYC,GAAS,CAC1B,KAAM,CAAE,OAAAC,CAAW,EAAAC,EAAe,OAAOC,CAAI,EACzC,OAAAH,EAAK,KAAK,OAAS,cACf,CACN,OAAAC,EACA,QAAS,CAAC,CAAE,KAAMD,EAAK,KAAO,EAAG,GAAIA,EAAK,GAAK,EAAG,CAAA,EAG7C,IAAA,CACP,EACD,WAAY,CACX,CACC,KAAM,cACN,OAAQ,iBACR,MAAO,CAACI,EAAkBC,IAAwB,CACjD,IAAIC,EACE,MAAAC,EAAW,IAAI,MACrB,GAAIH,EAAG,YAAc,GAAKX,EAAiB,KAAKY,EAAK,IAAI,EAAG,CAEpD,IADPE,EAAS,KAAKH,EAAG,IAAI,kBAAmB,EAAG,CAAC,CAAC,EACtCA,EAAG,YACT,GAAIX,EAAiB,KAAKY,EAAK,IAAI,EAAG,CACrCC,EAAMF,EAAG,UAAY,EACrB,KACD,CAED,OAAIE,IAAQ,SACXC,EAAS,KAAKH,EAAG,IAAI,kBAAmBE,EAAM,EAAGA,CAAG,CAAC,EACrDF,EAAG,WAAWA,EAAG,IAAI,cAAe,EAAGE,EAAKC,CAAQ,CAAC,GAE/C,EACR,CACO,MAAA,EACR,CACD,CACD,CACD"}