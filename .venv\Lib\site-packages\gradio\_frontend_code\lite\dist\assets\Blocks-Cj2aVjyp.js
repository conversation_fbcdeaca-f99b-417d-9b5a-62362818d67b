import{a as ke,i as we,s as ye,Q as xe,q,$ as re,l as b,v as u,w as he,o as h,f as z,p as E,x as D,k as m,c as W,m as Z,F as Se,n as R,t as I,b as O,d as Q,A as Ve,ap as it,ao as oe,E as ue,r as tt,y as _e,z as pe,aq as qe,L as Nn,B as We,I as Ce,a3 as Ze,G as De,H as Be,bo as il,an as An,ar as sl,as as wt,a5 as Ge,a0 as ct,a4 as ut,a6 as _t,X as yt,Y as He,Z as rl,e as ol,u as al,h as fl,j as cl,b2 as ul,bp as _l,b4 as pl,b9 as ml,am as Ue,bq as dl,br as hl,aA as gl,bs as jt,bt as bl,bb as vl}from"../lite.js";import{B as st}from"./Button-kP2IUsdy.js";import{T as kl}from"./Toast-FkD9e-n8.js";import{G as wl}from"./utils-BsGrhMNe.js";import"./index-DCygRGWm.js";function yl(l){let e,t,n,i;return{c(){e=xe("svg"),t=xe("g"),n=xe("path"),i=xe("path"),q(n,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),q(i,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),q(e,"width","100%"),q(e,"height","100%"),q(e,"viewBox","0 0 5 5"),q(e,"version","1.1"),q(e,"xmlns","http://www.w3.org/2000/svg"),q(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),q(e,"xml:space","preserve"),re(e,"fill","currentColor"),re(e,"fill-rule","evenodd"),re(e,"clip-rule","evenodd"),re(e,"stroke-linejoin","round"),re(e,"stroke-miterlimit","2")},m(s,r){b(s,e,r),u(e,t),u(t,n),u(t,i)},p:he,i:he,o:he,d(s){s&&h(e)}}}class Ln extends ke{constructor(e){super(),we(this,e,null,yl,ye,{})}}function jl(l){let e,t,n,i,s,r,a,f,o,c,_,p,g,w,v;return p=new Ln({}),{c(){e=E("div"),t=E("h1"),t.textContent="API Docs",n=D(),i=E("p"),s=m(`No API Routes found for
		`),r=E("code"),a=m(l[0]),f=D(),o=E("p"),o.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,c=D(),_=E("button"),W(p.$$.fragment),q(r,"class","svelte-e1ha0f"),q(i,"class","attention svelte-e1ha0f"),q(e,"class","wrap prose svelte-e1ha0f"),q(_,"class","svelte-e1ha0f")},m(y,d){b(y,e,d),u(e,t),u(e,n),u(e,i),u(i,s),u(i,r),u(r,a),u(e,f),u(e,o),b(y,c,d),b(y,_,d),Z(p,_,null),g=!0,w||(v=Se(_,"click",l[2]),w=!0)},p(y,[d]){(!g||d&1)&&R(a,y[0])},i(y){g||(I(p.$$.fragment,y),g=!0)},o(y){O(p.$$.fragment,y),g=!1},d(y){y&&(h(e),h(c),h(_)),Q(p),w=!1,v()}}}function Cl(l,e,t){const n=Ve();let{root:i}=e;const s=()=>n("close");return l.$$set=r=>{"root"in r&&t(0,i=r.root)},[i,n,s]}class ql extends ke{constructor(e){super(),we(this,e,Cl,jl,ye,{root:0})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),z()}}const Tn="data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e";function El(l){let e,t,n;return{c(){e=E("div"),t=D(),n=E("p"),n.textContent="API Recorder",q(e,"class","loading-dot self-baseline svelte-1i1gjw2"),q(n,"class","self-baseline btn-text svelte-1i1gjw2")},m(i,s){b(i,e,s),b(i,t,s),b(i,n,s)},p:he,d(i){i&&(h(e),h(t),h(n))}}}function Ct(l){let e;return{c(){e=m("s")},m(t,n){b(t,e,n)},d(t){t&&h(e)}}}function $l(l){let e,t,n,i,s,r,a,f,o,c,_,p,g,w,v,y,d,N,C,j,T,k,$;_=new st({props:{size:"sm",variant:"secondary",elem_id:"start-api-recorder",$$slots:{default:[El]},$$scope:{ctx:l}}}),_.$on("click",l[3]);let F=l[1]>1&&Ct();return j=new Ln({}),{c(){e=E("h2"),t=E("img"),i=D(),s=E("div"),r=m(`API documentation
		`),a=E("div"),f=m(l[0]),o=D(),c=E("span"),W(_.$$.fragment),p=D(),g=E("p"),w=E("span"),v=m(l[1]),y=m(" API endpoint"),F&&F.c(),d=E("br"),N=D(),C=E("button"),W(j.$$.fragment),it(t.src,n=Tn)||q(t,"src",n),q(t,"alt",""),q(t,"class","svelte-1i1gjw2"),q(a,"class","url svelte-1i1gjw2"),q(s,"class","title svelte-1i1gjw2"),q(w,"class","url svelte-1i1gjw2"),q(c,"class","counts svelte-1i1gjw2"),q(e,"class","svelte-1i1gjw2"),q(C,"class","svelte-1i1gjw2")},m(S,A){b(S,e,A),u(e,t),u(e,i),u(e,s),u(s,r),u(s,a),u(a,f),u(e,o),u(e,c),Z(_,c,null),u(c,p),u(c,g),u(g,w),u(w,v),u(g,y),F&&F.m(g,null),u(g,d),b(S,N,A),b(S,C,A),Z(j,C,null),T=!0,k||($=Se(C,"click",l[4]),k=!0)},p(S,[A]){(!T||A&1)&&R(f,S[0]);const P={};A&32&&(P.$$scope={dirty:A,ctx:S}),_.$set(P),(!T||A&2)&&R(v,S[1]),S[1]>1?F||(F=Ct(),F.c(),F.m(g,d)):F&&(F.d(1),F=null)},i(S){T||(I(_.$$.fragment,S),I(j.$$.fragment,S),T=!0)},o(S){O(_.$$.fragment,S),O(j.$$.fragment,S),T=!1},d(S){S&&(h(e),h(N),h(C)),Q(_),F&&F.d(),Q(j),k=!1,$()}}}function Nl(l,e,t){let{root:n}=e,{api_count:i}=e;const s=Ve(),r=()=>s("close",{api_recorder_visible:!0}),a=()=>s("close");return l.$$set=f=>{"root"in f&&t(0,n=f.root),"api_count"in f&&t(1,i=f.api_count)},[n,i,s,r,a]}class Al extends ke{constructor(e){super(),we(this,e,Nl,$l,ye,{root:0,api_count:1})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),z()}get api_count(){return this.$$.ctx[1]}set api_count(e){this.$$set({api_count:e}),z()}}function je(l,e,t=null){return e===void 0?t==="py"?"None":null:l===null&&t==="py"?"None":e==="string"||e==="str"?t===null?l:'"'+l+'"':e==="number"?t===null?parseFloat(l):l:e==="boolean"||e=="bool"?t==="py"?(l=String(l),l==="true"?"True":"False"):t==="js"||t==="bash"?l:l==="true":e==="List[str]"?(l=JSON.stringify(l),l):e.startsWith("Literal['")?'"'+l+'"':t===null?l===""?null:JSON.parse(l):typeof l=="string"?l===""?t==="py"?"None":"null":l:(t==="bash"&&(l=pt(l)),t==="py"&&(l=mt(l)),Ll(l))}function zn(l){if(typeof l=="object"&&l!==null&&l.hasOwnProperty("url")&&l.hasOwnProperty("meta")&&typeof l.meta=="object"&&l.meta!==null&&l.meta._type==="gradio.FileData")return!0;if(typeof l=="object"&&l!==null){for(let e in l)if(typeof l[e]=="object"&&zn(l[e]))return!0}return!1}function pt(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?{path:l.url}:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=pt(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=pt(l[e])}),l)}function mt(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?`handle_file('${l.url}')`:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=mt(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=mt(l[e])}),l)}function Ll(l){let e=JSON.stringify(l,(i,s)=>s===null?"UNQUOTEDNone":typeof s=="string"&&s.startsWith("handle_file(")&&s.endsWith(")")?`UNQUOTED${s}`:s);const t=/"UNQUOTEDhandle_file\(([^)]*)\)"/g;e=e.replace(t,(i,s)=>`handle_file(${s})`);const n=/"UNQUOTEDNone"/g;return e.replace(n,"None")}function qt(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].python_type,n[6]=e[t].component,n[7]=e[t].parameter_name,n[8]=e[t].parameter_has_default,n[9]=e[t].parameter_default,n[11]=t,n}function Et(l){let e;return{c(){e=m("s")},m(t,n){b(t,e,n)},d(t){t&&h(e)}}}function Tl(l){let e=(l[2][l[11]].type||"any")+"",t;return{c(){t=m(e)},m(n,i){b(n,t,i)},p(n,i){i&4&&e!==(e=(n[2][n[11]].type||"any")+"")&&R(t,e)},d(n){n&&h(t)}}}function zl(l){let e=l[5].type+"",t,n,i=l[8]&&l[9]===null&&$t();return{c(){t=m(e),i&&i.c(),n=ue()},m(s,r){b(s,t,r),i&&i.m(s,r),b(s,n,r)},p(s,r){r&2&&e!==(e=s[5].type+"")&&R(t,e),s[8]&&s[9]===null?i||(i=$t(),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},d(s){s&&(h(t),h(n)),i&&i.d(s)}}}function $t(l){let e;return{c(){e=m(` |
							None`)},m(t,n){b(t,e,n)},d(t){t&&h(e)}}}function Il(l){let e,t,n=je(l[9],l[5].type,"py")+"",i;return{c(){e=E("span"),e.textContent="Default: ",t=E("span"),i=m(n),q(t,"class","code svelte-1yt946s"),re(t,"font-size","var(--text-sm)")},m(s,r){b(s,e,r),b(s,t,r),u(t,i)},p(s,r){r&2&&n!==(n=je(s[9],s[5].type,"py")+"")&&R(i,n)},d(s){s&&(h(e),h(t))}}}function Sl(l){let e;return{c(){e=E("span"),e.textContent="Required",re(e,"font-weight","bold")},m(t,n){b(t,e,n)},p:he,d(t){t&&h(e)}}}function Nt(l){let e,t,n,i,s,r=(l[3]!=="bash"&&l[7]?l[7]:"["+l[11]+"]")+"",a,f,o,c,_,p,g,w=l[4]+"",v,y,d=l[6]+"",N,C,j=l[5].description+"",T,k;function $(M,B){return M[3]==="python"?zl:Tl}let F=$(l),S=F(l);function A(M,B){return!M[8]||M[3]=="bash"?Sl:Il}let P=A(l),U=P(l);return{c(){e=E("hr"),t=D(),n=E("div"),i=E("p"),s=E("span"),a=m(r),f=D(),o=E("span"),S.c(),c=D(),U.c(),_=D(),p=E("p"),g=m('The input value that is provided in the "'),v=m(w),y=m('" '),N=m(d),C=m(`
				component. `),T=m(j),k=D(),q(e,"class","hr svelte-1yt946s"),q(s,"class","code svelte-1yt946s"),re(s,"margin-right","10px"),q(o,"class","code highlight svelte-1yt946s"),re(o,"margin-right","10px"),re(i,"white-space","nowrap"),re(i,"overflow-x","auto"),q(p,"class","desc svelte-1yt946s"),re(n,"margin","10px")},m(M,B){b(M,e,B),b(M,t,B),b(M,n,B),u(n,i),u(i,s),u(s,a),u(i,f),u(i,o),S.m(o,null),u(i,c),U.m(i,null),u(n,_),u(n,p),u(p,g),u(p,v),u(p,y),u(p,N),u(p,C),u(p,T),u(n,k)},p(M,B){B&10&&r!==(r=(M[3]!=="bash"&&M[7]?M[7]:"["+M[11]+"]")+"")&&R(a,r),F===(F=$(M))&&S?S.p(M,B):(S.d(1),S=F(M),S&&(S.c(),S.m(o,null))),P===(P=A(M))&&U?U.p(M,B):(U.d(1),U=P(M),U&&(U.c(),U.m(i,null))),B&2&&w!==(w=M[4]+"")&&R(v,w),B&2&&d!==(d=M[6]+"")&&R(N,d),B&2&&j!==(j=M[5].description+"")&&R(T,j)},d(M){M&&(h(e),h(t),h(n)),S.d(),U.d()}}}function At(l){let e,t,n;return t=new Nn({props:{margin:!1}}),{c(){e=E("div"),W(t.$$.fragment),q(e,"class","load-wrap")},m(i,s){b(i,e,s),Z(t,e,null),n=!0},i(i){n||(I(t.$$.fragment,i),n=!0)},o(i){O(t.$$.fragment,i),n=!1},d(i){i&&h(e),Q(t)}}}function Ml(l){let e,t,n,i=l[1].length+"",s,r,a,f,o,c,_,p,g=l[1].length!=1&&Et(),w=oe(l[1]),v=[];for(let d=0;d<w.length;d+=1)v[d]=Nt(qt(l,w,d));let y=l[0]&&At();return{c(){e=E("h4"),t=E("div"),t.innerHTML='<div class="toggle-dot svelte-1yt946s"></div>',n=m(`
	Accepts `),s=m(i),r=m(" parameter"),g&&g.c(),a=m(":"),f=D(),o=E("div");for(let d=0;d<v.length;d+=1)v[d].c();c=D(),y&&y.c(),_=ue(),q(t,"class","toggle-icon svelte-1yt946s"),q(e,"class","svelte-1yt946s"),tt(o,"hide",l[0])},m(d,N){b(d,e,N),u(e,t),u(e,n),u(e,s),u(e,r),g&&g.m(e,null),u(e,a),b(d,f,N),b(d,o,N);for(let C=0;C<v.length;C+=1)v[C]&&v[C].m(o,null);b(d,c,N),y&&y.m(d,N),b(d,_,N),p=!0},p(d,[N]){if((!p||N&2)&&i!==(i=d[1].length+"")&&R(s,i),d[1].length!=1?g||(g=Et(),g.c(),g.m(e,a)):g&&(g.d(1),g=null),N&14){w=oe(d[1]);let C;for(C=0;C<w.length;C+=1){const j=qt(d,w,C);v[C]?v[C].p(j,N):(v[C]=Nt(j),v[C].c(),v[C].m(o,null))}for(;C<v.length;C+=1)v[C].d(1);v.length=w.length}(!p||N&1)&&tt(o,"hide",d[0]),d[0]?y?N&1&&I(y,1):(y=At(),y.c(),I(y,1),y.m(_.parentNode,_)):y&&(_e(),O(y,1,1,()=>{y=null}),pe())},i(d){p||(I(y),p=!0)},o(d){O(y),p=!1},d(d){d&&(h(e),h(f),h(o),h(c),h(_)),g&&g.d(),qe(v,d),y&&y.d(d)}}}function Pl(l,e,t){let{is_running:n}=e,{endpoint_returns:i}=e,{js_returns:s}=e,{current_language:r}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,i=a.endpoint_returns),"js_returns"in a&&t(2,s=a.js_returns),"current_language"in a&&t(3,r=a.current_language)},[n,i,s,r]}class Ol extends ke{constructor(e){super(),we(this,e,Pl,Ml,ye,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),z()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),z()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),z()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),z()}}function Fl(l){let e;return{c(){e=m(l[0])},m(t,n){b(t,e,n)},p(t,n){n&1&&R(e,t[0])},d(t){t&&h(e)}}}function Ul(l){let e,t;return e=new st({props:{size:"sm",$$slots:{default:[Fl]},$$scope:{ctx:l}}}),e.$on("click",l[1]),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,[i]){const s={};i&9&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function Dl(l,e,t){let{code:n}=e,i="copy";function s(){navigator.clipboard.writeText(n),t(0,i="copied!"),setTimeout(()=>{t(0,i="copy")},1500)}return l.$$set=r=>{"code"in r&&t(2,n=r.code)},[i,s,n]}class Le extends ke{constructor(e){super(),we(this,e,Dl,Ul,ye,{code:2})}get code(){return this.$$.ctx[2]}set code(e){this.$$set({code:e}),z()}}function Rl(l){let e,t,n,i,s,r;return t=new Le({props:{code:zt}}),{c(){e=E("div"),W(t.$$.fragment),n=D(),i=E("div"),s=E("pre"),s.textContent=`$ ${zt}`,q(e,"class","copy svelte-hq8ezf"),q(s,"class","svelte-hq8ezf")},m(a,f){b(a,e,f),Z(t,e,null),b(a,n,f),b(a,i,f),u(i,s),r=!0},p:he,i(a){r||(I(t.$$.fragment,a),r=!0)},o(a){O(t.$$.fragment,a),r=!1},d(a){a&&(h(e),h(n),h(i)),Q(t)}}}function Bl(l){let e,t,n,i,s,r;return t=new Le({props:{code:Tt}}),{c(){e=E("div"),W(t.$$.fragment),n=D(),i=E("div"),s=E("pre"),s.textContent=`$ ${Tt}`,q(e,"class","copy svelte-hq8ezf"),q(s,"class","svelte-hq8ezf")},m(a,f){b(a,e,f),Z(t,e,null),b(a,n,f),b(a,i,f),u(i,s),r=!0},p:he,i(a){r||(I(t.$$.fragment,a),r=!0)},o(a){O(t.$$.fragment,a),r=!1},d(a){a&&(h(e),h(n),h(i)),Q(t)}}}function Gl(l){let e,t,n,i,s,r;return t=new Le({props:{code:Lt}}),{c(){e=E("div"),W(t.$$.fragment),n=D(),i=E("div"),s=E("pre"),s.textContent=`$ ${Lt}`,q(e,"class","copy svelte-hq8ezf"),q(s,"class","svelte-hq8ezf")},m(a,f){b(a,e,f),Z(t,e,null),b(a,n,f),b(a,i,f),u(i,s),r=!0},p:he,i(a){r||(I(t.$$.fragment,a),r=!0)},o(a){O(t.$$.fragment,a),r=!1},d(a){a&&(h(e),h(n),h(i)),Q(t)}}}function Hl(l){let e,t,n,i;const s=[Gl,Bl,Rl],r=[];function a(f,o){return f[0]==="python"?0:f[0]==="javascript"?1:f[0]==="bash"?2:-1}return~(t=a(l))&&(n=r[t]=s[t](l)),{c(){e=E("code"),n&&n.c(),q(e,"class","svelte-hq8ezf")},m(f,o){b(f,e,o),~t&&r[t].m(e,null),i=!0},p(f,o){let c=t;t=a(f),t===c?~t&&r[t].p(f,o):(n&&(_e(),O(r[c],1,1,()=>{r[c]=null}),pe()),~t?(n=r[t],n?n.p(f,o):(n=r[t]=s[t](f),n.c()),I(n,1),n.m(e,null)):n=null)},i(f){i||(I(n),i=!0)},o(f){O(n),i=!1},d(f){f&&h(e),~t&&r[t].d()}}}function Vl(l){let e,t;return e=new We({props:{border_mode:"contrast",$$slots:{default:[Hl]},$$scope:{ctx:l}}}),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,[i]){const s={};i&3&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}let Lt="pip install gradio_client",Tt="npm i -D @gradio/client",zt="curl --version";function Wl(l,e,t){let{current_language:n}=e;return l.$$set=i=>{"current_language"in i&&t(0,n=i.current_language)},[n]}class Zl extends ke{constructor(e){super(),we(this,e,Wl,Vl,ye,{current_language:0})}get current_language(){return this.$$.ctx[0]}set current_language(e){this.$$set({current_language:e}),z()}}function Ql(l){let e,t,n,i;return{c(){e=E("h3"),t=m(`fn_index:
		`),n=E("span"),i=m(l[1]),q(n,"class","post svelte-41kcm6"),q(e,"class","svelte-41kcm6")},m(s,r){b(s,e,r),u(e,t),u(e,n),u(n,i)},p(s,r){r&2&&R(i,s[1])},d(s){s&&h(e)}}}function Jl(l){let e,t,n,i="/"+l[0],s;return{c(){e=E("h3"),t=m(`api_name:
		`),n=E("span"),s=m(i),q(n,"class","post svelte-41kcm6"),q(e,"class","svelte-41kcm6")},m(r,a){b(r,e,a),u(e,t),u(e,n),u(n,s)},p(r,a){a&1&&i!==(i="/"+r[0])&&R(s,i)},d(r){r&&h(e)}}}function Yl(l){let e;function t(s,r){return s[2]?Jl:Ql}let n=t(l),i=n(l);return{c(){i.c(),e=ue()},m(s,r){i.m(s,r),b(s,e,r)},p(s,[r]){n===(n=t(s))&&i?i.p(s,r):(i.d(1),i=n(s),i&&(i.c(),i.m(e.parentNode,e)))},i:he,o:he,d(s){s&&h(e),i.d(s)}}}function Xl(l,e,t){let{api_name:n=null}=e,{fn_index:i=null}=e,{named:s}=e;return l.$$set=r=>{"api_name"in r&&t(0,n=r.api_name),"fn_index"in r&&t(1,i=r.fn_index),"named"in r&&t(2,s=r.named)},[n,i,s]}class In extends ke{constructor(e){super(),we(this,e,Xl,Yl,ye,{api_name:0,fn_index:1,named:2})}get api_name(){return this.$$.ctx[0]}set api_name(e){this.$$set({api_name:e}),z()}get fn_index(){return this.$$.ctx[1]}set fn_index(e){this.$$set({fn_index:e}),z()}get named(){return this.$$.ctx[2]}set named(e){this.$$set({named:e}),z()}}function It(l,e,t){const n=l.slice();return n[28]=e[t].label,n[23]=e[t].parameter_name,n[29]=e[t].type,n[21]=e[t].python_type,n[30]=e[t].component,n[22]=e[t].example_input,n[31]=e[t].serializer,n[27]=t,n}function St(l,e,t){const n=l.slice();return n[28]=e[t].label,n[23]=e[t].parameter_name,n[29]=e[t].type,n[21]=e[t].python_type,n[30]=e[t].component,n[22]=e[t].example_input,n[31]=e[t].serializer,n[27]=t,n}function Mt(l,e,t){const n=l.slice();return n[30]=e[t].component,n[22]=e[t].example_input,n[27]=t,n}function Pt(l,e,t){const n=l.slice();return n[21]=e[t].python_type,n[22]=e[t].example_input,n[23]=e[t].parameter_name,n[24]=e[t].parameter_has_default,n[25]=e[t].parameter_default,n[27]=t,n}function Kl(l){let e,t;return e=new In({props:{named:l[5],fn_index:l[1]}}),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i[0]&32&&(s.named=n[5]),i[0]&2&&(s.fn_index=n[1]),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function xl(l){let e,t;return e=new In({props:{named:l[5],api_name:l[0].api_name}}),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i[0]&32&&(s.named=n[5]),i[0]&1&&(s.api_name=n[0].api_name),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function ei(l){let e,t;return e=new We({props:{$$slots:{default:[li]},$$scope:{ctx:l}}}),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i[0]&7185|i[1]&16&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function ti(l){let e,t;return e=new We({props:{$$slots:{default:[ai]},$$scope:{ctx:l}}}),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i[0]&639|i[1]&16&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function ni(l){let e,t;return e=new We({props:{$$slots:{default:[ci]},$$scope:{ctx:l}}}),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i[0]&349|i[1]&16&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function Ot(l){let e;return{c(){e=m(",")},m(t,n){b(t,e,n)},d(t){t&&h(e)}}}function Ft(l){let e,t=je(l[22],l[21].type,"bash")+"",n,i,s=l[27]<l[4].length-1&&Ot();return{c(){e=m(`
							`),n=m(t),s&&s.c(),i=ue()},m(r,a){b(r,e,a),b(r,n,a),s&&s.m(r,a),b(r,i,a)},p(r,a){a[0]&16&&t!==(t=je(r[22],r[21].type,"bash")+"")&&R(n,t),r[27]<r[4].length-1?s||(s=Ot(),s.c(),s.m(i.parentNode,i)):s&&(s.d(1),s=null)},d(r){r&&(h(e),h(n),h(i)),s&&s.d(r)}}}function li(l){let e,t,n,i,s,r,a,f,o,c,_=l[0].api_name+"",p,g,w="{",v,y,d,N="}",C,j,T="{",k,$,F="}",S,A,P,U,M,B=l[0].api_name+"",X,ge,x;n=new Le({props:{code:l[10]?.innerText}});let Y=oe(l[4]),ne=[];for(let J=0;J<Y.length;J+=1)ne[J]=Ft(It(l,Y,J));return{c(){e=E("code"),t=E("div"),W(n.$$.fragment),i=D(),s=E("div"),r=E("pre"),a=m("curl -X POST "),f=m(l[11]),o=m(l[12]),c=m("/call/"),p=m(_),g=m(` -s -H "Content-Type: application/json" -d '`),v=m(w),y=m(`
  "data": [`);for(let J=0;J<ne.length;J+=1)ne[J].c();d=m(`
]`),C=m(N),j=m(`' \\
  | awk -F'"' '`),k=m(T),$=m(" print $4"),S=m(F),A=m(`'  \\
  | read EVENT_ID; curl -N `),P=m(l[11]),U=m(l[12]),M=m("/call/"),X=m(B),ge=m("/$EVENT_ID"),q(t,"class","copy svelte-114qcyq"),q(r,"class","svelte-114qcyq"),q(e,"class","svelte-114qcyq")},m(J,se){b(J,e,se),u(e,t),Z(n,t,null),u(e,i),u(e,s),u(s,r),u(r,a),u(r,f),u(r,o),u(r,c),u(r,p),u(r,g),u(r,v),u(r,y);for(let me=0;me<ne.length;me+=1)ne[me]&&ne[me].m(r,null);u(r,d),u(r,C),u(r,j),u(r,k),u(r,$),u(r,S),u(r,A),u(r,P),u(r,U),u(r,M),u(r,X),u(r,ge),l[19](s),x=!0},p(J,se){const me={};if(se[0]&1024&&(me.code=J[10]?.innerText),n.$set(me),(!x||se[0]&2048)&&R(f,J[11]),(!x||se[0]&4096)&&R(o,J[12]),(!x||se[0]&1)&&_!==(_=J[0].api_name+"")&&R(p,_),se[0]&16){Y=oe(J[4]);let be;for(be=0;be<Y.length;be+=1){const Qe=It(J,Y,be);ne[be]?ne[be].p(Qe,se):(ne[be]=Ft(Qe),ne[be].c(),ne[be].m(r,d))}for(;be<ne.length;be+=1)ne[be].d(1);ne.length=Y.length}(!x||se[0]&2048)&&R(P,J[11]),(!x||se[0]&4096)&&R(U,J[12]),(!x||se[0]&1)&&B!==(B=J[0].api_name+"")&&R(X,B)},i(J){x||(I(n.$$.fragment,J),x=!0)},o(J){O(n.$$.fragment,J),x=!1},d(J){J&&h(e),Q(n),qe(ne,J),l[19](null)}}}function Ut(l){let e,t,n,i=l[22].url+"",s,r,a=l[30]+"",f,o,c,_;return{c(){e=m(`
const response_`),t=m(l[27]),n=m(' = await fetch("'),s=m(i),r=m(`");
const example`),f=m(a),o=m(" = await response_"),c=m(l[27]),_=m(`.blob();
						`)},m(p,g){b(p,e,g),b(p,t,g),b(p,n,g),b(p,s,g),b(p,r,g),b(p,f,g),b(p,o,g),b(p,c,g),b(p,_,g)},p:he,d(p){p&&(h(e),h(t),h(n),h(s),h(r),h(f),h(o),h(c),h(_))}}}function Dt(l){let e,t,n;return{c(){e=m(', {auth: ["'),t=m(l[6]),n=m('", **password**]}')},m(i,s){b(i,e,s),b(i,t,s),b(i,n,s)},p(i,s){s[0]&64&&R(t,i[6])},d(i){i&&(h(e),h(t),h(n))}}}function ii(l){let e;return{c(){e=m(l[1])},m(t,n){b(t,e,n)},p(t,n){n[0]&2&&R(e,t[1])},d(t){t&&h(e)}}}function si(l){let e,t,n=l[0].api_name+"",i,s;return{c(){e=E("span"),t=m('"/'),i=m(n),s=m('"'),q(e,"class","api-name svelte-114qcyq")},m(r,a){b(r,e,a),u(e,t),u(e,i),u(e,s)},p(r,a){a[0]&1&&n!==(n=r[0].api_name+"")&&R(i,n)},d(r){r&&h(e)}}}function ri(l){let e,t,n=l[23]+"",i,s,r=je(l[22],l[21].type,"js")+"",a,f;return{c(){e=m(`		
		`),t=E("span"),i=m(n),s=m(": "),a=m(r),f=m(", "),q(t,"class","example-inputs")},m(o,c){b(o,e,c),b(o,t,c),u(t,i),u(t,s),u(t,a),b(o,f,c)},p(o,c){c[0]&16&&n!==(n=o[23]+"")&&R(i,n),c[0]&16&&r!==(r=je(o[22],o[21].type,"js")+"")&&R(a,r)},d(o){o&&(h(e),h(t),h(f))}}}function oi(l){let e,t,n=l[23]+"",i,s,r=l[30]+"",a,f,o;return{c(){e=m(`
				`),t=E("span"),i=m(n),s=m(": example"),a=m(r),f=m(", "),o=E("span"),o.innerHTML="",q(t,"class","example-inputs"),q(o,"class","desc svelte-114qcyq")},m(c,_){b(c,e,_),b(c,t,_),u(t,i),u(t,s),u(t,a),b(c,f,_),b(c,o,_)},p(c,_){_[0]&16&&n!==(n=c[23]+"")&&R(i,n),_[0]&16&&r!==(r=c[30]+"")&&R(a,r)},d(c){c&&(h(e),h(t),h(f),h(o))}}}function Rt(l){let e,t;function n(r,a){return a[0]&16&&(e=null),e==null&&(e=!!r[14].includes(r[30])),e?oi:ri}let i=n(l,[-1,-1]),s=i(l);return{c(){s.c(),t=ue()},m(r,a){s.m(r,a),b(r,t,a)},p(r,a){i===(i=n(r,a))&&s?s.p(r,a):(s.d(1),s=i(r),s&&(s.c(),s.m(t.parentNode,t)))},d(r){r&&h(t),s.d(r)}}}function ai(l){let e,t,n,i,s,r,a,f,o,c,_=(l[3]||l[2])+"",p,g,w,v,y,d;n=new Le({props:{code:l[9]?.innerText}});let N=oe(l[15]),C=[];for(let A=0;A<N.length;A+=1)C[A]=Ut(Mt(l,N,A));let j=l[6]!==null&&Dt(l);function T(A,P){return A[5]?si:ii}let k=T(l),$=k(l),F=oe(l[4]),S=[];for(let A=0;A<F.length;A+=1)S[A]=Rt(St(l,F,A));return{c(){e=E("code"),t=E("div"),W(n.$$.fragment),i=D(),s=E("div"),r=E("pre"),a=m(`import { Client } from "@gradio/client";
`);for(let A=0;A<C.length;A+=1)C[A].c();f=m(`
const client = await Client.connect(`),o=E("span"),c=m('"'),p=m(_),g=m('"'),j&&j.c(),w=m(`);
const result = await client.predict(`),$.c(),v=m(", { ");for(let A=0;A<S.length;A+=1)S[A].c();y=m(`
});

console.log(result.data);
`),q(t,"class","copy svelte-114qcyq"),q(o,"class","token string svelte-114qcyq"),q(r,"class","svelte-114qcyq"),q(e,"class","svelte-114qcyq")},m(A,P){b(A,e,P),u(e,t),Z(n,t,null),u(e,i),u(e,s),u(s,r),u(r,a);for(let U=0;U<C.length;U+=1)C[U]&&C[U].m(r,null);u(r,f),u(r,o),u(o,c),u(o,p),u(o,g),j&&j.m(r,null),u(r,w),$.m(r,null),u(r,v);for(let U=0;U<S.length;U+=1)S[U]&&S[U].m(r,null);u(r,y),l[18](s),d=!0},p(A,P){const U={};if(P[0]&512&&(U.code=A[9]?.innerText),n.$set(U),P[0]&32768){N=oe(A[15]);let M;for(M=0;M<N.length;M+=1){const B=Mt(A,N,M);C[M]?C[M].p(B,P):(C[M]=Ut(B),C[M].c(),C[M].m(r,f))}for(;M<C.length;M+=1)C[M].d(1);C.length=N.length}if((!d||P[0]&12)&&_!==(_=(A[3]||A[2])+"")&&R(p,_),A[6]!==null?j?j.p(A,P):(j=Dt(A),j.c(),j.m(r,w)):j&&(j.d(1),j=null),k===(k=T(A))&&$?$.p(A,P):($.d(1),$=k(A),$&&($.c(),$.m(r,v))),P[0]&16400){F=oe(A[4]);let M;for(M=0;M<F.length;M+=1){const B=St(A,F,M);S[M]?S[M].p(B,P):(S[M]=Rt(B),S[M].c(),S[M].m(r,y))}for(;M<S.length;M+=1)S[M].d(1);S.length=F.length}},i(A){d||(I(n.$$.fragment,A),d=!0)},o(A){O(n.$$.fragment,A),d=!1},d(A){A&&h(e),Q(n),qe(C,A),j&&j.d(),$.d(),qe(S,A),l[18](null)}}}function fi(l){let e;return{c(){e=m(", handle_file")},m(t,n){b(t,e,n)},d(t){t&&h(e)}}}function Bt(l){let e,t,n;return{c(){e=m(', auth=("'),t=m(l[6]),n=m('", **password**)')},m(i,s){b(i,e,s),b(i,t,s),b(i,n,s)},p(i,s){s[0]&64&&R(t,i[6])},d(i){i&&(h(e),h(t),h(n))}}}function Gt(l){let e,t=l[23]?l[23]+"=":"",n,i,s=je(l[24]?l[25]:l[22],l[21].type,"py")+"",r,a;return{c(){e=m(`
		`),n=m(t),i=E("span"),r=m(s),a=m(",")},m(f,o){b(f,e,o),b(f,n,o),b(f,i,o),u(i,r),b(f,a,o)},p(f,o){o[0]&16&&t!==(t=f[23]?f[23]+"=":"")&&R(n,t),o[0]&16&&s!==(s=je(f[24]?f[25]:f[22],f[21].type,"py")+"")&&R(r,s)},d(f){f&&(h(e),h(n),h(i),h(a))}}}function ci(l){let e,t,n,i,s,r,a,f,o,c,_,p,g,w=(l[3]||l[2])+"",v,y,d,N,C,j,T,k,$=l[0].api_name+"",F,S,A,P,U,M;n=new Le({props:{code:l[8]?.innerText}});let B=l[13]&&fi(),X=l[6]!==null&&Bt(l),ge=oe(l[4]),x=[];for(let Y=0;Y<ge.length;Y+=1)x[Y]=Gt(Pt(l,ge,Y));return{c(){e=E("code"),t=E("div"),W(n.$$.fragment),i=D(),s=E("div"),r=E("pre"),a=E("span"),a.textContent="from",f=m(" gradio_client "),o=E("span"),o.textContent="import",c=m(" Client"),B&&B.c(),_=m(`

client = Client(`),p=E("span"),g=m('"'),v=m(w),y=m('"'),X&&X.c(),d=m(`)
result = client.`),N=E("span"),N.textContent="predict",C=m("(");for(let Y=0;Y<x.length;Y+=1)x[Y].c();j=m(`
		api_name=`),T=E("span"),k=m('"/'),F=m($),S=m('"'),A=m(`
)
`),P=E("span"),P.textContent="print",U=m("(result)"),q(t,"class","copy svelte-114qcyq"),q(a,"class","highlight"),q(o,"class","highlight"),q(p,"class","token string svelte-114qcyq"),q(N,"class","highlight"),q(T,"class","api-name svelte-114qcyq"),q(P,"class","highlight"),q(r,"class","svelte-114qcyq"),q(e,"class","svelte-114qcyq")},m(Y,ne){b(Y,e,ne),u(e,t),Z(n,t,null),u(e,i),u(e,s),u(s,r),u(r,a),u(r,f),u(r,o),u(r,c),B&&B.m(r,null),u(r,_),u(r,p),u(p,g),u(p,v),u(p,y),X&&X.m(r,null),u(r,d),u(r,N),u(r,C);for(let J=0;J<x.length;J+=1)x[J]&&x[J].m(r,null);u(r,j),u(r,T),u(T,k),u(T,F),u(T,S),u(r,A),u(r,P),u(r,U),l[17](s),M=!0},p(Y,ne){const J={};if(ne[0]&256&&(J.code=Y[8]?.innerText),n.$set(J),(!M||ne[0]&12)&&w!==(w=(Y[3]||Y[2])+"")&&R(v,w),Y[6]!==null?X?X.p(Y,ne):(X=Bt(Y),X.c(),X.m(r,d)):X&&(X.d(1),X=null),ne[0]&16){ge=oe(Y[4]);let se;for(se=0;se<ge.length;se+=1){const me=Pt(Y,ge,se);x[se]?x[se].p(me,ne):(x[se]=Gt(me),x[se].c(),x[se].m(r,j))}for(;se<x.length;se+=1)x[se].d(1);x.length=ge.length}(!M||ne[0]&1)&&$!==($=Y[0].api_name+"")&&R(F,$)},i(Y){M||(I(n.$$.fragment,Y),M=!0)},o(Y){O(n.$$.fragment,Y),M=!1},d(Y){Y&&h(e),Q(n),B&&B.d(),X&&X.d(),qe(x,Y),l[17](null)}}}function ui(l){let e,t,n,i,s,r,a;const f=[xl,Kl],o=[];function c(w,v){return w[5]?0:1}t=c(l),n=o[t]=f[t](l);const _=[ni,ti,ei],p=[];function g(w,v){return w[7]==="python"?0:w[7]==="javascript"?1:w[7]==="bash"?2:-1}return~(s=g(l))&&(r=p[s]=_[s](l)),{c(){e=E("div"),n.c(),i=D(),r&&r.c(),q(e,"class","container svelte-114qcyq")},m(w,v){b(w,e,v),o[t].m(e,null),u(e,i),~s&&p[s].m(e,null),a=!0},p(w,v){let y=t;t=c(w),t===y?o[t].p(w,v):(_e(),O(o[y],1,1,()=>{o[y]=null}),pe(),n=o[t],n?n.p(w,v):(n=o[t]=f[t](w),n.c()),I(n,1),n.m(e,i));let d=s;s=g(w),s===d?~s&&p[s].p(w,v):(r&&(_e(),O(p[d],1,1,()=>{p[d]=null}),pe()),~s?(r=p[s],r?r.p(w,v):(r=p[s]=_[s](w),r.c()),I(r,1),r.m(e,null)):r=null)},i(w){a||(I(n),I(r),a=!0)},o(w){O(n),O(r),a=!1},d(w){w&&h(e),o[t].d(),~s&&p[s].d()}}}function _i(l,e,t){let n,i,{dependency:s}=e,{dependency_index:r}=e,{root:a}=e,{api_prefix:f}=e,{space_id:o}=e,{endpoint_parameters:c}=e,{named:_}=e,{username:p}=e,{current_language:g}=e,w,v,y,d=c.some($=>zn($.example_input)),N=["Audio","File","Image","Video"],C=c.filter($=>N.includes($.component));function j($){Ce[$?"unshift":"push"](()=>{w=$,t(8,w)})}function T($){Ce[$?"unshift":"push"](()=>{v=$,t(9,v)})}function k($){Ce[$?"unshift":"push"](()=>{y=$,t(10,y)})}return l.$$set=$=>{"dependency"in $&&t(0,s=$.dependency),"dependency_index"in $&&t(1,r=$.dependency_index),"root"in $&&t(2,a=$.root),"api_prefix"in $&&t(16,f=$.api_prefix),"space_id"in $&&t(3,o=$.space_id),"endpoint_parameters"in $&&t(4,c=$.endpoint_parameters),"named"in $&&t(5,_=$.named),"username"in $&&t(6,p=$.username),"current_language"in $&&t(7,g=$.current_language)},l.$$.update=()=>{l.$$.dirty[0]&65536&&t(12,n=f||"/"),l.$$.dirty[0]&4&&t(11,i=a.replace(/\/$/,""))},[s,r,a,o,c,_,p,g,w,v,y,i,n,d,N,C,f,j,T,k]}class pi extends ke{constructor(e){super(),we(this,e,_i,ui,ye,{dependency:0,dependency_index:1,root:2,api_prefix:16,space_id:3,endpoint_parameters:4,named:5,username:6,current_language:7},null,[-1,-1])}get dependency(){return this.$$.ctx[0]}set dependency(e){this.$$set({dependency:e}),z()}get dependency_index(){return this.$$.ctx[1]}set dependency_index(e){this.$$set({dependency_index:e}),z()}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),z()}get api_prefix(){return this.$$.ctx[16]}set api_prefix(e){this.$$set({api_prefix:e}),z()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),z()}get endpoint_parameters(){return this.$$.ctx[4]}set endpoint_parameters(e){this.$$set({endpoint_parameters:e}),z()}get named(){return this.$$.ctx[5]}set named(e){this.$$set({named:e}),z()}get username(){return this.$$.ctx[6]}set username(e){this.$$set({username:e}),z()}get current_language(){return this.$$.ctx[7]}set current_language(e){this.$$set({current_language:e}),z()}}function Ht(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function Vt(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function Wt(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function mi(l){let e,t,n,i,s,r;n=new Le({props:{code:l[6]?.innerText}});let a=oe(l[9]),f=[];for(let o=0;o<a.length;o+=1)f[o]=Zt(Ht(l,a,o));return{c(){e=E("code"),t=E("div"),W(n.$$.fragment),i=D(),s=E("div");for(let o=0;o<f.length;o+=1)f[o].c();q(t,"class","copy svelte-j71ub0"),q(e,"class","svelte-j71ub0")},m(o,c){b(o,e,c),u(e,t),Z(n,t,null),u(e,i),u(e,s);for(let _=0;_<f.length;_+=1)f[_]&&f[_].m(s,null);l[16](s),r=!0},p(o,c){const _={};if(c&64&&(_.code=o[6]?.innerText),n.$set(_),c&513){a=oe(o[9]);let p;for(p=0;p<a.length;p+=1){const g=Ht(o,a,p);f[p]?f[p].p(g,c):(f[p]=Zt(g),f[p].c(),f[p].m(s,null))}for(;p<f.length;p+=1)f[p].d(1);f.length=a.length}},i(o){r||(I(n.$$.fragment,o),r=!0)},o(o){O(n.$$.fragment,o),r=!1},d(o){o&&h(e),Q(n),qe(f,o),l[16](null)}}}function di(l){let e,t,n,i,s,r,a,f,o,c,_,p,g;n=new Le({props:{code:l[5]?.innerText}});let w=l[2]!==null&&Qt(l),v=oe(l[8]),y=[];for(let d=0;d<v.length;d+=1)y[d]=Yt(Vt(l,v,d));return{c(){e=E("code"),t=E("div"),W(n.$$.fragment),i=D(),s=E("div"),r=E("pre"),a=m(`import { Client } from "@gradio/client";

const app = await Client.connect(`),f=E("span"),o=m('"'),c=m(l[0]),_=m('"'),w&&w.c(),p=m(`);
					`);for(let d=0;d<y.length;d+=1)y[d].c();q(t,"class","copy svelte-j71ub0"),q(f,"class","token string svelte-j71ub0"),q(r,"class","svelte-j71ub0"),q(e,"class","svelte-j71ub0")},m(d,N){b(d,e,N),u(e,t),Z(n,t,null),u(e,i),u(e,s),u(s,r),u(r,a),u(r,f),u(f,o),u(f,c),u(f,_),w&&w.m(r,null),u(r,p);for(let C=0;C<y.length;C+=1)y[C]&&y[C].m(r,null);l[15](s),g=!0},p(d,N){const C={};if(N&32&&(C.code=d[5]?.innerText),n.$set(C),(!g||N&1)&&R(c,d[0]),d[2]!==null?w?w.p(d,N):(w=Qt(d),w.c(),w.m(r,p)):w&&(w.d(1),w=null),N&256){v=oe(d[8]);let j;for(j=0;j<v.length;j+=1){const T=Vt(d,v,j);y[j]?y[j].p(T,N):(y[j]=Yt(T),y[j].c(),y[j].m(r,null))}for(;j<y.length;j+=1)y[j].d(1);y.length=v.length}},i(d){g||(I(n.$$.fragment,d),g=!0)},o(d){O(n.$$.fragment,d),g=!1},d(d){d&&h(e),Q(n),w&&w.d(),qe(y,d),l[15](null)}}}function hi(l){let e,t,n,i,s,r,a,f,o,c,_,p,g,w,v,y;n=new Le({props:{code:l[4]}});let d=l[2]!==null&&Xt(l),N=oe(l[7]),C=[];for(let j=0;j<N.length;j+=1)C[j]=Kt(Wt(l,N,j));return{c(){e=E("code"),t=E("div"),W(n.$$.fragment),i=D(),s=E("div"),r=E("pre"),a=E("span"),a.textContent="from",f=m(" gradio_client "),o=E("span"),o.textContent="import",c=m(` Client, file

client = Client(`),_=E("span"),p=m('"'),g=m(l[0]),w=m('"'),d&&d.c(),v=m(`)
`);for(let j=0;j<C.length;j+=1)C[j].c();q(t,"class","copy svelte-j71ub0"),q(a,"class","highlight"),q(o,"class","highlight"),q(_,"class","token string svelte-j71ub0"),q(r,"class","svelte-j71ub0"),q(e,"class","svelte-j71ub0")},m(j,T){b(j,e,T),u(e,t),Z(n,t,null),u(e,i),u(e,s),u(s,r),u(r,a),u(r,f),u(r,o),u(r,c),u(r,_),u(_,p),u(_,g),u(_,w),d&&d.m(r,null),u(r,v);for(let k=0;k<C.length;k+=1)C[k]&&C[k].m(r,null);l[14](s),y=!0},p(j,T){const k={};if(T&16&&(k.code=j[4]),n.$set(k),(!y||T&1)&&R(g,j[0]),j[2]!==null?d?d.p(j,T):(d=Xt(j),d.c(),d.m(r,v)):d&&(d.d(1),d=null),T&128){N=oe(j[7]);let $;for($=0;$<N.length;$+=1){const F=Wt(j,N,$);C[$]?C[$].p(F,T):(C[$]=Kt(F),C[$].c(),C[$].m(r,null))}for(;$<C.length;$+=1)C[$].d(1);C.length=N.length}},i(j){y||(I(n.$$.fragment,j),y=!0)},o(j){O(n.$$.fragment,j),y=!1},d(j){j&&h(e),Q(n),d&&d.d(),qe(C,j),l[14](null)}}}function Zt(l){let e,t,n,i,s=l[21]+"",r,a,f="{",o,c,_=l[20]+"",p,g,w="}",v,y,d="{",N,C,j="}",T,k,$,F,S=l[21]+"",A,P,U,M;return{c(){e=E("pre"),t=m("curl -X POST "),n=m(l[0]),i=m("call/"),r=m(s),a=m(` -s -H "Content-Type: application/json" -d '`),o=m(f),c=m(` 
	"data": [`),p=m(_),g=m("]"),v=m(w),y=m(`' \\
  | awk -F'"' '`),N=m(d),C=m(" print $4"),T=m(j),k=m(`' \\
  | read EVENT_ID; curl -N `),$=m(l[0]),F=m("call/"),A=m(S),P=m("/$EVENT_ID"),U=D(),M=E("br"),q(e,"class","svelte-j71ub0")},m(B,X){b(B,e,X),u(e,t),u(e,n),u(e,i),u(e,r),u(e,a),u(e,o),u(e,c),u(e,p),u(e,g),u(e,v),u(e,y),u(e,N),u(e,C),u(e,T),u(e,k),u(e,$),u(e,F),u(e,A),u(e,P),b(B,U,X),b(B,M,X)},p(B,X){X&1&&R(n,B[0]),X&512&&s!==(s=B[21]+"")&&R(r,s),X&512&&_!==(_=B[20]+"")&&R(p,_),X&1&&R($,B[0]),X&512&&S!==(S=B[21]+"")&&R(A,S)},d(B){B&&(h(e),h(U),h(M))}}}function Qt(l){let e,t,n;return{c(){e=m(', {auth: ["'),t=m(l[2]),n=m('", **password**]}')},m(i,s){b(i,e,s),b(i,t,s),b(i,n,s)},p(i,s){s&4&&R(t,i[2])},d(i){i&&(h(e),h(t),h(n))}}}function Jt(l){let e,t=l[20]+"",n;return{c(){e=m(", "),n=m(t)},m(i,s){b(i,e,s),b(i,n,s)},p(i,s){s&256&&t!==(t=i[20]+"")&&R(n,t)},d(i){i&&(h(e),h(n))}}}function Yt(l){let e,t,n,i=l[21]+"",s,r,a,f=l[20]&&Jt(l);return{c(){e=m(`
await client.predict(`),t=E("span"),n=m(`
  "/`),s=m(i),r=m('"'),f&&f.c(),a=m(`);
						`),q(t,"class","api-name svelte-j71ub0")},m(o,c){b(o,e,c),b(o,t,c),u(t,n),u(t,s),u(t,r),f&&f.m(o,c),b(o,a,c)},p(o,c){c&256&&i!==(i=o[21]+"")&&R(s,i),o[20]?f?f.p(o,c):(f=Jt(o),f.c(),f.m(a.parentNode,a)):f&&(f.d(1),f=null)},d(o){o&&(h(e),h(t),h(a)),f&&f.d(o)}}}function Xt(l){let e,t,n;return{c(){e=m(', auth=("'),t=m(l[2]),n=m('", **password**)')},m(i,s){b(i,e,s),b(i,t,s),b(i,n,s)},p(i,s){s&4&&R(t,i[2])},d(i){i&&(h(e),h(t),h(n))}}}function Kt(l){let e,t,n,i=l[20]+"",s,r,a,f,o=l[21]+"",c,_,p;return{c(){e=m(`
client.`),t=E("span"),n=m(`predict(
`),s=m(i),r=m("  api_name="),a=E("span"),f=m('"/'),c=m(o),_=m('"'),p=m(`
)
`),q(a,"class","api-name svelte-j71ub0"),q(t,"class","highlight")},m(g,w){b(g,e,w),b(g,t,w),u(t,n),u(t,s),u(t,r),u(t,a),u(a,f),u(a,c),u(a,_),u(t,p)},p(g,w){w&128&&i!==(i=g[20]+"")&&R(s,i),w&128&&o!==(o=g[21]+"")&&R(c,o)},d(g){g&&(h(e),h(t))}}}function gi(l){let e,t,n,i;const s=[hi,di,mi],r=[];function a(f,o){return f[1]==="python"?0:f[1]==="javascript"?1:f[1]==="bash"?2:-1}return~(e=a(l))&&(t=r[e]=s[e](l)),{c(){t&&t.c(),n=ue()},m(f,o){~e&&r[e].m(f,o),b(f,n,o),i=!0},p(f,o){let c=e;e=a(f),e===c?~e&&r[e].p(f,o):(t&&(_e(),O(r[c],1,1,()=>{r[c]=null}),pe()),~e?(t=r[e],t?t.p(f,o):(t=r[e]=s[e](f),t.c()),I(t,1),t.m(n.parentNode,n)):t=null)},i(f){i||(I(t),i=!0)},o(f){O(t),i=!1},d(f){f&&h(n),~e&&r[e].d(f)}}}function bi(l){let e,t,n;return t=new We({props:{border_mode:"focus",$$slots:{default:[gi]},$$scope:{ctx:l}}}),{c(){e=E("div"),W(t.$$.fragment),q(e,"class","container svelte-j71ub0")},m(i,s){b(i,e,s),Z(t,e,null),n=!0},p(i,[s]){const r={};s&268436479&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){n||(I(t.$$.fragment,i),n=!0)},o(i){O(t.$$.fragment,i),n=!1},d(i){i&&h(e),Q(t)}}}function vi(l,e,t){let{dependencies:n}=e,{short_root:i}=e,{root:s}=e,{api_prefix:r=""}=e,{current_language:a}=e,{username:f}=e,o,c,_,p,{api_calls:g=[]}=e;async function w(){return await(await fetch(s.replace(/\/$/,"")+r+"/info/?all_endpoints=true")).json()}let v,y=[],d=[],N=[];function C($,F){const S=`/${n[$.fn_index].api_name}`,P=$.data.filter(U=>typeof U<"u").map((U,M)=>{if(v[S]){const B=v[S].parameters[M];if(!B)return;const X=B.parameter_name,ge=B.python_type.type;if(F==="py")return`  ${X}=${je(U,ge,"py")}`;if(F==="js")return`    ${X}: ${je(U,ge,"js")}`;if(F==="bash")return`    ${je(U,ge,"bash")}`}return`  ${je(U,void 0,F)}`}).filter(U=>typeof U<"u").join(`,
`);if(P){if(F==="py")return`${P},
`;if(F==="js")return`{
${P},
}`;if(F==="bash")return`
${P}
`}return F==="py"?"":`
`}Ze(async()=>{v=(await w()).named_endpoints;let F=g.map(U=>C(U,"py")),S=g.map(U=>C(U,"js")),A=g.map(U=>C(U,"bash")),P=g.map(U=>n[U.fn_index].api_name||"");t(7,y=F.map((U,M)=>({call:U,api_name:P[M]}))),t(8,d=S.map((U,M)=>({call:U,api_name:P[M]}))),t(9,N=A.map((U,M)=>({call:U,api_name:P[M]}))),await De(),t(4,c=o.innerText)});function j($){Ce[$?"unshift":"push"](()=>{o=$,t(3,o)})}function T($){Ce[$?"unshift":"push"](()=>{_=$,t(5,_)})}function k($){Ce[$?"unshift":"push"](()=>{p=$,t(6,p)})}return l.$$set=$=>{"dependencies"in $&&t(10,n=$.dependencies),"short_root"in $&&t(0,i=$.short_root),"root"in $&&t(11,s=$.root),"api_prefix"in $&&t(12,r=$.api_prefix),"current_language"in $&&t(1,a=$.current_language),"username"in $&&t(2,f=$.username),"api_calls"in $&&t(13,g=$.api_calls)},[i,a,f,o,c,_,p,y,d,N,n,s,r,g,j,T,k]}class ki extends ke{constructor(e){super(),we(this,e,vi,bi,ye,{dependencies:10,short_root:0,root:11,api_prefix:12,current_language:1,username:2,api_calls:13})}get dependencies(){return this.$$.ctx[10]}set dependencies(e){this.$$set({dependencies:e}),z()}get short_root(){return this.$$.ctx[0]}set short_root(e){this.$$set({short_root:e}),z()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),z()}get api_prefix(){return this.$$.ctx[12]}set api_prefix(e){this.$$set({api_prefix:e}),z()}get current_language(){return this.$$.ctx[1]}set current_language(e){this.$$set({current_language:e}),z()}get username(){return this.$$.ctx[2]}set username(e){this.$$set({username:e}),z()}get api_calls(){return this.$$.ctx[13]}set api_calls(e){this.$$set({api_calls:e}),z()}}const wi="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e",yi="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e",ji="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e";function xt(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function Ci(l){let e;return{c(){e=m("1 element")},m(t,n){b(t,e,n)},p:he,d(t){t&&h(e)}}}function qi(l){let e=l[3]=="python"?"tuple":"list",t,n,i=l[1].length+"",s,r;return{c(){t=m(e),n=m(" of "),s=m(i),r=m(`
		elements`)},m(a,f){b(a,t,f),b(a,n,f),b(a,s,f),b(a,r,f)},p(a,f){f&8&&e!==(e=a[3]=="python"?"tuple":"list")&&R(t,e),f&2&&i!==(i=a[1].length+"")&&R(s,i)},d(a){a&&(h(t),h(n),h(s),h(r))}}}function en(l){let e;return{c(){e=E("span"),e.textContent=`[${l[10]}]`,q(e,"class","code svelte-16h224k")},m(t,n){b(t,e,n)},d(t){t&&h(e)}}}function Ei(l){let e=l[2][l[10]].type+"",t;return{c(){t=m(e)},m(n,i){b(n,t,i)},p(n,i){i&4&&e!==(e=n[2][n[10]].type+"")&&R(t,e)},d(n){n&&h(t)}}}function $i(l){let e=l[6].type+"",t;return{c(){t=m(e)},m(n,i){b(n,t,i)},p(n,i){i&2&&e!==(e=n[6].type+"")&&R(t,e)},d(n){n&&h(t)}}}function tn(l){let e,t,n,i,s,r,a,f,o,c=l[4]+"",_,p,g=l[7]+"",w,v,y,d=l[1].length>1&&en(l);function N(T,k){return T[3]==="python"?$i:Ei}let C=N(l),j=C(l);return{c(){e=E("hr"),t=D(),n=E("div"),i=E("p"),d&&d.c(),s=D(),r=E("span"),j.c(),a=D(),f=E("p"),o=m('The output value that appears in the "'),_=m(c),p=m('" '),w=m(g),v=m(`
				component.`),y=D(),q(e,"class","hr svelte-16h224k"),q(r,"class","code highlight svelte-16h224k"),q(f,"class","desc svelte-16h224k"),re(n,"margin","10px")},m(T,k){b(T,e,k),b(T,t,k),b(T,n,k),u(n,i),d&&d.m(i,null),u(i,s),u(i,r),j.m(r,null),u(n,a),u(n,f),u(f,o),u(f,_),u(f,p),u(f,w),u(f,v),u(n,y)},p(T,k){T[1].length>1?d||(d=en(T),d.c(),d.m(i,s)):d&&(d.d(1),d=null),C===(C=N(T))&&j?j.p(T,k):(j.d(1),j=C(T),j&&(j.c(),j.m(r,null))),k&2&&c!==(c=T[4]+"")&&R(_,c),k&2&&g!==(g=T[7]+"")&&R(w,g)},d(T){T&&(h(e),h(t),h(n)),d&&d.d(),j.d()}}}function nn(l){let e,t,n;return t=new Nn({props:{margin:!1}}),{c(){e=E("div"),W(t.$$.fragment),q(e,"class","load-wrap")},m(i,s){b(i,e,s),Z(t,e,null),n=!0},i(i){n||(I(t.$$.fragment,i),n=!0)},o(i){O(t.$$.fragment,i),n=!1},d(i){i&&h(e),Q(t)}}}function Ni(l){let e,t,n,i,s,r,a,f;function o(v,y){return v[1].length>1?qi:Ci}let c=o(l),_=c(l),p=oe(l[1]),g=[];for(let v=0;v<p.length;v+=1)g[v]=tn(xt(l,p,v));let w=l[0]&&nn();return{c(){e=E("h4"),t=E("div"),t.innerHTML='<div class="toggle-dot toggle-right svelte-16h224k"></div>',n=m(`
	Returns `),_.c(),i=D(),s=E("div");for(let v=0;v<g.length;v+=1)g[v].c();r=D(),w&&w.c(),a=ue(),q(t,"class","toggle-icon svelte-16h224k"),q(e,"class","svelte-16h224k"),tt(s,"hide",l[0])},m(v,y){b(v,e,y),u(e,t),u(e,n),_.m(e,null),b(v,i,y),b(v,s,y);for(let d=0;d<g.length;d+=1)g[d]&&g[d].m(s,null);b(v,r,y),w&&w.m(v,y),b(v,a,y),f=!0},p(v,[y]){if(c===(c=o(v))&&_?_.p(v,y):(_.d(1),_=c(v),_&&(_.c(),_.m(e,null))),y&14){p=oe(v[1]);let d;for(d=0;d<p.length;d+=1){const N=xt(v,p,d);g[d]?g[d].p(N,y):(g[d]=tn(N),g[d].c(),g[d].m(s,null))}for(;d<g.length;d+=1)g[d].d(1);g.length=p.length}(!f||y&1)&&tt(s,"hide",v[0]),v[0]?w?y&1&&I(w,1):(w=nn(),w.c(),I(w,1),w.m(a.parentNode,a)):w&&(_e(),O(w,1,1,()=>{w=null}),pe())},i(v){f||(I(w),f=!0)},o(v){O(w),f=!1},d(v){v&&(h(e),h(i),h(s),h(r),h(a)),_.d(),qe(g,v),w&&w.d(v)}}}function Ai(l,e,t){let{is_running:n}=e,{endpoint_returns:i}=e,{js_returns:s}=e,{current_language:r}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,i=a.endpoint_returns),"js_returns"in a&&t(2,s=a.js_returns),"current_language"in a&&t(3,r=a.current_language)},[n,i,s,r]}class Li extends ke{constructor(e){super(),we(this,e,Ai,Ni,ye,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),z()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),z()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),z()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),z()}}function ln(l,e,t){const n=l.slice();return n[19]=e[t],n[21]=t,n}function sn(l,e,t){const n=l.slice();return n[22]=e[t][0],n[23]=e[t][1],n}function rn(l){let e,t,n,i;const s=[zi,Ti],r=[];function a(f,o){return f[9]?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=ue()},m(f,o){r[e].m(f,o),b(f,n,o),i=!0},p(f,o){t.p(f,o)},i(f){i||(I(t),i=!0)},o(f){O(t),i=!1},d(f){f&&h(n),r[e].d(f)}}}function Ti(l){let e,t;return e=new ql({props:{root:l[0]}}),e.$on("close",l[16]),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.root=n[0]),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function zi(l){let e,t,n,i,s,r,a,f,o,c,_,p,g;t=new Al({props:{root:l[3]||l[0],api_count:l[9]}}),t.$on("close",l[13]);let w=oe(l[10]),v=[];for(let k=0;k<w.length;k+=1)v[k]=on(sn(l,w,k));const y=[Si,Ii],d=[];function N(k,$){return k[5].length?0:1}c=N(l),_=d[c]=y[c](l);let C=oe(l[1]),j=[];for(let k=0;k<C.length;k+=1)j[k]=_n(ln(l,C,k));const T=k=>O(j[k],1,1,()=>{j[k]=null});return{c(){e=E("div"),W(t.$$.fragment),n=D(),i=E("div"),s=E("div"),s.innerHTML=`<p style="font-size: var(--text-lg);">Choose a language to see the code snippets for interacting with the
					API.</p>`,r=D(),a=E("div"),f=E("div");for(let k=0;k<v.length;k+=1)v[k].c();o=D(),_.c(),p=D();for(let k=0;k<j.length;k+=1)j[k].c();q(e,"class","banner-wrap svelte-v3jjme"),q(s,"class","client-doc svelte-v3jjme"),q(f,"class","snippets svelte-v3jjme"),q(a,"class","endpoint svelte-v3jjme"),q(i,"class","docs-wrap svelte-v3jjme")},m(k,$){b(k,e,$),Z(t,e,null),b(k,n,$),b(k,i,$),u(i,s),u(i,r),u(i,a),u(a,f);for(let F=0;F<v.length;F+=1)v[F]&&v[F].m(f,null);u(a,o),d[c].m(a,null),u(a,p);for(let F=0;F<j.length;F+=1)j[F]&&j[F].m(a,null);g=!0},p(k,$){const F={};if($&9&&(F.root=k[3]||k[0]),t.$set(F),$&1088){w=oe(k[10]);let A;for(A=0;A<w.length;A+=1){const P=sn(k,w,A);v[A]?v[A].p(P,$):(v[A]=on(P),v[A].c(),v[A].m(f,null))}for(;A<v.length;A+=1)v[A].d(1);v.length=w.length}let S=c;if(c=N(k),c===S?d[c].p(k,$):(_e(),O(d[S],1,1,()=>{d[S]=null}),pe(),_=d[c],_?_.p(k,$):(_=d[c]=y[c](k),_.c()),I(_,1),_.m(a,p)),$&479){C=oe(k[1]);let A;for(A=0;A<C.length;A+=1){const P=ln(k,C,A);j[A]?(j[A].p(P,$),I(j[A],1)):(j[A]=_n(P),j[A].c(),I(j[A],1),j[A].m(a,null))}for(_e(),A=C.length;A<j.length;A+=1)T(A);pe()}},i(k){if(!g){I(t.$$.fragment,k),I(_);for(let $=0;$<C.length;$+=1)I(j[$]);g=!0}},o(k){O(t.$$.fragment,k),O(_),j=j.filter(Boolean);for(let $=0;$<j.length;$+=1)O(j[$]);g=!1},d(k){k&&(h(e),h(n),h(i)),Q(t),qe(v,k),d[c].d(),qe(j,k)}}}function on(l){let e,t,n,i,s=l[22]+"",r,a,f,o,c;function _(){return l[14](l[22])}return{c(){e=E("li"),t=E("img"),i=D(),r=m(s),a=D(),it(t.src,n=l[23])||q(t,"src",n),q(t,"alt",""),q(t,"class","svelte-v3jjme"),q(e,"class",f="snippet "+(l[6]===l[22]?"current-lang":"inactive-lang")+" svelte-v3jjme")},m(p,g){b(p,e,g),u(e,t),u(e,i),u(e,r),u(e,a),o||(c=Se(e,"click",_),o=!0)},p(p,g){l=p,g&64&&f!==(f="snippet "+(l[6]===l[22]?"current-lang":"inactive-lang")+" svelte-v3jjme")&&q(e,"class",f)},d(p){p&&h(e),o=!1,c()}}}function Ii(l){let e,t,n,i,s,r,a,f,o,c;function _(y,d){return y[6]=="python"||y[6]=="javascript"?Pi:Mi}let p=_(l),g=p(l);n=new Zl({props:{current_language:l[6]}});let w=l[3]&&an(l);f=new st({props:{size:"sm",variant:"secondary",$$slots:{default:[Oi]},$$scope:{ctx:l}}}),f.$on("click",l[15]);let v=l[6]=="bash"&&fn(l);return{c(){e=E("p"),g.c(),t=D(),W(n.$$.fragment),i=D(),s=E("p"),r=m(`2. Find the API endpoint below corresponding to your desired
						function in the app. Copy the code snippet, replacing the
						placeholder values with your own input data.
						`),w&&w.c(),a=m(`

						Or use the
						`),W(f.$$.fragment),o=m(`
						to automatically generate your API requests.
						`),v&&v.c(),q(e,"class","padded svelte-v3jjme"),q(s,"class","padded svelte-v3jjme")},m(y,d){b(y,e,d),g.m(e,null),b(y,t,d),Z(n,y,d),b(y,i,d),b(y,s,d),u(s,r),w&&w.m(s,null),u(s,a),Z(f,s,null),u(s,o),v&&v.m(s,null),c=!0},p(y,d){p===(p=_(y))&&g?g.p(y,d):(g.d(1),g=p(y),g&&(g.c(),g.m(e,null)));const N={};d&64&&(N.current_language=y[6]),n.$set(N),y[3]?w?w.p(y,d):(w=an(y),w.c(),w.m(s,a)):w&&(w.d(1),w=null);const C={};d&67108864&&(C.$$scope={dirty:d,ctx:y}),f.$set(C),y[6]=="bash"?v?v.p(y,d):(v=fn(y),v.c(),v.m(s,null)):v&&(v.d(1),v=null)},i(y){c||(I(n.$$.fragment,y),I(f.$$.fragment,y),c=!0)},o(y){O(n.$$.fragment,y),O(f.$$.fragment,y),c=!1},d(y){y&&(h(e),h(t),h(i),h(s)),g.d(),Q(n,y),w&&w.d(),Q(f),v&&v.d()}}}function Si(l){let e,t,n,i,s,r=l[5].length+"",a,f,o,c,_,p,g,w,v,y,d,N,C,j;return v=new ki({props:{current_language:l[6],api_calls:l[5],dependencies:l[1],root:l[0],api_prefix:l[2].api_prefix,short_root:l[3]||l[0],username:l[4]}}),{c(){e=E("div"),t=E("p"),n=m("🪄 Recorded API Calls "),i=E("span"),s=m("["),a=m(r),f=m("]"),o=D(),c=E("p"),_=m(`Here is the code snippet to replay the most recently recorded API
							calls using the `),p=m(l[6]),g=m(`
							client.`),w=D(),W(v.$$.fragment),y=D(),d=E("p"),d.textContent=`Note: Some API calls only affect the UI, so when using the
							clients, the desired result may be achieved with only a subset of
							the recorded calls.`,N=D(),C=E("p"),C.textContent="API Documentation",q(i,"class","api-count svelte-v3jjme"),q(t,"id","num-recorded-api-calls"),re(t,"font-size","var(--text-lg)"),re(t,"font-weight","bold"),re(t,"margin","10px 0px"),re(C,"font-size","var(--text-lg)"),re(C,"font-weight","bold"),re(C,"margin","30px 0px 10px")},m(T,k){b(T,e,k),u(e,t),u(t,n),u(t,i),u(i,s),u(i,a),u(i,f),u(e,o),u(e,c),u(c,_),u(c,p),u(c,g),u(e,w),Z(v,e,null),u(e,y),u(e,d),b(T,N,k),b(T,C,k),j=!0},p(T,k){(!j||k&32)&&r!==(r=T[5].length+"")&&R(a,r),(!j||k&64)&&R(p,T[6]);const $={};k&64&&($.current_language=T[6]),k&32&&($.api_calls=T[5]),k&2&&($.dependencies=T[1]),k&1&&($.root=T[0]),k&4&&($.api_prefix=T[2].api_prefix),k&9&&($.short_root=T[3]||T[0]),k&16&&($.username=T[4]),v.$set($)},i(T){j||(I(v.$$.fragment,T),j=!0)},o(T){O(v.$$.fragment,T),j=!1},d(T){T&&(h(e),h(N),h(C)),Q(v)}}}function Mi(l){let e;return{c(){e=m("1. Confirm that you have cURL installed on your system.")},m(t,n){b(t,e,n)},p:he,d(t){t&&h(e)}}}function Pi(l){let e,t,n,i,s,r,a,f;return{c(){e=m(`1. Install the
							`),t=E("span"),n=m(l[6]),i=m(`
							client (`),s=E("a"),r=m("docs"),f=m(") if you don't already have it installed."),re(t,"text-transform","capitalize"),q(s,"href",a=l[6]=="python"?lt:nt),q(s,"target","_blank"),q(s,"class","svelte-v3jjme")},m(o,c){b(o,e,c),b(o,t,c),u(t,n),b(o,i,c),b(o,s,c),u(s,r),b(o,f,c)},p(o,c){c&64&&R(n,o[6]),c&64&&a!==(a=o[6]=="python"?lt:nt)&&q(s,"href",a)},d(o){o&&(h(e),h(t),h(i),h(s),h(f))}}}function an(l){let e,t,n,i,s;return{c(){e=m(`If this is a private Space, you may need to pass your
							Hugging Face token as well (`),t=E("a"),n=m("read more"),s=m(")."),q(t,"href",i=l[6]=="python"?lt+et:l[6]=="javascript"?nt+et:dt),q(t,"class","underline svelte-v3jjme"),q(t,"target","_blank")},m(r,a){b(r,e,a),b(r,t,a),u(t,n),b(r,s,a)},p(r,a){a&64&&i!==(i=r[6]=="python"?lt+et:r[6]=="javascript"?nt+et:dt)&&q(t,"href",i)},d(r){r&&(h(e),h(t),h(s))}}}function Oi(l){let e,t,n;return{c(){e=E("div"),t=D(),n=E("p"),n.textContent="API Recorder",q(e,"class","loading-dot svelte-v3jjme"),q(n,"class","self-baseline svelte-v3jjme")},m(i,s){b(i,e,s),b(i,t,s),b(i,n,s)},p:he,d(i){i&&(h(e),h(t),h(n))}}}function fn(l){let e,t,n,i,s,r,a,f,o,c,_,p,g,w,v,y,d,N,C,j,T,k,$,F,S=l[4]!==null&&cn();return{c(){e=E("br"),t=m(" "),n=E("br"),i=m(`Making a
							prediction and getting a result requires
							`),s=E("strong"),s.textContent="2 requests",r=m(`: a
							`),a=E("code"),a.textContent="POST",f=m(`
							and a `),o=E("code"),o.textContent="GET",c=m(" request. The "),_=E("code"),_.textContent="POST",p=m(` request
							returns an `),g=E("code"),g.textContent="EVENT_ID",w=m(`, which is used in the second
							`),v=E("code"),v.textContent="GET",y=m(` request to fetch the results. In these snippets,
							we've used `),d=E("code"),d.textContent="awk",N=m(" and "),C=E("code"),C.textContent="read",j=m(` to parse the
							results, combining these two requests into one command for ease of
							use. `),S&&S.c(),T=m(` See
							`),k=E("a"),$=m("curl docs"),F=m("."),q(a,"class","svelte-v3jjme"),q(o,"class","svelte-v3jjme"),q(_,"class","svelte-v3jjme"),q(g,"class","svelte-v3jjme"),q(v,"class","svelte-v3jjme"),q(d,"class","svelte-v3jjme"),q(C,"class","svelte-v3jjme"),q(k,"href",dt),q(k,"target","_blank"),q(k,"class","svelte-v3jjme")},m(A,P){b(A,e,P),b(A,t,P),b(A,n,P),b(A,i,P),b(A,s,P),b(A,r,P),b(A,a,P),b(A,f,P),b(A,o,P),b(A,c,P),b(A,_,P),b(A,p,P),b(A,g,P),b(A,w,P),b(A,v,P),b(A,y,P),b(A,d,P),b(A,N,P),b(A,C,P),b(A,j,P),S&&S.m(A,P),b(A,T,P),b(A,k,P),u(k,$),b(A,F,P)},p(A,P){A[4]!==null?S||(S=cn(),S.c(),S.m(T.parentNode,T)):S&&(S.d(1),S=null)},d(A){A&&(h(e),h(t),h(n),h(i),h(s),h(r),h(a),h(f),h(o),h(c),h(_),h(p),h(g),h(w),h(v),h(y),h(d),h(N),h(C),h(j),h(T),h(k),h(F)),S&&S.d(A)}}}function cn(l){let e;return{c(){e=m(`Note: connecting to an authenticated app requires an additional
								request.`)},m(t,n){b(t,e,n)},d(t){t&&h(e)}}}function un(l){let e,t,n,i,s,r,a,f;return t=new pi({props:{named:!0,endpoint_parameters:l[7].named_endpoints["/"+l[19].api_name].parameters,dependency:l[19],dependency_index:l[21],current_language:l[6],root:l[0],space_id:l[3],username:l[4],api_prefix:l[2].api_prefix}}),i=new Ol({props:{endpoint_returns:l[7].named_endpoints["/"+l[19].api_name].parameters,js_returns:l[8].named_endpoints["/"+l[19].api_name].parameters,is_running:pn,current_language:l[6]}}),r=new Li({props:{endpoint_returns:l[7].named_endpoints["/"+l[19].api_name].returns,js_returns:l[8].named_endpoints["/"+l[19].api_name].returns,is_running:pn,current_language:l[6]}}),{c(){e=E("div"),W(t.$$.fragment),n=D(),W(i.$$.fragment),s=D(),W(r.$$.fragment),a=D(),q(e,"class","endpoint-container svelte-v3jjme")},m(o,c){b(o,e,c),Z(t,e,null),u(e,n),Z(i,e,null),u(e,s),Z(r,e,null),u(e,a),f=!0},p(o,c){const _={};c&130&&(_.endpoint_parameters=o[7].named_endpoints["/"+o[19].api_name].parameters),c&2&&(_.dependency=o[19]),c&64&&(_.current_language=o[6]),c&1&&(_.root=o[0]),c&8&&(_.space_id=o[3]),c&16&&(_.username=o[4]),c&4&&(_.api_prefix=o[2].api_prefix),t.$set(_);const p={};c&130&&(p.endpoint_returns=o[7].named_endpoints["/"+o[19].api_name].parameters),c&258&&(p.js_returns=o[8].named_endpoints["/"+o[19].api_name].parameters),c&64&&(p.current_language=o[6]),i.$set(p);const g={};c&130&&(g.endpoint_returns=o[7].named_endpoints["/"+o[19].api_name].returns),c&258&&(g.js_returns=o[8].named_endpoints["/"+o[19].api_name].returns),c&64&&(g.current_language=o[6]),r.$set(g)},i(o){f||(I(t.$$.fragment,o),I(i.$$.fragment,o),I(r.$$.fragment,o),f=!0)},o(o){O(t.$$.fragment,o),O(i.$$.fragment,o),O(r.$$.fragment,o),f=!1},d(o){o&&h(e),Q(t),Q(i),Q(r)}}}function _n(l){let e,t,n=l[19].show_api&&l[7].named_endpoints["/"+l[19].api_name]&&un(l);return{c(){n&&n.c(),e=ue()},m(i,s){n&&n.m(i,s),b(i,e,s),t=!0},p(i,s){i[19].show_api&&i[7].named_endpoints["/"+i[19].api_name]?n?(n.p(i,s),s&130&&I(n,1)):(n=un(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(_e(),O(n,1,1,()=>{n=null}),pe())},i(i){t||(I(n),t=!0)},o(i){O(n),t=!1},d(i){i&&h(e),n&&n.d(i)}}}function Fi(l){let e,t,n=l[7]&&rn(l);return{c(){n&&n.c(),e=ue()},m(i,s){n&&n.m(i,s),b(i,e,s),t=!0},p(i,[s]){i[7]?n?(n.p(i,s),s&128&&I(n,1)):(n=rn(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(_e(),O(n,1,1,()=>{n=null}),pe())},i(i){t||(I(n),t=!0)},o(i){O(n),t=!1},d(i){i&&h(e),n&&n.d(i)}}}const nt="https://www.gradio.app/guides/getting-started-with-the-js-client",lt="https://www.gradio.app/guides/getting-started-with-the-python-client",dt="https://www.gradio.app/guides/querying-gradio-apps-with-curl",et="#connecting-to-a-hugging-face-space";let pn=!1;function Ui(l,e,t){let{dependencies:n}=e,{root:i}=e,{app:s}=e,{space_id:r}=e,{root_node:a}=e,{username:f}=e,o=n.filter(k=>k.show_api).length;i===""&&(i=location.protocol+"//"+location.host+location.pathname),i.endsWith("/")||(i+="/");let{api_calls:c=[]}=e,_="python";const p=[["python",wi],["javascript",yi],["bash",ji]];async function g(){return await(await fetch(i.replace(/\/$/,"")+s.api_prefix+"/info")).json()}async function w(){return await s.view_api()}let v,y;g().then(k=>{t(7,v=k)}),w().then(k=>{t(8,y=k)});const d=Ve();Ze(()=>(document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0),()=>{document.body.style.overflow="auto"}));function N(k){Be.call(this,l,k)}const C=k=>t(6,_=k),j=()=>d("close",{api_recorder_visible:!0});function T(k){Be.call(this,l,k)}return l.$$set=k=>{"dependencies"in k&&t(1,n=k.dependencies),"root"in k&&t(0,i=k.root),"app"in k&&t(2,s=k.app),"space_id"in k&&t(3,r=k.space_id),"root_node"in k&&t(12,a=k.root_node),"username"in k&&t(4,f=k.username),"api_calls"in k&&t(5,c=k.api_calls)},[i,n,s,r,f,c,_,v,y,o,p,d,a,N,C,j,T]}class Di extends ke{constructor(e){super(),we(this,e,Ui,Fi,ye,{dependencies:1,root:0,app:2,space_id:3,root_node:12,username:4,api_calls:5})}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),z()}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),z()}get app(){return this.$$.ctx[2]}set app(e){this.$$set({app:e}),z()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),z()}get root_node(){return this.$$.ctx[12]}set root_node(e){this.$$set({root_node:e}),z()}get username(){return this.$$.ctx[4]}set username(e){this.$$set({username:e}),z()}get api_calls(){return this.$$.ctx[5]}set api_calls(e){this.$$set({api_calls:e}),z()}}function mn(l){let e,t,n=l[1][l[0][l[0].length-1].fn_index].api_name+"",i;return{c(){e=E("span"),t=m("/"),i=m(n),q(e,"class","api-name svelte-sy28j6")},m(s,r){b(s,e,r),u(e,t),u(e,i)},p(s,r){r&3&&n!==(n=s[1][s[0][s[0].length-1].fn_index].api_name+"")&&R(i,n)},d(s){s&&h(e)}}}function Ri(l){let e,t,n,i,s,r,a,f=l[0].length+"",o,c,_,p=l[0].length>0&&mn(l);return{c(){e=E("div"),t=D(),n=E("p"),n.textContent="Recording API Calls:",i=D(),s=E("p"),r=E("span"),a=m("["),o=m(f),c=m("]"),_=D(),p&&p.c(),q(e,"class","loading-dot self-baseline svelte-sy28j6"),q(n,"class","self-baseline svelte-sy28j6"),q(r,"class","api-count svelte-sy28j6"),q(s,"class","self-baseline api-section svelte-sy28j6")},m(g,w){b(g,e,w),b(g,t,w),b(g,n,w),b(g,i,w),b(g,s,w),u(s,r),u(r,a),u(r,o),u(r,c),u(s,_),p&&p.m(s,null)},p(g,w){w&1&&f!==(f=g[0].length+"")&&R(o,f),g[0].length>0?p?p.p(g,w):(p=mn(g),p.c(),p.m(s,null)):p&&(p.d(1),p=null)},d(g){g&&(h(e),h(t),h(n),h(i),h(s)),p&&p.d()}}}function Bi(l){let e,t,n;return t=new st({props:{size:"sm",variant:"secondary",$$slots:{default:[Ri]},$$scope:{ctx:l}}}),{c(){e=E("div"),W(t.$$.fragment),q(e,"id","api-recorder")},m(i,s){b(i,e,s),Z(t,e,null),n=!0},p(i,[s]){const r={};s&7&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){n||(I(t.$$.fragment,i),n=!0)},o(i){O(t.$$.fragment,i),n=!1},d(i){i&&h(e),Q(t)}}}function Gi(l,e,t){let{api_calls:n=[]}=e,{dependencies:i}=e;return l.$$set=s=>{"api_calls"in s&&t(0,n=s.api_calls),"dependencies"in s&&t(1,i=s.dependencies)},[n,i]}class Hi extends ke{constructor(e){super(),we(this,e,Gi,Bi,ye,{api_calls:0,dependencies:1})}get api_calls(){return this.$$.ctx[0]}set api_calls(e){this.$$set({api_calls:e}),z()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),z()}}const Vi=il(An);function Wi(l){let e;const t=l[11].default,n=ol(t,l,l[15],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,s){n&&n.p&&(!e||s&32768)&&al(n,t,i,i[15],e?cl(t,i[15],s,null):fl(i[15]),null)},i(i){e||(I(n,i),e=!0)},o(i){O(n,i),e=!1},d(i){n&&n.d(i)}}}function Zi(l){let e,t,n,i;const s=[{elem_id:l[5]},{elem_classes:l[6]},{target:l[3]},l[8],{theme_mode:l[4]},{root:l[2]}];function r(o){l[13](o)}var a=l[7];function f(o,c){let _={$$slots:{default:[Wi]},$$scope:{ctx:o}};for(let p=0;p<s.length;p+=1)_=He(_,s[p]);return c!==void 0&&c&380&&(_=He(_,ct(s,[c&32&&{elem_id:o[5]},c&64&&{elem_classes:o[6]},c&8&&{target:o[3]},c&256&&ut(o[8]),c&16&&{theme_mode:o[4]},c&4&&{root:o[2]}]))),o[1]!==void 0&&(_.value=o[1]),{props:_}}return a&&(e=wt(a,f(l)),l[12](e),Ce.push(()=>Ge(e,"value",r)),e.$on("prop_change",l[14])),{c(){e&&W(e.$$.fragment),n=ue()},m(o,c){e&&Z(e,o,c),b(o,n,c),i=!0},p(o,[c]){if(a!==(a=o[7])){if(e){_e();const _=e;O(_.$$.fragment,1,0,()=>{Q(_,1)}),pe()}a?(e=wt(a,f(o,c)),o[12](e),Ce.push(()=>Ge(e,"value",r)),e.$on("prop_change",o[14]),W(e.$$.fragment),I(e.$$.fragment,1),Z(e,n.parentNode,n)):e=null}else if(a){const _=c&380?ct(s,[c&32&&{elem_id:o[5]},c&64&&{elem_classes:o[6]},c&8&&{target:o[3]},c&256&&ut(o[8]),c&16&&{theme_mode:o[4]},c&4&&{root:o[2]}]):{};c&32768&&(_.$$scope={dirty:c,ctx:o}),!t&&c&2&&(t=!0,_.value=o[1],_t(()=>t=!1)),e.$set(_)}},i(o){i||(e&&I(e.$$.fragment,o),i=!0)},o(o){e&&O(e.$$.fragment,o),i=!1},d(o){o&&h(n),l[12](null),e&&Q(e,o)}}}function Qi(l,e,t){const n=["root","component","target","theme_mode","instance","value","elem_id","elem_classes","_id"];let i=yt(e,n),{$$slots:s={},$$scope:r}=e,{root:a}=e,{component:f}=e,{target:o}=e,{theme_mode:c}=e,{instance:_}=e,{value:p}=e,{elem_id:g}=e,{elem_classes:w}=e,{_id:v}=e;const y=(k,$,F)=>new CustomEvent("prop_change",{detail:{id:k,prop:$,value:F}});function d(k){return new Proxy(k,{construct(F,S){const A=new F(...S),P=Object.keys(A.$$.props);function U(M){return function(B){if(!o)return;const X=y(v,M,B);o.dispatchEvent(X)}}return P.forEach(M=>{Ce.push(()=>Ge(A,M,U(M)))}),A}})}const N=d(f);function C(k){Ce[k?"unshift":"push"](()=>{_=k,t(0,_)})}function j(k){p=k,t(1,p)}function T(k){Be.call(this,l,k)}return l.$$set=k=>{e=He(He({},e),rl(k)),t(8,i=yt(e,n)),"root"in k&&t(2,a=k.root),"component"in k&&t(9,f=k.component),"target"in k&&t(3,o=k.target),"theme_mode"in k&&t(4,c=k.theme_mode),"instance"in k&&t(0,_=k.instance),"value"in k&&t(1,p=k.value),"elem_id"in k&&t(5,g=k.elem_id),"elem_classes"in k&&t(6,w=k.elem_classes),"_id"in k&&t(10,v=k._id),"$$scope"in k&&t(15,r=k.$$scope)},[_,p,a,o,c,g,w,N,i,f,v,s,C,j,T,r]}class Ji extends ke{constructor(e){super(),we(this,e,Qi,Zi,sl,{root:2,component:9,target:3,theme_mode:4,instance:0,value:1,elem_id:5,elem_classes:6,_id:10})}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),z()}get component(){return this.$$.ctx[9]}set component(e){this.$$set({component:e}),z()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),z()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),z()}get instance(){return this.$$.ctx[0]}set instance(e){this.$$set({instance:e}),z()}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),z()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),z()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),z()}get _id(){return this.$$.ctx[10]}set _id(e){this.$$set({_id:e}),z()}}function dn(l,e,t){const n=l.slice();return n[15]=e[t],n}function hn(l){let e=[],t=new Map,n,i,s=oe(l[0].children);const r=a=>a[15].id;for(let a=0;a<s.length;a+=1){let f=dn(l,s,a),o=r(f);t.set(o,e[a]=gn(o,f))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=ue()},m(a,f){for(let o=0;o<e.length;o+=1)e[o]&&e[o].m(a,f);b(a,n,f),i=!0},p(a,f){f&63&&(s=oe(a[0].children),_e(),e=pl(e,f,r,1,a,s,t,n.parentNode,ml,gn,n,dn),pe())},i(a){if(!i){for(let f=0;f<s.length;f+=1)I(e[f]);i=!0}},o(a){for(let f=0;f<e.length;f+=1)O(e[f]);i=!1},d(a){a&&h(n);for(let f=0;f<e.length;f+=1)e[f].d(a)}}}function gn(l,e){let t,n,i;return n=new Sn({props:{node:e[15],component:e[15].component,target:e[2],id:e[15].id,root:e[1],theme_mode:e[3],max_file_size:e[4],client:e[5]}}),n.$on("destroy",e[9]),n.$on("mount",e[10]),{key:l,first:null,c(){t=ue(),W(n.$$.fragment),this.first=t},m(s,r){b(s,t,r),Z(n,s,r),i=!0},p(s,r){e=s;const a={};r&1&&(a.node=e[15]),r&1&&(a.component=e[15].component),r&4&&(a.target=e[2]),r&1&&(a.id=e[15].id),r&2&&(a.root=e[1]),r&8&&(a.theme_mode=e[3]),r&16&&(a.max_file_size=e[4]),r&32&&(a.client=e[5]),n.$set(a)},i(s){i||(I(n.$$.fragment,s),i=!0)},o(s){O(n.$$.fragment,s),i=!1},d(s){s&&h(t),Q(n,s)}}}function Yi(l){let e,t,n=l[0].children&&l[0].children.length&&hn(l);return{c(){n&&n.c(),e=ue()},m(i,s){n&&n.m(i,s),b(i,e,s),t=!0},p(i,s){i[0].children&&i[0].children.length?n?(n.p(i,s),s&1&&I(n,1)):(n=hn(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(_e(),O(n,1,1,()=>{n=null}),pe())},i(i){t||(I(n),t=!0)},o(i){O(n),t=!1},d(i){i&&h(e),n&&n.d(i)}}}function Xi(l){let e,t,n,i;const s=[{_id:l[0]?.id},{component:l[0].component},{elem_id:"elem_id"in l[0].props&&l[0].props.elem_id||`component-${l[0].id}`},{elem_classes:"elem_classes"in l[0].props&&l[0].props.elem_classes||[]},{target:l[2]},l[0].props,{theme_mode:l[3]},{root:l[1]}];function r(o){l[11](o)}function a(o){l[12](o)}let f={$$slots:{default:[Yi]},$$scope:{ctx:l}};for(let o=0;o<s.length;o+=1)f=He(f,s[o]);return l[0].instance!==void 0&&(f.instance=l[0].instance),l[0].props.value!==void 0&&(f.value=l[0].props.value),e=new Ji({props:f}),Ce.push(()=>Ge(e,"instance",r)),Ce.push(()=>Ge(e,"value",a)),{c(){W(e.$$.fragment)},m(o,c){Z(e,o,c),i=!0},p(o,[c]){const _=c&15?ct(s,[c&1&&{_id:o[0]?.id},c&1&&{component:o[0].component},c&1&&{elem_id:"elem_id"in o[0].props&&o[0].props.elem_id||`component-${o[0].id}`},c&1&&{elem_classes:"elem_classes"in o[0].props&&o[0].props.elem_classes||[]},c&4&&{target:o[2]},c&1&&ut(o[0].props),c&8&&{theme_mode:o[3]},c&2&&{root:o[1]}]):{};c&262207&&(_.$$scope={dirty:c,ctx:o}),!t&&c&1&&(t=!0,_.instance=o[0].instance,_t(()=>t=!1)),!n&&c&1&&(n=!0,_.value=o[0].props.value,_t(()=>n=!1)),e.$set(_)},i(o){i||(I(e.$$.fragment,o),i=!0)},o(o){O(e.$$.fragment,o),i=!1},d(o){Q(e,o)}}}function Ki(l,e,t){let{root:n}=e,{node:i}=e,{parent:s=null}=e,{target:r}=e,{theme_mode:a}=e,{version:f}=e,{autoscroll:o}=e,{max_file_size:c}=e,{client:_}=e;const p=Ve();let g=[];Ze(()=>{p("mount",i.id);for(const N of g)p("mount",N.id);return()=>{p("destroy",i.id);for(const N of g)p("mount",N.id)}}),ul("BLOCK_KEY",s);function w(N){Be.call(this,l,N)}function v(N){Be.call(this,l,N)}function y(N){l.$$.not_equal(i.instance,N)&&(i.instance=N,t(0,i),t(14,g),t(2,r),t(3,a),t(7,f),t(1,n),t(8,o),t(4,c),t(5,_))}function d(N){l.$$.not_equal(i.props.value,N)&&(i.props.value=N,t(0,i),t(14,g),t(2,r),t(3,a),t(7,f),t(1,n),t(8,o),t(4,c),t(5,_))}return l.$$set=N=>{"root"in N&&t(1,n=N.root),"node"in N&&t(0,i=N.node),"parent"in N&&t(6,s=N.parent),"target"in N&&t(2,r=N.target),"theme_mode"in N&&t(3,a=N.theme_mode),"version"in N&&t(7,f=N.version),"autoscroll"in N&&t(8,o=N.autoscroll),"max_file_size"in N&&t(4,c=N.max_file_size),"client"in N&&t(5,_=N.client)},l.$$.update=()=>{l.$$.dirty&1&&i&&t(0,i.children=i.children&&i.children.filter(N=>{const C=i.type!=="statustracker";return C||g.push(N),C}),i),l.$$.dirty&1&&i&&i.type==="form"&&(i.children?.every(N=>!N.props.visible)?t(0,i.props.visible=!1,i):t(0,i.props.visible=!0,i)),l.$$.dirty&447&&t(0,i.props.gradio=new wl(i.id,r,a,f,n,o,c,Vi,_,_l),i)},[i,n,r,a,c,_,s,f,o,w,v,y,d]}class Sn extends ke{constructor(e){super(),we(this,e,Ki,Xi,ye,{root:1,node:0,parent:6,target:2,theme_mode:3,version:7,autoscroll:8,max_file_size:4,client:5})}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),z()}get node(){return this.$$.ctx[0]}set node(e){this.$$set({node:e}),z()}get parent(){return this.$$.ctx[6]}set parent(e){this.$$set({parent:e}),z()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),z()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),z()}get version(){return this.$$.ctx[7]}set version(e){this.$$set({version:e}),z()}get autoscroll(){return this.$$.ctx[8]}set autoscroll(e){this.$$set({autoscroll:e}),z()}get max_file_size(){return this.$$.ctx[4]}set max_file_size(e){this.$$set({max_file_size:e}),z()}get client(){return this.$$.ctx[5]}set client(e){this.$$set({client:e}),z()}}function bn(l){let e,t;return e=new Sn({props:{node:l[0],root:l[1],target:l[2],theme_mode:l[3],version:l[4],autoscroll:l[5],max_file_size:l[6],client:l[7]}}),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.node=n[0]),i&2&&(s.root=n[1]),i&4&&(s.target=n[2]),i&8&&(s.theme_mode=n[3]),i&16&&(s.version=n[4]),i&32&&(s.autoscroll=n[5]),i&64&&(s.max_file_size=n[6]),i&128&&(s.client=n[7]),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function xi(l){let e,t,n=l[0]&&bn(l);return{c(){n&&n.c(),e=ue()},m(i,s){n&&n.m(i,s),b(i,e,s),t=!0},p(i,[s]){i[0]?n?(n.p(i,s),s&1&&I(n,1)):(n=bn(i),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(_e(),O(n,1,1,()=>{n=null}),pe())},i(i){t||(I(n),t=!0)},o(i){O(n),t=!1},d(i){i&&h(e),n&&n.d(i)}}}function es(l,e,t){let{rootNode:n}=e,{root:i}=e,{target:s}=e,{theme_mode:r}=e,{version:a}=e,{autoscroll:f}=e,{max_file_size:o=null}=e,{client:c}=e;const _=Ve();return Ze(()=>{_("mount")}),l.$$set=p=>{"rootNode"in p&&t(0,n=p.rootNode),"root"in p&&t(1,i=p.root),"target"in p&&t(2,s=p.target),"theme_mode"in p&&t(3,r=p.theme_mode),"version"in p&&t(4,a=p.version),"autoscroll"in p&&t(5,f=p.autoscroll),"max_file_size"in p&&t(6,o=p.max_file_size),"client"in p&&t(7,c=p.client)},[n,i,s,r,a,f,o,c]}class ts extends ke{constructor(e){super(),we(this,e,es,xi,ye,{rootNode:0,root:1,target:2,theme_mode:3,version:4,autoscroll:5,max_file_size:6,client:7})}get rootNode(){return this.$$.ctx[0]}set rootNode(e){this.$$set({rootNode:e}),z()}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),z()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),z()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),z()}get version(){return this.$$.ctx[4]}set version(e){this.$$set({version:e}),z()}get autoscroll(){return this.$$.ctx[5]}set autoscroll(e){this.$$set({autoscroll:e}),z()}get max_file_size(){return this.$$.ctx[6]}set max_file_size(e){this.$$set({max_file_size:e}),z()}get client(){return this.$$.ctx[7]}set client(e){this.$$set({client:e}),z()}}const ns="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",{document:Re}=gl;function vn(l){return Re.title=l[2],{c:he,m:he,d:he}}function kn(l){let e,t=`<style>${jt(l[15],l[12])}</style>`,n;return{c(){e=new vl(!1),n=ue(),e.a=n},m(i,s){e.m(t,i,s),b(i,n,s)},p(i,s){s[0]&36864&&t!==(t=`<style>${jt(i[15],i[12])}</style>`)&&e.p(t)},d(i){i&&(h(n),e.d())}}}function wn(l){let e,t;return e=new ts({props:{rootNode:l[16],root:l[0],target:l[3],theme_mode:l[9],version:l[12],autoscroll:l[4],max_file_size:l[14],client:l[10]}}),e.$on("mount",l[28]),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i[0]&65536&&(s.rootNode=n[16]),i[0]&1&&(s.root=n[0]),i[0]&8&&(s.target=n[3]),i[0]&512&&(s.theme_mode=n[9]),i[0]&4096&&(s.version=n[12]),i[0]&16&&(s.autoscroll=n[4]),i[0]&16384&&(s.max_file_size=n[14]),i[0]&1024&&(s.client=n[10]),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function yn(l){let e,t,n,i=l[21]("common.built_with_gradio")+"",s,r,a,f,o,c=l[5]&&jn(l);return{c(){e=E("footer"),c&&c.c(),t=D(),n=E("a"),s=m(i),r=D(),a=E("img"),it(a.src,f=ns)||q(a,"src",f),q(a,"alt",o=l[21]("common.logo")),q(a,"class","svelte-1rjryqp"),q(n,"href","https://gradio.app"),q(n,"class","built-with svelte-1rjryqp"),q(n,"target","_blank"),q(n,"rel","noreferrer"),q(e,"class","svelte-1rjryqp")},m(_,p){b(_,e,p),c&&c.m(e,null),u(e,t),u(e,n),u(n,s),u(n,r),u(n,a)},p(_,p){_[5]?c?c.p(_,p):(c=jn(_),c.c(),c.m(e,t)):c&&(c.d(1),c=null),p[0]&2097152&&i!==(i=_[21]("common.built_with_gradio")+"")&&R(s,i),p[0]&2097152&&o!==(o=_[21]("common.logo"))&&q(a,"alt",o)},d(_){_&&h(e),c&&c.d()}}}function jn(l){let e,t=l[21]("errors.use_via_api")+"",n,i,s,r,a,f,o,c,_;return{c(){e=E("button"),n=m(t),i=D(),s=E("img"),f=D(),o=E("div"),o.textContent="·",it(s.src,r=Tn)||q(s,"src",r),q(s,"alt",a=l[21]("common.logo")),q(s,"class","svelte-1rjryqp"),q(e,"class","show-api svelte-1rjryqp"),q(o,"class","svelte-1rjryqp")},m(p,g){b(p,e,g),u(e,n),u(e,i),u(e,s),b(p,f,g),b(p,o,g),c||(_=Se(e,"click",l[40]),c=!0)},p(p,g){g[0]&2097152&&t!==(t=p[21]("errors.use_via_api")+"")&&R(n,t),g[0]&2097152&&a!==(a=p[21]("common.logo"))&&q(s,"alt",a)},d(p){p&&(h(e),h(f),h(o)),c=!1,_()}}}function Cn(l){let e,t,n,i,s;return t=new Hi({props:{api_calls:l[19],dependencies:l[1]}}),{c(){e=E("div"),W(t.$$.fragment),q(e,"id","api-recorder-container"),q(e,"class","svelte-1rjryqp")},m(r,a){b(r,e,a),Z(t,e,null),n=!0,i||(s=Se(e,"click",l[41]),i=!0)},p(r,a){const f={};a[0]&524288&&(f.api_calls=r[19]),a[0]&2&&(f.dependencies=r[1]),t.$set(f)},i(r){n||(I(t.$$.fragment,r),n=!0)},o(r){O(t.$$.fragment,r),n=!1},d(r){r&&h(e),Q(t),i=!1,s()}}}function qn(l){let e,t,n,i,s,r,a,f;return s=new Di({props:{root_node:l[16],dependencies:l[1],root:l[0],app:l[10],space_id:l[11],api_calls:l[19],username:l[13]}}),s.$on("close",l[43]),{c(){e=E("div"),t=E("div"),n=D(),i=E("div"),W(s.$$.fragment),q(t,"class","backdrop svelte-1rjryqp"),q(i,"class","api-docs-wrap svelte-1rjryqp"),q(e,"class","api-docs svelte-1rjryqp")},m(o,c){b(o,e,c),u(e,t),u(e,n),u(e,i),Z(s,i,null),r=!0,a||(f=Se(t,"click",l[42]),a=!0)},p(o,c){const _={};c[0]&65536&&(_.root_node=o[16]),c[0]&2&&(_.dependencies=o[1]),c[0]&1&&(_.root=o[0]),c[0]&1024&&(_.app=o[10]),c[0]&2048&&(_.space_id=o[11]),c[0]&524288&&(_.api_calls=o[19]),c[0]&8192&&(_.username=o[13]),s.$set(_)},i(o){r||(I(s.$$.fragment,o),r=!0)},o(o){O(s.$$.fragment,o),r=!1},d(o){o&&h(e),Q(s),a=!1,f()}}}function En(l){let e,t;return e=new kl({props:{messages:l[20]}}),e.$on("close",l[27]),{c(){W(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i[0]&1048576&&(s.messages=n[20]),e.$set(s)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){Q(e,n)}}}function ls(l){let e,t,n,i,s,r,a,f,o,c,_,p=l[7]&&vn(l),g=l[15]&&kn(l),w=l[16]&&l[10].config&&wn(l),v=l[6]&&yn(l),y=l[18]&&Cn(l),d=l[17]&&l[16]&&qn(l),N=l[20]&&En(l);return{c(){p&&p.c(),e=ue(),g&&g.c(),t=ue(),n=D(),i=E("div"),s=E("div"),w&&w.c(),r=D(),v&&v.c(),a=D(),y&&y.c(),f=D(),d&&d.c(),o=D(),N&&N.c(),c=ue(),q(s,"class","contain svelte-1rjryqp"),re(s,"flex-grow",l[8]?"1":"auto"),q(i,"class","wrap svelte-1rjryqp"),re(i,"min-height",l[8]?"100%":"auto")},m(C,j){p&&p.m(Re.head,null),u(Re.head,e),g&&g.m(Re.head,null),u(Re.head,t),b(C,n,j),b(C,i,j),u(i,s),w&&w.m(s,null),u(i,r),v&&v.m(i,null),b(C,a,j),y&&y.m(C,j),b(C,f,j),d&&d.m(C,j),b(C,o,j),N&&N.m(C,j),b(C,c,j),_=!0},p(C,j){C[7]?p||(p=vn(C),p.c(),p.m(e.parentNode,e)):p&&(p.d(1),p=null),C[15]?g?g.p(C,j):(g=kn(C),g.c(),g.m(t.parentNode,t)):g&&(g.d(1),g=null),C[16]&&C[10].config?w?(w.p(C,j),j[0]&66560&&I(w,1)):(w=wn(C),w.c(),I(w,1),w.m(s,null)):w&&(_e(),O(w,1,1,()=>{w=null}),pe()),j[0]&256&&re(s,"flex-grow",C[8]?"1":"auto"),C[6]?v?v.p(C,j):(v=yn(C),v.c(),v.m(i,null)):v&&(v.d(1),v=null),j[0]&256&&re(i,"min-height",C[8]?"100%":"auto"),C[18]?y?(y.p(C,j),j[0]&262144&&I(y,1)):(y=Cn(C),y.c(),I(y,1),y.m(f.parentNode,f)):y&&(_e(),O(y,1,1,()=>{y=null}),pe()),C[17]&&C[16]?d?(d.p(C,j),j[0]&196608&&I(d,1)):(d=qn(C),d.c(),I(d,1),d.m(o.parentNode,o)):d&&(_e(),O(d,1,1,()=>{d=null}),pe()),C[20]?N?(N.p(C,j),j[0]&1048576&&I(N,1)):(N=En(C),N.c(),I(N,1),N.m(c.parentNode,c)):N&&(_e(),O(N,1,1,()=>{N=null}),pe())},i(C){_||(I(w),I(y),I(d),I(N),_=!0)},o(C){O(w),O(y),O(d),O(N),_=!1},d(C){C&&(h(n),h(i),h(a),h(f),h(o),h(c)),p&&p.d(C),h(e),g&&g.d(C),h(t),w&&w.d(),v&&v.d(),y&&y.d(C),d&&d.d(C),N&&N.d(C)}}}const is=/^'([^]+)'$/,ss=15,rs=10;function $n(l){return"detail"in l}function os(l,e,t){let n,i,s,r,a;Ue(l,An,L=>t(21,r=L)),dl();let{root:f}=e,{components:o}=e,{layout:c}=e,{dependencies:_}=e,{title:p="Gradio"}=e,{target:g}=e,{autoscroll:w}=e,{show_api:v=!0}=e,{show_footer:y=!0}=e,{control_page_title:d=!1}=e,{app_mode:N}=e,{theme_mode:C}=e,{app:j}=e,{space_id:T}=e,{version:k}=e,{js:$}=e,{fill_height:F=!1}=e,{ready:S}=e,{username:A}=e,{api_prefix:P=""}=e,{max_file_size:U=void 0}=e,{initial_layout:M=void 0}=e,{css:B=null}=e,{layout:X,targets:ge,update_value:x,get_data:Y,modify_stream:ne,get_stream_state:J,set_time_limit:se,loading_status:me,scheduled_updates:be,create_layout:Qe,rerender_layout:Mn}=hl(M);Ue(l,X,L=>t(16,a=L)),Ue(l,ge,L=>t(50,i=L)),Ue(l,me,L=>t(39,n=L)),Ue(l,be,L=>t(51,s=L));async function Pn(){await Qe({components:o,layout:c,dependencies:_,root:f+P,app:j,options:{fill_height:F}})}let{search_params:Je}=e,rt=Je.get("view")==="api"&&v,Me=Je.get("view")==="api-recorder"&&v;function Pe(L){t(18,Me=!1),t(17,rt=L);let ee=new URLSearchParams(window.location.search);L?ee.set("view","api"):ee.delete("view"),history.replaceState(null,"","?"+ee.toString())}let Ye=[],{render_complete:Xe=!1}=e;async function ht(L,ee){const H=_.find(K=>K.id==ee).outputs,G=L?.map((K,de)=>({id:H[de],prop:"value_is_output",value:!0}));x(G),await De();const le=[];L?.forEach((K,de)=>{if(typeof K=="object"&&K!==null&&K.__type__==="update")for(const[ve,V]of Object.entries(K))ve!=="__type__"&&le.push({id:H[de],prop:ve,value:V});else le.push({id:H[de],prop:"value",value:K})}),x(le),await De()}let Ee=new Map,ae=[];function $e(L,ee,H,G,le=10,K=!0){return{title:L,message:ee,fn_index:H,type:G,id:++On,duration:le,visible:K}}function gt(L,ee,H){t(20,ae=[$e(L,ee,-1,H),...ae])}let On=-1,ot=!1;const Fn=r("blocks.long_requests_queue"),Un=r("blocks.connection_can_break"),Dn=r("blocks.lost_connection"),Rn=r("blocks.waiting_for_inputs");let at=!1,bt=!1,vt=!1,Oe=[];function Ie(L,ee=null,H=null){let G=()=>{};function le(){G()}s?G=be.subscribe(K=>{K||De().then(()=>{kt(L,ee,H),le()})}):kt(L,ee,H)}async function Bn(L,ee,H){return L===ee&&H&&H.is_value_data===!0?H.value:Y(L)}async function kt(L,ee=null,H=null){let G=_.find(V=>V.id===L);if(Oe.length>0){for(const V of Oe)if(G.inputs.includes(V)){gt("Warning",Rn,"warning");return}}const le=me.get_status_for_fn(L);t(20,ae=ae.filter(({fn_index:V})=>V!==L)),(le==="pending"||le==="generating")&&(G.pending_request=!0);let K={fn_index:L,data:await Promise.all(G.inputs.map(V=>Bn(V,ee,H))),event_data:G.collects_event_data?H:null,trigger_id:ee};G.frontend_fn?G.frontend_fn(K.data.concat(await Promise.all(G.outputs.map(V=>Y(V))))).then(V=>{G.backend_fn?(K.data=V,de(G,K)):ht(V,L)}):G.types.cancel&&G.cancels?await Promise.all(G.cancels.map(async V=>{const Ne=Ee.get(V);return Ne?.cancel(),Ne})):G.backend_fn&&de(G,K);function de(V,Ne){V.trigger_mode==="once"?V.pending_request||ve(Ne,V.connection=="stream"):V.trigger_mode==="multiple"?ve(Ne,V.connection=="stream"):V.trigger_mode==="always_last"&&(V.pending_request?V.final_event=Ne:ve(Ne,V.connection=="stream"))}async function ve(V,Ne=!1){Me&&t(19,Ye=[...Ye,JSON.parse(JSON.stringify(V))]);let ft;if(j.set_current_payload(V),Ne)if(!Ee.has(L))G.inputs.forEach(ie=>ne(ie,"waiting"));else{if(Ee.has(L)&&G.inputs.some(ie=>J(ie)==="waiting"))return;if(Ee.has(L)&&G.inputs.some(ie=>J(ie)==="open")){await j.send_ws_message(`${j.config.root+j.config.api_prefix}/stream/${Ee.get(L).event_id()}`,{...V,session_hash:j.session_hash});return}}try{ft=j.submit(V.fn_index,V.data,V.event_data,V.trigger_id)}catch(ie){t(20,ae=[$e("Error",String(ie),0,"error"),...ae]),me.update({status:"error",fn_index:0,eta:0,queue:!1,queue_position:null}),Ke(n);return}Ee.set(L,ft);for await(const ie of ft)ie.type==="data"?Kn(ie):ie.type==="render"?xn(ie):ie.type==="status"?nl(ie):ie.type==="log"&&el(ie);function Kn(ie){const{data:ce,fn_index:te}=ie;G.pending_request&&G.final_event&&(G.pending_request=!1,ve(G.final_event,G.connection=="stream")),G.pending_request=!1,ht(ce,te),Ke(n)}function xn(ie){const{data:ce}=ie;let te=ce.components,fe=ce.layout,Ae=ce.dependencies,Te=ce.render_id,ze=[];_.forEach((Fe,ll)=>{Fe.rendered_in===Te&&ze.push(ll)}),ze.reverse().forEach(Fe=>{_.splice(Fe,1)}),Ae.forEach(Fe=>{_.push(Fe)}),Mn({components:te,layout:fe,root:f,dependencies:_,render_id:Te})}function el(ie){const{title:ce,log:te,fn_index:fe,level:Ae,duration:Te,visible:ze}=ie;t(20,ae=[$e(ce,te,fe,Ae,Te,ze),...ae])}function tl(ie,ce,te){ie.original_msg==="process_starts"&&te.connection==="stream"&&ne(ce,"open")}function nl(ie){const{fn_index:ce,...te}=ie;if(te.stage==="streaming"&&te.time_limit&&G.inputs.forEach(fe=>{se(fe,te.time_limit)}),G.inputs.forEach(fe=>{tl(ie,fe,G)}),me.update({...te,time_limit:te.time_limit,status:te.stage,progress:te.progress_data,fn_index:ce}),Ke(n),!bt&&T!==null&&te.position!==void 0&&te.position>=2&&te.eta!==void 0&&te.eta>ss&&(bt=!0,t(20,ae=[$e("Warning",Fn,ce,"warning"),...ae])),!vt&&at&&te.eta!==void 0&&te.eta>rs&&(vt=!0,t(20,ae=[$e("Warning",Un,ce,"warning"),...ae])),(te.stage==="complete"||te.stage==="generating")&&te.changed_state_ids?.forEach(fe=>{_.filter(Ae=>Ae.targets.some(([Te,ze])=>Te===fe)).forEach(Ae=>{Ie(Ae.id,V.trigger_id)})}),te.stage==="complete"&&(_.forEach(async fe=>{fe.trigger_after===ce&&Ie(fe.id,V.trigger_id)}),G.inputs.forEach(fe=>{ne(fe,"closed")}),Ee.delete(L)),te.broken&&at&&ot)window.setTimeout(()=>{t(20,ae=[$e("Error",Dn,ce,"error"),...ae])},0),Ie(G.id,V.trigger_id,H),ot=!1;else if(te.stage==="error"){if(te.message){const fe=te.message.replace(is,(Te,ze)=>ze),Ae=te.title??"Error";t(20,ae=[$e(Ae,fe,ce,"error",te.duration,te.visible),...ae])}_.map(async fe=>{fe.trigger_after===ce&&!fe.trigger_only_on_success&&Ie(fe.id,V.trigger_id)})}}}}function Gn(L,ee){if(T===null)return;const H=new URL(`https://huggingface.co/spaces/${T}/discussions/new`);L!==void 0&&L.length>0&&H.searchParams.set("title",L),H.searchParams.set("description",ee),window.open(H.toString(),"_blank")}function Hn(L){const ee=L.detail;t(20,ae=ae.filter(H=>H.id!==ee))}const Vn=L=>!!(L&&new URL(L,location.href).origin!==location.origin);async function Wn(){$&&await new bl(`let result = await (${$})();
					return (!Array.isArray(result)) ? [result] : result;`)(),await De();for(var L=g.getElementsByTagName("a"),ee=0;ee<L.length;ee++){const H=L[ee].getAttribute("target"),G=L[ee].getAttribute("href");Vn(G)&&H!=="_blank"&&L[ee].setAttribute("target","_blank")}_.forEach(H=>{H.targets.some(G=>G[1]==="load")&&Ie(H.id)}),!(!g||Xe)&&(g.addEventListener("prop_change",H=>{if(!$n(H))throw new Error("not a custom event");const{id:G,prop:le,value:K}=H.detail;x([{id:G,prop:le,value:K}]),le==="input_ready"&&K===!1&&Oe.push(G),le==="input_ready"&&K===!0&&(Oe=Oe.filter(de=>de!==G))}),g.addEventListener("gradio",H=>{if(!$n(H))throw new Error("not a custom event");const{id:G,event:le,data:K}=H.detail;if(le==="share"){const{title:de,description:ve}=K;Gn(de,ve)}else le==="error"?t(20,ae=[$e("Error",K,-1,le),...ae]):le==="warning"?t(20,ae=[$e("Warning",K,-1,le),...ae]):le=="clear_status"?Zn(G,"complete",K):le=="close_stream"?i[G]?.[K]?.forEach(ve=>{if(Ee.has(ve)){const V=`${j.config.root+j.config.api_prefix}/stream/${Ee.get(ve).event_id()}`;j.post_data(`${V}/close`,{}),j.close_ws(V)}}):i[G]?.[le]?.forEach(ve=>{requestAnimationFrame(()=>{Ie(ve,G,K)})})}),t(30,Xe=!0))}function Zn(L,ee,H){H.status=ee,x([{id:L,prop:"loading_status",value:H}])}function Ke(L){let ee=[];Object.entries(L).forEach(([le,K])=>{let de=_.find(ve=>ve.id==K.fn_index);de!==void 0&&(K.scroll_to_output=de.scroll_to_output,K.show_progress=de.show_progress,ee.push({id:parseInt(le),prop:"loading_status",value:K}))});const H=me.get_inputs_to_update(),G=Array.from(H).map(([le,K])=>({id:le,prop:"pending",value:K==="pending"}));x([...ee,...G])}Ze(()=>{document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&(ot=!0)}),at=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)});const Qn=()=>{Pe(!rt)},Jn=()=>{Pe(!0),t(18,Me=!1)},Yn=()=>{Pe(!1)},Xn=L=>{Pe(!1),t(19,Ye=[]),t(18,Me=L.detail?.api_recorder_visible)};return l.$$set=L=>{"root"in L&&t(0,f=L.root),"components"in L&&t(31,o=L.components),"layout"in L&&t(32,c=L.layout),"dependencies"in L&&t(1,_=L.dependencies),"title"in L&&t(2,p=L.title),"target"in L&&t(3,g=L.target),"autoscroll"in L&&t(4,w=L.autoscroll),"show_api"in L&&t(5,v=L.show_api),"show_footer"in L&&t(6,y=L.show_footer),"control_page_title"in L&&t(7,d=L.control_page_title),"app_mode"in L&&t(8,N=L.app_mode),"theme_mode"in L&&t(9,C=L.theme_mode),"app"in L&&t(10,j=L.app),"space_id"in L&&t(11,T=L.space_id),"version"in L&&t(12,k=L.version),"js"in L&&t(33,$=L.js),"fill_height"in L&&t(34,F=L.fill_height),"ready"in L&&t(29,S=L.ready),"username"in L&&t(13,A=L.username),"api_prefix"in L&&t(35,P=L.api_prefix),"max_file_size"in L&&t(14,U=L.max_file_size),"initial_layout"in L&&t(36,M=L.initial_layout),"css"in L&&t(15,B=L.css),"search_params"in L&&t(37,Je=L.search_params),"render_complete"in L&&t(30,Xe=L.render_complete)},l.$$.update=()=>{l.$$.dirty[0]&1035|l.$$.dirty[1]&11&&Pn(),l.$$.dirty[0]&65536&&t(29,S=!!a),l.$$.dirty[1]&256&&Ke(n)},[f,_,p,g,w,v,y,d,N,C,j,T,k,A,U,B,a,rt,Me,Ye,ae,r,X,ge,me,be,Pe,Hn,Wn,S,Xe,o,c,$,F,P,M,Je,gt,n,Qn,Jn,Yn,Xn]}class ps extends ke{constructor(e){super(),we(this,e,os,ls,ye,{root:0,components:31,layout:32,dependencies:1,title:2,target:3,autoscroll:4,show_api:5,show_footer:6,control_page_title:7,app_mode:8,theme_mode:9,app:10,space_id:11,version:12,js:33,fill_height:34,ready:29,username:13,api_prefix:35,max_file_size:14,initial_layout:36,css:15,search_params:37,render_complete:30,add_new_message:38},null,[-1,-1,-1])}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),z()}get components(){return this.$$.ctx[31]}set components(e){this.$$set({components:e}),z()}get layout(){return this.$$.ctx[32]}set layout(e){this.$$set({layout:e}),z()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),z()}get title(){return this.$$.ctx[2]}set title(e){this.$$set({title:e}),z()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),z()}get autoscroll(){return this.$$.ctx[4]}set autoscroll(e){this.$$set({autoscroll:e}),z()}get show_api(){return this.$$.ctx[5]}set show_api(e){this.$$set({show_api:e}),z()}get show_footer(){return this.$$.ctx[6]}set show_footer(e){this.$$set({show_footer:e}),z()}get control_page_title(){return this.$$.ctx[7]}set control_page_title(e){this.$$set({control_page_title:e}),z()}get app_mode(){return this.$$.ctx[8]}set app_mode(e){this.$$set({app_mode:e}),z()}get theme_mode(){return this.$$.ctx[9]}set theme_mode(e){this.$$set({theme_mode:e}),z()}get app(){return this.$$.ctx[10]}set app(e){this.$$set({app:e}),z()}get space_id(){return this.$$.ctx[11]}set space_id(e){this.$$set({space_id:e}),z()}get version(){return this.$$.ctx[12]}set version(e){this.$$set({version:e}),z()}get js(){return this.$$.ctx[33]}set js(e){this.$$set({js:e}),z()}get fill_height(){return this.$$.ctx[34]}set fill_height(e){this.$$set({fill_height:e}),z()}get ready(){return this.$$.ctx[29]}set ready(e){this.$$set({ready:e}),z()}get username(){return this.$$.ctx[13]}set username(e){this.$$set({username:e}),z()}get api_prefix(){return this.$$.ctx[35]}set api_prefix(e){this.$$set({api_prefix:e}),z()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),z()}get initial_layout(){return this.$$.ctx[36]}set initial_layout(e){this.$$set({initial_layout:e}),z()}get css(){return this.$$.ctx[15]}set css(e){this.$$set({css:e}),z()}get search_params(){return this.$$.ctx[37]}set search_params(e){this.$$set({search_params:e}),z()}get render_complete(){return this.$$.ctx[30]}set render_complete(e){this.$$set({render_complete:e}),z()}get add_new_message(){return this.$$.ctx[38]}}export{ps as default};
//# sourceMappingURL=Blocks-Cj2aVjyp.js.map
