import{a as x,i as ee,s as le,Q as ae,q as _,l as v,v as C,w as W,o as w,b6 as _e,f as E,p as N,A as Ue,ao as A,x as B,aq as J,k as K,n as X,F as T,P as Q,E as U,r as H,$ as q,y as Y,b as z,z as Z,t as p,a3 as Ll,I as fe,a5 as ce,c as F,m as D,a6 as ue,d as P,B as Ge,Y as Je,S as We,a0 as Xe,a4 as $e}from"../lite.js";import{g as xe}from"./color-Dz5ygqaR.js";import{B as el}from"./BlockLabel-B0HN-MOU.js";import{E as ll}from"./Empty-C76eC2zW.js";function Ml(l){let e,n,t;return{c(){e=ae("svg"),n=ae("path"),t=ae("path"),_(n,"fill","currentColor"),_(n,"d","M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z"),_(t,"fill","currentColor"),_(t,"d","M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),_(e,"aria-hidden","true"),_(e,"role","img"),_(e,"class","iconify iconify--carbon"),_(e,"width","100%"),_(e,"height","100%"),_(e,"preserveAspectRatio","xMidYMid meet"),_(e,"viewBox","0 0 32 32")},m(s,i){v(s,e,i),C(e,n),C(e,t)},p:W,i:W,o:W,d(s){s&&w(e)}}}class ie extends x{constructor(e){super(),ee(this,e,null,Ml,le,{})}}function he(l,e,n){if(!n){var t=document.createElement("canvas");n=t.getContext("2d")}n.fillStyle=l,n.fillRect(0,0,1,1);const[s,i,o]=n.getImageData(0,0,1,1).data;return n.clearRect(0,0,1,1),`rgba(${s}, ${i}, ${o}, ${255/e})`}function nl(l,e,n,t){for(const s in l){const i=l[s].trim();i in _e?e[s]=_e[i]:e[s]={primary:n?he(l[s],1,t):l[s],secondary:n?he(l[s],.5,t):l[s]}}}function tl(l,e){let n=[],t=null,s=null;for(const i of l)s===i.class_or_confidence?t=t?t+i.token:i.token:(t!==null&&n.push({token:t,class_or_confidence:s}),t=i.token,s=i.class_or_confidence);return t!==null&&n.push({token:t,class_or_confidence:s}),n}function de(l,e,n){const t=l.slice();t[18]=e[n];const s=typeof t[18].class_or_confidence=="string"?parseInt(t[18].class_or_confidence):t[18].class_or_confidence;return t[27]=s,t}function ge(l,e,n){const t=l.slice();return t[18]=e[n],t[20]=n,t}function me(l,e,n){const t=l.slice();return t[21]=e[n],t[23]=n,t}function be(l,e,n){const t=l.slice();return t[24]=e[n][0],t[25]=e[n][1],t[20]=n,t}function Vl(l){let e,n,t=l[1]&&ke(),s=A(l[0]),i=[];for(let o=0;o<s.length;o+=1)i[o]=pe(de(l,s,o));return{c(){t&&t.c(),e=B(),n=N("div");for(let o=0;o<i.length;o+=1)i[o].c();_(n,"class","textfield svelte-ju12zg"),_(n,"data-testid","highlighted-text:textfield")},m(o,a){t&&t.m(o,a),v(o,e,a),v(o,n,a);for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(n,null)},p(o,a){if(o[1]?t||(t=ke(),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),a&1){s=A(o[0]);let r;for(r=0;r<s.length;r+=1){const c=de(o,s,r);i[r]?i[r].p(c,a):(i[r]=pe(c),i[r].c(),i[r].m(n,null))}for(;r<i.length;r+=1)i[r].d(1);i.length=s.length}},d(o){o&&(w(e),w(n)),t&&t.d(o),J(i,o)}}}function Hl(l){let e,n,t=l[1]&&ve(l),s=A(l[0]),i=[];for(let o=0;o<s.length;o+=1)i[o]=Ee(ge(l,s,o));return{c(){t&&t.c(),e=B(),n=N("div");for(let o=0;o<i.length;o+=1)i[o].c();_(n,"class","textfield svelte-ju12zg")},m(o,a){t&&t.m(o,a),v(o,e,a),v(o,n,a);for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(n,null)},p(o,a){if(o[1]?t?t.p(o,a):(t=ve(o),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),a&223){s=A(o[0]);let r;for(r=0;r<s.length;r+=1){const c=ge(o,s,r);i[r]?i[r].p(c,a):(i[r]=Ee(c),i[r].c(),i[r].m(n,null))}for(;r<i.length;r+=1)i[r].d(1);i.length=s.length}},d(o){o&&(w(e),w(n)),t&&t.d(o),J(i,o)}}}function ke(l){let e;return{c(){e=N("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",_(e,"class","color-legend svelte-ju12zg"),_(e,"data-testid","highlighted-text:color-legend")},m(n,t){v(n,e,t)},d(n){n&&w(e)}}}function pe(l){let e,n,t=l[18].token+"",s,i,o;return{c(){e=N("span"),n=N("span"),s=K(t),i=B(),_(n,"class","text svelte-ju12zg"),_(e,"class","textspan score-text svelte-ju12zg"),_(e,"style",o="background-color: rgba("+(l[27]&&l[27]<0?"128, 90, 213,"+-l[27]:"239, 68, 60,"+l[27])+")")},m(a,r){v(a,e,r),C(e,n),C(n,s),C(e,i)},p(a,r){r&1&&t!==(t=a[18].token+"")&&X(s,t),r&1&&o!==(o="background-color: rgba("+(a[27]&&a[27]<0?"128, 90, 213,"+-a[27]:"239, 68, 60,"+a[27])+")")&&_(e,"style",o)},d(a){a&&w(e)}}}function ve(l){let e,n=A(Object.entries(l[6])),t=[];for(let s=0;s<n.length;s+=1)t[s]=we(be(l,n,s));return{c(){e=N("div");for(let s=0;s<t.length;s+=1)t[s].c();_(e,"class","category-legend svelte-ju12zg"),_(e,"data-testid","highlighted-text:category-legend")},m(s,i){v(s,e,i);for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(e,null)},p(s,i){if(i&832){n=A(Object.entries(s[6]));let o;for(o=0;o<n.length;o+=1){const a=be(s,n,o);t[o]?t[o].p(a,i):(t[o]=we(a),t[o].c(),t[o].m(e,null))}for(;o<t.length;o+=1)t[o].d(1);t.length=n.length}},d(s){s&&w(e),J(t,s)}}}function we(l){let e,n=l[24]+"",t,s,i,o;function a(){return l[11](l[24])}function r(){return l[12](l[24])}return{c(){e=N("div"),t=K(n),s=B(),_(e,"class","category-label svelte-ju12zg"),_(e,"style","background-color:"+l[25].secondary)},m(c,f){v(c,e,f),C(e,t),C(e,s),i||(o=[T(e,"mouseover",a),T(e,"focus",r),T(e,"mouseout",l[13]),T(e,"blur",l[14])],i=!0)},p(c,f){l=c},d(c){c&&w(e),i=!1,Q(o)}}}function ye(l){let e,n,t=l[21]+"",s,i,o,a,r=!l[1]&&l[2]&&l[18].class_or_confidence!==null&&je(l);function c(){return l[15](l[20],l[18])}return{c(){e=N("span"),n=N("span"),s=K(t),i=B(),r&&r.c(),_(n,"class","text svelte-ju12zg"),H(n,"no-label",l[18].class_or_confidence===null||!l[6][l[18].class_or_confidence]),_(e,"class","textspan svelte-ju12zg"),H(e,"no-cat",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence),H(e,"hl",l[18].class_or_confidence!==null),H(e,"selectable",l[3]),q(e,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].secondary)},m(f,m){v(f,e,m),C(e,n),C(n,s),C(e,i),r&&r.m(e,null),o||(a=T(e,"click",c),o=!0)},p(f,m){l=f,m&1&&t!==(t=l[21]+"")&&X(s,t),m&65&&H(n,"no-label",l[18].class_or_confidence===null||!l[6][l[18].class_or_confidence]),!l[1]&&l[2]&&l[18].class_or_confidence!==null?r?r.p(l,m):(r=je(l),r.c(),r.m(e,null)):r&&(r.d(1),r=null),m&17&&H(e,"no-cat",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence),m&1&&H(e,"hl",l[18].class_or_confidence!==null),m&8&&H(e,"selectable",l[3]),m&17&&q(e,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].secondary)},d(f){f&&w(e),r&&r.d(),o=!1,a()}}}function je(l){let e,n,t=l[18].class_or_confidence+"",s;return{c(){e=K(` 
								`),n=N("span"),s=K(t),_(n,"class","label svelte-ju12zg"),q(n,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].primary)},m(i,o){v(i,e,o),v(i,n,o),C(n,s)},p(i,o){o&1&&t!==(t=i[18].class_or_confidence+"")&&X(s,t),o&17&&q(n,"background-color",i[18].class_or_confidence===null||i[4]&&i[4]!==i[18].class_or_confidence?"":i[6][i[18].class_or_confidence].primary)},d(i){i&&(w(e),w(n))}}}function ze(l){let e;return{c(){e=N("br")},m(n,t){v(n,e,t)},d(n){n&&w(e)}}}function Se(l){let e=l[21].trim()!=="",n,t=l[23]<oe(l[18].token).length-1,s,i=e&&ye(l),o=t&&ze();return{c(){i&&i.c(),n=B(),o&&o.c(),s=U()},m(a,r){i&&i.m(a,r),v(a,n,r),o&&o.m(a,r),v(a,s,r)},p(a,r){r&1&&(e=a[21].trim()!==""),e?i?i.p(a,r):(i=ye(a),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null),r&1&&(t=a[23]<oe(a[18].token).length-1),t?o||(o=ze(),o.c(),o.m(s.parentNode,s)):o&&(o.d(1),o=null)},d(a){a&&(w(n),w(s)),i&&i.d(a),o&&o.d(a)}}}function Ee(l){let e,n=A(oe(l[18].token)),t=[];for(let s=0;s<n.length;s+=1)t[s]=Se(me(l,n,s));return{c(){for(let s=0;s<t.length;s+=1)t[s].c();e=U()},m(s,i){for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(s,i);v(s,e,i)},p(s,i){if(i&223){n=A(oe(s[18].token));let o;for(o=0;o<n.length;o+=1){const a=me(s,n,o);t[o]?t[o].p(a,i):(t[o]=Se(a),t[o].c(),t[o].m(e.parentNode,e))}for(;o<t.length;o+=1)t[o].d(1);t.length=n.length}},d(s){s&&w(e),J(t,s)}}}function Bl(l){let e;function n(i,o){return i[5]==="categories"?Hl:Vl}let t=n(l),s=t(l);return{c(){e=N("div"),s.c(),_(e,"class","container svelte-ju12zg")},m(i,o){v(i,e,o),s.m(e,null)},p(i,[o]){t===(t=n(i))&&s?s.p(i,o):(s.d(1),s=t(i),s&&(s.c(),s.m(e,null)))},i:W,o:W,d(i){i&&w(e),s.d()}}}function oe(l){return l.split(`
`)}function Il(l,e,n){const t=typeof document<"u";let{value:s=[]}=e,{show_legend:i=!1}=e,{show_inline_category:o=!0}=e,{color_map:a={}}=e,{selectable:r=!1}=e,c,f={},m="";const y=Ue();let b;function u(k){n(4,m=k)}function d(){n(4,m="")}const S=k=>u(k),M=k=>u(k),O=()=>d(),g=()=>d(),L=(k,V)=>{y("select",{index:k,value:[V.token,V.class_or_confidence]})};return l.$$set=k=>{"value"in k&&n(0,s=k.value),"show_legend"in k&&n(1,i=k.show_legend),"show_inline_category"in k&&n(2,o=k.show_inline_category),"color_map"in k&&n(10,a=k.color_map),"selectable"in k&&n(3,r=k.selectable)},l.$$.update=()=>{if(l.$$.dirty&1025){if(a||n(10,a={}),s.length>0){for(let k of s)if(k.class_or_confidence!==null)if(typeof k.class_or_confidence=="string"){if(n(5,b="categories"),!(k.class_or_confidence in a)){let V=xe(Object.keys(a).length);n(10,a[k.class_or_confidence]=V,a)}}else n(5,b="scores")}nl(a,f,t,c)}},[s,i,o,r,m,b,f,y,u,d,a,S,M,O,g,L]}class Rl extends x{constructor(e){super(),ee(this,e,Il,Bl,le,{value:0,show_legend:1,show_inline_category:2,color_map:10,selectable:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get show_legend(){return this.$$.ctx[1]}set show_legend(e){this.$$set({show_legend:e}),E()}get show_inline_category(){return this.$$.ctx[2]}set show_inline_category(e){this.$$set({show_inline_category:e}),E()}get color_map(){return this.$$.ctx[10]}set color_map(e){this.$$set({color_map:e}),E()}get selectable(){return this.$$.ctx[3]}set selectable(e){this.$$set({selectable:e}),E()}}const Al=Rl;function ql(l){let e,n,t,s;return{c(){e=N("input"),_(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,_(e,"type","number"),_(e,"step","0.1"),_(e,"style",n="background-color: rgba("+(typeof l[1]=="number"&&l[1]<0?"128, 90, 213,"+-l[1]:"239, 68, 60,"+l[1])+")"),e.value=l[1],q(e,"width","7ch")},m(i,o){v(i,e,o),e.focus(),t||(s=[T(e,"input",l[8]),T(e,"blur",l[14]),T(e,"keydown",l[15])],t=!0)},p(i,o){o&2&&n!==(n="background-color: rgba("+(typeof i[1]=="number"&&i[1]<0?"128, 90, 213,"+-i[1]:"239, 68, 60,"+i[1])+")")&&_(e,"style",n),o&2&&e.value!==i[1]&&(e.value=i[1]);const a=o&2;(o&2||a)&&q(e,"width","7ch")},d(i){i&&w(e),t=!1,Q(s)}}}function Fl(l){let e,n,t,s;return{c(){e=N("input"),_(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,_(e,"id",n=`label-input-${l[3]}`),_(e,"type","text"),_(e,"placeholder","label"),e.value=l[1],q(e,"background-color",l[1]===null||l[2]&&l[2]!==l[1]?"":l[6][l[1]].primary),q(e,"width",l[7]?l[7].toString()?.length+4+"ch":"8ch")},m(i,o){v(i,e,o),e.focus(),t||(s=[T(e,"input",l[8]),T(e,"blur",l[12]),T(e,"keydown",l[13]),T(e,"focus",Pl)],t=!0)},p(i,o){o&8&&n!==(n=`label-input-${i[3]}`)&&_(e,"id",n),o&2&&e.value!==i[1]&&(e.value=i[1]),o&70&&q(e,"background-color",i[1]===null||i[2]&&i[2]!==i[1]?"":i[6][i[1]].primary),o&128&&q(e,"width",i[7]?i[7].toString()?.length+4+"ch":"8ch")},d(i){i&&w(e),t=!1,Q(s)}}}function Dl(l){let e;function n(i,o){return i[5]?ql:Fl}let t=n(l),s=t(l);return{c(){s.c(),e=U()},m(i,o){s.m(i,o),v(i,e,o)},p(i,[o]){t===(t=n(i))&&s?s.p(i,o):(s.d(1),s=t(i),s&&(s.c(),s.m(e.parentNode,e)))},i:W,o:W,d(i){i&&w(e),s.d(i)}}}function Pl(l){let e=l.target;e&&e.placeholder&&(e.placeholder="")}function Yl(l,e,n){let{value:t}=e,{category:s}=e,{active:i}=e,{labelToEdit:o}=e,{indexOfLabel:a}=e,{text:r}=e,{handleValueChange:c}=e,{isScoresMode:f=!1}=e,{_color_map:m}=e,y=s;function b(g){let L=g.target;L&&n(7,y=L.value)}function u(g,L,k){let V=g.target;n(10,t=[...t.slice(0,L),{token:k,class_or_confidence:V.value===""?null:f?Number(V.value):V.value},...t.slice(L+1)]),c()}const d=g=>u(g,a,r),S=g=>{g.key==="Enter"&&(u(g,a,r),n(0,o=-1))},M=g=>u(g,a,r),O=g=>{g.key==="Enter"&&(u(g,a,r),n(0,o=-1))};return l.$$set=g=>{"value"in g&&n(10,t=g.value),"category"in g&&n(1,s=g.category),"active"in g&&n(2,i=g.active),"labelToEdit"in g&&n(0,o=g.labelToEdit),"indexOfLabel"in g&&n(3,a=g.indexOfLabel),"text"in g&&n(4,r=g.text),"handleValueChange"in g&&n(11,c=g.handleValueChange),"isScoresMode"in g&&n(5,f=g.isScoresMode),"_color_map"in g&&n(6,m=g._color_map)},[o,s,i,a,r,f,m,y,b,u,t,c,d,S,M,O]}class ol extends x{constructor(e){super(),ee(this,e,Yl,Dl,le,{value:10,category:1,active:2,labelToEdit:0,indexOfLabel:3,text:4,handleValueChange:11,isScoresMode:5,_color_map:6})}get value(){return this.$$.ctx[10]}set value(e){this.$$set({value:e}),E()}get category(){return this.$$.ctx[1]}set category(e){this.$$set({category:e}),E()}get active(){return this.$$.ctx[2]}set active(e){this.$$set({active:e}),E()}get labelToEdit(){return this.$$.ctx[0]}set labelToEdit(e){this.$$set({labelToEdit:e}),E()}get indexOfLabel(){return this.$$.ctx[3]}set indexOfLabel(e){this.$$set({indexOfLabel:e}),E()}get text(){return this.$$.ctx[4]}set text(e){this.$$set({text:e}),E()}get handleValueChange(){return this.$$.ctx[11]}set handleValueChange(e){this.$$set({handleValueChange:e}),E()}get isScoresMode(){return this.$$.ctx[5]}set isScoresMode(e){this.$$set({isScoresMode:e}),E()}get _color_map(){return this.$$.ctx[6]}set _color_map(e){this.$$set({_color_map:e}),E()}}function Te(l,e,n){const t=l.slice();t[45]=e[n].token,t[46]=e[n].class_or_confidence,t[48]=n;const s=typeof t[46]=="string"?parseInt(t[46]):t[46];return t[54]=s,t}function Ne(l,e,n){const t=l.slice();return t[45]=e[n].token,t[46]=e[n].class_or_confidence,t[48]=n,t}function Oe(l,e,n){const t=l.slice();return t[49]=e[n],t[51]=n,t}function Ce(l,e,n){const t=l.slice();return t[46]=e[n][0],t[52]=e[n][1],t[48]=n,t}function Zl(l){let e,n,t,s=l[1]&&Le(),i=A(l[0]),o=[];for(let r=0;r<i.length;r+=1)o[r]=He(Te(l,i,r));const a=r=>z(o[r],1,1,()=>{o[r]=null});return{c(){s&&s.c(),e=B(),n=N("div");for(let r=0;r<o.length;r+=1)o[r].c();_(n,"class","textfield svelte-1ozsnjl"),_(n,"data-testid","highlighted-text:textfield")},m(r,c){s&&s.m(r,c),v(r,e,c),v(r,n,c);for(let f=0;f<o.length;f+=1)o[f]&&o[f].m(n,null);t=!0},p(r,c){if(r[1]?s||(s=Le(),s.c(),s.m(e.parentNode,e)):s&&(s.d(1),s=null),c[0]&889){i=A(r[0]);let f;for(f=0;f<i.length;f+=1){const m=Te(r,i,f);o[f]?(o[f].p(m,c),p(o[f],1)):(o[f]=He(m),o[f].c(),p(o[f],1),o[f].m(n,null))}for(Y(),f=i.length;f<o.length;f+=1)a(f);Z()}},i(r){if(!t){for(let c=0;c<i.length;c+=1)p(o[c]);t=!0}},o(r){o=o.filter(Boolean);for(let c=0;c<o.length;c+=1)z(o[c]);t=!1},d(r){r&&(w(e),w(n)),s&&s.d(r),J(o,r)}}}function Kl(l){let e,n,t,s=l[1]&&Be(l),i=A(l[0]),o=[];for(let r=0;r<i.length;r+=1)o[r]=Ze(Ne(l,i,r));const a=r=>z(o[r],1,1,()=>{o[r]=null});return{c(){s&&s.c(),e=B(),n=N("div");for(let r=0;r<o.length;r+=1)o[r].c();_(n,"class","textfield svelte-1ozsnjl")},m(r,c){s&&s.m(r,c),v(r,e,c),v(r,n,c);for(let f=0;f<o.length;f+=1)o[f]&&o[f].m(n,null);t=!0},p(r,c){if(r[1]?s?s.p(r,c):(s=Be(r),s.c(),s.m(e.parentNode,e)):s&&(s.d(1),s=null),c[0]&13183){i=A(r[0]);let f;for(f=0;f<i.length;f+=1){const m=Ne(r,i,f);o[f]?(o[f].p(m,c),p(o[f],1)):(o[f]=Ze(m),o[f].c(),p(o[f],1),o[f].m(n,null))}for(Y(),f=i.length;f<o.length;f+=1)a(f);Z()}},i(r){if(!t){for(let c=0;c<i.length;c+=1)p(o[c]);t=!0}},o(r){o=o.filter(Boolean);for(let c=0;c<o.length;c+=1)z(o[c]);t=!1},d(r){r&&(w(e),w(n)),s&&s.d(r),J(o,r)}}}function Le(l){let e;return{c(){e=N("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",_(e,"class","color-legend svelte-1ozsnjl"),_(e,"data-testid","highlighted-text:color-legend")},m(n,t){v(n,e,t)},d(n){n&&w(e)}}}function Me(l){let e,n,t;function s(o){l[32](o)}let i={labelToEdit:l[6],_color_map:l[3],category:l[46],active:l[5],indexOfLabel:l[48],text:l[45],handleValueChange:l[9],isScoresMode:!0};return l[0]!==void 0&&(i.value=l[0]),e=new ol({props:i}),fe.push(()=>ce(e,"value",s)),{c(){F(e.$$.fragment)},m(o,a){D(e,o,a),t=!0},p(o,a){const r={};a[0]&64&&(r.labelToEdit=o[6]),a[0]&8&&(r._color_map=o[3]),a[0]&1&&(r.category=o[46]),a[0]&32&&(r.active=o[5]),a[0]&1&&(r.text=o[45]),!n&&a[0]&1&&(n=!0,r.value=o[0],ue(()=>n=!1)),e.$set(r)},i(o){t||(p(e.$$.fragment,o),t=!0)},o(o){z(e.$$.fragment,o),t=!1},d(o){P(e,o)}}}function Ve(l){let e,n,t;function s(){return l[37](l[48])}function i(...o){return l[38](l[48],...o)}return{c(){e=N("span"),e.textContent="×",_(e,"class","label-clear-button svelte-1ozsnjl"),_(e,"role","button"),_(e,"aria-roledescription","Remove label from text"),_(e,"tabindex","0")},m(o,a){v(o,e,a),n||(t=[T(e,"click",s),T(e,"keydown",i)],n=!0)},p(o,a){l=o},d(o){o&&w(e),n=!1,Q(t)}}}function He(l){let e,n,t,s=l[45]+"",i,o,a,r,c,f,m,y,b=l[46]&&l[6]===l[48]&&Me(l);function u(){return l[33](l[48])}function d(){return l[34](l[48])}function S(){return l[35](l[48])}function M(...g){return l[36](l[48],...g)}let O=l[46]&&l[4]===l[48]&&Ve(l);return{c(){e=N("span"),n=N("span"),t=N("span"),i=K(s),o=B(),b&&b.c(),r=B(),O&&O.c(),c=B(),_(t,"class","text svelte-1ozsnjl"),_(n,"class","textspan score-text svelte-1ozsnjl"),_(n,"role","button"),_(n,"tabindex","0"),_(n,"style",a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"),H(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),H(n,"hl",l[46]!==null),_(e,"class","score-text-container svelte-1ozsnjl")},m(g,L){v(g,e,L),C(e,n),C(n,t),C(t,i),C(n,o),b&&b.m(n,null),C(e,r),O&&O.m(e,null),C(e,c),f=!0,m||(y=[T(n,"mouseover",u),T(n,"focus",d),T(n,"click",S),T(n,"keydown",M)],m=!0)},p(g,L){l=g,(!f||L[0]&1)&&s!==(s=l[45]+"")&&X(i,s),l[46]&&l[6]===l[48]?b?(b.p(l,L),L[0]&65&&p(b,1)):(b=Me(l),b.c(),p(b,1),b.m(n,null)):b&&(Y(),z(b,1,1,()=>{b=null}),Z()),(!f||L[0]&1&&a!==(a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"))&&_(n,"style",a),(!f||L[0]&33)&&H(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!f||L[0]&1)&&H(n,"hl",l[46]!==null),l[46]&&l[4]===l[48]?O?O.p(l,L):(O=Ve(l),O.c(),O.m(e,c)):O&&(O.d(1),O=null)},i(g){f||(p(b),f=!0)},o(g){z(b),f=!1},d(g){g&&w(e),b&&b.d(),O&&O.d(),m=!1,Q(y)}}}function Be(l){let e,n=l[3]&&Ie(l);return{c(){e=N("div"),n&&n.c(),_(e,"class","class_or_confidence-legend svelte-1ozsnjl"),_(e,"data-testid","highlighted-text:class_or_confidence-legend")},m(t,s){v(t,e,s),n&&n.m(e,null)},p(t,s){t[3]?n?n.p(t,s):(n=Ie(t),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},d(t){t&&w(e),n&&n.d()}}}function Ie(l){let e,n=A(Object.entries(l[3])),t=[];for(let s=0;s<n.length;s+=1)t[s]=Re(Ce(l,n,s));return{c(){for(let s=0;s<t.length;s+=1)t[s].c();e=U()},m(s,i){for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(s,i);v(s,e,i)},p(s,i){if(i[0]&3080){n=A(Object.entries(s[3]));let o;for(o=0;o<n.length;o+=1){const a=Ce(s,n,o);t[o]?t[o].p(a,i):(t[o]=Re(a),t[o].c(),t[o].m(e.parentNode,e))}for(;o<t.length;o+=1)t[o].d(1);t.length=n.length}},d(s){s&&w(e),J(t,s)}}}function Re(l){let e,n=l[46]+"",t,s,i,o,a;function r(){return l[15](l[46])}function c(){return l[16](l[46])}return{c(){e=N("div"),t=K(n),s=B(),_(e,"role","button"),_(e,"aria-roledescription","Categories of highlighted text. Hover to see text with this class_or_confidence highlighted."),_(e,"tabindex","0"),_(e,"class","class_or_confidence-label svelte-1ozsnjl"),_(e,"style",i="background-color:"+l[52].secondary)},m(f,m){v(f,e,m),C(e,t),C(e,s),o||(a=[T(e,"mouseover",r),T(e,"focus",c),T(e,"mouseout",l[17]),T(e,"blur",l[18])],o=!0)},p(f,m){l=f,m[0]&8&&n!==(n=l[46]+"")&&X(t,n),m[0]&8&&i!==(i="background-color:"+l[52].secondary)&&_(e,"style",i)},d(f){f&&w(e),o=!1,Q(a)}}}function Ae(l){let e,n,t,s=l[49]+"",i,o,a,r,c,f,m;function y(){return l[20](l[48])}function b(){return l[21](l[48])}function u(){return l[22](l[48])}let d=!l[1]&&l[46]!==null&&l[6]!==l[48]&&qe(l),S=l[6]===l[48]&&l[46]!==null&&Fe(l);function M(){return l[26](l[46],l[48],l[45])}function O(...V){return l[27](l[46],l[48],l[45],...V)}function g(){return l[28](l[48])}function L(){return l[29](l[48])}let k=l[46]!==null&&De(l);return{c(){e=N("span"),n=N("span"),t=N("span"),i=K(s),o=B(),d&&d.c(),a=B(),S&&S.c(),r=B(),k&&k.c(),_(t,"class","text svelte-1ozsnjl"),_(t,"role","button"),_(t,"tabindex","0"),H(t,"no-label",l[46]===null),_(n,"role","button"),_(n,"tabindex","0"),_(n,"class","textspan svelte-1ozsnjl"),H(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),H(n,"hl",l[46]!==null),H(n,"selectable",l[2]),q(n,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),_(e,"class","text-class_or_confidence-container svelte-1ozsnjl")},m(V,R){v(V,e,R),C(e,n),C(n,t),C(t,i),C(n,o),d&&d.m(n,null),C(n,a),S&&S.m(n,null),C(e,r),k&&k.m(e,null),c=!0,f||(m=[T(t,"keydown",l[19]),T(t,"focus",y),T(t,"mouseover",b),T(t,"click",u),T(n,"click",M),T(n,"keydown",O),T(n,"focus",g),T(n,"mouseover",L)],f=!0)},p(V,R){l=V,(!c||R[0]&1)&&s!==(s=l[49]+"")&&X(i,s),(!c||R[0]&1)&&H(t,"no-label",l[46]===null),!l[1]&&l[46]!==null&&l[6]!==l[48]?d?d.p(l,R):(d=qe(l),d.c(),d.m(n,a)):d&&(d.d(1),d=null),l[6]===l[48]&&l[46]!==null?S?(S.p(l,R),R[0]&65&&p(S,1)):(S=Fe(l),S.c(),p(S,1),S.m(n,null)):S&&(Y(),z(S,1,1,()=>{S=null}),Z()),(!c||R[0]&33)&&H(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!c||R[0]&1)&&H(n,"hl",l[46]!==null),(!c||R[0]&4)&&H(n,"selectable",l[2]),R[0]&41&&q(n,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),l[46]!==null?k?k.p(l,R):(k=De(l),k.c(),k.m(e,null)):k&&(k.d(1),k=null)},i(V){c||(p(S),c=!0)},o(V){z(S),c=!1},d(V){V&&w(e),d&&d.d(),S&&S.d(),k&&k.d(),f=!1,Q(m)}}}function qe(l){let e,n=l[46]+"",t,s,i;function o(){return l[23](l[48])}function a(){return l[24](l[48])}return{c(){e=N("span"),t=K(n),_(e,"id",`label-tag-${l[48]}`),_(e,"class","label svelte-1ozsnjl"),_(e,"role","button"),_(e,"tabindex","0"),q(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},m(r,c){v(r,e,c),C(e,t),s||(i=[T(e,"click",o),T(e,"keydown",a)],s=!0)},p(r,c){l=r,c[0]&1&&n!==(n=l[46]+"")&&X(t,n),c[0]&41&&q(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},d(r){r&&w(e),s=!1,Q(i)}}}function Fe(l){let e,n,t,s;function i(a){l[25](a)}let o={labelToEdit:l[6],category:l[46],active:l[5],_color_map:l[3],indexOfLabel:l[48],text:l[45],handleValueChange:l[9]};return l[0]!==void 0&&(o.value=l[0]),n=new ol({props:o}),fe.push(()=>ce(n,"value",i)),{c(){e=K(` 
									`),F(n.$$.fragment)},m(a,r){v(a,e,r),D(n,a,r),s=!0},p(a,r){const c={};r[0]&64&&(c.labelToEdit=a[6]),r[0]&1&&(c.category=a[46]),r[0]&32&&(c.active=a[5]),r[0]&8&&(c._color_map=a[3]),r[0]&1&&(c.text=a[45]),!t&&r[0]&1&&(t=!0,c.value=a[0],ue(()=>t=!1)),n.$set(c)},i(a){s||(p(n.$$.fragment,a),s=!0)},o(a){z(n.$$.fragment,a),s=!1},d(a){a&&w(e),P(n,a)}}}function De(l){let e,n,t;function s(){return l[30](l[48])}function i(...o){return l[31](l[48],...o)}return{c(){e=N("span"),e.textContent="×",_(e,"class","label-clear-button svelte-1ozsnjl"),_(e,"role","button"),_(e,"aria-roledescription","Remove label from text"),_(e,"tabindex","0")},m(o,a){v(o,e,a),n||(t=[T(e,"click",s),T(e,"keydown",i)],n=!0)},p(o,a){l=o},d(o){o&&w(e),n=!1,Q(t)}}}function Pe(l){let e;return{c(){e=N("br")},m(n,t){v(n,e,t)},d(n){n&&w(e)}}}function Ye(l){let e=l[49].trim()!=="",n,t=l[51]<se(l[45]).length-1,s,i,o=e&&Ae(l),a=t&&Pe();return{c(){o&&o.c(),n=B(),a&&a.c(),s=U()},m(r,c){o&&o.m(r,c),v(r,n,c),a&&a.m(r,c),v(r,s,c),i=!0},p(r,c){c[0]&1&&(e=r[49].trim()!==""),e?o?(o.p(r,c),c[0]&1&&p(o,1)):(o=Ae(r),o.c(),p(o,1),o.m(n.parentNode,n)):o&&(Y(),z(o,1,1,()=>{o=null}),Z()),c[0]&1&&(t=r[51]<se(r[45]).length-1),t?a||(a=Pe(),a.c(),a.m(s.parentNode,s)):a&&(a.d(1),a=null)},i(r){i||(p(o),i=!0)},o(r){z(o),i=!1},d(r){r&&(w(n),w(s)),o&&o.d(r),a&&a.d(r)}}}function Ze(l){let e,n,t=A(se(l[45])),s=[];for(let o=0;o<t.length;o+=1)s[o]=Ye(Oe(l,t,o));const i=o=>z(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=U()},m(o,a){for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(o,a);v(o,e,a),n=!0},p(o,a){if(a[0]&13183){t=A(se(o[45]));let r;for(r=0;r<t.length;r+=1){const c=Oe(o,t,r);s[r]?(s[r].p(c,a),p(s[r],1)):(s[r]=Ye(c),s[r].c(),p(s[r],1),s[r].m(e.parentNode,e))}for(Y(),r=t.length;r<s.length;r+=1)i(r);Z()}},i(o){if(!n){for(let a=0;a<t.length;a+=1)p(s[a]);n=!0}},o(o){s=s.filter(Boolean);for(let a=0;a<s.length;a+=1)z(s[a]);n=!1},d(o){o&&w(e),J(s,o)}}}function Ql(l){let e,n,t,s;const i=[Kl,Zl],o=[];function a(r,c){return r[7]==="categories"?0:1}return n=a(l),t=o[n]=i[n](l),{c(){e=N("div"),t.c(),_(e,"class","container svelte-1ozsnjl")},m(r,c){v(r,e,c),o[n].m(e,null),s=!0},p(r,c){let f=n;n=a(r),n===f?o[n].p(r,c):(Y(),z(o[f],1,1,()=>{o[f]=null}),Z(),t=o[n],t?t.p(r,c):(t=o[n]=i[n](r),t.c()),p(t,1),t.m(e,null))},i(r){s||(p(t),s=!0)},o(r){z(t),s=!1},d(r){r&&w(e),o[n].d()}}}function se(l){return l.split(`
`)}function Ul(l,e,n){const t=typeof document<"u";let{value:s=[]}=e,{show_legend:i=!1}=e,{color_map:o={}}=e,{selectable:a=!1}=e,r=-1,c,f={},m="",y,b=-1;Ll(()=>{const h=()=>{y=window.getSelection(),V(),window.removeEventListener("mouseup",h)};window.addEventListener("mousedown",()=>{window.addEventListener("mouseup",h)})});async function u(h,I){if(y?.toString()&&r!==-1&&s[r].token.toString().includes(y.toString())){const G=Symbol(),$=s[r].token,[Tl,Nl,Ol]=[$.substring(0,h),$.substring(h,I),$.substring(I)];let ne=[...s.slice(0,r),{token:Tl,class_or_confidence:null},{token:Nl,class_or_confidence:O==="scores"?1:"label",flag:G},{token:Ol,class_or_confidence:null},...s.slice(r+1)];n(6,b=ne.findIndex(({flag:te})=>te===G)),ne=ne.filter(te=>te.token.trim()!==""),n(0,s=ne.map(({flag:te,...Cl})=>Cl)),M(),document.getElementById(`label-input-${b}`)?.focus()}}const d=Ue();function S(h){!s||h<0||h>=s.length||(n(0,s[h].class_or_confidence=null,s),n(0,s=tl(s)),M(),window.getSelection()?.empty())}function M(){d("change",s),n(6,b=-1),i&&(n(14,o={}),n(3,f={}))}let O;function g(h){n(5,m=h)}function L(){n(5,m="")}async function k(h){y=window.getSelection(),h.key==="Enter"&&V()}function V(){if(y&&y?.toString().trim()!==""){const h=y.getRangeAt(0).startOffset,I=y.getRangeAt(0).endOffset;u(h,I)}}function R(h,I,G){d("select",{index:h,value:[I,G]})}const re=h=>g(h),j=h=>g(h),sl=()=>L(),il=()=>L(),rl=h=>k(h),al=h=>n(4,r=h),fl=h=>n(4,r=h),cl=h=>n(6,b=h),ul=h=>n(6,b=h),_l=h=>n(6,b=h);function hl(h){s=h,n(0,s)}const dl=(h,I,G)=>{h!==null&&R(I,G,h)},gl=(h,I,G,$)=>{h!==null?(n(6,b=I),R(I,G,h)):k($)},ml=h=>n(4,r=h),bl=h=>n(4,r=h),kl=h=>S(h),pl=(h,I)=>{I.key==="Enter"&&S(h)};function vl(h){s=h,n(0,s)}const wl=h=>n(4,r=h),yl=h=>n(4,r=h),jl=h=>n(6,b=h),zl=(h,I)=>{I.key==="Enter"&&n(6,b=h)},Sl=h=>S(h),El=(h,I)=>{I.key==="Enter"&&S(h)};return l.$$set=h=>{"value"in h&&n(0,s=h.value),"show_legend"in h&&n(1,i=h.show_legend),"color_map"in h&&n(14,o=h.color_map),"selectable"in h&&n(2,a=h.selectable)},l.$$.update=()=>{if(l.$$.dirty[0]&16393){if(o||n(14,o={}),s.length>0){for(let h of s)if(h.class_or_confidence!==null)if(typeof h.class_or_confidence=="string"){if(n(7,O="categories"),!(h.class_or_confidence in o)){let I=xe(Object.keys(o).length);n(14,o[h.class_or_confidence]=I,o)}}else n(7,O="scores")}nl(o,f,t,c)}},[s,i,a,f,r,m,b,O,S,M,g,L,k,R,o,re,j,sl,il,rl,al,fl,cl,ul,_l,hl,dl,gl,ml,bl,kl,pl,vl,wl,yl,jl,zl,Sl,El]}class Gl extends x{constructor(e){super(),ee(this,e,Ul,Ql,le,{value:0,show_legend:1,color_map:14,selectable:2},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get show_legend(){return this.$$.ctx[1]}set show_legend(e){this.$$set({show_legend:e}),E()}get color_map(){return this.$$.ctx[14]}set color_map(e){this.$$set({color_map:e}),E()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),E()}}const Jl=Gl;function Wl(l){let e,n;return e=new Ge({props:{variant:l[13]?"dashed":"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[9],scale:l[10],min_width:l[11],$$slots:{default:[ln]},$$scope:{ctx:l}}}),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},p(t,s){const i={};s&8192&&(i.variant=t[13]?"dashed":"solid"),s&32&&(i.visible=t[5]),s&8&&(i.elem_id=t[3]),s&16&&(i.elem_classes=t[4]),s&512&&(i.container=t[9]),s&1024&&(i.scale=t[10]),s&2048&&(i.min_width=t[11]),s&4215623&&(i.$$scope={dirty:s,ctx:t}),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function Xl(l){let e,n;return e=new Ge({props:{variant:"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[9],scale:l[10],min_width:l[11],$$slots:{default:[sn]},$$scope:{ctx:l}}}),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},p(t,s){const i={};s&32&&(i.visible=t[5]),s&8&&(i.elem_id=t[3]),s&16&&(i.elem_classes=t[4]),s&512&&(i.container=t[9]),s&1024&&(i.scale=t[10]),s&2048&&(i.min_width=t[11]),s&4215751&&(i.$$scope={dirty:s,ctx:t}),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function Ke(l){let e,n;return e=new el({props:{Icon:ie,label:l[8],float:!1,disable:l[9]===!1}}),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},p(t,s){const i={};s&256&&(i.label=t[8]),s&512&&(i.disable=t[9]===!1),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function $l(l){let e,n;return e=new ll({props:{$$slots:{default:[en]},$$scope:{ctx:l}}}),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},p(t,s){const i={};s&4194304&&(i.$$scope={dirty:s,ctx:t}),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function xl(l){let e,n,t;function s(o){l[20](o)}let i={selectable:l[12],show_legend:l[6],color_map:l[1]};return l[0]!==void 0&&(i.value=l[0]),e=new Jl({props:i}),fe.push(()=>ce(e,"value",s)),e.$on("change",l[21]),{c(){F(e.$$.fragment)},m(o,a){D(e,o,a),t=!0},p(o,a){const r={};a&4096&&(r.selectable=o[12]),a&64&&(r.show_legend=o[6]),a&2&&(r.color_map=o[1]),!n&&a&1&&(n=!0,r.value=o[0],ue(()=>n=!1)),e.$set(r)},i(o){t||(p(e.$$.fragment,o),t=!0)},o(o){z(e.$$.fragment,o),t=!1},d(o){P(e,o)}}}function en(l){let e,n;return e=new ie({}),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function ln(l){let e,n,t,s,i,o,a;const r=[{autoscroll:l[2].autoscroll},l[14],{i18n:l[2].i18n}];let c={};for(let u=0;u<r.length;u+=1)c=Je(c,r[u]);e=new We({props:c}),e.$on("clear_status",l[19]);let f=l[8]&&Ke(l);const m=[xl,$l],y=[];function b(u,d){return u[0]?0:1}return s=b(l),i=y[s]=m[s](l),{c(){F(e.$$.fragment),n=B(),f&&f.c(),t=B(),i.c(),o=U()},m(u,d){D(e,u,d),v(u,n,d),f&&f.m(u,d),v(u,t,d),y[s].m(u,d),v(u,o,d),a=!0},p(u,d){const S=d&16388?Xe(r,[d&4&&{autoscroll:u[2].autoscroll},d&16384&&$e(u[14]),d&4&&{i18n:u[2].i18n}]):{};e.$set(S),u[8]?f?(f.p(u,d),d&256&&p(f,1)):(f=Ke(u),f.c(),p(f,1),f.m(t.parentNode,t)):f&&(Y(),z(f,1,1,()=>{f=null}),Z());let M=s;s=b(u),s===M?y[s].p(u,d):(Y(),z(y[M],1,1,()=>{y[M]=null}),Z(),i=y[s],i?i.p(u,d):(i=y[s]=m[s](u),i.c()),p(i,1),i.m(o.parentNode,o))},i(u){a||(p(e.$$.fragment,u),p(f),p(i),a=!0)},o(u){z(e.$$.fragment,u),z(f),z(i),a=!1},d(u){u&&(w(n),w(t),w(o)),P(e,u),f&&f.d(u),y[s].d(u)}}}function Qe(l){let e,n;return e=new el({props:{Icon:ie,label:l[8],float:!1,disable:l[9]===!1}}),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},p(t,s){const i={};s&256&&(i.label=t[8]),s&512&&(i.disable=t[9]===!1),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function nn(l){let e,n;return e=new ll({props:{$$slots:{default:[on]},$$scope:{ctx:l}}}),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},p(t,s){const i={};s&4194304&&(i.$$scope={dirty:s,ctx:t}),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function tn(l){let e,n;return e=new Al({props:{selectable:l[12],value:l[0],show_legend:l[6],show_inline_category:l[7],color_map:l[1]}}),e.$on("select",l[18]),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},p(t,s){const i={};s&4096&&(i.selectable=t[12]),s&1&&(i.value=t[0]),s&64&&(i.show_legend=t[6]),s&128&&(i.show_inline_category=t[7]),s&2&&(i.color_map=t[1]),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function on(l){let e,n;return e=new ie({}),{c(){F(e.$$.fragment)},m(t,s){D(e,t,s),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){P(e,t)}}}function sn(l){let e,n,t,s,i,o,a;const r=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[14]];let c={};for(let u=0;u<r.length;u+=1)c=Je(c,r[u]);e=new We({props:c}),e.$on("clear_status",l[17]);let f=l[8]&&Qe(l);const m=[tn,nn],y=[];function b(u,d){return u[0]?0:1}return s=b(l),i=y[s]=m[s](l),{c(){F(e.$$.fragment),n=B(),f&&f.c(),t=B(),i.c(),o=U()},m(u,d){D(e,u,d),v(u,n,d),f&&f.m(u,d),v(u,t,d),y[s].m(u,d),v(u,o,d),a=!0},p(u,d){const S=d&16388?Xe(r,[d&4&&{autoscroll:u[2].autoscroll},d&4&&{i18n:u[2].i18n},d&16384&&$e(u[14])]):{};e.$set(S),u[8]?f?(f.p(u,d),d&256&&p(f,1)):(f=Qe(u),f.c(),p(f,1),f.m(t.parentNode,t)):f&&(Y(),z(f,1,1,()=>{f=null}),Z());let M=s;s=b(u),s===M?y[s].p(u,d):(Y(),z(y[M],1,1,()=>{y[M]=null}),Z(),i=y[s],i?i.p(u,d):(i=y[s]=m[s](u),i.c()),p(i,1),i.m(o.parentNode,o))},i(u){a||(p(e.$$.fragment,u),p(f),p(i),a=!0)},o(u){z(e.$$.fragment,u),z(f),z(i),a=!1},d(u){u&&(w(n),w(t),w(o)),P(e,u),f&&f.d(u),y[s].d(u)}}}function rn(l){let e,n,t,s;const i=[Xl,Wl],o=[];function a(r,c){return r[13]?1:0}return e=a(l),n=o[e]=i[e](l),{c(){n.c(),t=U()},m(r,c){o[e].m(r,c),v(r,t,c),s=!0},p(r,[c]){let f=e;e=a(r),e===f?o[e].p(r,c):(Y(),z(o[f],1,1,()=>{o[f]=null}),Z(),n=o[e],n?n.p(r,c):(n=o[e]=i[e](r),n.c()),p(n,1),n.m(t.parentNode,t))},i(r){s||(p(n),s=!0)},o(r){z(n),s=!1},d(r){r&&w(t),o[e].d(r)}}}function an(l,e,n){let{gradio:t}=e,{elem_id:s=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:a}=e,r,{show_legend:c}=e,{show_inline_category:f}=e,{color_map:m={}}=e,{label:y=t.i18n("highlighted_text.highlighted_text")}=e,{container:b=!0}=e,{scale:u=null}=e,{min_width:d=void 0}=e,{_selectable:S=!1}=e,{combine_adjacent:M=!1}=e,{interactive:O}=e,{loading_status:g}=e;const L=()=>t.dispatch("clear_status",g),k=({detail:j})=>t.dispatch("select",j),V=()=>t.dispatch("clear_status",g);function R(j){a=j,n(0,a),n(15,M)}const re=()=>t.dispatch("change");return l.$$set=j=>{"gradio"in j&&n(2,t=j.gradio),"elem_id"in j&&n(3,s=j.elem_id),"elem_classes"in j&&n(4,i=j.elem_classes),"visible"in j&&n(5,o=j.visible),"value"in j&&n(0,a=j.value),"show_legend"in j&&n(6,c=j.show_legend),"show_inline_category"in j&&n(7,f=j.show_inline_category),"color_map"in j&&n(1,m=j.color_map),"label"in j&&n(8,y=j.label),"container"in j&&n(9,b=j.container),"scale"in j&&n(10,u=j.scale),"min_width"in j&&n(11,d=j.min_width),"_selectable"in j&&n(12,S=j._selectable),"combine_adjacent"in j&&n(15,M=j.combine_adjacent),"interactive"in j&&n(13,O=j.interactive),"loading_status"in j&&n(14,g=j.loading_status)},l.$$.update=()=>{l.$$.dirty&2&&!m&&Object.keys(m).length&&n(1,m),l.$$.dirty&32769&&a&&M&&n(0,a=tl(a)),l.$$.dirty&65541&&a!==r&&(n(16,r=a),t.dispatch("change"))},[a,m,t,s,i,o,c,f,y,b,u,d,S,O,g,M,r,L,k,V,R,re]}class hn extends x{constructor(e){super(),ee(this,e,an,rn,le,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,show_legend:6,show_inline_category:7,color_map:1,label:8,container:9,scale:10,min_width:11,_selectable:12,combine_adjacent:15,interactive:13,loading_status:14})}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),E()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),E()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),E()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),E()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),E()}get show_inline_category(){return this.$$.ctx[7]}set show_inline_category(e){this.$$set({show_inline_category:e}),E()}get color_map(){return this.$$.ctx[1]}set color_map(e){this.$$set({color_map:e}),E()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),E()}get container(){return this.$$.ctx[9]}set container(e){this.$$set({container:e}),E()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),E()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),E()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),E()}get combine_adjacent(){return this.$$.ctx[15]}set combine_adjacent(e){this.$$set({combine_adjacent:e}),E()}get interactive(){return this.$$.ctx[13]}set interactive(e){this.$$set({interactive:e}),E()}get loading_status(){return this.$$.ctx[14]}set loading_status(e){this.$$set({loading_status:e}),E()}}export{Jl as BaseInteractiveHighlightedText,Al as BaseStaticHighlightedText,hn as default};
//# sourceMappingURL=Index-CS_j7239.js.map
