import{a as A,i as I,s as N,f,c as S,m as F,t as k,b as B,d as G,A as H,e as J,x as K,l as q,u as L,h as M,j as O,o as C,p as P,q as w,ap as j,k as Q,n as R}from"../lite.js";import{B as T}from"./Button-kP2IUsdy.js";function E(n){let e,i,t;return{c(){e=P("img"),w(e,"class","button-icon svelte-yjn27e"),j(e.src,i=n[6].url)||w(e,"src",i),w(e,"alt",t=`${n[5]} icon`)},m(s,l){q(s,e,l)},p(s,l){l&64&&!j(e.src,i=s[6].url)&&w(e,"src",i),l&32&&t!==(t=`${s[5]} icon`)&&w(e,"alt",t)},d(s){s&&C(e)}}}function U(n){let e,i,t=n[6]&&E(n);const s=n[11].default,l=J(s,n,n[12],null);return{c(){t&&t.c(),e=K(),l&&l.c()},m(c,_){t&&t.m(c,_),q(c,e,_),l&&l.m(c,_),i=!0},p(c,_){c[6]?t?t.p(c,_):(t=E(c),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),l&&l.p&&(!i||_&4096)&&L(l,s,c,c[12],i?O(s,c[12],_,null):M(c[12]),null)},i(c){i||(k(l,c),i=!0)},o(c){B(l,c),i=!1},d(c){c&&C(e),t&&t.d(c),l&&l.d(c)}}}function V(n){let e,i;return e=new T({props:{size:n[4],variant:n[3],elem_id:n[0],elem_classes:n[1],visible:n[2],scale:n[8],min_width:n[9],disabled:n[7],$$slots:{default:[U]},$$scope:{ctx:n}}}),e.$on("click",n[10]),{c(){S(e.$$.fragment)},m(t,s){F(e,t,s),i=!0},p(t,[s]){const l={};s&16&&(l.size=t[4]),s&8&&(l.variant=t[3]),s&1&&(l.elem_id=t[0]),s&2&&(l.elem_classes=t[1]),s&4&&(l.visible=t[2]),s&256&&(l.scale=t[8]),s&512&&(l.min_width=t[9]),s&128&&(l.disabled=t[7]),s&4192&&(l.$$scope={dirty:s,ctx:t}),e.$set(l)},i(t){i||(k(e.$$.fragment,t),i=!0)},o(t){B(e.$$.fragment,t),i=!1},d(t){G(e,t)}}}function W(n,e,i){let{$$slots:t={},$$scope:s}=e,{elem_id:l=""}=e,{elem_classes:c=[]}=e,{visible:_=!0}=e,{variant:h="secondary"}=e,{size:d="lg"}=e,{value:m}=e,{icon:o}=e,{disabled:g=!1}=e,{scale:b=null}=e,{min_width:r=void 0}=e;const z=H();function u(){if(z("click"),!m?.url)return;let a;if(!m.orig_name&&m.url){const D=m.url.split("/");a=D[D.length-1],a=a.split("?")[0].split("#")[0]}else a=m.orig_name;const v=document.createElement("a");v.href=m.url,v.download=a||"file",document.body.appendChild(v),v.click(),document.body.removeChild(v)}return n.$$set=a=>{"elem_id"in a&&i(0,l=a.elem_id),"elem_classes"in a&&i(1,c=a.elem_classes),"visible"in a&&i(2,_=a.visible),"variant"in a&&i(3,h=a.variant),"size"in a&&i(4,d=a.size),"value"in a&&i(5,m=a.value),"icon"in a&&i(6,o=a.icon),"disabled"in a&&i(7,g=a.disabled),"scale"in a&&i(8,b=a.scale),"min_width"in a&&i(9,r=a.min_width),"$$scope"in a&&i(12,s=a.$$scope)},[l,c,_,h,d,m,o,g,b,r,u,t,s]}class X extends A{constructor(e){super(),I(this,e,W,V,N,{elem_id:0,elem_classes:1,visible:2,variant:3,size:4,value:5,icon:6,disabled:7,scale:8,min_width:9})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),f()}get variant(){return this.$$.ctx[3]}set variant(e){this.$$set({variant:e}),f()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),f()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),f()}get icon(){return this.$$.ctx[6]}set icon(e){this.$$set({icon:e}),f()}get disabled(){return this.$$.ctx[7]}set disabled(e){this.$$set({disabled:e}),f()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),f()}}const Y=X;function Z(n){let e=(n[10]?n[11].i18n(n[10]):"")+"",i;return{c(){i=Q(e)},m(t,s){q(t,i,s)},p(t,s){s&3072&&e!==(e=(t[10]?t[11].i18n(t[10]):"")+"")&&R(i,e)},d(t){t&&C(i)}}}function y(n){let e,i;return e=new Y({props:{value:n[3],variant:n[4],elem_id:n[0],elem_classes:n[1],size:n[6],scale:n[7],icon:n[8],min_width:n[9],visible:n[2],disabled:!n[5],$$slots:{default:[Z]},$$scope:{ctx:n}}}),e.$on("click",n[12]),{c(){S(e.$$.fragment)},m(t,s){F(e,t,s),i=!0},p(t,[s]){const l={};s&8&&(l.value=t[3]),s&16&&(l.variant=t[4]),s&1&&(l.elem_id=t[0]),s&2&&(l.elem_classes=t[1]),s&64&&(l.size=t[6]),s&128&&(l.scale=t[7]),s&256&&(l.icon=t[8]),s&512&&(l.min_width=t[9]),s&4&&(l.visible=t[2]),s&32&&(l.disabled=!t[5]),s&11264&&(l.$$scope={dirty:s,ctx:t}),e.$set(l)},i(t){i||(k(e.$$.fragment,t),i=!0)},o(t){B(e.$$.fragment,t),i=!1},d(t){G(e,t)}}}function x(n,e,i){let{elem_id:t=""}=e,{elem_classes:s=[]}=e,{visible:l=!0}=e,{value:c}=e,{variant:_="secondary"}=e,{interactive:h}=e,{size:d="lg"}=e,{scale:m=null}=e,{icon:o=null}=e,{min_width:g=void 0}=e,{label:b=null}=e,{gradio:r}=e;const z=()=>r.dispatch("click");return n.$$set=u=>{"elem_id"in u&&i(0,t=u.elem_id),"elem_classes"in u&&i(1,s=u.elem_classes),"visible"in u&&i(2,l=u.visible),"value"in u&&i(3,c=u.value),"variant"in u&&i(4,_=u.variant),"interactive"in u&&i(5,h=u.interactive),"size"in u&&i(6,d=u.size),"scale"in u&&i(7,m=u.scale),"icon"in u&&i(8,o=u.icon),"min_width"in u&&i(9,g=u.min_width),"label"in u&&i(10,b=u.label),"gradio"in u&&i(11,r=u.gradio)},[t,s,l,c,_,h,d,m,o,g,b,r,z]}class ee extends A{constructor(e){super(),I(this,e,x,y,N,{elem_id:0,elem_classes:1,visible:2,value:3,variant:4,interactive:5,size:6,scale:7,icon:8,min_width:9,label:10,gradio:11})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),f()}get variant(){return this.$$.ctx[4]}set variant(e){this.$$set({variant:e}),f()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),f()}get size(){return this.$$.ctx[6]}set size(e){this.$$set({size:e}),f()}get scale(){return this.$$.ctx[7]}set scale(e){this.$$set({scale:e}),f()}get icon(){return this.$$.ctx[8]}set icon(e){this.$$set({icon:e}),f()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),f()}get label(){return this.$$.ctx[10]}set label(e){this.$$set({label:e}),f()}get gradio(){return this.$$.ctx[11]}set gradio(e){this.$$set({gradio:e}),f()}}export{Y as BaseButton,ee as default};
//# sourceMappingURL=Index-B2bxkEqj.js.map
