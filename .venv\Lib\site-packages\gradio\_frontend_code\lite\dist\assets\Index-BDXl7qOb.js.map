{"version": 3, "file": "Index-BDXl7qOb.js", "sources": ["../../../tabitem/shared/TabItem.svelte", "../../../tabitem/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { getContext, onMount, createEventDispatcher, tick } from \"svelte\";\n\timport { TABS } from \"@gradio/tabs\";\n\timport Column from \"@gradio/column\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let label: string;\n\texport let id: string | number | object = {};\n\texport let visible: boolean;\n\texport let interactive: boolean;\n\n\tconst dispatch = createEventDispatcher<{ select: SelectData }>();\n\n\tconst { register_tab, unregister_tab, selected_tab, selected_tab_index } =\n\t\tgetContext(TABS) as any;\n\n\tlet tab_index: number;\n\n\t$: tab_index = register_tab({ label, id, elem_id, visible, interactive });\n\n\tonMount(() => {\n\t\treturn (): void => unregister_tab({ label, id, elem_id });\n\t});\n\n\t$: $selected_tab_index === tab_index &&\n\t\ttick().then(() => dispatch(\"select\", { value: label, index: tab_index }));\n</script>\n\n<div\n\tid={elem_id}\n\tclass=\"tabitem {elem_classes.join(' ')}\"\n\tstyle:display={$selected_tab === id && visible ? \"block\" : \"none\"}\n\trole=\"tabpanel\"\n>\n\t<Column>\n\t\t<slot />\n\t</Column>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tborder: none;\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--block-padding);\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseTabItem } from \"./shared/TabItem.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport TabItem from \"./shared/TabItem.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let label: string;\n\texport let id: string | number;\n\texport let gradio:\n\t\t| Gradio<{\n\t\t\t\tselect: SelectData;\n\t\t  }>\n\t\t| undefined;\n\texport let visible = true;\n\texport let interactive = true;\n</script>\n\n<TabItem\n\t{elem_id}\n\t{elem_classes}\n\t{label}\n\t{visible}\n\t{interactive}\n\t{id}\n\ton:select={({ detail }) => gradio?.dispatch(\"select\", detail)}\n>\n\t<slot />\n</TabItem>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "elem_id", "$$props", "elem_classes", "label", "id", "visible", "interactive", "dispatch", "createEventDispatcher", "register_tab", "unregister_tab", "selected_tab", "selected_tab_index", "getContext", "TABS", "tab_index", "onMount", "$$invalidate", "$selected_tab_index", "tick", "gradio", "select_handler", "detail"], "mappings": "+nBA+BKA,EAAO,CAAA,CAAA,2BACKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gBAAA,uCACtBA,EAAa,CAAA,IAAKA,EAAE,CAAA,GAAIA,EAAO,CAAA,EAAG,QAAU,MAAM,UAHlEC,EASKC,EAAAC,EAAAC,CAAA,0GARAJ,EAAO,CAAA,CAAA,8BACKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,uDACtBA,EAAa,CAAA,IAAKA,EAAE,CAAA,GAAIA,EAAO,CAAA,EAAG,QAAU,MAAM,8IA3BtD,QAAAK,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,EACA,CAAA,MAAAE,CAAA,EAAAF,EACA,CAAA,GAAAG,EAAA,EAAA,EAAAH,EACA,CAAA,QAAAI,CAAA,EAAAJ,EACA,CAAA,YAAAK,CAAA,EAAAL,QAELM,EAAWC,IAET,CAAA,aAAAC,EAAc,eAAAC,EAAgB,aAAAC,EAAc,mBAAAC,GACnDC,EAAWC,CAAI,yCAEZ,IAAAC,EAIJ,OAAAC,EAAA,IACoB,IAAAN,EAAA,CAAiB,MAAAP,EAAO,GAAAC,EAAI,QAAAJ,CAAA,CAAA,sRAHhDiB,EAAA,EAAGF,EAAYN,EAAA,CAAe,MAAAN,EAAO,GAAAC,EAAI,QAAAJ,EAAS,QAAAK,EAAS,YAAAC,CAAA,CAAA,CAAA,mBAMxDY,IAAwBH,GAC1BI,IAAO,KAAA,IAAWZ,EAAS,SAAY,CAAA,MAAOJ,EAAO,MAAOY,CAAA,CAAA,CAAA,o6CCnBlD,QAAAf,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,EACA,CAAA,MAAAE,CAAA,EAAAF,EACA,CAAA,GAAAG,CAAA,EAAAH,EACA,CAAA,OAAAmB,CAAA,EAAAnB,GAKA,QAAAI,EAAU,EAAA,EAAAJ,GACV,YAAAK,EAAc,EAAA,EAAAL,EAUX,MAAAoB,EAAA,CAAA,CAAA,OAAAC,KAAaF,GAAQ,SAAS,SAAUE,CAAM"}