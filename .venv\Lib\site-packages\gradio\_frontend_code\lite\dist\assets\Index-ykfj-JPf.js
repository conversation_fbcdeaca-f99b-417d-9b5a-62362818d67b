const __vite__fileDeps=["./Canvas3D-DAkavCz2.js","../lite.js","../lite.css","./file-url-Co2ROWca.js","./Canvas3DGS-CemWZsSW.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{a as ee,i as te,s as le,f as g,c as k,x as T,E as y,m as z,l as $,t as p,y as j,b,z as B,o as I,d as v,p as ne,q as G,v as ue,W as fe,ay as Y,I as S,as as O,a5 as R,a6 as H,A as ze,G as ie,H as ve,e as De,u as $e,h as Ie,j as Ee,B as ce,Y as me,S as de,a0 as he,a4 as ge}from"../lite.js";import{B as se}from"./BlockLabel-B0HN-MOU.js";import{D as Ce}from"./Download-CgLP-Xl6.js";import{F as J}from"./File-Kbo-bXuF.js";import{U as Me}from"./Undo-DitIvwTU.js";import{I as Ue}from"./IconButtonWrapper-Ck50MZwX.js";import{U as je}from"./Upload-TGDabKXH.js";import{M as Be}from"./ModifyUpload-CWo4TCq1.js";import{E as Ae}from"./Empty-C76eC2zW.js";import{U as Se}from"./UploadText-CW3z6Ye_.js";import{default as Ct}from"./Example-ZfpHSYuy.js";/* empty css                                             */import"./DownloadLink-B8hI46-W.js";import"./file-url-Co2ROWca.js";import"./Upload-D_TzP4YC.js";var ae=Object.prototype.hasOwnProperty;function oe(s,e,l){for(l of s.keys())if(F(l,e))return l}function F(s,e){var l,t,n;if(s===e)return!0;if(s&&e&&(l=s.constructor)===e.constructor){if(l===Date)return s.getTime()===e.getTime();if(l===RegExp)return s.toString()===e.toString();if(l===Array){if((t=s.length)===e.length)for(;t--&&F(s[t],e[t]););return t===-1}if(l===Set){if(s.size!==e.size)return!1;for(t of s)if(n=t,n&&typeof n=="object"&&(n=oe(e,n),!n)||!e.has(n))return!1;return!0}if(l===Map){if(s.size!==e.size)return!1;for(t of s)if(n=t[0],n&&typeof n=="object"&&(n=oe(e,n),!n)||!F(t[1],e.get(n)))return!1;return!0}if(l===ArrayBuffer)s=new Uint8Array(s),e=new Uint8Array(e);else if(l===DataView){if((t=s.byteLength)===e.byteLength)for(;t--&&s.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(s)){if((t=s.byteLength)===e.byteLength)for(;t--&&s[t]===e[t];);return t===-1}if(!l||typeof s=="object"){t=0;for(l in s)if(ae.call(s,l)&&++t&&!ae.call(e,l)||!(l in e)||!F(s[l],e[l]))return!1;return Object.keys(e).length===t}}return s!==s&&e!==e}function re(s){let e,l,t,n,a,r;l=new Ue({props:{$$slots:{default:[Le]},$$scope:{ctx:s}}});const _=[Oe,Ne],i=[];function u(o,f){return o[10]?0:1}return n=u(s),a=i[n]=_[n](s),{c(){e=ne("div"),k(l.$$.fragment),t=T(),a.c(),G(e,"class","model3D svelte-1mxwah3")},m(o,f){$(o,e,f),z(l,e,null),ue(e,t),i[n].m(e,null),r=!0},p(o,f){const d={};f&2115105&&(d.$$scope={dirty:f,ctx:o}),l.$set(d);let m=n;n=u(o),n===m?i[n].p(o,f):(j(),b(i[m],1,1,()=>{i[m]=null}),B(),a=i[n],a?a.p(o,f):(a=i[n]=_[n](o),a.c()),p(a,1),a.m(e,null))},i(o){r||(p(l.$$.fragment,o),p(a),r=!0)},o(o){b(l.$$.fragment,o),b(a),r=!1},d(o){o&&I(e),v(l),i[n].d()}}}function _e(s){let e,l;return e=new fe({props:{Icon:Me,label:"Undo",disabled:!s[9]}}),e.$on("click",s[17]),{c(){k(e.$$.fragment)},m(t,n){z(e,t,n),l=!0},p(t,n){const a={};n&512&&(a.disabled=!t[9]),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function Le(s){let e,l,t,n,a,r=!s[10]&&_e(s);return t=new fe({props:{Icon:Ce,label:s[5]("common.download")}}),{c(){r&&r.c(),e=T(),l=ne("a"),k(t.$$.fragment),G(l,"href",s[14]),G(l,"target",window.__is_colab__?"_blank":null),G(l,"download",n=window.__is_colab__?null:s[0].orig_name||s[0].path)},m(_,i){r&&r.m(_,i),$(_,e,i),$(_,l,i),z(t,l,null),a=!0},p(_,i){_[10]?r&&(j(),b(r,1,1,()=>{r=null}),B()):r?(r.p(_,i),i&1024&&p(r,1)):(r=_e(_),r.c(),p(r,1),r.m(e.parentNode,e));const u={};i&32&&(u.label=_[5]("common.download")),t.$set(u),(!a||i&16384)&&G(l,"href",_[14]),(!a||i&1&&n!==(n=window.__is_colab__?null:_[0].orig_name||_[0].path))&&G(l,"download",n)},i(_){a||(p(r),p(t.$$.fragment,_),a=!0)},o(_){b(r),b(t.$$.fragment,_),a=!1},d(_){_&&(I(e),I(l)),r&&r.d(_),v(t)}}}function Ne(s){let e,l,t,n;function a(i){s[20](i)}var r=s[13];function _(i,u){let o={value:i[0],display_mode:i[1],clear_color:i[2],camera_position:i[8],zoom_speed:i[6],pan_speed:i[7]};return i[14]!==void 0&&(o.resolved_url=i[14]),{props:o}}return r&&(e=O(r,_(s)),s[19](e),S.push(()=>R(e,"resolved_url",a))),{c(){e&&k(e.$$.fragment),t=y()},m(i,u){e&&z(e,i,u),$(i,t,u),n=!0},p(i,u){if(u&8192&&r!==(r=i[13])){if(e){j();const o=e;b(o.$$.fragment,1,0,()=>{v(o,1)}),B()}r?(e=O(r,_(i)),i[19](e),S.push(()=>R(e,"resolved_url",a)),k(e.$$.fragment),p(e.$$.fragment,1),z(e,t.parentNode,t)):e=null}else if(r){const o={};u&1&&(o.value=i[0]),u&2&&(o.display_mode=i[1]),u&4&&(o.clear_color=i[2]),u&256&&(o.camera_position=i[8]),u&64&&(o.zoom_speed=i[6]),u&128&&(o.pan_speed=i[7]),!l&&u&16384&&(l=!0,o.resolved_url=i[14],H(()=>l=!1)),e.$set(o)}},i(i){n||(e&&p(e.$$.fragment,i),n=!0)},o(i){e&&b(e.$$.fragment,i),n=!1},d(i){i&&I(t),s[19](null),e&&v(e,i)}}}function Oe(s){let e,l,t,n;function a(i){s[18](i)}var r=s[12];function _(i,u){let o={value:i[0],zoom_speed:i[6],pan_speed:i[7]};return i[14]!==void 0&&(o.resolved_url=i[14]),{props:o}}return r&&(e=O(r,_(s)),S.push(()=>R(e,"resolved_url",a))),{c(){e&&k(e.$$.fragment),t=y()},m(i,u){e&&z(e,i,u),$(i,t,u),n=!0},p(i,u){if(u&4096&&r!==(r=i[12])){if(e){j();const o=e;b(o.$$.fragment,1,0,()=>{v(o,1)}),B()}r?(e=O(r,_(i)),S.push(()=>R(e,"resolved_url",a)),k(e.$$.fragment),p(e.$$.fragment,1),z(e,t.parentNode,t)):e=null}else if(r){const o={};u&1&&(o.value=i[0]),u&64&&(o.zoom_speed=i[6]),u&128&&(o.pan_speed=i[7]),!l&&u&16384&&(l=!0,o.resolved_url=i[14],H(()=>l=!1)),e.$set(o)}},i(i){n||(e&&p(e.$$.fragment,i),n=!0)},o(i){e&&b(e.$$.fragment,i),n=!1},d(i){i&&I(t),e&&v(e,i)}}}function Te(s){let e,l,t,n;e=new se({props:{show_label:s[4],Icon:J,label:s[3]||s[5]("3D_model.3d_model")}});let a=s[0]&&re(s);return{c(){k(e.$$.fragment),l=T(),a&&a.c(),t=y()},m(r,_){z(e,r,_),$(r,l,_),a&&a.m(r,_),$(r,t,_),n=!0},p(r,[_]){const i={};_&16&&(i.show_label=r[4]),_&40&&(i.label=r[3]||r[5]("3D_model.3d_model")),e.$set(i),r[0]?a?(a.p(r,_),_&1&&p(a,1)):(a=re(r),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(j(),b(a,1,1,()=>{a=null}),B())},i(r){n||(p(e.$$.fragment,r),p(a),n=!0)},o(r){b(e.$$.fragment,r),b(a),n=!1},d(r){r&&(I(l),I(t)),v(e,r),a&&a.d(r)}}}async function ye(){return(await Y(()=>import("./Canvas3D-DAkavCz2.js"),__vite__mapDeps([0,1,2,3]),import.meta.url)).default}async function Pe(){return(await Y(()=>import("./Canvas3DGS-CemWZsSW.js"),__vite__mapDeps([4,1,2,3]),import.meta.url)).default}function Ve(s,e,l){let{value:t}=e,{display_mode:n="solid"}=e,{clear_color:a=[0,0,0,0]}=e,{label:r=""}=e,{show_label:_}=e,{i18n:i}=e,{zoom_speed:u=1}=e,{pan_speed:o=1}=e,{camera_position:f=[null,null,null]}=e,{has_change_history:d=!1}=e,m={camera_position:f,zoom_speed:u,pan_speed:o},C=!1,E,D,M;function L(){M?.reset_camera_position(f,u,o)}let U;const N=()=>L();function A(w){U=w,l(14,U)}function P(w){S[w?"unshift":"push"](()=>{M=w,l(11,M)})}function V(w){U=w,l(14,U)}return s.$$set=w=>{"value"in w&&l(0,t=w.value),"display_mode"in w&&l(1,n=w.display_mode),"clear_color"in w&&l(2,a=w.clear_color),"label"in w&&l(3,r=w.label),"show_label"in w&&l(4,_=w.show_label),"i18n"in w&&l(5,i=w.i18n),"zoom_speed"in w&&l(6,u=w.zoom_speed),"pan_speed"in w&&l(7,o=w.pan_speed),"camera_position"in w&&l(8,f=w.camera_position),"has_change_history"in w&&l(9,d=w.has_change_history)},s.$$.update=()=>{s.$$.dirty&1025&&t&&(l(10,C=t.path.endsWith(".splat")||t.path.endsWith(".ply")),C?Pe().then(w=>{l(12,E=w)}):ye().then(w=>{l(13,D=w)})),s.$$.dirty&68032&&(!F(m.camera_position,f)||m.zoom_speed!==u||m.pan_speed!==o)&&(M?.reset_camera_position(f,u,o),l(16,m={camera_position:f,zoom_speed:u,pan_speed:o}))},[t,n,a,r,_,i,u,o,f,d,C,M,E,D,U,L,m,N,A,P,V]}class We extends ee{constructor(e){super(),te(this,e,Ve,Te,le,{value:0,display_mode:1,clear_color:2,label:3,show_label:4,i18n:5,zoom_speed:6,pan_speed:7,camera_position:8,has_change_history:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get display_mode(){return this.$$.ctx[1]}set display_mode(e){this.$$set({display_mode:e}),g()}get clear_color(){return this.$$.ctx[2]}set clear_color(e){this.$$set({clear_color:e}),g()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),g()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),g()}get zoom_speed(){return this.$$.ctx[6]}set zoom_speed(e){this.$$set({zoom_speed:e}),g()}get pan_speed(){return this.$$.ctx[7]}set pan_speed(e){this.$$set({pan_speed:e}),g()}get camera_position(){return this.$$.ctx[8]}set camera_position(e){this.$$set({camera_position:e}),g()}get has_change_history(){return this.$$.ctx[9]}set has_change_history(e){this.$$set({has_change_history:e}),g()}}const Ge=We;function Re(s){let e,l,t,n,a,r;l=new Be({props:{undoable:!s[14],i18n:s[7]}}),l.$on("clear",s[20]),l.$on("undo",s[21]);const _=[He,Fe],i=[];function u(o,f){return o[14]?0:1}return n=u(s),a=i[n]=_[n](s),{c(){e=ne("div"),k(l.$$.fragment),t=T(),a.c(),G(e,"class","input-model svelte-jub4pj")},m(o,f){$(o,e,f),z(l,e,null),ue(e,t),i[n].m(e,null),r=!0},p(o,f){const d={};f&16384&&(d.undoable=!o[14]),f&128&&(d.i18n=o[7]),l.$set(d);let m=n;n=u(o),n===m?i[n].p(o,f):(j(),b(i[m],1,1,()=>{i[m]=null}),B(),a=i[n],a?a.p(o,f):(a=i[n]=_[n](o),a.c()),p(a,1),a.m(e,null))},i(o){r||(p(l.$$.fragment,o),p(a),r=!0)},o(o){b(l.$$.fragment,o),b(a),r=!1},d(o){o&&I(e),v(l),i[n].d()}}}function qe(s){let e,l,t,n;function a(i){s[23](i)}function r(i){s[24](i)}let _={upload:s[12],stream_handler:s[13],root:s[6],max_file_size:s[10],filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],$$slots:{default:[Ye]},$$scope:{ctx:s}};return s[15]!==void 0&&(_.dragging=s[15]),s[1]!==void 0&&(_.uploading=s[1]),e=new je({props:_}),S.push(()=>R(e,"dragging",a)),S.push(()=>R(e,"uploading",r)),e.$on("load",s[19]),e.$on("error",s[25]),{c(){k(e.$$.fragment)},m(i,u){z(e,i,u),n=!0},p(i,u){const o={};u&4096&&(o.upload=i[12]),u&8192&&(o.stream_handler=i[13]),u&64&&(o.root=i[6]),u&1024&&(o.max_file_size=i[10]),u&134217728&&(o.$$scope={dirty:u,ctx:i}),!l&&u&32768&&(l=!0,o.dragging=i[15],H(()=>l=!1)),!t&&u&2&&(t=!0,o.uploading=i[1],H(()=>t=!1)),e.$set(o)},i(i){n||(p(e.$$.fragment,i),n=!0)},o(i){b(e.$$.fragment,i),n=!1},d(i){v(e,i)}}}function Fe(s){let e,l,t;var n=s[17];function a(r,_){return{props:{value:r[0],display_mode:r[2],clear_color:r[3],camera_position:r[11],zoom_speed:r[8],pan_speed:r[9]}}}return n&&(e=O(n,a(s)),s[26](e)),{c(){e&&k(e.$$.fragment),l=y()},m(r,_){e&&z(e,r,_),$(r,l,_),t=!0},p(r,_){if(_&131072&&n!==(n=r[17])){if(e){j();const i=e;b(i.$$.fragment,1,0,()=>{v(i,1)}),B()}n?(e=O(n,a(r)),r[26](e),k(e.$$.fragment),p(e.$$.fragment,1),z(e,l.parentNode,l)):e=null}else if(n){const i={};_&1&&(i.value=r[0]),_&4&&(i.display_mode=r[2]),_&8&&(i.clear_color=r[3]),_&2048&&(i.camera_position=r[11]),_&256&&(i.zoom_speed=r[8]),_&512&&(i.pan_speed=r[9]),e.$set(i)}},i(r){t||(e&&p(e.$$.fragment,r),t=!0)},o(r){e&&b(e.$$.fragment,r),t=!1},d(r){r&&I(l),s[26](null),e&&v(e,r)}}}function He(s){let e,l,t;var n=s[16];function a(r,_){return{props:{value:r[0],zoom_speed:r[8],pan_speed:r[9]}}}return n&&(e=O(n,a(s))),{c(){e&&k(e.$$.fragment),l=y()},m(r,_){e&&z(e,r,_),$(r,l,_),t=!0},p(r,_){if(_&65536&&n!==(n=r[16])){if(e){j();const i=e;b(i.$$.fragment,1,0,()=>{v(i,1)}),B()}n?(e=O(n,a(r)),k(e.$$.fragment),p(e.$$.fragment,1),z(e,l.parentNode,l)):e=null}else if(n){const i={};_&1&&(i.value=r[0]),_&256&&(i.zoom_speed=r[8]),_&512&&(i.pan_speed=r[9]),e.$set(i)}},i(r){t||(e&&p(e.$$.fragment,r),t=!0)},o(r){e&&b(e.$$.fragment,r),t=!1},d(r){r&&I(l),e&&v(e,r)}}}function Ye(s){let e;const l=s[22].default,t=De(l,s,s[27],null);return{c(){t&&t.c()},m(n,a){t&&t.m(n,a),e=!0},p(n,a){t&&t.p&&(!e||a&134217728)&&$e(t,l,n,n[27],e?Ee(l,n[27],a,null):Ie(n[27]),null)},i(n){e||(p(t,n),e=!0)},o(n){b(t,n),e=!1},d(n){t&&t.d(n)}}}function Je(s){let e,l,t,n,a,r;e=new se({props:{show_label:s[5],Icon:J,label:s[4]||"3D Model"}});const _=[qe,Re],i=[];function u(o,f){return o[0]===null?0:1}return t=u(s),n=i[t]=_[t](s),{c(){k(e.$$.fragment),l=T(),n.c(),a=y()},m(o,f){z(e,o,f),$(o,l,f),i[t].m(o,f),$(o,a,f),r=!0},p(o,[f]){const d={};f&32&&(d.show_label=o[5]),f&16&&(d.label=o[4]||"3D Model"),e.$set(d);let m=t;t=u(o),t===m?i[t].p(o,f):(j(),b(i[m],1,1,()=>{i[m]=null}),B(),n=i[t],n?n.p(o,f):(n=i[t]=_[t](o),n.c()),p(n,1),n.m(a.parentNode,a))},i(o){r||(p(e.$$.fragment,o),p(n),r=!0)},o(o){b(e.$$.fragment,o),b(n),r=!1},d(o){o&&(I(l),I(a)),v(e,o),i[t].d(o)}}}async function Ke(){return(await Y(()=>import("./Canvas3D-DAkavCz2.js"),__vite__mapDeps([0,1,2,3]),import.meta.url)).default}async function Qe(){return(await Y(()=>import("./Canvas3DGS-CemWZsSW.js"),__vite__mapDeps([4,1,2,3]),import.meta.url)).default}function Xe(s,e,l){let{$$slots:t={},$$scope:n}=e,{value:a}=e,{display_mode:r="solid"}=e,{clear_color:_=[0,0,0,0]}=e,{label:i=""}=e,{show_label:u}=e,{root:o}=e,{i18n:f}=e,{zoom_speed:d=1}=e,{pan_speed:m=1}=e,{max_file_size:C=null}=e,{uploading:E=!1}=e,{camera_position:D=[null,null,null]}=e,{upload:M}=e,{stream_handler:L}=e;async function U({detail:h}){l(0,a=h),await ie(),W("change",a),W("load",a)}async function N(){l(0,a=null),await ie(),W("clear"),W("change")}let A=!1,P,V,w;async function K(){w?.reset_camera_position(D,d,m)}const W=ze();let q=!1;function Q(h){q=h,l(15,q)}function X(h){E=h,l(1,E)}function Z(h){ve.call(this,s,h)}function x(h){S[h?"unshift":"push"](()=>{w=h,l(18,w)})}return s.$$set=h=>{"value"in h&&l(0,a=h.value),"display_mode"in h&&l(2,r=h.display_mode),"clear_color"in h&&l(3,_=h.clear_color),"label"in h&&l(4,i=h.label),"show_label"in h&&l(5,u=h.show_label),"root"in h&&l(6,o=h.root),"i18n"in h&&l(7,f=h.i18n),"zoom_speed"in h&&l(8,d=h.zoom_speed),"pan_speed"in h&&l(9,m=h.pan_speed),"max_file_size"in h&&l(10,C=h.max_file_size),"uploading"in h&&l(1,E=h.uploading),"camera_position"in h&&l(11,D=h.camera_position),"upload"in h&&l(12,M=h.upload),"stream_handler"in h&&l(13,L=h.stream_handler),"$$scope"in h&&l(27,n=h.$$scope)},s.$$.update=()=>{s.$$.dirty&16385&&a&&(l(14,A=a.path.endsWith(".splat")||a.path.endsWith(".ply")),A?Qe().then(h=>{l(16,P=h)}):Ke().then(h=>{l(17,V=h)})),s.$$.dirty&32768&&W("drag",q)},[a,E,r,_,i,u,o,f,d,m,C,D,M,L,A,q,P,V,w,U,N,K,t,Q,X,Z,x,n]}class Ze extends ee{constructor(e){super(),te(this,e,Xe,Je,le,{value:0,display_mode:2,clear_color:3,label:4,show_label:5,root:6,i18n:7,zoom_speed:8,pan_speed:9,max_file_size:10,uploading:1,camera_position:11,upload:12,stream_handler:13})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get display_mode(){return this.$$.ctx[2]}set display_mode(e){this.$$set({display_mode:e}),g()}get clear_color(){return this.$$.ctx[3]}set clear_color(e){this.$$set({clear_color:e}),g()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),g()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),g()}get i18n(){return this.$$.ctx[7]}set i18n(e){this.$$set({i18n:e}),g()}get zoom_speed(){return this.$$.ctx[8]}set zoom_speed(e){this.$$set({zoom_speed:e}),g()}get pan_speed(){return this.$$.ctx[9]}set pan_speed(e){this.$$set({pan_speed:e}),g()}get max_file_size(){return this.$$.ctx[10]}set max_file_size(e){this.$$set({max_file_size:e}),g()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),g()}get camera_position(){return this.$$.ctx[11]}set camera_position(e){this.$$set({camera_position:e}),g()}get upload(){return this.$$.ctx[12]}set upload(e){this.$$set({upload:e}),g()}get stream_handler(){return this.$$.ctx[13]}set stream_handler(e){this.$$set({stream_handler:e}),g()}}const xe=Ze;function et(s){let e,l;return e=new ce({props:{visible:s[5],variant:s[0]===null?"dashed":"solid",border_mode:s[20]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],container:s[11],scale:s[12],min_width:s[13],height:s[15],$$slots:{default:[nt]},$$scope:{ctx:s}}}),{c(){k(e.$$.fragment)},m(t,n){z(e,t,n),l=!0},p(t,n){const a={};n[0]&32&&(a.visible=t[5]),n[0]&1&&(a.variant=t[0]===null?"dashed":"solid"),n[0]&1048576&&(a.border_mode=t[20]?"focus":"base"),n[0]&8&&(a.elem_id=t[3]),n[0]&16&&(a.elem_classes=t[4]),n[0]&2048&&(a.container=t[11]),n[0]&4096&&(a.scale=t[12]),n[0]&8192&&(a.min_width=t[13]),n[0]&32768&&(a.height=t[15]),n[0]&1787847|n[1]&8&&(a.$$scope={dirty:n,ctx:t}),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function tt(s){let e,l;return e=new ce({props:{visible:s[5],variant:s[0]===null?"dashed":"solid",border_mode:s[20]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],container:s[11],scale:s[12],min_width:s[13],height:s[15],$$slots:{default:[ot]},$$scope:{ctx:s}}}),{c(){k(e.$$.fragment)},m(t,n){z(e,t,n),l=!0},p(t,n){const a={};n[0]&32&&(a.visible=t[5]),n[0]&1&&(a.variant=t[0]===null?"dashed":"solid"),n[0]&1048576&&(a.border_mode=t[20]?"focus":"base"),n[0]&8&&(a.elem_id=t[3]),n[0]&16&&(a.elem_classes=t[4]),n[0]&2048&&(a.container=t[11]),n[0]&4096&&(a.scale=t[12]),n[0]&8192&&(a.min_width=t[13]),n[0]&32768&&(a.height=t[15]),n[0]&214919|n[1]&8&&(a.$$scope={dirty:n,ctx:t}),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function lt(s){let e,l;return e=new Se({props:{i18n:s[14].i18n,type:"file"}}),{c(){k(e.$$.fragment)},m(t,n){z(e,t,n),l=!0},p(t,n){const a={};n[0]&16384&&(a.i18n=t[14].i18n),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function nt(s){let e,l,t,n,a;const r=[{autoscroll:s[14].autoscroll},{i18n:s[14].i18n},s[1]];let _={};for(let o=0;o<r.length;o+=1)_=me(_,r[o]);e=new de({props:_}),e.$on("clear_status",s[24]);function i(o){s[27](o)}let u={label:s[9],show_label:s[10],root:s[6],display_mode:s[7],clear_color:s[8],value:s[0],camera_position:s[17],zoom_speed:s[16],i18n:s[14].i18n,max_file_size:s[14].max_file_size,upload:s[25],stream_handler:s[26],$$slots:{default:[lt]},$$scope:{ctx:s}};return s[19]!==void 0&&(u.uploading=s[19]),t=new xe({props:u}),S.push(()=>R(t,"uploading",i)),t.$on("change",s[28]),t.$on("drag",s[29]),t.$on("change",s[30]),t.$on("clear",s[31]),t.$on("load",s[32]),t.$on("error",s[33]),{c(){k(e.$$.fragment),l=T(),k(t.$$.fragment)},m(o,f){z(e,o,f),$(o,l,f),z(t,o,f),a=!0},p(o,f){const d=f[0]&16386?he(r,[f[0]&16384&&{autoscroll:o[14].autoscroll},f[0]&16384&&{i18n:o[14].i18n},f[0]&2&&ge(o[1])]):{};e.$set(d);const m={};f[0]&512&&(m.label=o[9]),f[0]&1024&&(m.show_label=o[10]),f[0]&64&&(m.root=o[6]),f[0]&128&&(m.display_mode=o[7]),f[0]&256&&(m.clear_color=o[8]),f[0]&1&&(m.value=o[0]),f[0]&131072&&(m.camera_position=o[17]),f[0]&65536&&(m.zoom_speed=o[16]),f[0]&16384&&(m.i18n=o[14].i18n),f[0]&16384&&(m.max_file_size=o[14].max_file_size),f[0]&16384&&(m.upload=o[25]),f[0]&16384&&(m.stream_handler=o[26]),f[0]&16384|f[1]&8&&(m.$$scope={dirty:f,ctx:o}),!n&&f[0]&524288&&(n=!0,m.uploading=o[19],H(()=>n=!1)),t.$set(m)},i(o){a||(p(e.$$.fragment,o),p(t.$$.fragment,o),a=!0)},o(o){b(e.$$.fragment,o),b(t.$$.fragment,o),a=!1},d(o){o&&I(l),v(e,o),v(t,o)}}}function st(s){let e,l,t,n;return e=new se({props:{show_label:s[10],Icon:J,label:s[9]||"3D Model"}}),t=new Ae({props:{unpadded_box:!0,size:"large",$$slots:{default:[at]},$$scope:{ctx:s}}}),{c(){k(e.$$.fragment),l=T(),k(t.$$.fragment)},m(a,r){z(e,a,r),$(a,l,r),z(t,a,r),n=!0},p(a,r){const _={};r[0]&1024&&(_.show_label=a[10]),r[0]&512&&(_.label=a[9]||"3D Model"),e.$set(_);const i={};r[1]&8&&(i.$$scope={dirty:r,ctx:a}),t.$set(i)},i(a){n||(p(e.$$.fragment,a),p(t.$$.fragment,a),n=!0)},o(a){b(e.$$.fragment,a),b(t.$$.fragment,a),n=!1},d(a){a&&I(l),v(e,a),v(t,a)}}}function it(s){let e,l;return e=new Ge({props:{value:s[0],i18n:s[14].i18n,display_mode:s[7],clear_color:s[8],label:s[9],show_label:s[10],camera_position:s[17],zoom_speed:s[16],has_change_history:s[2]}}),{c(){k(e.$$.fragment)},m(t,n){z(e,t,n),l=!0},p(t,n){const a={};n[0]&1&&(a.value=t[0]),n[0]&16384&&(a.i18n=t[14].i18n),n[0]&128&&(a.display_mode=t[7]),n[0]&256&&(a.clear_color=t[8]),n[0]&512&&(a.label=t[9]),n[0]&1024&&(a.show_label=t[10]),n[0]&131072&&(a.camera_position=t[17]),n[0]&65536&&(a.zoom_speed=t[16]),n[0]&4&&(a.has_change_history=t[2]),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function at(s){let e,l;return e=new J({}),{c(){k(e.$$.fragment)},m(t,n){z(e,t,n),l=!0},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function ot(s){let e,l,t,n,a,r;const _=[{autoscroll:s[14].autoscroll},{i18n:s[14].i18n},s[1]];let i={};for(let d=0;d<_.length;d+=1)i=me(i,_[d]);e=new de({props:i}),e.$on("clear_status",s[23]);const u=[it,st],o=[];function f(d,m){return d[0]&&d[21]?0:1}return t=f(s),n=o[t]=u[t](s),{c(){k(e.$$.fragment),l=T(),n.c(),a=y()},m(d,m){z(e,d,m),$(d,l,m),o[t].m(d,m),$(d,a,m),r=!0},p(d,m){const C=m[0]&16386?he(_,[m[0]&16384&&{autoscroll:d[14].autoscroll},m[0]&16384&&{i18n:d[14].i18n},m[0]&2&&ge(d[1])]):{};e.$set(C);let E=t;t=f(d),t===E?o[t].p(d,m):(j(),b(o[E],1,1,()=>{o[E]=null}),B(),n=o[t],n?n.p(d,m):(n=o[t]=u[t](d),n.c()),p(n,1),n.m(a.parentNode,a))},i(d){r||(p(e.$$.fragment,d),p(n),r=!0)},o(d){b(e.$$.fragment,d),b(n),r=!1},d(d){d&&(I(l),I(a)),v(e,d),o[t].d(d)}}}function rt(s){let e,l,t,n;const a=[tt,et],r=[];function _(i,u){return i[18]?1:0}return e=_(s),l=r[e]=a[e](s),{c(){l.c(),t=y()},m(i,u){r[e].m(i,u),$(i,t,u),n=!0},p(i,u){let o=e;e=_(i),e===o?r[e].p(i,u):(j(),b(r[o],1,1,()=>{r[o]=null}),B(),l=r[e],l?l.p(i,u):(l=r[e]=a[e](i),l.c()),p(l,1),l.m(t.parentNode,t))},i(i){n||(p(l),n=!0)},o(i){b(l),n=!1},d(i){i&&I(t),r[e].d(i)}}}function _t(s,e,l){let{elem_id:t=""}=e,{elem_classes:n=[]}=e,{visible:a=!0}=e,{value:r=null}=e,{root:_}=e,{display_mode:i="solid"}=e,{clear_color:u}=e,{loading_status:o}=e,{label:f}=e,{show_label:d}=e,{container:m=!0}=e,{scale:C=null}=e,{min_width:E=void 0}=e,{gradio:D}=e,{height:M=void 0}=e,{zoom_speed:L=1}=e,{input_ready:U}=e,N=!1,{has_change_history:A=!1}=e,{camera_position:P=[null,null,null]}=e,{interactive:V}=e,w=!1;const K=typeof window<"u",W=()=>D.dispatch("clear_status",o),q=()=>D.dispatch("clear_status",o),Q=(...c)=>D.client.upload(...c),X=(...c)=>D.client.stream(...c);function Z(c){N=c,l(19,N)}const x=({detail:c})=>l(0,r=c),h=({detail:c})=>l(20,w=c),pe=({detail:c})=>{D.dispatch("change",c),l(2,A=!0)},be=()=>{l(0,r=null),D.dispatch("clear")},we=({detail:c})=>{l(0,r=c),D.dispatch("upload")},ke=({detail:c})=>{l(1,o=o||{}),l(1,o.status="error",o),D.dispatch("error",c)};return s.$$set=c=>{"elem_id"in c&&l(3,t=c.elem_id),"elem_classes"in c&&l(4,n=c.elem_classes),"visible"in c&&l(5,a=c.visible),"value"in c&&l(0,r=c.value),"root"in c&&l(6,_=c.root),"display_mode"in c&&l(7,i=c.display_mode),"clear_color"in c&&l(8,u=c.clear_color),"loading_status"in c&&l(1,o=c.loading_status),"label"in c&&l(9,f=c.label),"show_label"in c&&l(10,d=c.show_label),"container"in c&&l(11,m=c.container),"scale"in c&&l(12,C=c.scale),"min_width"in c&&l(13,E=c.min_width),"gradio"in c&&l(14,D=c.gradio),"height"in c&&l(15,M=c.height),"zoom_speed"in c&&l(16,L=c.zoom_speed),"input_ready"in c&&l(22,U=c.input_ready),"has_change_history"in c&&l(2,A=c.has_change_history),"camera_position"in c&&l(17,P=c.camera_position),"interactive"in c&&l(18,V=c.interactive)},s.$$.update=()=>{s.$$.dirty[0]&524288&&l(22,U=!N)},[r,o,A,t,n,a,_,i,u,f,d,m,C,E,D,M,L,P,V,N,w,K,U,W,q,Q,X,Z,x,h,pe,be,we,ke]}class $t extends ee{constructor(e){super(),te(this,e,_t,rt,le,{elem_id:3,elem_classes:4,visible:5,value:0,root:6,display_mode:7,clear_color:8,loading_status:1,label:9,show_label:10,container:11,scale:12,min_width:13,gradio:14,height:15,zoom_speed:16,input_ready:22,has_change_history:2,camera_position:17,interactive:18},null,[-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),g()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),g()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),g()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),g()}get display_mode(){return this.$$.ctx[7]}set display_mode(e){this.$$set({display_mode:e}),g()}get clear_color(){return this.$$.ctx[8]}set clear_color(e){this.$$set({clear_color:e}),g()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),g()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),g()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),g()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),g()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),g()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),g()}get height(){return this.$$.ctx[15]}set height(e){this.$$set({height:e}),g()}get zoom_speed(){return this.$$.ctx[16]}set zoom_speed(e){this.$$set({zoom_speed:e}),g()}get input_ready(){return this.$$.ctx[22]}set input_ready(e){this.$$set({input_ready:e}),g()}get has_change_history(){return this.$$.ctx[2]}set has_change_history(e){this.$$set({has_change_history:e}),g()}get camera_position(){return this.$$.ctx[17]}set camera_position(e){this.$$set({camera_position:e}),g()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),g()}}export{Ct as BaseExample,Ge as BaseModel3D,xe as BaseModel3DUpload,$t as default};
//# sourceMappingURL=Index-ykfj-JPf.js.map
