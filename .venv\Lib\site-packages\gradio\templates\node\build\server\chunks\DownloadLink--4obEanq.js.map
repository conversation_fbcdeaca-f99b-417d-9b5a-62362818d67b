{"version": 3, "file": "DownloadLink--4obEanq.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/DownloadLink.js"], "sourcesContent": ["import { create_ssr_component, compute_rest_props, spread, escape_object, escape_attribute_value } from \"svelte/internal\";\nimport { getContext, createEventDispatcher } from \"svelte\";\nconst WORKER_PROXY_CONTEXT_KEY = \"WORKER_PROXY_CONTEXT_KEY\";\nfunction getWorkerProxyContext() {\n  return getContext(WORKER_PROXY_CONTEXT_KEY);\n}\nconst FAKE_LITE_HOST = \"lite.local\";\nfunction is_self_host(url) {\n  return url.host === window.location.host || url.host === \"localhost:7860\" || url.host === \"127.0.0.1:7860\" || // Ref: https://github.com/gradio-app/gradio/blob/v3.32.0/js/app/src/Index.svelte#L194\n  url.host === FAKE_LITE_HOST;\n}\nfunction getHeaderValue(headers, key) {\n  const unifiedKey = key.toLowerCase();\n  for (const [k, v] of Object.entries(headers)) {\n    if (k.toLowerCase() === unifiedKey) {\n      return v;\n    }\n  }\n}\nfunction should_proxy_wasm_src(src) {\n  const is_browser = typeof window !== \"undefined\";\n  if (src == null || !is_browser) {\n    return false;\n  }\n  const url = new URL(src, window.location.href);\n  if (!is_self_host(url)) {\n    return false;\n  }\n  if (url.protocol !== \"http:\" && url.protocol !== \"https:\") {\n    return false;\n  }\n  return true;\n}\nlet maybeWorkerProxy;\nasync function resolve_wasm_src(src) {\n  const is_browser = typeof window !== \"undefined\";\n  if (src == null || !is_browser || !should_proxy_wasm_src(src)) {\n    return src;\n  }\n  if (maybeWorkerProxy == null) {\n    try {\n      maybeWorkerProxy = getWorkerProxyContext();\n    } catch (e) {\n      return src;\n    }\n  }\n  if (maybeWorkerProxy == null) {\n    return src;\n  }\n  const url = new URL(src, window.location.href);\n  const path = url.pathname;\n  return maybeWorkerProxy.httpRequest({\n    method: \"GET\",\n    path,\n    headers: {},\n    query_string: \"\"\n  }).then((response) => {\n    if (response.status !== 200) {\n      throw new Error(`Failed to get file ${path} from the Wasm worker.`);\n    }\n    const blob = new Blob([response.body], {\n      type: getHeaderValue(response.headers, \"content-type\")\n    });\n    const blobUrl = URL.createObjectURL(blob);\n    return blobUrl;\n  });\n}\nconst css = {\n  code: \".unstyled-link.svelte-1s8vnbx{all:unset;cursor:pointer}\",\n  map: '{\"version\":3,\"file\":\"DownloadLink.svelte\",\"sources\":[\"DownloadLink.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, onMount } from \\\\\"svelte\\\\\";\\\\nimport { getWorkerProxyContext } from \\\\\"./context\\\\\";\\\\nimport { should_proxy_wasm_src } from \\\\\"./file-url\\\\\";\\\\nimport { getHeaderValue } from \\\\\"../src/http\\\\\";\\\\nexport let href = void 0;\\\\nexport let download;\\\\nconst dispatch = createEventDispatcher();\\\\nlet is_downloading = false;\\\\nconst worker_proxy = getWorkerProxyContext();\\\\nasync function wasm_click_handler() {\\\\n    if (is_downloading) {\\\\n        return;\\\\n    }\\\\n    dispatch(\\\\\"click\\\\\");\\\\n    if (href == null) {\\\\n        throw new Error(\\\\\"href is not defined.\\\\\");\\\\n    }\\\\n    if (worker_proxy == null) {\\\\n        throw new Error(\\\\\"Wasm worker proxy is not available.\\\\\");\\\\n    }\\\\n    const url = new URL(href, window.location.href);\\\\n    const path = url.pathname;\\\\n    is_downloading = true;\\\\n    worker_proxy.httpRequest({\\\\n        method: \\\\\"GET\\\\\",\\\\n        path,\\\\n        headers: {},\\\\n        query_string: \\\\\"\\\\\"\\\\n    }).then((response) => {\\\\n        if (response.status !== 200) {\\\\n            throw new Error(`Failed to get file ${path} from the Wasm worker.`);\\\\n        }\\\\n        const blob = new Blob([response.body], {\\\\n            type: getHeaderValue(response.headers, \\\\\"content-type\\\\\")\\\\n        });\\\\n        const blobUrl = URL.createObjectURL(blob);\\\\n        const link = document.createElement(\\\\\"a\\\\\");\\\\n        link.href = blobUrl;\\\\n        link.download = download;\\\\n        link.click();\\\\n        URL.revokeObjectURL(blobUrl);\\\\n    }).finally(() => {\\\\n        is_downloading = false;\\\\n    });\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if worker_proxy && should_proxy_wasm_src(href)}\\\\n\\\\t{#if is_downloading}\\\\n\\\\t\\\\t<slot />\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<a {...$$restProps} {href} on:click|preventDefault={wasm_click_handler}>\\\\n\\\\t\\\\t\\\\t<slot />\\\\n\\\\t\\\\t</a>\\\\n\\\\t{/if}\\\\n{:else}\\\\n\\\\t<a\\\\n\\\\t\\\\tstyle:position=\\\\\"relative\\\\\"\\\\n\\\\t\\\\tclass=\\\\\"download-link\\\\\"\\\\n\\\\t\\\\t{href}\\\\n\\\\t\\\\ttarget={typeof window !== \\\\\"undefined\\\\\" && window.__is_colab__\\\\n\\\\t\\\\t\\\\t? \\\\\"_blank\\\\\"\\\\n\\\\t\\\\t\\\\t: null}\\\\n\\\\t\\\\trel=\\\\\"noopener noreferrer\\\\\"\\\\n\\\\t\\\\t{download}\\\\n\\\\t\\\\t{...$$restProps}\\\\n\\\\t\\\\ton:click={dispatch.bind(null, \\\\\"click\\\\\")}\\\\n\\\\t>\\\\n\\\\t\\\\t<slot />\\\\n\\\\t</a>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.unstyled-link {\\\\n\\\\t\\\\tall: unset;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAyEC,6BAAe,CACd,GAAG,CAAE,KAAK,CACV,MAAM,CAAE,OACT\"}'\n};\nconst DownloadLink = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let $$restProps = compute_rest_props($$props, [\"href\", \"download\"]);\n  let { href = void 0 } = $$props;\n  let { download } = $$props;\n  createEventDispatcher();\n  const worker_proxy = getWorkerProxyContext();\n  if ($$props.href === void 0 && $$bindings.href && href !== void 0)\n    $$bindings.href(href);\n  if ($$props.download === void 0 && $$bindings.download && download !== void 0)\n    $$bindings.download(download);\n  $$result.css.add(css);\n  return `${worker_proxy && should_proxy_wasm_src(href) ? `${`<a${spread([escape_object($$restProps), { href: escape_attribute_value(href) }], { classes: \"svelte-1s8vnbx\" })}>${slots.default ? slots.default({}) : ``}</a>`}` : `<a${spread(\n    [\n      { class: \"download-link\" },\n      { href: escape_attribute_value(href) },\n      {\n        target: escape_attribute_value(typeof window !== \"undefined\" && window.__is_colab__ ? \"_blank\" : null)\n      },\n      { rel: \"noopener noreferrer\" },\n      {\n        download: escape_attribute_value(download)\n      },\n      escape_object($$restProps)\n    ],\n    {\n      classes: \"svelte-1s8vnbx\",\n      styles: { \"position\": `relative` }\n    }\n  )}>${slots.default ? slots.default({}) : ``}</a>`}`;\n});\nexport {\n  DownloadLink as D,\n  resolve_wasm_src as r\n};\n"], "names": [], "mappings": ";;AAEA,MAAM,wBAAwB,GAAG,0BAA0B,CAAC;AAC5D,SAAS,qBAAqB,GAAG;AACjC,EAAE,OAAO,UAAU,CAAC,wBAAwB,CAAC,CAAC;AAC9C,CAAC;AACD,MAAM,cAAc,GAAG,YAAY,CAAC;AACpC,SAAS,YAAY,CAAC,GAAG,EAAE;AAC3B,EAAE,OAAO,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB;AAC5G,EAAE,GAAG,CAAC,IAAI,KAAK,cAAc,CAAC;AAC9B,CAAC;AACD,SAAS,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE;AACtC,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;AACvC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAChD,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,UAAU,EAAE;AACxC,MAAM,OAAO,CAAC,CAAC;AACf,KAAK;AACL,GAAG;AACH,CAAC;AACD,SAAS,qBAAqB,CAAC,GAAG,EAAE;AACpC,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;AAClC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACjD,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;AAC1B,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,IAAI,GAAG,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAC7D,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,IAAI,gBAAgB,CAAC;AACrB,eAAe,gBAAgB,CAAC,GAAG,EAAE;AACrC,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE;AACjE,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,EAAE,IAAI,gBAAgB,IAAI,IAAI,EAAE;AAChC,IAAI,IAAI;AACR,MAAM,gBAAgB,GAAG,qBAAqB,EAAE,CAAC;AACjD,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,gBAAgB,IAAI,IAAI,EAAE;AAChC,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACjD,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;AAC5B,EAAE,OAAO,gBAAgB,CAAC,WAAW,CAAC;AACtC,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,IAAI;AACR,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,YAAY,EAAE,EAAE;AACpB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK;AACxB,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;AACjC,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC1E,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC3C,MAAM,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC;AAC5D,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG,CAAC,CAAC;AACL,CAAC;AACD,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,yDAAyD;AACjE,EAAE,GAAG,EAAE,s+EAAs+E;AAC7+E,CAAC,CAAC;AACG,MAAC,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,IAAI,WAAW,GAAG,kBAAkB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;AACtE,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,MAAM,YAAY,GAAG,qBAAqB,EAAE,CAAC;AAC/C,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM;AAC7O,IAAI;AACJ,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE;AAChC,MAAM,EAAE,IAAI,EAAE,sBAAsB,CAAC,IAAI,CAAC,EAAE;AAC5C,MAAM;AACN,QAAQ,MAAM,EAAE,sBAAsB,CAAC,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY,GAAG,QAAQ,GAAG,IAAI,CAAC;AAC9G,OAAO;AACP,MAAM,EAAE,GAAG,EAAE,qBAAqB,EAAE;AACpC,MAAM;AACN,QAAQ,QAAQ,EAAE,sBAAsB,CAAC,QAAQ,CAAC;AAClD,OAAO;AACP,MAAM,aAAa,CAAC,WAAW,CAAC;AAChC,KAAK;AACL,IAAI;AACJ,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,MAAM,EAAE,EAAE,UAAU,EAAE,CAAC,QAAQ,CAAC,EAAE;AACxC,KAAK;AACL,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;;;;"}