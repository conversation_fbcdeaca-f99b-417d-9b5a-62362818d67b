<script>import { getContext, onMount, createEventDispatcher, tick } from "svelte";
import { TABS } from "@gradio/tabs";
import Column from "@gradio/column";
export let elem_id = "";
export let elem_classes = [];
export let label;
export let id = {};
export let visible;
export let interactive;
const dispatch = createEventDispatcher();
const { register_tab, unregister_tab, selected_tab, selected_tab_index } = getContext(TABS);
let tab_index;
$:
  tab_index = register_tab({ label, id, elem_id, visible, interactive });
onMount(() => {
  return () => unregister_tab({ label, id, elem_id });
});
$:
  $selected_tab_index === tab_index && tick().then(() => dispatch("select", { value: label, index: tab_index }));
</script>

<div
	id={elem_id}
	class="tabitem {elem_classes.join(' ')}"
	style:display={$selected_tab === id && visible ? "block" : "none"}
	role="tabpanel"
>
	<Column>
		<slot />
	</Column>
</div>

<style>
	div {
		display: flex;
		position: relative;
		border: none;
		border-radius: var(--radius-sm);
		padding: var(--block-padding);
		width: 100%;
		box-sizing: border-box;
	}
</style>
