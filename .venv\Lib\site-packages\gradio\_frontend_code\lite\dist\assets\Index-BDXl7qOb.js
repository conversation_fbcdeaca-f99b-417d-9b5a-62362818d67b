import{a as w,i as A,s as B,f as r,p as J,c as C,q as h,$ as k,l as K,m as S,t as v,b as I,o as L,d as D,A as N,b0 as O,am as q,a3 as P,G as Q,e as E,u as G,h as M,j as z}from"../lite.js";import{a as R}from"./Tabs-oCuqAIAc.js";import U from"./Index-DdV7UDNf.js";function V(n){let e;const i=n[11].default,t=E(i,n,n[12],null);return{c(){t&&t.c()},m(s,l){t&&t.m(s,l),e=!0},p(s,l){t&&t.p&&(!e||l&4096)&&G(t,i,s,s[12],e?z(i,s[12],l,null):M(s[12]),null)},i(s){e||(v(t,s),e=!0)},o(s){I(t,s),e=!1},d(s){t&&t.d(s)}}}function W(n){let e,i,t,s;return i=new U({props:{$$slots:{default:[V]},$$scope:{ctx:n}}}),{c(){e=J("div"),C(i.$$.fragment),h(e,"id",n[0]),h(e,"class",t="tabitem "+n[1].join(" ")+" svelte-tcemt9"),h(e,"role","tabpanel"),k(e,"display",n[4]===n[2]&&n[3]?"block":"none")},m(l,u){K(l,e,u),S(i,e,null),s=!0},p(l,[u]){const f={};u&4096&&(f.$$scope={dirty:u,ctx:l}),i.$set(f),(!s||u&1)&&h(e,"id",l[0]),(!s||u&2&&t!==(t="tabitem "+l[1].join(" ")+" svelte-tcemt9"))&&h(e,"class",t),u&28&&k(e,"display",l[4]===l[2]&&l[3]?"block":"none")},i(l){s||(v(i.$$.fragment,l),s=!0)},o(l){I(i.$$.fragment,l),s=!1},d(l){l&&L(e),D(i)}}}function X(n,e,i){let t,s,{$$slots:l={},$$scope:u}=e,{elem_id:f=""}=e,{elem_classes:d=[]}=e,{label:_}=e,{id:m={}}=e,{visible:o}=e,{interactive:b}=e;const a=N(),{register_tab:F,unregister_tab:H,selected_tab:T,selected_tab_index:j}=O(R);q(n,T,c=>i(4,s=c)),q(n,j,c=>i(10,t=c));let g;return P(()=>()=>H({label:_,id:m,elem_id:f})),n.$$set=c=>{"elem_id"in c&&i(0,f=c.elem_id),"elem_classes"in c&&i(1,d=c.elem_classes),"label"in c&&i(7,_=c.label),"id"in c&&i(2,m=c.id),"visible"in c&&i(3,o=c.visible),"interactive"in c&&i(8,b=c.interactive),"$$scope"in c&&i(12,u=c.$$scope)},n.$$.update=()=>{n.$$.dirty&397&&i(9,g=F({label:_,id:m,elem_id:f,visible:o,interactive:b})),n.$$.dirty&1664&&t===g&&Q().then(()=>a("select",{value:_,index:g}))},[f,d,m,o,s,T,j,_,b,g,t,l,u]}class Y extends w{constructor(e){super(),A(this,e,X,W,B,{elem_id:0,elem_classes:1,label:7,id:2,visible:3,interactive:8})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),r()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),r()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),r()}get id(){return this.$$.ctx[2]}set id(e){this.$$set({id:e}),r()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),r()}get interactive(){return this.$$.ctx[8]}set interactive(e){this.$$set({interactive:e}),r()}}const Z=Y;function y(n){let e;const i=n[7].default,t=E(i,n,n[9],null);return{c(){t&&t.c()},m(s,l){t&&t.m(s,l),e=!0},p(s,l){t&&t.p&&(!e||l&512)&&G(t,i,s,s[9],e?z(i,s[9],l,null):M(s[9]),null)},i(s){e||(v(t,s),e=!0)},o(s){I(t,s),e=!1},d(s){t&&t.d(s)}}}function x(n){let e,i;return e=new Z({props:{elem_id:n[0],elem_classes:n[1],label:n[2],visible:n[5],interactive:n[6],id:n[3],$$slots:{default:[y]},$$scope:{ctx:n}}}),e.$on("select",n[8]),{c(){C(e.$$.fragment)},m(t,s){S(e,t,s),i=!0},p(t,[s]){const l={};s&1&&(l.elem_id=t[0]),s&2&&(l.elem_classes=t[1]),s&4&&(l.label=t[2]),s&32&&(l.visible=t[5]),s&64&&(l.interactive=t[6]),s&8&&(l.id=t[3]),s&512&&(l.$$scope={dirty:s,ctx:t}),e.$set(l)},i(t){i||(v(e.$$.fragment,t),i=!0)},o(t){I(e.$$.fragment,t),i=!1},d(t){D(e,t)}}}function $(n,e,i){let{$$slots:t={},$$scope:s}=e,{elem_id:l=""}=e,{elem_classes:u=[]}=e,{label:f}=e,{id:d}=e,{gradio:_}=e,{visible:m=!0}=e,{interactive:o=!0}=e;const b=({detail:a})=>_?.dispatch("select",a);return n.$$set=a=>{"elem_id"in a&&i(0,l=a.elem_id),"elem_classes"in a&&i(1,u=a.elem_classes),"label"in a&&i(2,f=a.label),"id"in a&&i(3,d=a.id),"gradio"in a&&i(4,_=a.gradio),"visible"in a&&i(5,m=a.visible),"interactive"in a&&i(6,o=a.interactive),"$$scope"in a&&i(9,s=a.$$scope)},[l,u,f,d,_,m,o,t,b,s]}class se extends w{constructor(e){super(),A(this,e,$,x,B,{elem_id:0,elem_classes:1,label:2,id:3,gradio:4,visible:5,interactive:6})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),r()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),r()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),r()}get id(){return this.$$.ctx[3]}set id(e){this.$$set({id:e}),r()}get gradio(){return this.$$.ctx[4]}set gradio(e){this.$$set({gradio:e}),r()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),r()}get interactive(){return this.$$.ctx[6]}set interactive(e){this.$$set({interactive:e}),r()}}export{Z as BaseTabItem,se as default};
//# sourceMappingURL=Index-BDXl7qOb.js.map
