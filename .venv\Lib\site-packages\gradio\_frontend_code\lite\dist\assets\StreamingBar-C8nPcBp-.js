import{a as f,i as _,s as c,f as u,E as d,l as r,w as n,o,p,q as $,$ as l}from"../lite.js";function m(a){let i,t=`${a[0]}s`;return{c(){i=p("div"),$(i,"class","streaming-bar svelte-roz8lq"),l(i,"animation-duration",t)},m(e,s){r(e,i,s)},p(e,s){s&1&&t!==(t=`${e[0]}s`)&&l(i,"animation-duration",t)},d(e){e&&o(i)}}}function b(a){let i,t=a[0]&&m(a);return{c(){t&&t.c(),i=d()},m(e,s){t&&t.m(e,s),r(e,i,s)},p(e,[s]){e[0]?t?t.p(e,s):(t=m(e),t.c(),t.m(i.parentNode,i)):t&&(t.d(1),t=null)},i:n,o:n,d(e){e&&o(i),t&&t.d(e)}}}function h(a,i,t){let{time_limit:e}=i;return a.$$set=s=>{"time_limit"in s&&t(0,e=s.time_limit)},[e]}class k extends f{constructor(i){super(),_(this,i,h,b,c,{time_limit:0})}get time_limit(){return this.$$.ctx[0]}set time_limit(i){this.$$set({time_limit:i}),u()}}export{k as S};
//# sourceMappingURL=StreamingBar-C8nPcBp-.js.map
