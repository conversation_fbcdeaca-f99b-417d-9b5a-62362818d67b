import{a as T,i as k,s as A,f as o,I as B,a5 as I,c as S,m as j,a6 as q,t as m,b,d as w,A as C,e as D,u as E,h as z,j as F}from"../lite.js";import{T as G}from"./Tabs-oCuqAIAc.js";import{a as Q}from"./Tabs-oCuqAIAc.js";function H(t){let e;const n=t[6].default,i=D(n,t,t[10],null);return{c(){i&&i.c()},m(l,f){i&&i.m(l,f),e=!0},p(l,f){i&&i.p&&(!e||f&1024)&&E(i,n,l,l[10],e?F(n,l[10],f,null):z(l[10]),null)},i(l){e||(m(i,l),e=!0)},o(l){b(i,l),e=!1},d(l){i&&i.d(l)}}}function J(t){let e,n,i;function l(s){t[7](s)}let f={visible:t[1],elem_id:t[2],elem_classes:t[3],initial_tabs:t[4],$$slots:{default:[H]},$$scope:{ctx:t}};return t[0]!==void 0&&(f.selected=t[0]),e=new G({props:f}),B.push(()=>I(e,"selected",l)),e.$on("change",t[8]),e.$on("select",t[9]),{c(){S(e.$$.fragment)},m(s,c){j(e,s,c),i=!0},p(s,[c]){const _={};c&2&&(_.visible=s[1]),c&4&&(_.elem_id=s[2]),c&8&&(_.elem_classes=s[3]),c&16&&(_.initial_tabs=s[4]),c&1024&&(_.$$scope={dirty:c,ctx:s}),!n&&c&1&&(n=!0,_.selected=s[0],q(()=>n=!1)),e.$set(_)},i(s){i||(m(e.$$.fragment,s),i=!0)},o(s){b(e.$$.fragment,s),i=!1},d(s){w(e,s)}}}function K(t,e,n){let{$$slots:i={},$$scope:l}=e;const f=C();let{visible:s=!0}=e,{elem_id:c=""}=e,{elem_classes:_=[]}=e,{selected:u}=e,{initial_tabs:r=[]}=e,{gradio:d}=e;function h(a){u=a,n(0,u)}const g=()=>d?.dispatch("change"),v=a=>d?.dispatch("select",a.detail);return t.$$set=a=>{"visible"in a&&n(1,s=a.visible),"elem_id"in a&&n(2,c=a.elem_id),"elem_classes"in a&&n(3,_=a.elem_classes),"selected"in a&&n(0,u=a.selected),"initial_tabs"in a&&n(4,r=a.initial_tabs),"gradio"in a&&n(5,d=a.gradio),"$$scope"in a&&n(10,l=a.$$scope)},t.$$.update=()=>{t.$$.dirty&1&&f("prop_change",{selected:u})},[u,s,c,_,r,d,i,h,g,v,l]}class N extends T{constructor(e){super(),k(this,e,K,J,A,{visible:1,elem_id:2,elem_classes:3,selected:0,initial_tabs:4,gradio:5})}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),o()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),o()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),o()}get selected(){return this.$$.ctx[0]}set selected(e){this.$$set({selected:e}),o()}get initial_tabs(){return this.$$.ctx[4]}set initial_tabs(e){this.$$set({initial_tabs:e}),o()}get gradio(){return this.$$.ctx[5]}set gradio(e){this.$$set({gradio:e}),o()}}export{G as BaseTabs,Q as TABS,N as default};
//# sourceMappingURL=Index-CoduRn9X.js.map
