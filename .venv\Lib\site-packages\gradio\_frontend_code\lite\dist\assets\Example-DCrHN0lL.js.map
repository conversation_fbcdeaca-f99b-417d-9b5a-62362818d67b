{"version": 3, "file": "Example-DCrHN0lL.js", "sources": ["../../../dropdown/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string | string[] | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let choices: [string, string | number][];\n\n\tlet value_array = value ? (Array.isArray(value) ? value : [value]) : [];\n\tlet names = value_array\n\t\t.map(\n\t\t\t(val) =>\n\t\t\t\t(\n\t\t\t\t\tchoices.find((pair) => pair[1] === val) as\n\t\t\t\t\t\t| [string, string | number]\n\t\t\t\t\t\t| undefined\n\t\t\t\t)?.[0]\n\t\t)\n\t\t.filter((name) => name !== undefined);\n\tlet names_string = names.join(\", \");\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{names_string}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected", "choices", "names_string", "val", "pair", "name"], "mappings": "yJAyBEA,EAAY,CAAA,CAAA,iCAJAC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,iBALSJ,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,sEArBtB,GAAA,CAAA,MAAAM,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF,EACX,CAAA,QAAAG,CAAA,EAAAH,EAaPI,GAXcL,EAAS,MAAM,QAAQA,CAAK,EAAIA,GAASA,CAAK,EAAA,IAE9D,IACCM,GAECF,EAAQ,KAAMG,GAASA,EAAK,CAAC,IAAMD,CAAG,IAGnC,CAAC,CAAA,EAEN,OAAQE,GAASA,UAAkB,EACZ,KAAK,IAAI"}