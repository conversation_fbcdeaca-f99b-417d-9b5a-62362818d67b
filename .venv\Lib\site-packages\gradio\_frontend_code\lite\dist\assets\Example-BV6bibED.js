import{a as c,i as g,s as o,f,p as d,k as v,q as h,r as u,l as y,v as m,n as _,w as r,o as b}from"../lite.js";function q(s){let e,a=(s[0]!==null?s[0].toLocaleString():"")+"",n;return{c(){e=d("div"),n=v(a),h(e,"class","svelte-1ayixqk"),u(e,"table",s[1]==="table"),u(e,"gallery",s[1]==="gallery"),u(e,"selected",s[2])},m(t,l){y(t,e,l),m(e,n)},p(t,[l]){l&1&&a!==(a=(t[0]!==null?t[0].toLocaleString():"")+"")&&_(n,a),l&2&&u(e,"table",t[1]==="table"),l&2&&u(e,"gallery",t[1]==="gallery"),l&4&&u(e,"selected",t[2])},i:r,o:r,d(t){t&&b(e)}}}function S(s,e,a){let{value:n}=e,{type:t}=e,{selected:l=!1}=e;return s.$$set=i=>{"value"in i&&a(0,n=i.value),"type"in i&&a(1,t=i.type),"selected"in i&&a(2,l=i.selected)},[n,t,l]}class p extends c{constructor(e){super(),g(this,e,S,q,o,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),f()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),f()}}export{p as default};
//# sourceMappingURL=Example-BV6bibED.js.map
