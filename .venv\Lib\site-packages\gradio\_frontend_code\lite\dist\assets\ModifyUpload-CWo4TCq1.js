import{a as z,i as E,s as W,Q as N,q as _,l as h,v as A,w as B,o as k,f as I,c as p,m as b,t as c,b as d,d as w,A as P,e as Q,W as v,ax as S,x as D,y as C,z as L,u as F,h as G,j as H}from"../lite.js";import{D as J}from"./Download-CgLP-Xl6.js";import{U as K}from"./Undo-DitIvwTU.js";import{I as O}from"./IconButtonWrapper-Ck50MZwX.js";import{D as R}from"./DownloadLink-B8hI46-W.js";function T(a){let e,n;return{c(){e=N("svg"),n=N("path"),_(n,"d","M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 24 24"),_(e,"fill","none"),_(e,"stroke","currentColor"),_(e,"stroke-width","1.5"),_(e,"stroke-linecap","round"),_(e,"stroke-linejoin","round"),_(e,"class","feather feather-edit-2")},m(t,s){h(t,e,s),A(e,n)},p:B,i:B,o:B,d(t){t&&k(e)}}}class V extends z{constructor(e){super(),E(this,e,null,T,W,{})}}function U(a){let e,n;return e=new v({props:{Icon:V,label:a[3]("common.edit")}}),e.$on("click",a[6]),{c(){p(e.$$.fragment)},m(t,s){b(e,t,s),n=!0},p(t,s){const i={};s&8&&(i.label=t[3]("common.edit")),e.$set(i)},i(t){n||(c(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function j(a){let e,n;return e=new v({props:{Icon:K,label:a[3]("common.undo")}}),e.$on("click",a[7]),{c(){p(e.$$.fragment)},m(t,s){b(e,t,s),n=!0},p(t,s){const i={};s&8&&(i.label=t[3]("common.undo")),e.$set(i)},i(t){n||(c(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function q(a){let e,n;return e=new R({props:{href:a[2],download:!0,$$slots:{default:[X]},$$scope:{ctx:a}}}),{c(){p(e.$$.fragment)},m(t,s){b(e,t,s),n=!0},p(t,s){const i={};s&4&&(i.href=t[2]),s&520&&(i.$$scope={dirty:s,ctx:t}),e.$set(i)},i(t){n||(c(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function X(a){let e,n;return e=new v({props:{Icon:J,label:a[3]("common.download")}}),{c(){p(e.$$.fragment)},m(t,s){b(e,t,s),n=!0},p(t,s){const i={};s&8&&(i.label=t[3]("common.download")),e.$set(i)},i(t){n||(c(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function Y(a){let e,n,t,s,i,$,f=a[0]&&U(a),u=a[1]&&j(a),r=a[2]&&q(a);const g=a[5].default,m=Q(g,a,a[9],null);return i=new v({props:{Icon:S,label:a[3]("common.clear")}}),i.$on("click",a[8]),{c(){f&&f.c(),e=D(),u&&u.c(),n=D(),r&&r.c(),t=D(),m&&m.c(),s=D(),p(i.$$.fragment)},m(o,l){f&&f.m(o,l),h(o,e,l),u&&u.m(o,l),h(o,n,l),r&&r.m(o,l),h(o,t,l),m&&m.m(o,l),h(o,s,l),b(i,o,l),$=!0},p(o,l){o[0]?f?(f.p(o,l),l&1&&c(f,1)):(f=U(o),f.c(),c(f,1),f.m(e.parentNode,e)):f&&(C(),d(f,1,1,()=>{f=null}),L()),o[1]?u?(u.p(o,l),l&2&&c(u,1)):(u=j(o),u.c(),c(u,1),u.m(n.parentNode,n)):u&&(C(),d(u,1,1,()=>{u=null}),L()),o[2]?r?(r.p(o,l),l&4&&c(r,1)):(r=q(o),r.c(),c(r,1),r.m(t.parentNode,t)):r&&(C(),d(r,1,1,()=>{r=null}),L()),m&&m.p&&(!$||l&512)&&F(m,g,o,o[9],$?H(g,o[9],l,null):G(o[9]),null);const M={};l&8&&(M.label=o[3]("common.clear")),i.$set(M)},i(o){$||(c(f),c(u),c(r),c(m,o),c(i.$$.fragment,o),$=!0)},o(o){d(f),d(u),d(r),d(m,o),d(i.$$.fragment,o),$=!1},d(o){o&&(k(e),k(n),k(t),k(s)),f&&f.d(o),u&&u.d(o),r&&r.d(o),m&&m.d(o),w(i,o)}}}function Z(a){let e,n;return e=new O({props:{$$slots:{default:[Y]},$$scope:{ctx:a}}}),{c(){p(e.$$.fragment)},m(t,s){b(e,t,s),n=!0},p(t,[s]){const i={};s&527&&(i.$$scope={dirty:s,ctx:t}),e.$set(i)},i(t){n||(c(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function y(a,e,n){let{$$slots:t={},$$scope:s}=e,{editable:i=!1}=e,{undoable:$=!1}=e,{download:f=null}=e,{i18n:u}=e;const r=P(),g=()=>r("edit"),m=()=>r("undo"),o=l=>{r("clear"),l.stopPropagation()};return a.$$set=l=>{"editable"in l&&n(0,i=l.editable),"undoable"in l&&n(1,$=l.undoable),"download"in l&&n(2,f=l.download),"i18n"in l&&n(3,u=l.i18n),"$$scope"in l&&n(9,s=l.$$scope)},[i,$,f,u,r,t,g,m,o,s]}class le extends z{constructor(e){super(),E(this,e,y,Z,W,{editable:0,undoable:1,download:2,i18n:3})}get editable(){return this.$$.ctx[0]}set editable(e){this.$$set({editable:e}),I()}get undoable(){return this.$$.ctx[1]}set undoable(e){this.$$set({undoable:e}),I()}get download(){return this.$$.ctx[2]}set download(e){this.$$set({download:e}),I()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),I()}}export{le as M};
//# sourceMappingURL=ModifyUpload-CWo4TCq1.js.map
