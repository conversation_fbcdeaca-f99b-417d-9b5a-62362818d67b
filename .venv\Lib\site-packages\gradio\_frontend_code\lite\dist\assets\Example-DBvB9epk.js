import{a as o,i as g,s as d,f as i,p as h,$ as r,q as b,r as n,l as v,w as f,o as y}from"../lite.js";function m(s){let e;return{c(){e=h("div"),r(e,"background-color",s[0]?s[0]:"black"),b(e,"class","svelte-h6ogpl"),n(e,"table",s[1]==="table"),n(e,"gallery",s[1]==="gallery"),n(e,"selected",s[2])},m(t,l){v(t,e,l)},p(t,[l]){l&1&&r(e,"background-color",t[0]?t[0]:"black"),l&2&&n(e,"table",t[1]==="table"),l&2&&n(e,"gallery",t[1]==="gallery"),l&4&&n(e,"selected",t[2])},i:f,o:f,d(t){t&&y(e)}}}function _(s,e,t){let{value:l}=e,{type:c}=e,{selected:u=!1}=e;return s.$$set=a=>{"value"in a&&t(0,l=a.value),"type"in a&&t(1,c=a.type),"selected"in a&&t(2,u=a.selected)},[l,c,u]}class q extends o{constructor(e){super(),g(this,e,_,m,d,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),i()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),i()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),i()}}export{q as default};
//# sourceMappingURL=Example-DBvB9epk.js.map
