import{a as Q,i as R,s as U,f as p,c as h,x as z,E as C,m as w,l as k,y as v,b,z as E,t as m,o as I,d,A as X,a3 as Y,H as N,I as Z,p as F,q as M,r as L,v as V,F as y,W as q}from"../lite.js";import{u as x}from"./utils-BsGrhMNe.js";import{B as ee}from"./BlockLabel-B0HN-MOU.js";import{E as te}from"./Empty-C76eC2zW.js";import{S as ne}from"./ShareButton-Bn_rnIUK.js";import{D as le}from"./Download-CgLP-Xl6.js";import{M as oe,a as se}from"./Minimize-DJwpjnSa.js";import{I as P}from"./Image-Cvn5Jo_E.js";import{I as re}from"./IconButtonWrapper-Ck50MZwX.js";import{g as ie}from"./utils-Gtzs_Zla.js";import{I as ae}from"./Image-Bd-jnd8M.js";import{D as ue}from"./DownloadLink-B8hI46-W.js";/* empty css                                                   */import"./Community-_qL8Iyvr.js";import"./file-url-Co2ROWca.js";function fe(s){let e,l,t,o,r,a,f,u,_;return l=new re({props:{$$slots:{default:[me]},$$scope:{ctx:s}}}),a=new ae({props:{src:s[0].url,alt:"",loading:"lazy"}}),a.$on("load",s[15]),{c(){e=F("div"),h(l.$$.fragment),t=z(),o=F("button"),r=F("div"),h(a.$$.fragment),M(r,"class","image-frame svelte-1kpcxni"),L(r,"selectable",s[4]),M(o,"class","svelte-1kpcxni"),M(e,"class","image-container svelte-1kpcxni")},m(n,i){k(n,e,i),w(l,e,null),V(e,t),V(e,o),V(o,r),w(a,r,null),s[16](e),f=!0,u||(_=y(o,"click",s[10]),u=!0)},p(n,i){const $={};i&262633&&($.$$scope={dirty:i,ctx:n}),l.$set($);const g={};i&1&&(g.src=n[0].url),a.$set(g),(!f||i&16)&&L(r,"selectable",n[4])},i(n){f||(m(l.$$.fragment,n),m(a.$$.fragment,n),f=!0)},o(n){b(l.$$.fragment,n),b(a.$$.fragment,n),f=!1},d(n){n&&I(e),d(l),d(a),s[16](null),u=!1,_()}}}function ce(s){let e,l;return e=new te({props:{unpadded_box:!0,size:"large",$$slots:{default:[be]},$$scope:{ctx:s}}}),{c(){h(e.$$.fragment)},m(t,o){w(e,t,o),l=!0},p(t,o){const r={};o&262144&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){d(e,t)}}}function S(s){let e,l;return e=new q({props:{Icon:oe,label:s[8]?"Exit full screen":"View in full screen"}}),e.$on("click",s[11]),{c(){h(e.$$.fragment)},m(t,o){w(e,t,o),l=!0},p(t,o){const r={};o&256&&(r.label=t[8]?"Exit full screen":"View in full screen"),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){d(e,t)}}}function H(s){let e,l;return e=new q({props:{Icon:se,label:s[8]?"Exit full screen":"View in full screen"}}),e.$on("click",s[11]),{c(){h(e.$$.fragment)},m(t,o){w(e,t,o),l=!0},p(t,o){const r={};o&256&&(r.label=t[8]?"Exit full screen":"View in full screen"),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){d(e,t)}}}function W(s){let e,l;return e=new ue({props:{href:s[0].url,download:s[0].orig_name||"image",$$slots:{default:[_e]},$$scope:{ctx:s}}}),{c(){h(e.$$.fragment)},m(t,o){w(e,t,o),l=!0},p(t,o){const r={};o&1&&(r.href=t[0].url),o&1&&(r.download=t[0].orig_name||"image"),o&262208&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){d(e,t)}}}function _e(s){let e,l;return e=new q({props:{Icon:le,label:s[6]("common.download")}}),{c(){h(e.$$.fragment)},m(t,o){w(e,t,o),l=!0},p(t,o){const r={};o&64&&(r.label=t[6]("common.download")),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){d(e,t)}}}function A(s){let e,l;return e=new ne({props:{i18n:s[6],formatter:s[12],value:s[0]}}),e.$on("share",s[13]),e.$on("error",s[14]),{c(){h(e.$$.fragment)},m(t,o){w(e,t,o),l=!0},p(t,o){const r={};o&64&&(r.i18n=t[6]),o&1&&(r.value=t[0]),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){d(e,t)}}}function me(s){let e,l,t,o,r,a=!s[8]&&s[7]&&S(s),f=s[8]&&s[7]&&H(s),u=s[3]&&W(s),_=s[5]&&A(s);return{c(){a&&a.c(),e=z(),f&&f.c(),l=z(),u&&u.c(),t=z(),_&&_.c(),o=C()},m(n,i){a&&a.m(n,i),k(n,e,i),f&&f.m(n,i),k(n,l,i),u&&u.m(n,i),k(n,t,i),_&&_.m(n,i),k(n,o,i),r=!0},p(n,i){!n[8]&&n[7]?a?(a.p(n,i),i&384&&m(a,1)):(a=S(n),a.c(),m(a,1),a.m(e.parentNode,e)):a&&(v(),b(a,1,1,()=>{a=null}),E()),n[8]&&n[7]?f?(f.p(n,i),i&384&&m(f,1)):(f=H(n),f.c(),m(f,1),f.m(l.parentNode,l)):f&&(v(),b(f,1,1,()=>{f=null}),E()),n[3]?u?(u.p(n,i),i&8&&m(u,1)):(u=W(n),u.c(),m(u,1),u.m(t.parentNode,t)):u&&(v(),b(u,1,1,()=>{u=null}),E()),n[5]?_?(_.p(n,i),i&32&&m(_,1)):(_=A(n),_.c(),m(_,1),_.m(o.parentNode,o)):_&&(v(),b(_,1,1,()=>{_=null}),E())},i(n){r||(m(a),m(f),m(u),m(_),r=!0)},o(n){b(a),b(f),b(u),b(_),r=!1},d(n){n&&(I(e),I(l),I(t),I(o)),a&&a.d(n),f&&f.d(n),u&&u.d(n),_&&_.d(n)}}}function be(s){let e,l;return e=new P({}),{c(){h(e.$$.fragment)},m(t,o){w(e,t,o),l=!0},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){d(e,t)}}}function ge(s){let e,l,t,o,r,a;e=new ee({props:{show_label:s[2],Icon:P,label:s[2]?s[1]||s[6]("image.image"):""}});const f=[ce,fe],u=[];function _(n,i){return n[0]===null||!n[0].url?0:1}return t=_(s),o=u[t]=f[t](s),{c(){h(e.$$.fragment),l=z(),o.c(),r=C()},m(n,i){w(e,n,i),k(n,l,i),u[t].m(n,i),k(n,r,i),a=!0},p(n,[i]){const $={};i&4&&($.show_label=n[2]),i&70&&($.label=n[2]?n[1]||n[6]("image.image"):""),e.$set($);let g=t;t=_(n),t===g?u[t].p(n,i):(v(),b(u[g],1,1,()=>{u[g]=null}),E(),o=u[t],o?o.p(n,i):(o=u[t]=f[t](n),o.c()),m(o,1),o.m(r.parentNode,r))},i(n){a||(m(e.$$.fragment,n),m(o),a=!0)},o(n){b(e.$$.fragment,n),b(o),a=!1},d(n){n&&(I(l),I(r)),d(e,n),u[t].d(n)}}}function he(s,e,l){let{value:t}=e,{label:o=void 0}=e,{show_label:r}=e,{show_download_button:a=!0}=e,{selectable:f=!1}=e,{show_share_button:u=!1}=e,{i18n:_}=e,{show_fullscreen_button:n=!0}=e;const i=X(),$=c=>{let D=ie(c);D&&i("select",{index:D,value:null})};let g=!1,B;Y(()=>{document.addEventListener("fullscreenchange",()=>{l(8,g=!!document.fullscreenElement)})});const T=async()=>{g?(await document.exitFullscreen(),l(8,g=!g)):await B.requestFullscreen()},j=async c=>c?`<img src="${await x(c)}" />`:"";function G(c){N.call(this,s,c)}function J(c){N.call(this,s,c)}function K(c){N.call(this,s,c)}function O(c){Z[c?"unshift":"push"](()=>{B=c,l(9,B)})}return s.$$set=c=>{"value"in c&&l(0,t=c.value),"label"in c&&l(1,o=c.label),"show_label"in c&&l(2,r=c.show_label),"show_download_button"in c&&l(3,a=c.show_download_button),"selectable"in c&&l(4,f=c.selectable),"show_share_button"in c&&l(5,u=c.show_share_button),"i18n"in c&&l(6,_=c.i18n),"show_fullscreen_button"in c&&l(7,n=c.show_fullscreen_button)},[t,o,r,a,f,u,_,n,g,B,$,T,j,G,J,K,O]}class qe extends Q{constructor(e){super(),R(this,e,he,ge,U,{value:0,label:1,show_label:2,show_download_button:3,selectable:4,show_share_button:5,i18n:6,show_fullscreen_button:7})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),p()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),p()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),p()}get show_download_button(){return this.$$.ctx[3]}set show_download_button(e){this.$$set({show_download_button:e}),p()}get selectable(){return this.$$.ctx[4]}set selectable(e){this.$$set({selectable:e}),p()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),p()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),p()}get show_fullscreen_button(){return this.$$.ctx[7]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),p()}}export{qe as default};
//# sourceMappingURL=ImagePreview-2NMpgMNU.js.map
