import{a as m,i as g,s as _,f as o,p as d,q as h,r as n,l as p,t as u,y as b,b as f,z as v,o as y,c as k,m as $,d as q}from"../lite.js";import{I as z}from"./Image-Bd-jnd8M.js";/* empty css                                              */import"./file-url-Co2ROWca.js";function c(r){let e,s;return e=new z({props:{src:r[0].url,alt:""}}),{c(){k(e.$$.fragment)},m(t,l){$(e,t,l),s=!0},p(t,l){const a={};l&1&&(a.src=t[0].url),e.$set(a)},i(t){s||(u(e.$$.fragment,t),s=!0)},o(t){f(e.$$.fragment,t),s=!1},d(t){q(e,t)}}}function I(r){let e,s,t=r[0]&&c(r);return{c(){e=d("div"),t&&t.c(),h(e,"class","container svelte-a9zvka"),n(e,"table",r[1]==="table"),n(e,"gallery",r[1]==="gallery"),n(e,"selected",r[2]),n(e,"border",r[0])},m(l,a){p(l,e,a),t&&t.m(e,null),s=!0},p(l,[a]){l[0]?t?(t.p(l,a),a&1&&u(t,1)):(t=c(l),t.c(),u(t,1),t.m(e,null)):t&&(b(),f(t,1,1,()=>{t=null}),v()),(!s||a&2)&&n(e,"table",l[1]==="table"),(!s||a&2)&&n(e,"gallery",l[1]==="gallery"),(!s||a&4)&&n(e,"selected",l[2]),(!s||a&1)&&n(e,"border",l[0])},i(l){s||(u(t),s=!0)},o(l){f(t),s=!1},d(l){l&&y(e),t&&t.d()}}}function w(r,e,s){let{value:t}=e,{type:l}=e,{selected:a=!1}=e;return r.$$set=i=>{"value"in i&&s(0,t=i.value),"type"in i&&s(1,l=i.type),"selected"in i&&s(2,a=i.selected)},[t,l,a]}class A extends m{constructor(e){super(),g(this,e,w,I,_,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),o()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),o()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),o()}}export{A as default};
//# sourceMappingURL=Example-B3LHr6Wt.js.map
