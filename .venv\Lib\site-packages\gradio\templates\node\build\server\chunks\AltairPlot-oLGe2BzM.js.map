{"version": 3, "file": "AltairPlot-oLGe2BzM.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/AltairPlot.js"], "sourcesContent": ["import { create_ssr_component, add_attribute, escape } from \"svelte/internal\";\nimport { y as colors } from \"./client.js\";\nimport { g as get_next_color } from \"./color.js\";\nimport { onMount, onDestroy } from \"svelte\";\nimport embed from \"./vega-embed.module.js\";\nfunction set_config(spec, computed_style, chart_type, colors2) {\n  let accentColor = computed_style.getPropertyValue(\"--color-accent\");\n  let bodyTextColor = computed_style.getPropertyValue(\"--body-text-color\");\n  let borderColorPrimary = computed_style.getPropertyValue(\n    \"--border-color-primary\"\n  );\n  let fontFamily = computed_style.fontFamily;\n  let titleWeight = computed_style.getPropertyValue(\n    \"--block-title-text-weight\"\n  );\n  const fontToPxVal = (font) => {\n    return font.endsWith(\"px\") ? parseFloat(font.slice(0, -2)) : 12;\n  };\n  let textSizeMd = fontToPxVal(computed_style.getPropertyValue(\"--text-md\"));\n  let textSizeSm = fontToPxVal(computed_style.getPropertyValue(\"--text-sm\"));\n  let config = {\n    autosize: { type: \"fit\", contains: \"padding\" },\n    axis: {\n      labelFont: fontFamily,\n      labelColor: bodyTextColor,\n      titleFont: fontFamily,\n      titleColor: bodyTextColor,\n      tickColor: borderColorPrimary,\n      labelFontSize: textSizeSm,\n      gridColor: borderColorPrimary,\n      titleFontWeight: \"normal\",\n      titleFontSize: textSizeSm,\n      labelFontWeight: \"normal\",\n      domain: false,\n      labelAngle: 0\n    },\n    legend: {\n      labelColor: bodyTextColor,\n      labelFont: fontFamily,\n      titleColor: bodyTextColor,\n      titleFont: fontFamily,\n      titleFontWeight: \"normal\",\n      titleFontSize: textSizeSm,\n      labelFontWeight: \"normal\",\n      offset: 2\n    },\n    title: {\n      color: bodyTextColor,\n      font: fontFamily,\n      fontSize: textSizeMd,\n      fontWeight: titleWeight,\n      anchor: \"middle\"\n    },\n    view: {\n      stroke: borderColorPrimary\n    }\n  };\n  spec.config = config;\n  let encoding = spec.encoding;\n  let layer = spec.layer;\n  switch (chart_type) {\n    case \"scatter\":\n      spec.config.mark = { stroke: accentColor };\n      if (encoding.color && encoding.color.type == \"nominal\") {\n        encoding.color.scale.range = encoding.color.scale.range.map(\n          (_, i) => get_color(colors2, i)\n        );\n      } else if (encoding.color && encoding.color.type == \"quantitative\") {\n        encoding.color.scale.range = [\"#eff6ff\", \"#1e3a8a\"];\n        encoding.color.scale.range.interpolate = \"hsl\";\n      }\n      break;\n    case \"line\":\n      spec.config.mark = { stroke: accentColor, cursor: \"crosshair\" };\n      layer.forEach((d) => {\n        if (d.encoding.color) {\n          d.encoding.color.scale.range = d.encoding.color.scale.range.map(\n            (_, i) => get_color(colors2, i)\n          );\n        }\n      });\n      break;\n    case \"bar\":\n      spec.config.mark = { opacity: 0.8, fill: accentColor };\n      if (encoding.color) {\n        encoding.color.scale.range = encoding.color.scale.range.map(\n          (_, i) => get_color(colors2, i)\n        );\n      }\n      break;\n  }\n  return spec;\n}\nfunction get_color(colors$1, index) {\n  let current_color = colors$1[index % colors$1.length];\n  if (current_color && current_color in colors) {\n    return colors[current_color]?.primary;\n  } else if (!current_color) {\n    return colors[get_next_color(index)].primary;\n  }\n  return current_color;\n}\nconst css = {\n  code: \".altair.svelte-1qhqpn7 canvas{padding:6px}.altair.svelte-1qhqpn7 .vega-embed{padding:0px !important}.altair.svelte-1qhqpn7 .vega-actions{right:0px !important}.layout.svelte-1qhqpn7{display:flex;flex-direction:column;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full);color:var(--body-text-color)}.altair.svelte-1qhqpn7{display:flex;flex-direction:column;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full)}.caption.svelte-1qhqpn7{font-size:var(--text-sm);margin-bottom:6px}#vg-tooltip-element{font-family:var(--font) !important;font-size:var(--text-xs) !important;box-shadow:none !important;background-color:var(--block-background-fill) !important;border:1px solid var(--border-color-primary) !important;color:var(--body-text-color) !important}#vg-tooltip-element .key{color:var(--body-text-color-subdued) !important}\",\n  map: '{\"version\":3,\"file\":\"AltairPlot.svelte\",\"sources\":[\"AltairPlot.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { set_config } from \\\\\"./altair_utils\\\\\";\\\\nimport { onMount, onDestroy } from \\\\\"svelte\\\\\";\\\\nimport vegaEmbed from \\\\\"vega-embed\\\\\";\\\\nexport let value;\\\\nexport let colors = [];\\\\nexport let caption;\\\\nexport let show_actions_button;\\\\nexport let gradio;\\\\nlet element;\\\\nlet parent_element;\\\\nlet view;\\\\nexport let _selectable;\\\\nlet computed_style = window.getComputedStyle(document.body);\\\\nlet old_spec;\\\\nlet spec_width;\\\\n$: plot = value?.plot;\\\\n$: spec = JSON.parse(plot);\\\\n$: if (spec && spec.params && !_selectable) {\\\\n    spec.params = spec.params.filter((param) => param.name !== \\\\\"brush\\\\\");\\\\n}\\\\n$: if (old_spec !== spec) {\\\\n    old_spec = spec;\\\\n    spec_width = spec.width;\\\\n}\\\\n$: if (value.chart) {\\\\n    spec = set_config(spec, computed_style, value.chart, colors);\\\\n}\\\\n$: fit_width_to_parent = spec.encoding?.column?.field || spec.encoding?.row?.field || value.chart === void 0 ? false : true;\\\\nconst get_width = () => {\\\\n    return Math.min(parent_element.offsetWidth, spec_width || parent_element.offsetWidth);\\\\n};\\\\nlet resize_callback = () => {\\\\n};\\\\nconst renderPlot = () => {\\\\n    if (fit_width_to_parent) {\\\\n        spec.width = get_width();\\\\n    }\\\\n    vegaEmbed(element, spec, { actions: show_actions_button }).then(function (result) {\\\\n        view = result.view;\\\\n        resize_callback = () => {\\\\n            view.signal(\\\\\"width\\\\\", get_width()).run();\\\\n        };\\\\n        if (!_selectable)\\\\n            return;\\\\n        const callback = (event, item) => {\\\\n            const brushValue = view.signal(\\\\\"brush\\\\\");\\\\n            if (brushValue) {\\\\n                if (Object.keys(brushValue).length === 0) {\\\\n                    gradio.dispatch(\\\\\"select\\\\\", {\\\\n                        value: null,\\\\n                        index: null,\\\\n                        selected: false\\\\n                    });\\\\n                }\\\\n                else {\\\\n                    const key = Object.keys(brushValue)[0];\\\\n                    let range = brushValue[key].map((x) => x / 1e3);\\\\n                    gradio.dispatch(\\\\\"select\\\\\", {\\\\n                        value: brushValue,\\\\n                        index: range,\\\\n                        selected: true\\\\n                    });\\\\n                }\\\\n            }\\\\n        };\\\\n        view.addEventListener(\\\\\"mouseup\\\\\", callback);\\\\n        view.addEventListener(\\\\\"touchup\\\\\", callback);\\\\n    });\\\\n};\\\\nlet resizeObserver = new ResizeObserver(() => {\\\\n    if (fit_width_to_parent && spec.width !== parent_element.offsetWidth) {\\\\n        resize_callback();\\\\n    }\\\\n});\\\\nonMount(() => {\\\\n    renderPlot();\\\\n    resizeObserver.observe(parent_element);\\\\n});\\\\nonDestroy(() => {\\\\n    resizeObserver.disconnect();\\\\n});\\\\n<\\/script>\\\\n\\\\n<div data-testid={\\\\\"altair\\\\\"} class=\\\\\"altair layout\\\\\" bind:this={parent_element}>\\\\n\\\\t<div bind:this={element}></div>\\\\n\\\\t{#if caption}\\\\n\\\\t\\\\t<div class=\\\\\"caption layout\\\\\">\\\\n\\\\t\\\\t\\\\t{caption}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.altair :global(canvas) {\\\\n\\\\t\\\\tpadding: 6px;\\\\n\\\\t}\\\\n\\\\t.altair :global(.vega-embed) {\\\\n\\\\t\\\\tpadding: 0px !important;\\\\n\\\\t}\\\\n\\\\t.altair :global(.vega-actions) {\\\\n\\\\t\\\\tright: 0px !important;\\\\n\\\\t}\\\\n\\\\t.layout {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\t.altair {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\t.caption {\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tmargin-bottom: 6px;\\\\n\\\\t}\\\\n\\\\t:global(#vg-tooltip-element) {\\\\n\\\\t\\\\tfont-family: var(--font) !important;\\\\n\\\\t\\\\tfont-size: var(--text-xs) !important;\\\\n\\\\t\\\\tbox-shadow: none !important;\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill) !important;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary) !important;\\\\n\\\\t\\\\tcolor: var(--body-text-color) !important;\\\\n\\\\t}\\\\n\\\\t:global(#vg-tooltip-element .key) {\\\\n\\\\t\\\\tcolor: var(--body-text-color-subdued) !important;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA6FC,sBAAO,CAAS,MAAQ,CACvB,OAAO,CAAE,GACV,CACA,sBAAO,CAAS,WAAa,CAC5B,OAAO,CAAE,GAAG,CAAC,UACd,CACA,sBAAO,CAAS,aAAe,CAC9B,KAAK,CAAE,GAAG,CAAC,UACZ,CACA,sBAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CACA,sBAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CACA,uBAAS,CACR,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,aAAa,CAAE,GAChB,CACQ,mBAAqB,CAC5B,WAAW,CAAE,IAAI,MAAM,CAAC,CAAC,UAAU,CACnC,SAAS,CAAE,IAAI,SAAS,CAAC,CAAC,UAAU,CACpC,UAAU,CAAE,IAAI,CAAC,UAAU,CAC3B,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAAC,UAAU,CACzD,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAAC,UAAU,CACxD,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAAC,UAC/B,CACQ,wBAA0B,CACjC,KAAK,CAAE,IAAI,yBAAyB,CAAC,CAAC,UACvC\"}'\n};\nconst AltairPlot = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let plot;\n  let spec;\n  let fit_width_to_parent;\n  let { value } = $$props;\n  let { colors: colors2 = [] } = $$props;\n  let { caption } = $$props;\n  let { show_actions_button } = $$props;\n  let { gradio } = $$props;\n  let element;\n  let parent_element;\n  let view;\n  let { _selectable } = $$props;\n  let computed_style = window.getComputedStyle(document.body);\n  let old_spec;\n  let spec_width;\n  const get_width = () => {\n    return Math.min(parent_element.offsetWidth, spec_width || parent_element.offsetWidth);\n  };\n  let resize_callback = () => {\n  };\n  const renderPlot = () => {\n    if (fit_width_to_parent) {\n      spec.width = get_width();\n    }\n    embed(element, spec, { actions: show_actions_button }).then(function(result) {\n      view = result.view;\n      resize_callback = () => {\n        view.signal(\"width\", get_width()).run();\n      };\n      if (!_selectable)\n        return;\n      const callback = (event, item) => {\n        const brushValue = view.signal(\"brush\");\n        if (brushValue) {\n          if (Object.keys(brushValue).length === 0) {\n            gradio.dispatch(\"select\", {\n              value: null,\n              index: null,\n              selected: false\n            });\n          } else {\n            const key = Object.keys(brushValue)[0];\n            let range = brushValue[key].map((x) => x / 1e3);\n            gradio.dispatch(\"select\", {\n              value: brushValue,\n              index: range,\n              selected: true\n            });\n          }\n        }\n      };\n      view.addEventListener(\"mouseup\", callback);\n      view.addEventListener(\"touchup\", callback);\n    });\n  };\n  let resizeObserver = new ResizeObserver(() => {\n    if (fit_width_to_parent && spec.width !== parent_element.offsetWidth) {\n      resize_callback();\n    }\n  });\n  onMount(() => {\n    renderPlot();\n    resizeObserver.observe(parent_element);\n  });\n  onDestroy(() => {\n    resizeObserver.disconnect();\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.colors === void 0 && $$bindings.colors && colors2 !== void 0)\n    $$bindings.colors(colors2);\n  if ($$props.caption === void 0 && $$bindings.caption && caption !== void 0)\n    $$bindings.caption(caption);\n  if ($$props.show_actions_button === void 0 && $$bindings.show_actions_button && show_actions_button !== void 0)\n    $$bindings.show_actions_button(show_actions_button);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props._selectable === void 0 && $$bindings._selectable && _selectable !== void 0)\n    $$bindings._selectable(_selectable);\n  $$result.css.add(css);\n  plot = value?.plot;\n  spec = JSON.parse(plot);\n  {\n    if (spec && spec.params && !_selectable) {\n      spec.params = spec.params.filter((param) => param.name !== \"brush\");\n    }\n  }\n  {\n    if (value.chart) {\n      spec = set_config(spec, computed_style, value.chart, colors2);\n    }\n  }\n  {\n    if (old_spec !== spec) {\n      old_spec = spec;\n      spec_width = spec.width;\n    }\n  }\n  fit_width_to_parent = spec.encoding?.column?.field || spec.encoding?.row?.field || value.chart === void 0 ? false : true;\n  return `<div${add_attribute(\"data-testid\", \"altair\", 0)} class=\"altair layout svelte-1qhqpn7\"${add_attribute(\"this\", parent_element, 0)}><div${add_attribute(\"this\", element, 0)}></div> ${caption ? `<div class=\"caption layout svelte-1qhqpn7\">${escape(caption)}</div>` : ``} </div>`;\n});\nexport {\n  AltairPlot as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAKA,SAAS,UAAU,CAAC,IAAI,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE;AAC/D,EAAE,IAAI,WAAW,GAAG,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AACtE,EAAE,IAAI,aAAa,GAAG,cAAc,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;AAC3E,EAAE,IAAI,kBAAkB,GAAG,cAAc,CAAC,gBAAgB;AAC1D,IAAI,wBAAwB;AAC5B,GAAG,CAAC;AACJ,EAAE,IAAI,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;AAC7C,EAAE,IAAI,WAAW,GAAG,cAAc,CAAC,gBAAgB;AACnD,IAAI,2BAA2B;AAC/B,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,IAAI,KAAK;AAChC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACpE,GAAG,CAAC;AACJ,EAAE,IAAI,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7E,EAAE,IAAI,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7E,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;AAClD,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,eAAe,EAAE,QAAQ;AAC/B,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,eAAe,EAAE,QAAQ;AAC/B,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,eAAe,EAAE,QAAQ;AAC/B,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,eAAe,EAAE,QAAQ;AAC/B,MAAM,MAAM,EAAE,CAAC;AACf,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,UAAU,EAAE,WAAW;AAC7B,MAAM,MAAM,EAAE,QAAQ;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,MAAM,EAAE,kBAAkB;AAChC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACvB,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACzB,EAAE,QAAQ,UAAU;AACpB,IAAI,KAAK,SAAS;AAClB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;AACjD,MAAM,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;AAC9D,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG;AACnE,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;AACzC,SAAS,CAAC;AACV,OAAO,MAAM,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,cAAc,EAAE;AAC1E,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAC5D,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;AACvD,OAAO;AACP,MAAM,MAAM;AACZ,IAAI,KAAK,MAAM;AACf,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;AACtE,MAAM,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;AAC3B,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE;AAC9B,UAAU,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG;AACzE,YAAY,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;AAC3C,WAAW,CAAC;AACZ,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,MAAM;AACZ,IAAI,KAAK,KAAK;AACd,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;AAC7D,MAAM,IAAI,QAAQ,CAAC,KAAK,EAAE;AAC1B,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG;AACnE,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;AACzC,SAAS,CAAC;AACV,OAAO;AACP,MAAM,MAAM;AACZ,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,SAAS,SAAS,CAAC,QAAQ,EAAE,KAAK,EAAE;AACpC,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AACxD,EAAE,IAAI,aAAa,IAAI,aAAa,IAAI,MAAM,EAAE;AAChD,IAAI,OAAO,MAAM,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC;AAC1C,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE;AAC7B,IAAI,OAAO,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;AACjD,GAAG;AACH,EAAE,OAAO,aAAa,CAAC;AACvB,CAAC;AACD,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,83BAA83B;AACt4B,EAAE,GAAG,EAAE,qqKAAqqK;AAC5qK,CAAC,CAAC;AACG,MAAC,UAAU,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAClF,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,mBAAmB,CAAC;AAC1B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,IAAI,cAAc,CAAC;AAErB,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,cAAc,GAAG,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC9D,EAAE,IAAI,QAAQ,CAAC;AA0Cf,EAAE,IAAI,cAAc,GAAG,IAAI,cAAc,CAAC,MAAM;AAChD,IAAI,IAAI,mBAAmB,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,CAAC,WAAW,EAAE,CAErE;AACL,GAAG,CAAC,CAAC;AAKL,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;AAChC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,KAAK,KAAK,CAAC;AAC1E,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,mBAAmB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,mBAAmB,IAAI,mBAAmB,KAAK,KAAK,CAAC;AAChH,IAAI,UAAU,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AACxD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,CAAC;AACrB,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE;AACF,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE;AAC7C,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;AAC1E,KAAK;AACL,GAAG;AACH,EAAE;AACF,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AACrB,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACpE,KAAK;AACL,GAAG;AACH,EAAE;AACF,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE;AAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,MAAmB,IAAI,CAAC,KAAK,CAAC;AAC9B,KAAK;AACL,GAAG;AACH,EAAE,mBAAmB,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;AAC3H,EAAE,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,qCAAqC,EAAE,aAAa,CAAC,MAAM,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,2CAA2C,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAC3R,CAAC;;;;"}