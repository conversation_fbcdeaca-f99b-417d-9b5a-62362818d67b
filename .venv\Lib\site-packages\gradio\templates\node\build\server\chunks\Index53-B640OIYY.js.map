{"version": 3, "file": "Index53-B640OIYY.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index53.js"], "sourcesContent": ["import { create_ssr_component, add_attribute, validate_component, escape, each, add_styles, merge_ssr_styles } from \"svelte/internal\";\nimport { createEventDispatcher, onMount, tick, afterUpdate } from \"svelte\";\nimport { l as MarkdownCode, B as Block, S as Static } from \"./client.js\";\nimport { d as dequal } from \"./index6.js\";\nimport { U as Upload } from \"./ModifyUpload.js\";\nimport { default as default2 } from \"./Example8.js\";\nconst css$3 = {\n  code: \"input.svelte-z9gpua{position:absolute;top:var(--size-2);right:var(--size-2);bottom:var(--size-2);left:var(--size-2);flex:1 1 0%;transform:translateX(-0.1px);outline:none;border:none;background:transparent}span.svelte-z9gpua{flex:1 1 0%;outline:none;padding:var(--size-2);-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}.header.svelte-z9gpua{transform:translateX(0);font:var(--weight-bold)}.edit.svelte-z9gpua{opacity:0;pointer-events:none}\",\n  map: '{\"version\":3,\"file\":\"EditableCell.svelte\",\"sources\":[\"EditableCell.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport { MarkdownCode } from \\\\\"@gradio/markdown-code\\\\\";\\\\nexport let edit;\\\\nexport let value = \\\\\"\\\\\";\\\\nexport let display_value = null;\\\\nexport let styling = \\\\\"\\\\\";\\\\nexport let header = false;\\\\nexport let datatype = \\\\\"str\\\\\";\\\\nexport let latex_delimiters;\\\\nexport let clear_on_focus = false;\\\\nexport let select_on_focus = false;\\\\nexport let line_breaks = true;\\\\nexport let editable = true;\\\\nexport let root;\\\\nconst dispatch = createEventDispatcher();\\\\nexport let el;\\\\n$: _value = value;\\\\nfunction use_focus(node) {\\\\n    if (clear_on_focus) {\\\\n        _value = \\\\\"\\\\\";\\\\n    }\\\\n    if (select_on_focus) {\\\\n        node.select();\\\\n    }\\\\n    node.focus();\\\\n    return {};\\\\n}\\\\nfunction handle_blur({ currentTarget }) {\\\\n    value = currentTarget.value;\\\\n    dispatch(\\\\\"blur\\\\\");\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if edit}\\\\n\\\\t<input\\\\n\\\\t\\\\trole=\\\\\"textbox\\\\\"\\\\n\\\\t\\\\tbind:this={el}\\\\n\\\\t\\\\tbind:value={_value}\\\\n\\\\t\\\\tclass:header\\\\n\\\\t\\\\ttabindex=\\\\\"-1\\\\\"\\\\n\\\\t\\\\ton:blur={handle_blur}\\\\n\\\\t\\\\tuse:use_focus\\\\n\\\\t\\\\ton:keydown\\\\n\\\\t/>\\\\n{/if}\\\\n\\\\n<span\\\\n\\\\ton:dblclick\\\\n\\\\ttabindex=\\\\\"-1\\\\\"\\\\n\\\\trole=\\\\\"button\\\\\"\\\\n\\\\tclass:edit\\\\n\\\\ton:focus|preventDefault\\\\n\\\\tstyle={styling}\\\\n>\\\\n\\\\t{#if datatype === \\\\\"html\\\\\"}\\\\n\\\\t\\\\t{@html value}\\\\n\\\\t{:else if datatype === \\\\\"markdown\\\\\"}\\\\n\\\\t\\\\t<MarkdownCode\\\\n\\\\t\\\\t\\\\tmessage={value.toLocaleString()}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\tchatbot={false}\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t/>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t{editable ? value : display_value || value}\\\\n\\\\t{/if}\\\\n</span>\\\\n\\\\n<style>\\\\n\\\\tinput {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: var(--size-2);\\\\n\\\\t\\\\tright: var(--size-2);\\\\n\\\\t\\\\tbottom: var(--size-2);\\\\n\\\\t\\\\tleft: var(--size-2);\\\\n\\\\t\\\\tflex: 1 1 0%;\\\\n\\\\t\\\\ttransform: translateX(-0.1px);\\\\n\\\\t\\\\toutline: none;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbackground: transparent;\\\\n\\\\t}\\\\n\\\\n\\\\tspan {\\\\n\\\\t\\\\tflex: 1 1 0%;\\\\n\\\\t\\\\toutline: none;\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t\\\\t-webkit-user-select: text;\\\\n\\\\t\\\\t-moz-user-select: text;\\\\n\\\\t\\\\t-ms-user-select: text;\\\\n\\\\t\\\\tuser-select: text;\\\\n\\\\t}\\\\n\\\\n\\\\t.header {\\\\n\\\\t\\\\ttransform: translateX(0);\\\\n\\\\t\\\\tfont: var(--weight-bold);\\\\n\\\\t}\\\\n\\\\n\\\\t.edit {\\\\n\\\\t\\\\topacity: 0;\\\\n\\\\t\\\\tpointer-events: none;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAsEC,mBAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,IAAI,CAAE,IAAI,QAAQ,CAAC,CACnB,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CACZ,SAAS,CAAE,WAAW,MAAM,CAAC,CAC7B,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,WACb,CAEA,kBAAK,CACJ,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CACZ,OAAO,CAAE,IAAI,CACb,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,mBAAmB,CAAE,IAAI,CACzB,gBAAgB,CAAE,IAAI,CACtB,eAAe,CAAE,IAAI,CACrB,WAAW,CAAE,IACd,CAEA,qBAAQ,CACP,SAAS,CAAE,WAAW,CAAC,CAAC,CACxB,IAAI,CAAE,IAAI,aAAa,CACxB,CAEA,mBAAM,CACL,OAAO,CAAE,CAAC,CACV,cAAc,CAAE,IACjB\"}'\n};\nconst EditableCell = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let _value;\n  let { edit } = $$props;\n  let { value = \"\" } = $$props;\n  let { display_value = null } = $$props;\n  let { styling = \"\" } = $$props;\n  let { header = false } = $$props;\n  let { datatype = \"str\" } = $$props;\n  let { latex_delimiters } = $$props;\n  let { clear_on_focus = false } = $$props;\n  let { select_on_focus = false } = $$props;\n  let { line_breaks = true } = $$props;\n  let { editable = true } = $$props;\n  let { root } = $$props;\n  createEventDispatcher();\n  let { el } = $$props;\n  if ($$props.edit === void 0 && $$bindings.edit && edit !== void 0)\n    $$bindings.edit(edit);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.display_value === void 0 && $$bindings.display_value && display_value !== void 0)\n    $$bindings.display_value(display_value);\n  if ($$props.styling === void 0 && $$bindings.styling && styling !== void 0)\n    $$bindings.styling(styling);\n  if ($$props.header === void 0 && $$bindings.header && header !== void 0)\n    $$bindings.header(header);\n  if ($$props.datatype === void 0 && $$bindings.datatype && datatype !== void 0)\n    $$bindings.datatype(datatype);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.clear_on_focus === void 0 && $$bindings.clear_on_focus && clear_on_focus !== void 0)\n    $$bindings.clear_on_focus(clear_on_focus);\n  if ($$props.select_on_focus === void 0 && $$bindings.select_on_focus && select_on_focus !== void 0)\n    $$bindings.select_on_focus(select_on_focus);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.el === void 0 && $$bindings.el && el !== void 0)\n    $$bindings.el(el);\n  $$result.css.add(css$3);\n  _value = value;\n  return `${edit ? `<input role=\"textbox\" tabindex=\"-1\" class=\"${[\"svelte-z9gpua\", header ? \"header\" : \"\"].join(\" \").trim()}\"${add_attribute(\"this\", el, 0)}${add_attribute(\"value\", _value, 0)}>` : ``} <span tabindex=\"-1\" role=\"button\"${add_attribute(\"style\", styling, 0)} class=\"${[\"svelte-z9gpua\", edit ? \"edit\" : \"\"].join(\" \").trim()}\">${datatype === \"html\" ? `<!-- HTML_TAG_START -->${value}<!-- HTML_TAG_END -->` : `${datatype === \"markdown\" ? `${validate_component(MarkdownCode, \"MarkdownCode\").$$render(\n    $$result,\n    {\n      message: value.toLocaleString(),\n      latex_delimiters,\n      line_breaks,\n      chatbot: false,\n      root\n    },\n    {},\n    {}\n  )}` : `${escape(editable ? value : display_value || value)}`}`} </span>`;\n});\nconst css$2 = {\n  code: \"table.svelte-1xyl3gk.svelte-1xyl3gk{position:relative;overflow-y:scroll;overflow-x:scroll;-webkit-overflow-scrolling:touch;max-height:100vh;box-sizing:border-box;display:block;padding:0;margin:0;color:var(--body-text-color);font-size:var(--input-text-size);line-height:var(--line-md);font-family:var(--font-mono);border-spacing:0;width:100%;scroll-snap-type:x proximity;border-collapse:separate}table.svelte-1xyl3gk .svelte-1xyl3gk:is(thead, tfoot, tbody){display:table;table-layout:fixed;width:100%;box-sizing:border-box}tbody.svelte-1xyl3gk.svelte-1xyl3gk{overflow-x:scroll;overflow-y:hidden}table.svelte-1xyl3gk tbody.svelte-1xyl3gk{padding-top:var(--bw-svt-p-top);padding-bottom:var(--bw-svt-p-bottom)}tbody.svelte-1xyl3gk.svelte-1xyl3gk{position:relative;box-sizing:border-box;border:0px solid currentColor}tbody.svelte-1xyl3gk>tr:last-child{border:none}table.svelte-1xyl3gk td{scroll-snap-align:start}tbody.svelte-1xyl3gk>tr:nth-child(even){background:var(--table-even-background-fill)}thead.svelte-1xyl3gk.svelte-1xyl3gk{position:sticky;top:0;left:0;z-index:var(--layer-1);overflow:hidden}\",\n  map: '{\"version\":3,\"file\":\"VirtualTable.svelte\",\"sources\":[\"VirtualTable.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount, tick } from \\\\\"svelte\\\\\";\\\\nimport { _ } from \\\\\"svelte-i18n\\\\\";\\\\nexport let items = [];\\\\nexport let max_height;\\\\nexport let actual_height;\\\\nexport let table_scrollbar_width;\\\\nexport let start = 0;\\\\nexport let end = 20;\\\\nexport let selected;\\\\nlet height = \\\\\"100%\\\\\";\\\\nlet average_height = 30;\\\\nlet bottom = 0;\\\\nlet contents;\\\\nlet head_height = 0;\\\\nlet foot_height = 0;\\\\nlet height_map = [];\\\\nlet mounted;\\\\nlet rows;\\\\nlet top = 0;\\\\nlet viewport;\\\\nlet viewport_height = 200;\\\\nlet visible = [];\\\\nlet viewport_box;\\\\n$: viewport_height = viewport_box?.height || 200;\\\\nconst is_browser = typeof window !== \\\\\"undefined\\\\\";\\\\nconst raf = is_browser ? window.requestAnimationFrame : (cb) => cb();\\\\n$: mounted && raf(() => refresh_height_map(sortedItems));\\\\nlet content_height = 0;\\\\nasync function refresh_height_map(_items) {\\\\n    if (viewport_height === 0) {\\\\n        return;\\\\n    }\\\\n    const { scrollTop } = viewport;\\\\n    table_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\\\\n    content_height = top - (scrollTop - head_height);\\\\n    let i = start;\\\\n    while (content_height < max_height && i < _items.length) {\\\\n        let row = rows[i - start];\\\\n        if (!row) {\\\\n            end = i + 1;\\\\n            await tick();\\\\n            row = rows[i - start];\\\\n        }\\\\n        let _h = row?.getBoundingClientRect().height;\\\\n        if (!_h) {\\\\n            _h = average_height;\\\\n        }\\\\n        const row_height = height_map[i] = _h;\\\\n        content_height += row_height;\\\\n        i += 1;\\\\n    }\\\\n    end = i;\\\\n    const remaining = _items.length - end;\\\\n    const scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\\\\n    if (scrollbar_height > 0) {\\\\n        content_height += scrollbar_height;\\\\n    }\\\\n    let filtered_height_map = height_map.filter((v) => typeof v === \\\\\"number\\\\\");\\\\n    average_height = filtered_height_map.reduce((a, b) => a + b, 0) / filtered_height_map.length;\\\\n    bottom = remaining * average_height;\\\\n    height_map.length = _items.length;\\\\n    await tick();\\\\n    if (!max_height) {\\\\n        actual_height = content_height + 1;\\\\n    }\\\\n    else if (content_height < max_height) {\\\\n        actual_height = content_height + 2;\\\\n    }\\\\n    else {\\\\n        actual_height = max_height;\\\\n    }\\\\n    await tick();\\\\n}\\\\n$: scroll_and_render(selected);\\\\nasync function scroll_and_render(n) {\\\\n    raf(async () => {\\\\n        if (typeof n !== \\\\\"number\\\\\")\\\\n            return;\\\\n        const direction = typeof n !== \\\\\"number\\\\\" ? false : is_in_view(n);\\\\n        if (direction === true) {\\\\n            return;\\\\n        }\\\\n        if (direction === \\\\\"back\\\\\") {\\\\n            await scroll_to_index(n, { behavior: \\\\\"instant\\\\\" });\\\\n        }\\\\n        if (direction === \\\\\"forwards\\\\\") {\\\\n            await scroll_to_index(n, { behavior: \\\\\"instant\\\\\" }, true);\\\\n        }\\\\n    });\\\\n}\\\\nfunction is_in_view(n) {\\\\n    const current = rows && rows[n - start];\\\\n    if (!current && n < start) {\\\\n        return \\\\\"back\\\\\";\\\\n    }\\\\n    if (!current && n >= end - 1) {\\\\n        return \\\\\"forwards\\\\\";\\\\n    }\\\\n    const { top: viewport_top } = viewport.getBoundingClientRect();\\\\n    const { top: top2, bottom: bottom2 } = current.getBoundingClientRect();\\\\n    if (top2 - viewport_top < 37) {\\\\n        return \\\\\"back\\\\\";\\\\n    }\\\\n    if (bottom2 - viewport_top > viewport_height) {\\\\n        return \\\\\"forwards\\\\\";\\\\n    }\\\\n    return true;\\\\n}\\\\nfunction get_computed_px_amount(elem, property) {\\\\n    if (!elem) {\\\\n        return 0;\\\\n    }\\\\n    const compStyle = getComputedStyle(elem);\\\\n    let x = parseInt(compStyle.getPropertyValue(property));\\\\n    return x;\\\\n}\\\\nasync function handle_scroll(e) {\\\\n    const scroll_top = viewport.scrollTop;\\\\n    rows = contents.children;\\\\n    const is_start_overflow = sortedItems.length < start;\\\\n    const row_top_border = get_computed_px_amount(rows[1], \\\\\"border-top-width\\\\\");\\\\n    const actual_border_collapsed_width = 0;\\\\n    if (is_start_overflow) {\\\\n        await scroll_to_index(sortedItems.length - 1, { behavior: \\\\\"auto\\\\\" });\\\\n    }\\\\n    let new_start = 0;\\\\n    for (let v = 0; v < rows.length; v += 1) {\\\\n        height_map[start + v] = rows[v].getBoundingClientRect().height;\\\\n    }\\\\n    let i = 0;\\\\n    let y = head_height + row_top_border / 2;\\\\n    let row_heights = [];\\\\n    while (i < sortedItems.length) {\\\\n        const row_height = height_map[i] || average_height;\\\\n        row_heights[i] = row_height;\\\\n        if (y + row_height + actual_border_collapsed_width > scroll_top) {\\\\n            new_start = i;\\\\n            top = y - (head_height + row_top_border / 2);\\\\n            break;\\\\n        }\\\\n        y += row_height;\\\\n        i += 1;\\\\n    }\\\\n    new_start = Math.max(0, new_start);\\\\n    while (i < sortedItems.length) {\\\\n        const row_height = height_map[i] || average_height;\\\\n        y += row_height;\\\\n        i += 1;\\\\n        if (y > scroll_top + viewport_height) {\\\\n            break;\\\\n        }\\\\n    }\\\\n    start = new_start;\\\\n    end = i;\\\\n    const remaining = sortedItems.length - end;\\\\n    if (end === 0) {\\\\n        end = 10;\\\\n    }\\\\n    average_height = (y - head_height) / end;\\\\n    let remaining_height = remaining * average_height;\\\\n    while (i < sortedItems.length) {\\\\n        i += 1;\\\\n        height_map[i] = average_height;\\\\n    }\\\\n    bottom = remaining_height;\\\\n    if (!isFinite(bottom)) {\\\\n        bottom = 2e5;\\\\n    }\\\\n}\\\\nexport async function scroll_to_index(index, opts, align_end = false) {\\\\n    await tick();\\\\n    const _itemHeight = average_height;\\\\n    let distance = index * _itemHeight;\\\\n    if (align_end) {\\\\n        distance = distance - viewport_height + _itemHeight + head_height;\\\\n    }\\\\n    const scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\\\\n    if (scrollbar_height > 0) {\\\\n        distance += scrollbar_height;\\\\n    }\\\\n    const _opts = {\\\\n        top: distance,\\\\n        behavior: \\\\\"smooth\\\\\",\\\\n        ...opts\\\\n    };\\\\n    viewport.scrollTo(_opts);\\\\n}\\\\n$: sortedItems = items;\\\\n$: visible = is_browser ? sortedItems.slice(start, end).map((data, i) => {\\\\n    return { index: i + start, data };\\\\n}) : sortedItems.slice(0, max_height / sortedItems.length * average_height + 1).map((data, i) => {\\\\n    return { index: i + start, data };\\\\n});\\\\nonMount(() => {\\\\n    rows = contents.children;\\\\n    mounted = true;\\\\n    refresh_height_map(items);\\\\n});\\\\n<\\/script>\\\\n\\\\n<svelte-virtual-table-viewport>\\\\n\\\\t<table\\\\n\\\\t\\\\tclass=\\\\\"table\\\\\"\\\\n\\\\t\\\\tbind:this={viewport}\\\\n\\\\t\\\\tbind:contentRect={viewport_box}\\\\n\\\\t\\\\ton:scroll={handle_scroll}\\\\n\\\\t\\\\tstyle=\\\\\"height: {height}; --bw-svt-p-top: {top}px; --bw-svt-p-bottom: {bottom}px; --bw-svt-head-height: {head_height}px; --bw-svt-foot-height: {foot_height}px; --bw-svt-avg-row-height: {average_height}px\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<thead class=\\\\\"thead\\\\\" bind:offsetHeight={head_height}>\\\\n\\\\t\\\\t\\\\t<slot name=\\\\\"thead\\\\\" />\\\\n\\\\t\\\\t</thead>\\\\n\\\\t\\\\t<tbody bind:this={contents} class=\\\\\"tbody\\\\\">\\\\n\\\\t\\\\t\\\\t{#if visible.length && visible[0].data.length}\\\\n\\\\t\\\\t\\\\t\\\\t{#each visible as item (item.data[0].id)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<slot name=\\\\\"tbody\\\\\" item={item.data} index={item.index}>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tMissing Table Row\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</slot>\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</tbody>\\\\n\\\\t\\\\t<tfoot class=\\\\\"tfoot\\\\\" bind:offsetHeight={foot_height}>\\\\n\\\\t\\\\t\\\\t<slot name=\\\\\"tfoot\\\\\" />\\\\n\\\\t\\\\t</tfoot>\\\\n\\\\t</table>\\\\n</svelte-virtual-table-viewport>\\\\n\\\\n<style type=\\\\\"text/css\\\\\">\\\\n\\\\ttable {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\toverflow-y: scroll;\\\\n\\\\t\\\\toverflow-x: scroll;\\\\n\\\\t\\\\t-webkit-overflow-scrolling: touch;\\\\n\\\\t\\\\tmax-height: 100vh;\\\\n\\\\t\\\\tbox-sizing: border-box;\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--input-text-size);\\\\n\\\\t\\\\tline-height: var(--line-md);\\\\n\\\\t\\\\tfont-family: var(--font-mono);\\\\n\\\\t\\\\tborder-spacing: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tscroll-snap-type: x proximity;\\\\n\\\\t\\\\tborder-collapse: separate;\\\\n\\\\t}\\\\n\\\\ttable :is(thead, tfoot, tbody) {\\\\n\\\\t\\\\tdisplay: table;\\\\n\\\\t\\\\ttable-layout: fixed;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tbox-sizing: border-box;\\\\n\\\\t}\\\\n\\\\n\\\\ttbody {\\\\n\\\\t\\\\toverflow-x: scroll;\\\\n\\\\t\\\\toverflow-y: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\ttable tbody {\\\\n\\\\t\\\\tpadding-top: var(--bw-svt-p-top);\\\\n\\\\t\\\\tpadding-bottom: var(--bw-svt-p-bottom);\\\\n\\\\t}\\\\n\\\\ttbody {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tbox-sizing: border-box;\\\\n\\\\t\\\\tborder: 0px solid currentColor;\\\\n\\\\t}\\\\n\\\\n\\\\ttbody > :global(tr:last-child) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}\\\\n\\\\n\\\\ttable :global(td) {\\\\n\\\\t\\\\tscroll-snap-align: start;\\\\n\\\\t}\\\\n\\\\n\\\\ttbody > :global(tr:nth-child(even)) {\\\\n\\\\t\\\\tbackground: var(--table-even-background-fill);\\\\n\\\\t}\\\\n\\\\n\\\\tthead {\\\\n\\\\t\\\\tposition: sticky;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAmOC,mCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,MAAM,CAClB,UAAU,CAAE,MAAM,CAClB,0BAA0B,CAAE,KAAK,CACjC,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,UAAU,CACtB,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,CAAC,CACT,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,WAAW,CAAE,IAAI,WAAW,CAAC,CAC7B,cAAc,CAAE,CAAC,CACjB,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,CAAC,CAAC,SAAS,CAC7B,eAAe,CAAE,QAClB,CACA,oBAAK,gBAAC,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,CAAE,CAC9B,OAAO,CAAE,KAAK,CACd,YAAY,CAAE,KAAK,CACnB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,UACb,CAEA,mCAAM,CACL,UAAU,CAAE,MAAM,CAClB,UAAU,CAAE,MACb,CAEA,oBAAK,CAAC,oBAAM,CACX,WAAW,CAAE,IAAI,cAAc,CAAC,CAChC,cAAc,CAAE,IAAI,iBAAiB,CACtC,CACA,mCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,UAAU,CACtB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,YACnB,CAEA,oBAAK,CAAW,aAAe,CAC9B,MAAM,CAAE,IACT,CAEA,oBAAK,CAAS,EAAI,CACjB,iBAAiB,CAAE,KACpB,CAEA,oBAAK,CAAW,kBAAoB,CACnC,UAAU,CAAE,IAAI,4BAA4B,CAC7C,CAEA,mCAAM,CACL,QAAQ,CAAE,MAAM,CAChB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,QAAQ,CAAE,MACX\"}'\n};\nlet height = \"100%\";\nconst VirtualTable = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let sortedItems;\n  let { items = [] } = $$props;\n  let { max_height } = $$props;\n  let { actual_height } = $$props;\n  let { table_scrollbar_width } = $$props;\n  let { start = 0 } = $$props;\n  let { end = 20 } = $$props;\n  let { selected } = $$props;\n  let average_height = 30;\n  let bottom = 0;\n  let contents;\n  let head_height = 0;\n  let foot_height = 0;\n  let height_map = [];\n  let mounted;\n  let rows;\n  let top = 0;\n  let viewport;\n  let viewport_height = 200;\n  let visible = [];\n  const is_browser = typeof window !== \"undefined\";\n  const raf = is_browser ? window.requestAnimationFrame : (cb) => cb();\n  let content_height = 0;\n  async function refresh_height_map(_items) {\n    if (viewport_height === 0) {\n      return;\n    }\n    const { scrollTop } = viewport;\n    table_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\n    content_height = top - (scrollTop - head_height);\n    let i = start;\n    while (content_height < max_height && i < _items.length) {\n      let row = rows[i - start];\n      if (!row) {\n        end = i + 1;\n        await tick();\n        row = rows[i - start];\n      }\n      let _h = row?.getBoundingClientRect().height;\n      if (!_h) {\n        _h = average_height;\n      }\n      const row_height = height_map[i] = _h;\n      content_height += row_height;\n      i += 1;\n    }\n    end = i;\n    const remaining = _items.length - end;\n    viewport.offsetHeight - viewport.clientHeight;\n    let filtered_height_map = height_map.filter((v) => typeof v === \"number\");\n    average_height = filtered_height_map.reduce((a, b) => a + b, 0) / filtered_height_map.length;\n    bottom = remaining * average_height;\n    height_map.length = _items.length;\n    await tick();\n    if (!max_height) {\n      actual_height = content_height + 1;\n    } else if (content_height < max_height) {\n      actual_height = content_height + 2;\n    } else {\n      actual_height = max_height;\n    }\n    await tick();\n  }\n  async function scroll_and_render(n) {\n    raf(async () => {\n      if (typeof n !== \"number\")\n        return;\n      const direction = typeof n !== \"number\" ? false : is_in_view(n);\n      if (direction === true) {\n        return;\n      }\n      if (direction === \"back\") {\n        await scroll_to_index(n, { behavior: \"instant\" });\n      }\n      if (direction === \"forwards\") {\n        await scroll_to_index(n, { behavior: \"instant\" }, true);\n      }\n    });\n  }\n  function is_in_view(n) {\n    const current = rows && rows[n - start];\n    if (!current && n < start) {\n      return \"back\";\n    }\n    if (!current && n >= end - 1) {\n      return \"forwards\";\n    }\n    const { top: viewport_top } = viewport.getBoundingClientRect();\n    const { top: top2, bottom: bottom2 } = current.getBoundingClientRect();\n    if (top2 - viewport_top < 37) {\n      return \"back\";\n    }\n    if (bottom2 - viewport_top > viewport_height) {\n      return \"forwards\";\n    }\n    return true;\n  }\n  async function scroll_to_index(index, opts, align_end = false) {\n    await tick();\n    const _itemHeight = average_height;\n    let distance = index * _itemHeight;\n    if (align_end) {\n      distance = distance - viewport_height + _itemHeight + head_height;\n    }\n    viewport.offsetHeight - viewport.clientHeight;\n    const _opts = {\n      top: distance,\n      behavior: \"smooth\",\n      ...opts\n    };\n    viewport.scrollTo(_opts);\n  }\n  onMount(() => {\n    rows = contents.children;\n    mounted = true;\n    refresh_height_map(items);\n  });\n  if ($$props.items === void 0 && $$bindings.items && items !== void 0)\n    $$bindings.items(items);\n  if ($$props.max_height === void 0 && $$bindings.max_height && max_height !== void 0)\n    $$bindings.max_height(max_height);\n  if ($$props.actual_height === void 0 && $$bindings.actual_height && actual_height !== void 0)\n    $$bindings.actual_height(actual_height);\n  if ($$props.table_scrollbar_width === void 0 && $$bindings.table_scrollbar_width && table_scrollbar_width !== void 0)\n    $$bindings.table_scrollbar_width(table_scrollbar_width);\n  if ($$props.start === void 0 && $$bindings.start && start !== void 0)\n    $$bindings.start(start);\n  if ($$props.end === void 0 && $$bindings.end && end !== void 0)\n    $$bindings.end(end);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  if ($$props.scroll_to_index === void 0 && $$bindings.scroll_to_index && scroll_to_index !== void 0)\n    $$bindings.scroll_to_index(scroll_to_index);\n  $$result.css.add(css$2);\n  viewport_height = 200;\n  sortedItems = items;\n  mounted && raf(() => refresh_height_map(sortedItems));\n  {\n    scroll_and_render(selected);\n  }\n  visible = is_browser ? sortedItems.slice(start, end).map((data, i) => {\n    return { index: i + start, data };\n  }) : sortedItems.slice(0, max_height / sortedItems.length * average_height + 1).map((data, i) => {\n    return { index: i + start, data };\n  });\n  return `<svelte-virtual-table-viewport><table class=\"table svelte-1xyl3gk\" style=\"${\"height: \" + escape(height, true) + \"; --bw-svt-p-top: \" + escape(top, true) + \"px; --bw-svt-p-bottom: \" + escape(bottom, true) + \"px; --bw-svt-head-height: \" + escape(head_height, true) + \"px; --bw-svt-foot-height: \" + escape(foot_height, true) + \"px; --bw-svt-avg-row-height: \" + escape(average_height, true) + \"px\"}\"${add_attribute(\"this\", viewport, 0)}><thead class=\"thead svelte-1xyl3gk\">${slots.thead ? slots.thead({}) : ``}</thead> <tbody class=\"tbody svelte-1xyl3gk\"${add_attribute(\"this\", contents, 0)}>${visible.length && visible[0].data.length ? `${each(visible, (item) => {\n    return `${slots.tbody ? slots.tbody({ item: item.data, index: item.index }) : `\n\t\t\t\t\t\tMissing Table Row\n\t\t\t\t\t`}`;\n  })}` : ``}</tbody> <tfoot class=\"tfoot svelte-1xyl3gk\">${slots.tfoot ? slots.tfoot({}) : ``}</tfoot></table> </svelte-virtual-table-viewport>`;\n});\nconst Arrow = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { transform } = $$props;\n  if ($$props.transform === void 0 && $$bindings.transform && transform !== void 0)\n    $$bindings.transform(transform);\n  return `<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\"><path d=\"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z\"${add_attribute(\"transform\", transform, 0)}></path></svg>`;\n});\nconst css$1 = {\n  code: \".cell-menu.svelte-1ygaf0d.svelte-1ygaf0d{position:fixed;z-index:var(--layer-2);background:var(--background-fill-primary);border:1px solid var(--border-color-primary);border-radius:var(--radius-sm);padding:var(--size-1);display:flex;flex-direction:column;gap:var(--size-1);box-shadow:var(--shadow-drop-lg);min-width:150px}.cell-menu.svelte-1ygaf0d button.svelte-1ygaf0d{background:none;border:none;cursor:pointer;text-align:left;padding:var(--size-1) var(--size-2);border-radius:var(--radius-sm);color:var(--body-text-color);font-size:var(--text-sm);transition:background-color 0.2s,\\n\t\t\tcolor 0.2s;display:flex;align-items:center;gap:var(--size-2)}.cell-menu.svelte-1ygaf0d button.svelte-1ygaf0d:hover{background-color:var(--background-fill-secondary)}.cell-menu.svelte-1ygaf0d button.svelte-1ygaf0d svg{fill:currentColor;transition:fill 0.2s}.cell-menu.svelte-1ygaf0d button.svelte-1ygaf0d:hover svg{fill:var(--color-accent)}\",\n  map: '{\"version\":3,\"file\":\"CellMenu.svelte\",\"sources\":[\"CellMenu.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport Arrow from \\\\\"./Arrow.svelte\\\\\";\\\\nexport let x;\\\\nexport let y;\\\\nexport let on_add_row_above;\\\\nexport let on_add_row_below;\\\\nexport let on_add_column_left;\\\\nexport let on_add_column_right;\\\\nexport let row;\\\\nexport let col_count;\\\\nexport let row_count;\\\\nexport let i18n;\\\\nlet menu_element;\\\\n$: is_header = row === -1;\\\\n$: can_add_rows = row_count[1] === \\\\\"dynamic\\\\\";\\\\n$: can_add_columns = col_count[1] === \\\\\"dynamic\\\\\";\\\\nonMount(() => {\\\\n    position_menu();\\\\n});\\\\nfunction position_menu() {\\\\n    if (!menu_element)\\\\n        return;\\\\n    const viewport_width = window.innerWidth;\\\\n    const viewport_height = window.innerHeight;\\\\n    const menu_rect = menu_element.getBoundingClientRect();\\\\n    let new_x = x - 30;\\\\n    let new_y = y - 20;\\\\n    if (new_x + menu_rect.width > viewport_width) {\\\\n        new_x = x - menu_rect.width + 10;\\\\n    }\\\\n    if (new_y + menu_rect.height > viewport_height) {\\\\n        new_y = y - menu_rect.height + 10;\\\\n    }\\\\n    menu_element.style.left = `${new_x}px`;\\\\n    menu_element.style.top = `${new_y}px`;\\\\n}\\\\n<\\/script>\\\\n\\\\n<div bind:this={menu_element} class=\\\\\"cell-menu\\\\\">\\\\n\\\\t{#if !is_header && can_add_rows}\\\\n\\\\t\\\\t<button on:click={() => on_add_row_above()}>\\\\n\\\\t\\\\t\\\\t<Arrow transform=\\\\\"rotate(-90 12 12)\\\\\" />\\\\n\\\\t\\\\t\\\\t{i18n(\\\\\"dataframe.add_row_above\\\\\")}\\\\n\\\\t\\\\t</button>\\\\n\\\\t\\\\t<button on:click={() => on_add_row_below()}>\\\\n\\\\t\\\\t\\\\t<Arrow transform=\\\\\"rotate(90 12 12)\\\\\" />\\\\n\\\\t\\\\t\\\\t{i18n(\\\\\"dataframe.add_row_below\\\\\")}\\\\n\\\\t\\\\t</button>\\\\n\\\\t{/if}\\\\n\\\\t{#if can_add_columns}\\\\n\\\\t\\\\t<button on:click={() => on_add_column_left()}>\\\\n\\\\t\\\\t\\\\t<Arrow transform=\\\\\"rotate(180 12 12)\\\\\" />\\\\n\\\\t\\\\t\\\\t{i18n(\\\\\"dataframe.add_column_left\\\\\")}\\\\n\\\\t\\\\t</button>\\\\n\\\\t\\\\t<button on:click={() => on_add_column_right()}>\\\\n\\\\t\\\\t\\\\t<Arrow transform=\\\\\"rotate(0 12 12)\\\\\" />\\\\n\\\\t\\\\t\\\\t{i18n(\\\\\"dataframe.add_column_right\\\\\")}\\\\n\\\\t\\\\t</button>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.cell-menu {\\\\n\\\\t\\\\tposition: fixed;\\\\n\\\\t\\\\tz-index: var(--layer-2);\\\\n\\\\t\\\\tbackground: var(--background-fill-primary);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tpadding: var(--size-1);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--size-1);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop-lg);\\\\n\\\\t\\\\tmin-width: 150px;\\\\n\\\\t}\\\\n\\\\n\\\\t.cell-menu button {\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\ttransition:\\\\n\\\\t\\\\t\\\\tbackground-color 0.2s,\\\\n\\\\t\\\\t\\\\tcolor 0.2s;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgap: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\t.cell-menu button:hover {\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\t.cell-menu button :global(svg) {\\\\n\\\\t\\\\tfill: currentColor;\\\\n\\\\t\\\\ttransition: fill 0.2s;\\\\n\\\\t}\\\\n\\\\n\\\\t.cell-menu button:hover :global(svg) {\\\\n\\\\t\\\\tfill: var(--color-accent);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8DC,wCAAW,CACV,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,UAAU,CAAE,IAAI,gBAAgB,CAAC,CACjC,SAAS,CAAE,KACZ,CAEA,yBAAU,CAAC,qBAAO,CACjB,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CACT,gBAAgB,CAAC,IAAI,CAAC;AACzB,GAAG,KAAK,CAAC,IAAI,CACX,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,QAAQ,CAClB,CAEA,yBAAU,CAAC,qBAAM,MAAO,CACvB,gBAAgB,CAAE,IAAI,2BAA2B,CAClD,CAEA,yBAAU,CAAC,qBAAM,CAAS,GAAK,CAC9B,IAAI,CAAE,YAAY,CAClB,UAAU,CAAE,IAAI,CAAC,IAClB,CAEA,yBAAU,CAAC,qBAAM,MAAM,CAAS,GAAK,CACpC,IAAI,CAAE,IAAI,cAAc,CACzB\"}'\n};\nconst CellMenu = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let is_header;\n  let can_add_rows;\n  let can_add_columns;\n  let { x } = $$props;\n  let { y } = $$props;\n  let { on_add_row_above } = $$props;\n  let { on_add_row_below } = $$props;\n  let { on_add_column_left } = $$props;\n  let { on_add_column_right } = $$props;\n  let { row } = $$props;\n  let { col_count } = $$props;\n  let { row_count } = $$props;\n  let { i18n } = $$props;\n  let menu_element;\n  onMount(() => {\n  });\n  if ($$props.x === void 0 && $$bindings.x && x !== void 0)\n    $$bindings.x(x);\n  if ($$props.y === void 0 && $$bindings.y && y !== void 0)\n    $$bindings.y(y);\n  if ($$props.on_add_row_above === void 0 && $$bindings.on_add_row_above && on_add_row_above !== void 0)\n    $$bindings.on_add_row_above(on_add_row_above);\n  if ($$props.on_add_row_below === void 0 && $$bindings.on_add_row_below && on_add_row_below !== void 0)\n    $$bindings.on_add_row_below(on_add_row_below);\n  if ($$props.on_add_column_left === void 0 && $$bindings.on_add_column_left && on_add_column_left !== void 0)\n    $$bindings.on_add_column_left(on_add_column_left);\n  if ($$props.on_add_column_right === void 0 && $$bindings.on_add_column_right && on_add_column_right !== void 0)\n    $$bindings.on_add_column_right(on_add_column_right);\n  if ($$props.row === void 0 && $$bindings.row && row !== void 0)\n    $$bindings.row(row);\n  if ($$props.col_count === void 0 && $$bindings.col_count && col_count !== void 0)\n    $$bindings.col_count(col_count);\n  if ($$props.row_count === void 0 && $$bindings.row_count && row_count !== void 0)\n    $$bindings.row_count(row_count);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  $$result.css.add(css$1);\n  is_header = row === -1;\n  can_add_rows = row_count[1] === \"dynamic\";\n  can_add_columns = col_count[1] === \"dynamic\";\n  return `<div class=\"cell-menu svelte-1ygaf0d\"${add_attribute(\"this\", menu_element, 0)}>${!is_header && can_add_rows ? `<button class=\"svelte-1ygaf0d\">${validate_component(Arrow, \"Arrow\").$$render($$result, { transform: \"rotate(-90 12 12)\" }, {}, {})} ${escape(i18n(\"dataframe.add_row_above\"))}</button> <button class=\"svelte-1ygaf0d\">${validate_component(Arrow, \"Arrow\").$$render($$result, { transform: \"rotate(90 12 12)\" }, {}, {})} ${escape(i18n(\"dataframe.add_row_below\"))}</button>` : ``} ${can_add_columns ? `<button class=\"svelte-1ygaf0d\">${validate_component(Arrow, \"Arrow\").$$render($$result, { transform: \"rotate(180 12 12)\" }, {}, {})} ${escape(i18n(\"dataframe.add_column_left\"))}</button> <button class=\"svelte-1ygaf0d\">${validate_component(Arrow, \"Arrow\").$$render($$result, { transform: \"rotate(0 12 12)\" }, {}, {})} ${escape(i18n(\"dataframe.add_column_right\"))}</button>` : ``} </div>`;\n});\nconst css = {\n  code: \".button-wrap.svelte-1o4y3py:hover svg.svelte-1o4y3py.svelte-1o4y3py{color:var(--color-accent)}.button-wrap.svelte-1o4y3py svg.svelte-1o4y3py.svelte-1o4y3py{margin-right:var(--size-1);margin-left:-5px}.label.svelte-1o4y3py p.svelte-1o4y3py.svelte-1o4y3py{position:relative;z-index:var(--layer-4);margin-bottom:var(--size-2);color:var(--block-label-text-color);font-size:var(--block-label-text-size)}.table-wrap.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{position:relative;transition:150ms;border:1px solid var(--border-color-primary);border-radius:var(--table-radius);overflow:hidden}.table-wrap.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py:focus-within{outline:none;background-color:none}.dragging.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{border-color:var(--color-accent)}.no-wrap.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{white-space:nowrap}table.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{position:absolute;opacity:0;transition:150ms;width:var(--size-full);table-layout:auto;color:var(--body-text-color);font-size:var(--input-text-size);line-height:var(--line-md);font-family:var(--font-mono);border-spacing:0}div.svelte-1o4y3py:not(.no-wrap) td.svelte-1o4y3py.svelte-1o4y3py{overflow-wrap:anywhere}div.no-wrap.svelte-1o4y3py td.svelte-1o4y3py.svelte-1o4y3py{overflow-x:hidden}table.fixed-layout.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{table-layout:fixed}thead.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{position:sticky;top:0;left:0;z-index:var(--layer-1);box-shadow:var(--shadow-drop)}tr.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{border-bottom:1px solid var(--border-color-primary);text-align:left}tr.svelte-1o4y3py>.svelte-1o4y3py+.svelte-1o4y3py{border-right-width:0px;border-left-width:1px;border-style:solid;border-color:var(--border-color-primary)}th.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py,td.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{--ring-color:transparent;position:relative;outline:none;box-shadow:inset 0 0 0 1px var(--ring-color);padding:0}th.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py:first-child{border-top-left-radius:var(--table-radius)}th.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py:last-child{border-top-right-radius:var(--table-radius)}th.focus.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py,td.focus.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{--ring-color:var(--color-accent)}tr.svelte-1o4y3py:last-child td.svelte-1o4y3py.svelte-1o4y3py:first-child{border-bottom-left-radius:var(--table-radius)}tr.svelte-1o4y3py:last-child td.svelte-1o4y3py.svelte-1o4y3py:last-child{border-bottom-right-radius:var(--table-radius)}tr.svelte-1o4y3py th.svelte-1o4y3py.svelte-1o4y3py{background:var(--table-even-background-fill)}th.svelte-1o4y3py svg.svelte-1o4y3py.svelte-1o4y3py{fill:currentColor;font-size:10px}.sort-button.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{display:flex;flex:none;justify-content:center;align-items:center;transition:150ms;cursor:pointer;padding:var(--size-2);color:var(--body-text-color-subdued);line-height:var(--text-sm)}.sort-button.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py:hover{color:var(--body-text-color)}.des.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{transform:scaleY(-1)}.sort-button.sorted.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{color:var(--color-accent)}.editing.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{background:var(--table-editing)}.cell-wrap.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{display:flex;align-items:center;outline:none;height:var(--size-full);min-height:var(--size-9);overflow:hidden}.header-content.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{display:flex;align-items:center;overflow:hidden;flex-grow:1;min-width:0}.controls-wrap.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{display:flex;justify-content:flex-end;padding-top:var(--size-2)}.row_odd.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{background:var(--table-odd-background-fill)}.row_odd.focus.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{background:var(--background-fill-primary)}table.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{border-collapse:separate}.cell-menu-button.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py{flex-shrink:0;display:none;background-color:var(--block-background-fill);border:1px solid var(--border-color-primary);border-radius:var(--block-radius);width:var(--size-5);height:var(--size-5);min-width:var(--size-5);padding:0;margin-right:var(--spacing-sm);z-index:var(--layer-2)}.cell-menu-button.svelte-1o4y3py.svelte-1o4y3py.svelte-1o4y3py:hover{background-color:var(--color-bg-hover)}td.focus.svelte-1o4y3py .cell-menu-button.svelte-1o4y3py.svelte-1o4y3py{display:flex;align-items:center;justify-content:center}th.svelte-1o4y3py .header-content.svelte-1o4y3py.svelte-1o4y3py{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}\",\n  map: '{\"version\":3,\"file\":\"Table.svelte\",\"sources\":[\"Table.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, tick, onMount } from \\\\\"svelte\\\\\";\\\\nimport { dsvFormat } from \\\\\"d3-dsv\\\\\";\\\\nimport { dequal } from \\\\\"dequal/lite\\\\\";\\\\nimport { copy } from \\\\\"@gradio/utils\\\\\";\\\\nimport { Upload } from \\\\\"@gradio/upload\\\\\";\\\\nimport EditableCell from \\\\\"./EditableCell.svelte\\\\\";\\\\nimport {} from \\\\\"@gradio/client\\\\\";\\\\nimport VirtualTable from \\\\\"./VirtualTable.svelte\\\\\";\\\\nimport CellMenu from \\\\\"./CellMenu.svelte\\\\\";\\\\nexport let datatype;\\\\nexport let label = null;\\\\nexport let show_label = true;\\\\nexport let headers = [];\\\\nexport let values = [];\\\\nexport let col_count;\\\\nexport let row_count;\\\\nexport let latex_delimiters;\\\\nexport let editable = true;\\\\nexport let wrap = false;\\\\nexport let root;\\\\nexport let i18n;\\\\nexport let max_height = 500;\\\\nexport let line_breaks = true;\\\\nexport let column_widths = [];\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nlet selected = false;\\\\nexport let display_value = null;\\\\nexport let styling = null;\\\\nlet t_rect;\\\\nconst dispatch = createEventDispatcher();\\\\nlet editing = false;\\\\nconst get_data_at = (row, col) => data?.[row]?.[col]?.value;\\\\nlet last_selected = null;\\\\n$: {\\\\n    if (selected !== false && !dequal(selected, last_selected)) {\\\\n        const [row, col] = selected;\\\\n        if (!isNaN(row) && !isNaN(col) && data[row]) {\\\\n            dispatch(\\\\\"select\\\\\", {\\\\n                index: [row, col],\\\\n                value: get_data_at(row, col),\\\\n                row_value: data[row].map((d) => d.value)\\\\n            });\\\\n            last_selected = selected;\\\\n        }\\\\n    }\\\\n}\\\\nlet els = {};\\\\nlet data_binding = {};\\\\nfunction make_id() {\\\\n    return Math.random().toString(36).substring(2, 15);\\\\n}\\\\nfunction make_headers(_head) {\\\\n    let _h = _head || [];\\\\n    if (col_count[1] === \\\\\"fixed\\\\\" && _h.length < col_count[0]) {\\\\n        const fill = Array(col_count[0] - _h.length).fill(\\\\\"\\\\\").map((_, i) => `${i + _h.length}`);\\\\n        _h = _h.concat(fill);\\\\n    }\\\\n    if (!_h || _h.length === 0) {\\\\n        return Array(col_count[0]).fill(0).map((_, i) => {\\\\n            const _id = make_id();\\\\n            els[_id] = { cell: null, input: null };\\\\n            return { id: _id, value: JSON.stringify(i + 1) };\\\\n        });\\\\n    }\\\\n    return _h.map((h, i) => {\\\\n        const _id = make_id();\\\\n        els[_id] = { cell: null, input: null };\\\\n        return { id: _id, value: h ?? \\\\\"\\\\\" };\\\\n    });\\\\n}\\\\nfunction process_data(_values) {\\\\n    const data_row_length = _values.length;\\\\n    return Array(row_count[1] === \\\\\"fixed\\\\\" ? row_count[0] : data_row_length < row_count[0] ? row_count[0] : data_row_length).fill(0).map((_, i) => Array(col_count[1] === \\\\\"fixed\\\\\" ? col_count[0] : data_row_length > 0 ? _values[0].length : headers.length).fill(0).map((_2, j) => {\\\\n        const id = make_id();\\\\n        els[id] = els[id] || { input: null, cell: null };\\\\n        const obj = { value: _values?.[i]?.[j] ?? \\\\\"\\\\\", id };\\\\n        data_binding[id] = obj;\\\\n        return obj;\\\\n    }));\\\\n}\\\\nlet _headers = make_headers(headers);\\\\nlet old_headers;\\\\n$: {\\\\n    if (!dequal(headers, old_headers)) {\\\\n        trigger_headers();\\\\n    }\\\\n}\\\\nfunction trigger_headers() {\\\\n    _headers = make_headers(headers);\\\\n    old_headers = headers.slice();\\\\n    trigger_change();\\\\n}\\\\n$: if (!dequal(values, old_val)) {\\\\n    data = process_data(values);\\\\n    old_val = values;\\\\n}\\\\nlet data = [[]];\\\\nlet old_val = void 0;\\\\nasync function trigger_change() {\\\\n    dispatch(\\\\\"change\\\\\", {\\\\n        data: data.map((r) => r.map(({ value }) => value)),\\\\n        headers: _headers.map((h) => h.value),\\\\n        metadata: editable ? null : { display_value, styling }\\\\n    });\\\\n}\\\\nfunction get_sort_status(name, _sort, direction) {\\\\n    if (!_sort)\\\\n        return \\\\\"none\\\\\";\\\\n    if (headers[_sort] === name) {\\\\n        if (direction === \\\\\"asc\\\\\")\\\\n            return \\\\\"ascending\\\\\";\\\\n        if (direction === \\\\\"des\\\\\")\\\\n            return \\\\\"descending\\\\\";\\\\n    }\\\\n    return \\\\\"none\\\\\";\\\\n}\\\\nfunction get_current_indices(id) {\\\\n    return data.reduce((acc, arr, i) => {\\\\n        const j = arr.reduce((_acc, _data, k) => id === _data.id ? k : _acc, -1);\\\\n        return j === -1 ? acc : [i, j];\\\\n    }, [-1, -1]);\\\\n}\\\\nasync function start_edit(i, j) {\\\\n    if (!editable || dequal(editing, [i, j]))\\\\n        return;\\\\n    editing = [i, j];\\\\n}\\\\nfunction move_cursor(key, current_coords) {\\\\n    const dir = {\\\\n        ArrowRight: [0, 1],\\\\n        ArrowLeft: [0, -1],\\\\n        ArrowDown: [1, 0],\\\\n        ArrowUp: [-1, 0]\\\\n    }[key];\\\\n    const i = current_coords[0] + dir[0];\\\\n    const j = current_coords[1] + dir[1];\\\\n    if (i < 0 && j <= 0) {\\\\n        selected_header = j;\\\\n        selected = false;\\\\n    }\\\\n    else {\\\\n        const is_data = data[i]?.[j];\\\\n        selected = is_data ? [i, j] : selected;\\\\n    }\\\\n}\\\\nlet clear_on_focus = false;\\\\nasync function handle_keydown(event) {\\\\n    if (selected_header !== false && header_edit === false) {\\\\n        switch (event.key) {\\\\n            case \\\\\"ArrowDown\\\\\":\\\\n                selected = [0, selected_header];\\\\n                selected_header = false;\\\\n                return;\\\\n            case \\\\\"ArrowLeft\\\\\":\\\\n                selected_header = selected_header > 0 ? selected_header - 1 : selected_header;\\\\n                return;\\\\n            case \\\\\"ArrowRight\\\\\":\\\\n                selected_header = selected_header < _headers.length - 1 ? selected_header + 1 : selected_header;\\\\n                return;\\\\n            case \\\\\"Escape\\\\\":\\\\n                event.preventDefault();\\\\n                selected_header = false;\\\\n                break;\\\\n            case \\\\\"Enter\\\\\":\\\\n                event.preventDefault();\\\\n                break;\\\\n        }\\\\n    }\\\\n    if (!selected) {\\\\n        return;\\\\n    }\\\\n    const [i, j] = selected;\\\\n    switch (event.key) {\\\\n        case \\\\\"ArrowRight\\\\\":\\\\n        case \\\\\"ArrowLeft\\\\\":\\\\n        case \\\\\"ArrowDown\\\\\":\\\\n        case \\\\\"ArrowUp\\\\\":\\\\n            if (editing)\\\\n                break;\\\\n            event.preventDefault();\\\\n            move_cursor(event.key, [i, j]);\\\\n            break;\\\\n        case \\\\\"Escape\\\\\":\\\\n            if (!editable)\\\\n                break;\\\\n            event.preventDefault();\\\\n            editing = false;\\\\n            break;\\\\n        case \\\\\"Enter\\\\\":\\\\n            if (!editable)\\\\n                break;\\\\n            event.preventDefault();\\\\n            if (event.shiftKey) {\\\\n                add_row(i);\\\\n                await tick();\\\\n                selected = [i + 1, j];\\\\n            }\\\\n            else {\\\\n                if (dequal(editing, [i, j])) {\\\\n                    editing = false;\\\\n                    await tick();\\\\n                    selected = [i, j];\\\\n                }\\\\n                else {\\\\n                    editing = [i, j];\\\\n                }\\\\n            }\\\\n            break;\\\\n        case \\\\\"Backspace\\\\\":\\\\n            if (!editable)\\\\n                break;\\\\n            if (!editing) {\\\\n                event.preventDefault();\\\\n                data[i][j].value = \\\\\"\\\\\";\\\\n            }\\\\n            break;\\\\n        case \\\\\"Delete\\\\\":\\\\n            if (!editable)\\\\n                break;\\\\n            if (!editing) {\\\\n                event.preventDefault();\\\\n                data[i][j].value = \\\\\"\\\\\";\\\\n            }\\\\n            break;\\\\n        case \\\\\"Tab\\\\\":\\\\n            let direction = event.shiftKey ? -1 : 1;\\\\n            let is_data_x = data[i][j + direction];\\\\n            let is_data_y = data?.[i + direction]?.[direction > 0 ? 0 : _headers.length - 1];\\\\n            if (is_data_x || is_data_y) {\\\\n                event.preventDefault();\\\\n                selected = is_data_x ? [i, j + direction] : [i + direction, direction > 0 ? 0 : _headers.length - 1];\\\\n            }\\\\n            editing = false;\\\\n            break;\\\\n        default:\\\\n            if (!editable)\\\\n                break;\\\\n            if ((!editing || editing && dequal(editing, [i, j])) && event.key.length === 1) {\\\\n                clear_on_focus = true;\\\\n                editing = [i, j];\\\\n            }\\\\n    }\\\\n}\\\\nlet active_cell = null;\\\\nasync function handle_cell_click(i, j) {\\\\n    if (active_cell && active_cell.row === i && active_cell.col === j) {\\\\n        active_cell = null;\\\\n    }\\\\n    else {\\\\n        active_cell = { row: i, col: j };\\\\n    }\\\\n    if (dequal(editing, [i, j]))\\\\n        return;\\\\n    header_edit = false;\\\\n    selected_header = false;\\\\n    editing = false;\\\\n    if (!dequal(selected, [i, j])) {\\\\n        selected = [i, j];\\\\n        await tick();\\\\n        parent.focus();\\\\n    }\\\\n}\\\\nlet sort_direction;\\\\nlet sort_by;\\\\nfunction handle_sort(col) {\\\\n    if (typeof sort_by !== \\\\\"number\\\\\" || sort_by !== col) {\\\\n        sort_direction = \\\\\"asc\\\\\";\\\\n        sort_by = col;\\\\n    }\\\\n    else {\\\\n        if (sort_direction === \\\\\"asc\\\\\") {\\\\n            sort_direction = \\\\\"des\\\\\";\\\\n        }\\\\n        else if (sort_direction === \\\\\"des\\\\\") {\\\\n            sort_direction = \\\\\"asc\\\\\";\\\\n        }\\\\n    }\\\\n}\\\\nlet header_edit;\\\\nlet select_on_focus = false;\\\\nlet selected_header = false;\\\\nasync function edit_header(i, _select = false) {\\\\n    if (!editable || col_count[1] !== \\\\\"dynamic\\\\\" || header_edit === i)\\\\n        return;\\\\n    selected = false;\\\\n    selected_header = i;\\\\n    header_edit = i;\\\\n    select_on_focus = _select;\\\\n}\\\\nfunction end_header_edit(event) {\\\\n    if (!editable)\\\\n        return;\\\\n    switch (event.key) {\\\\n        case \\\\\"Escape\\\\\":\\\\n        case \\\\\"Enter\\\\\":\\\\n        case \\\\\"Tab\\\\\":\\\\n            event.preventDefault();\\\\n            selected = false;\\\\n            selected_header = header_edit;\\\\n            header_edit = false;\\\\n            parent.focus();\\\\n            break;\\\\n    }\\\\n}\\\\nasync function add_row(index) {\\\\n    parent.focus();\\\\n    if (row_count[1] !== \\\\\"dynamic\\\\\")\\\\n        return;\\\\n    if (data.length === 0) {\\\\n        values = [Array(headers.length).fill(\\\\\"\\\\\")];\\\\n        return;\\\\n    }\\\\n    const new_row = Array(data[0].length).fill(0).map((_, i) => {\\\\n        const _id = make_id();\\\\n        els[_id] = { cell: null, input: null };\\\\n        return { id: _id, value: \\\\\"\\\\\" };\\\\n    });\\\\n    if (index !== void 0 && index >= 0 && index <= data.length) {\\\\n        data.splice(index, 0, new_row);\\\\n    }\\\\n    else {\\\\n        data.push(new_row);\\\\n    }\\\\n    data = data;\\\\n    selected = [index !== void 0 ? index : data.length - 1, 0];\\\\n}\\\\n$: (data || selected_header) && trigger_change();\\\\nasync function add_col(index) {\\\\n    parent.focus();\\\\n    if (col_count[1] !== \\\\\"dynamic\\\\\")\\\\n        return;\\\\n    const insert_index = index !== void 0 ? index : data[0].length;\\\\n    for (let i = 0; i < data.length; i++) {\\\\n        const _id = make_id();\\\\n        els[_id] = { cell: null, input: null };\\\\n        data[i].splice(insert_index, 0, { id: _id, value: \\\\\"\\\\\" });\\\\n    }\\\\n    headers.splice(insert_index, 0, `Header ${headers.length + 1}`);\\\\n    data = data;\\\\n    headers = headers;\\\\n    await tick();\\\\n    requestAnimationFrame(() => {\\\\n        edit_header(insert_index, true);\\\\n        const new_w = parent.querySelectorAll(\\\\\"tbody\\\\\")[1].offsetWidth;\\\\n        parent.querySelectorAll(\\\\\"table\\\\\")[1].scrollTo({ left: new_w });\\\\n    });\\\\n}\\\\nfunction handle_click_outside(event) {\\\\n    if (active_cell_menu && !event.target.closest(\\\\\".cell-menu\\\\\") || active_header_menu && !event.target.closest(\\\\\".cell-menu\\\\\")) {\\\\n        active_cell_menu = null;\\\\n        active_header_menu = null;\\\\n    }\\\\n    event.stopImmediatePropagation();\\\\n    const [trigger] = event.composedPath();\\\\n    if (parent.contains(trigger)) {\\\\n        return;\\\\n    }\\\\n    editing = false;\\\\n    header_edit = false;\\\\n    selected_header = false;\\\\n    reset_selection();\\\\n    active_cell = null;\\\\n    active_cell_menu = null;\\\\n    active_header_menu = null;\\\\n}\\\\nfunction guess_delimitaor(text, possibleDelimiters) {\\\\n    return possibleDelimiters.filter(weedOut);\\\\n    function weedOut(delimiter) {\\\\n        var cache = -1;\\\\n        return text.split(\\\\\"\\\\\\\\n\\\\\").every(checkLength);\\\\n        function checkLength(line) {\\\\n            if (!line) {\\\\n                return true;\\\\n            }\\\\n            var length = line.split(delimiter).length;\\\\n            if (cache < 0) {\\\\n                cache = length;\\\\n            }\\\\n            return cache === length && length > 1;\\\\n        }\\\\n    }\\\\n}\\\\nfunction data_uri_to_blob(data_uri) {\\\\n    const byte_str = atob(data_uri.split(\\\\\",\\\\\")[1]);\\\\n    const mime_str = data_uri.split(\\\\\",\\\\\")[0].split(\\\\\":\\\\\")[1].split(\\\\\";\\\\\")[0];\\\\n    const ab = new ArrayBuffer(byte_str.length);\\\\n    const ia = new Uint8Array(ab);\\\\n    for (let i = 0; i < byte_str.length; i++) {\\\\n        ia[i] = byte_str.charCodeAt(i);\\\\n    }\\\\n    return new Blob([ab], { type: mime_str });\\\\n}\\\\nfunction blob_to_string(blob) {\\\\n    const reader = new FileReader();\\\\n    function handle_read(e) {\\\\n        if (!e?.target?.result || typeof e.target.result !== \\\\\"string\\\\\")\\\\n            return;\\\\n        const [delimiter] = guess_delimitaor(e.target.result, [\\\\\",\\\\\", \\\\\"\\\\t\\\\\"]);\\\\n        const [head, ...rest] = dsvFormat(delimiter).parseRows(e.target.result);\\\\n        _headers = make_headers(col_count[1] === \\\\\"fixed\\\\\" ? head.slice(0, col_count[0]) : head);\\\\n        values = rest;\\\\n        reader.removeEventListener(\\\\\"loadend\\\\\", handle_read);\\\\n    }\\\\n    reader.addEventListener(\\\\\"loadend\\\\\", handle_read);\\\\n    reader.readAsText(blob);\\\\n}\\\\nlet dragging = false;\\\\nfunction get_max(_d) {\\\\n    let max2 = _d[0].slice();\\\\n    for (let i = 0; i < _d.length; i++) {\\\\n        for (let j = 0; j < _d[i].length; j++) {\\\\n            if (`${max2[j].value}`.length < `${_d[i][j].value}`.length) {\\\\n                max2[j] = _d[i][j];\\\\n            }\\\\n        }\\\\n    }\\\\n    return max2;\\\\n}\\\\n$: max = get_max(data);\\\\n$: cells[0] && set_cell_widths();\\\\nlet cells = [];\\\\nlet parent;\\\\nlet table;\\\\nfunction set_cell_widths() {\\\\n    const widths = cells.map((el, i) => {\\\\n        return el?.clientWidth || 0;\\\\n    });\\\\n    if (widths.length === 0)\\\\n        return;\\\\n    for (let i = 0; i < widths.length; i++) {\\\\n        parent.style.setProperty(`--cell-width-${i}`, `${widths[i] - scrollbar_width / widths.length}px`);\\\\n    }\\\\n}\\\\nlet table_height = values.slice(0, max_height / values.length * 37).length * 37 + 37;\\\\nlet scrollbar_width = 0;\\\\nfunction sort_data(_data, _display_value, _styling, col, dir) {\\\\n    let id = null;\\\\n    if (selected && selected[0] in data && selected[1] in data[selected[0]]) {\\\\n        id = data[selected[0]][selected[1]].id;\\\\n    }\\\\n    if (typeof col !== \\\\\"number\\\\\" || !dir) {\\\\n        return;\\\\n    }\\\\n    const indices = [...Array(_data.length).keys()];\\\\n    if (dir === \\\\\"asc\\\\\") {\\\\n        indices.sort((i, j) => _data[i][col].value < _data[j][col].value ? -1 : 1);\\\\n    }\\\\n    else if (dir === \\\\\"des\\\\\") {\\\\n        indices.sort((i, j) => _data[i][col].value > _data[j][col].value ? -1 : 1);\\\\n    }\\\\n    else {\\\\n        return;\\\\n    }\\\\n    const temp_data = [..._data];\\\\n    const temp_display_value = _display_value ? [..._display_value] : null;\\\\n    const temp_styling = _styling ? [..._styling] : null;\\\\n    indices.forEach((originalIndex, sortedIndex) => {\\\\n        _data[sortedIndex] = temp_data[originalIndex];\\\\n        if (_display_value && temp_display_value)\\\\n            _display_value[sortedIndex] = temp_display_value[originalIndex];\\\\n        if (_styling && temp_styling)\\\\n            _styling[sortedIndex] = temp_styling[originalIndex];\\\\n    });\\\\n    data = data;\\\\n    if (id) {\\\\n        const [i, j] = get_current_indices(id);\\\\n        selected = [i, j];\\\\n    }\\\\n}\\\\n$: sort_data(data, display_value, styling, sort_by, sort_direction);\\\\n$: selected_index = !!selected && selected[0];\\\\nlet is_visible = false;\\\\nonMount(() => {\\\\n    const observer = new IntersectionObserver((entries, observer2) => {\\\\n        entries.forEach((entry) => {\\\\n            if (entry.isIntersecting && !is_visible) {\\\\n                set_cell_widths();\\\\n                data = data;\\\\n            }\\\\n            is_visible = entry.isIntersecting;\\\\n        });\\\\n    });\\\\n    observer.observe(parent);\\\\n    return () => {\\\\n        observer.disconnect();\\\\n    };\\\\n});\\\\nlet highlighted_column = null;\\\\nlet active_cell_menu = null;\\\\nfunction toggle_cell_menu(event, row, col) {\\\\n    event.stopPropagation();\\\\n    if (active_cell_menu && active_cell_menu.row === row && active_cell_menu.col === col) {\\\\n        active_cell_menu = null;\\\\n    }\\\\n    else {\\\\n        const cell = event.target.closest(\\\\\"td\\\\\");\\\\n        if (cell) {\\\\n            const rect = cell.getBoundingClientRect();\\\\n            active_cell_menu = {\\\\n                row,\\\\n                col,\\\\n                x: rect.right,\\\\n                y: rect.bottom\\\\n            };\\\\n        }\\\\n    }\\\\n}\\\\nfunction add_row_at(index, position) {\\\\n    const row_index = position === \\\\\"above\\\\\" ? index : index + 1;\\\\n    add_row(row_index);\\\\n    active_cell_menu = null;\\\\n    active_header_menu = null;\\\\n}\\\\nfunction add_col_at(index, position) {\\\\n    const col_index = position === \\\\\"left\\\\\" ? index : index + 1;\\\\n    add_col(col_index);\\\\n    active_cell_menu = null;\\\\n    active_header_menu = null;\\\\n}\\\\nfunction handle_resize() {\\\\n    active_cell_menu = null;\\\\n    active_header_menu = null;\\\\n    set_cell_widths();\\\\n}\\\\nonMount(() => {\\\\n    document.addEventListener(\\\\\"click\\\\\", handle_click_outside);\\\\n    window.addEventListener(\\\\\"resize\\\\\", handle_resize);\\\\n    return () => {\\\\n        document.removeEventListener(\\\\\"click\\\\\", handle_click_outside);\\\\n        window.removeEventListener(\\\\\"resize\\\\\", handle_resize);\\\\n    };\\\\n});\\\\nlet active_button = null;\\\\nfunction toggle_header_button(col) {\\\\n    if (active_button?.type === \\\\\"header\\\\\" && active_button.col === col) {\\\\n        active_button = null;\\\\n    }\\\\n    else {\\\\n        active_button = { type: \\\\\"header\\\\\", col };\\\\n    }\\\\n}\\\\nfunction toggle_cell_button(row, col) {\\\\n    if (active_button?.type === \\\\\"cell\\\\\" && active_button.row === row && active_button.col === col) {\\\\n        active_button = null;\\\\n    }\\\\n    else {\\\\n        active_button = { type: \\\\\"cell\\\\\", row, col };\\\\n    }\\\\n}\\\\nlet active_header_menu = null;\\\\nfunction toggle_header_menu(event, col) {\\\\n    event.stopPropagation();\\\\n    if (active_header_menu && active_header_menu.col === col) {\\\\n        active_header_menu = null;\\\\n    }\\\\n    else {\\\\n        const header = event.target.closest(\\\\\"th\\\\\");\\\\n        if (header) {\\\\n            const rect = header.getBoundingClientRect();\\\\n            active_header_menu = {\\\\n                col,\\\\n                x: rect.right,\\\\n                y: rect.bottom\\\\n            };\\\\n        }\\\\n    }\\\\n}\\\\nfunction reset_selection() {\\\\n    selected = false;\\\\n    last_selected = null;\\\\n}\\\\n<\\/script>\\\\n\\\\n<svelte:window\\\\n\\\\ton:click={handle_click_outside}\\\\n\\\\ton:touchstart={handle_click_outside}\\\\n\\\\ton:resize={() => set_cell_widths()}\\\\n/>\\\\n\\\\n<div class:label={label && label.length !== 0} use:copy>\\\\n\\\\t{#if label && label.length !== 0 && show_label}\\\\n\\\\t\\\\t<p>\\\\n\\\\t\\\\t\\\\t{label}\\\\n\\\\t\\\\t</p>\\\\n\\\\t{/if}\\\\n\\\\t<div\\\\n\\\\t\\\\tbind:this={parent}\\\\n\\\\t\\\\tclass=\\\\\"table-wrap\\\\\"\\\\n\\\\t\\\\tclass:dragging\\\\n\\\\t\\\\tclass:no-wrap={!wrap}\\\\n\\\\t\\\\tstyle=\\\\\"height:{table_height}px\\\\\"\\\\n\\\\t\\\\ton:keydown={(e) => handle_keydown(e)}\\\\n\\\\t\\\\trole=\\\\\"grid\\\\\"\\\\n\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<table\\\\n\\\\t\\\\t\\\\tbind:contentRect={t_rect}\\\\n\\\\t\\\\t\\\\tbind:this={table}\\\\n\\\\t\\\\t\\\\tclass:fixed-layout={column_widths.length != 0}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#if label && label.length !== 0}\\\\n\\\\t\\\\t\\\\t\\\\t<caption class=\\\\\"sr-only\\\\\">{label}</caption>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t<thead>\\\\n\\\\t\\\\t\\\\t\\\\t<tr>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each _headers as { value, id }, i (id)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<th\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:editing={header_edit === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-sort={get_sort_status(value, sort_by, sort_direction)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:width={column_widths.length ? column_widths[i] : undefined}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"cell-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<EditableCell\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\theader\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tedit={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tel={null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:sorted={sort_by === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:des={sort_by === i && sort_direction === \\\\\"des\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"sort-button {sort_direction} \\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<svg\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\twidth=\\\\\"1em\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\theight=\\\\\"1em\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tviewBox=\\\\\"0 0 9 7\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tfill=\\\\\"none\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\txmlns=\\\\\"http://www.w3.org/2000/svg\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<path d=\\\\\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</svg>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</th>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</tr>\\\\n\\\\t\\\\t\\\\t</thead>\\\\n\\\\t\\\\t\\\\t<tbody>\\\\n\\\\t\\\\t\\\\t\\\\t<tr>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each max as { value, id }, j (id)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<td tabindex=\\\\\"-1\\\\\" bind:this={cells[j]}>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"cell-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<EditableCell\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tedit={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tel={null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</td>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</tr>\\\\n\\\\t\\\\t\\\\t</tbody>\\\\n\\\\t\\\\t</table>\\\\n\\\\t\\\\t<Upload\\\\n\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\tflex={false}\\\\n\\\\t\\\\t\\\\tcenter={false}\\\\n\\\\t\\\\t\\\\tboundedheight={false}\\\\n\\\\t\\\\t\\\\tdisable_click={true}\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\ton:load={(e) => blob_to_string(data_uri_to_blob(e.detail.data))}\\\\n\\\\t\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<VirtualTable\\\\n\\\\t\\\\t\\\\t\\\\tbind:items={data}\\\\n\\\\t\\\\t\\\\t\\\\t{max_height}\\\\n\\\\t\\\\t\\\\t\\\\tbind:actual_height={table_height}\\\\n\\\\t\\\\t\\\\t\\\\tbind:table_scrollbar_width={scrollbar_width}\\\\n\\\\t\\\\t\\\\t\\\\tselected={selected_index}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if label && label.length !== 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<caption class=\\\\\"sr-only\\\\\">{label}</caption>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t<tr slot=\\\\\"thead\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each _headers as { value, id }, i (id)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<th\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:focus={header_edit === i || selected_header === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-sort={get_sort_status(value, sort_by, sort_direction)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle=\\\\\"width: var(--cell-width-{i});\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttoggle_header_button(i);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"cell-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"header-content\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<EditableCell\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:value={_headers[i].value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:el={els[id].input}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tedit={header_edit === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={end_header_edit}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:dblclick={() => edit_header(i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{select_on_focus}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\theader\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- TODO: fix -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-click-events-have-key-events -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-no-static-element-interactions-->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:sorted={sort_by === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:des={sort_by === i && sort_direction === \\\\\"des\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"sort-button {sort_direction}\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={(event) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tevent.stopPropagation();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_sort(i);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<svg\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\twidth=\\\\\"1em\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\theight=\\\\\"1em\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tviewBox=\\\\\"0 0 9 7\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tfill=\\\\\"none\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\txmlns=\\\\\"http://www.w3.org/2000/svg\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<path d=\\\\\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</svg>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if editable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"cell-menu-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={(event) => toggle_header_menu(event, i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t⋮\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</th>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</tr>\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t<tr slot=\\\\\"tbody\\\\\" let:item let:index class:row_odd={index % 2 === 0}>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each item as { value, id }, j (id)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<td\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:touchstart={() => start_edit(index, j)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_cell_click(index, j);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttoggle_cell_button(index, j);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:dblclick={() => start_edit(index, j)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:width=\\\\\"var(--cell-width-{j})\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle={styling?.[index]?.[j] || \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:focus={dequal(selected, [index, j])}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:menu-active={active_cell_menu &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tactive_cell_menu.row === index &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tactive_cell_menu.col === j}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"cell-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<EditableCell\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:value={data[index][j].value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:el={els[id].input}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdisplay_value={display_value?.[index]?.[j]}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tedit={dequal(editing, [index, j])}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:blur={() => ((clear_on_focus = false), parent.focus())}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{clear_on_focus}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if editable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"cell-menu-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={(event) => toggle_cell_menu(event, index, j)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t⋮\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</td>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</tr>\\\\n\\\\t\\\\t\\\\t</VirtualTable>\\\\n\\\\t\\\\t</Upload>\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n{#if active_cell_menu !== null}\\\\n\\\\t<CellMenu\\\\n\\\\t\\\\t{i18n}\\\\n\\\\t\\\\tx={active_cell_menu.x}\\\\n\\\\t\\\\ty={active_cell_menu.y}\\\\n\\\\t\\\\trow={active_cell_menu?.row ?? -1}\\\\n\\\\t\\\\t{col_count}\\\\n\\\\t\\\\t{row_count}\\\\n\\\\t\\\\ton_add_row_above={() => add_row_at(active_cell_menu?.row ?? -1, \\\\\"above\\\\\")}\\\\n\\\\t\\\\ton_add_row_below={() => add_row_at(active_cell_menu?.row ?? -1, \\\\\"below\\\\\")}\\\\n\\\\t\\\\ton_add_column_left={() => add_col_at(active_cell_menu?.col ?? -1, \\\\\"left\\\\\")}\\\\n\\\\t\\\\ton_add_column_right={() => add_col_at(active_cell_menu?.col ?? -1, \\\\\"right\\\\\")}\\\\n\\\\t/>\\\\n{/if}\\\\n\\\\n{#if active_header_menu !== null}\\\\n\\\\t<CellMenu\\\\n\\\\t\\\\t{i18n}\\\\n\\\\t\\\\tx={active_header_menu.x}\\\\n\\\\t\\\\ty={active_header_menu.y}\\\\n\\\\t\\\\trow={-1}\\\\n\\\\t\\\\t{col_count}\\\\n\\\\t\\\\t{row_count}\\\\n\\\\t\\\\ton_add_row_above={() => add_row_at(active_cell_menu?.row ?? -1, \\\\\"above\\\\\")}\\\\n\\\\t\\\\ton_add_row_below={() => add_row_at(active_cell_menu?.row ?? -1, \\\\\"below\\\\\")}\\\\n\\\\t\\\\ton_add_column_left={() => add_col_at(active_header_menu?.col ?? -1, \\\\\"left\\\\\")}\\\\n\\\\t\\\\ton_add_column_right={() =>\\\\n\\\\t\\\\t\\\\tadd_col_at(active_header_menu?.col ?? -1, \\\\\"right\\\\\")}\\\\n\\\\t/>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.button-wrap:hover svg {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.button-wrap svg {\\\\n\\\\t\\\\tmargin-right: var(--size-1);\\\\n\\\\t\\\\tmargin-left: -5px;\\\\n\\\\t}\\\\n\\\\n\\\\t.label p {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tz-index: var(--layer-4);\\\\n\\\\t\\\\tmargin-bottom: var(--size-2);\\\\n\\\\t\\\\tcolor: var(--block-label-text-color);\\\\n\\\\t\\\\tfont-size: var(--block-label-text-size);\\\\n\\\\t}\\\\n\\\\n\\\\t.table-wrap {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\ttransition: 150ms;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--table-radius);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.table-wrap:focus-within {\\\\n\\\\t\\\\toutline: none;\\\\n\\\\t\\\\tbackground-color: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.dragging {\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.no-wrap {\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t}\\\\n\\\\n\\\\ttable {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\topacity: 0;\\\\n\\\\t\\\\ttransition: 150ms;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\ttable-layout: auto;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--input-text-size);\\\\n\\\\t\\\\tline-height: var(--line-md);\\\\n\\\\t\\\\tfont-family: var(--font-mono);\\\\n\\\\t\\\\tborder-spacing: 0;\\\\n\\\\t}\\\\n\\\\n\\\\tdiv:not(.no-wrap) td {\\\\n\\\\t\\\\toverflow-wrap: anywhere;\\\\n\\\\t}\\\\n\\\\n\\\\tdiv.no-wrap td {\\\\n\\\\t\\\\toverflow-x: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\ttable.fixed-layout {\\\\n\\\\t\\\\ttable-layout: fixed;\\\\n\\\\t}\\\\n\\\\n\\\\tthead {\\\\n\\\\t\\\\tposition: sticky;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t}\\\\n\\\\n\\\\ttr {\\\\n\\\\t\\\\tborder-bottom: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\n\\\\ttr > * + * {\\\\n\\\\t\\\\tborder-right-width: 0px;\\\\n\\\\t\\\\tborder-left-width: 1px;\\\\n\\\\t\\\\tborder-style: solid;\\\\n\\\\t\\\\tborder-color: var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\tth,\\\\n\\\\ttd {\\\\n\\\\t\\\\t--ring-color: transparent;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\toutline: none;\\\\n\\\\t\\\\tbox-shadow: inset 0 0 0 1px var(--ring-color);\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t}\\\\n\\\\n\\\\tth:first-child {\\\\n\\\\t\\\\tborder-top-left-radius: var(--table-radius);\\\\n\\\\t}\\\\n\\\\n\\\\tth:last-child {\\\\n\\\\t\\\\tborder-top-right-radius: var(--table-radius);\\\\n\\\\t}\\\\n\\\\n\\\\tth.focus,\\\\n\\\\ttd.focus {\\\\n\\\\t\\\\t--ring-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\ttr:last-child td:first-child {\\\\n\\\\t\\\\tborder-bottom-left-radius: var(--table-radius);\\\\n\\\\t}\\\\n\\\\n\\\\ttr:last-child td:last-child {\\\\n\\\\t\\\\tborder-bottom-right-radius: var(--table-radius);\\\\n\\\\t}\\\\n\\\\n\\\\ttr th {\\\\n\\\\t\\\\tbackground: var(--table-even-background-fill);\\\\n\\\\t}\\\\n\\\\n\\\\tth svg {\\\\n\\\\t\\\\tfill: currentColor;\\\\n\\\\t\\\\tfont-size: 10px;\\\\n\\\\t}\\\\n\\\\n\\\\t.sort-button {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex: none;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\ttransition: 150ms;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t\\\\tcolor: var(--body-text-color-subdued);\\\\n\\\\t\\\\tline-height: var(--text-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.sort-button:hover {\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.des {\\\\n\\\\t\\\\ttransform: scaleY(-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.sort-button.sorted {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.editing {\\\\n\\\\t\\\\tbackground: var(--table-editing);\\\\n\\\\t}\\\\n\\\\n\\\\t.cell-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\toutline: none;\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tmin-height: var(--size-9);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.header-content {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t\\\\tmin-width: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.controls-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: flex-end;\\\\n\\\\t\\\\tpadding-top: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\t.row_odd {\\\\n\\\\t\\\\tbackground: var(--table-odd-background-fill);\\\\n\\\\t}\\\\n\\\\n\\\\t.row_odd.focus {\\\\n\\\\t\\\\tbackground: var(--background-fill-primary);\\\\n\\\\t}\\\\n\\\\n\\\\ttable {\\\\n\\\\t\\\\tborder-collapse: separate;\\\\n\\\\t}\\\\n\\\\n\\\\t.cell-menu-button {\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--block-radius);\\\\n\\\\t\\\\twidth: var(--size-5);\\\\n\\\\t\\\\theight: var(--size-5);\\\\n\\\\t\\\\tmin-width: var(--size-5);\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tmargin-right: var(--spacing-sm);\\\\n\\\\t\\\\tz-index: var(--layer-2);\\\\n\\\\t}\\\\n\\\\n\\\\t.cell-menu-button:hover {\\\\n\\\\t\\\\tbackground-color: var(--color-bg-hover);\\\\n\\\\t}\\\\n\\\\n\\\\ttd.focus .cell-menu-button {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\n\\\\tth .header-content {\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAyzBC,2BAAY,MAAM,CAAC,iCAAI,CACtB,KAAK,CAAE,IAAI,cAAc,CAC1B,CAEA,2BAAY,CAAC,iCAAI,CAChB,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,WAAW,CAAE,IACd,CAEA,qBAAM,CAAC,+BAAE,CACR,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,KAAK,CAAE,IAAI,wBAAwB,CAAC,CACpC,SAAS,CAAE,IAAI,uBAAuB,CACvC,CAEA,wDAAY,CACX,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,QAAQ,CAAE,MACX,CAEA,wDAAW,aAAc,CACxB,OAAO,CAAE,IAAI,CACb,gBAAgB,CAAE,IACnB,CAEA,sDAAU,CACT,YAAY,CAAE,IAAI,cAAc,CACjC,CAEA,qDAAS,CACR,WAAW,CAAE,MACd,CAEA,kDAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,KAAK,CACjB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,YAAY,CAAE,IAAI,CAClB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,WAAW,CAAE,IAAI,WAAW,CAAC,CAC7B,cAAc,CAAE,CACjB,CAEA,kBAAG,KAAK,QAAQ,CAAC,CAAC,gCAAG,CACpB,aAAa,CAAE,QAChB,CAEA,GAAG,uBAAQ,CAAC,gCAAG,CACd,UAAU,CAAE,MACb,CAEA,KAAK,0DAAc,CAClB,YAAY,CAAE,KACf,CAEA,kDAAM,CACL,QAAQ,CAAE,MAAM,CAChB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,UAAU,CAAE,IAAI,aAAa,CAC9B,CAEA,+CAAG,CACF,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CACpD,UAAU,CAAE,IACb,CAEA,iBAAE,CAAG,eAAC,CAAG,eAAE,CACV,kBAAkB,CAAE,GAAG,CACvB,iBAAiB,CAAE,GAAG,CACtB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,IAAI,sBAAsB,CACzC,CAEA,+CAAE,CACF,+CAAG,CACF,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,CAC7C,OAAO,CAAE,CACV,CAEA,+CAAE,YAAa,CACd,sBAAsB,CAAE,IAAI,cAAc,CAC3C,CAEA,+CAAE,WAAY,CACb,uBAAuB,CAAE,IAAI,cAAc,CAC5C,CAEA,EAAE,mDAAM,CACR,EAAE,mDAAO,CACR,YAAY,CAAE,mBACf,CAEA,iBAAE,WAAW,CAAC,gCAAE,YAAa,CAC5B,yBAAyB,CAAE,IAAI,cAAc,CAC9C,CAEA,iBAAE,WAAW,CAAC,gCAAE,WAAY,CAC3B,0BAA0B,CAAE,IAAI,cAAc,CAC/C,CAEA,iBAAE,CAAC,gCAAG,CACL,UAAU,CAAE,IAAI,4BAA4B,CAC7C,CAEA,iBAAE,CAAC,iCAAI,CACN,IAAI,CAAE,YAAY,CAClB,SAAS,CAAE,IACZ,CAEA,yDAAa,CACZ,OAAO,CAAE,IAAI,CACb,IAAI,CAAE,IAAI,CACV,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,WAAW,CAAE,IAAI,SAAS,CAC3B,CAEA,yDAAY,MAAO,CAClB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,iDAAK,CACJ,SAAS,CAAE,OAAO,EAAE,CACrB,CAEA,YAAY,oDAAQ,CACnB,KAAK,CAAE,IAAI,cAAc,CAC1B,CAEA,qDAAS,CACR,UAAU,CAAE,IAAI,eAAe,CAChC,CAEA,uDAAW,CACV,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,IAAI,QAAQ,CAAC,CACzB,QAAQ,CAAE,MACX,CAEA,4DAAgB,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MAAM,CAChB,SAAS,CAAE,CAAC,CACZ,SAAS,CAAE,CACZ,CAEA,2DAAe,CACd,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,qDAAS,CACR,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,QAAQ,mDAAO,CACd,UAAU,CAAE,IAAI,yBAAyB,CAC1C,CAEA,kDAAM,CACL,eAAe,CAAE,QAClB,CAEA,8DAAkB,CACjB,WAAW,CAAE,CAAC,CACd,OAAO,CAAE,IAAI,CACb,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,OAAO,CAAE,CAAC,CACV,YAAY,CAAE,IAAI,YAAY,CAAC,CAC/B,OAAO,CAAE,IAAI,SAAS,CACvB,CAEA,8DAAiB,MAAO,CACvB,gBAAgB,CAAE,IAAI,gBAAgB,CACvC,CAEA,EAAE,qBAAM,CAAC,+CAAkB,CAC1B,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB,CAEA,iBAAE,CAAC,6CAAgB,CAClB,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB\"}'\n};\nfunction make_id() {\n  return Math.random().toString(36).substring(2, 15);\n}\nfunction get_max(_d) {\n  let max2 = _d[0].slice();\n  for (let i = 0; i < _d.length; i++) {\n    for (let j = 0; j < _d[i].length; j++) {\n      if (`${max2[j].value}`.length < `${_d[i][j].value}`.length) {\n        max2[j] = _d[i][j];\n      }\n    }\n  }\n  return max2;\n}\nconst Table = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let max;\n  let selected_index;\n  let { datatype } = $$props;\n  let { label = null } = $$props;\n  let { show_label = true } = $$props;\n  let { headers = [] } = $$props;\n  let { values = [] } = $$props;\n  let { col_count } = $$props;\n  let { row_count } = $$props;\n  let { latex_delimiters } = $$props;\n  let { editable = true } = $$props;\n  let { wrap = false } = $$props;\n  let { root } = $$props;\n  let { i18n } = $$props;\n  let { max_height = 500 } = $$props;\n  let { line_breaks = true } = $$props;\n  let { column_widths = [] } = $$props;\n  let { upload } = $$props;\n  let { stream_handler } = $$props;\n  let selected = false;\n  let { display_value = null } = $$props;\n  let { styling = null } = $$props;\n  const dispatch = createEventDispatcher();\n  let editing = false;\n  const get_data_at = (row, col) => data?.[row]?.[col]?.value;\n  let last_selected = null;\n  let els = {};\n  function make_headers(_head) {\n    let _h = _head || [];\n    if (col_count[1] === \"fixed\" && _h.length < col_count[0]) {\n      const fill = Array(col_count[0] - _h.length).fill(\"\").map((_, i) => `${i + _h.length}`);\n      _h = _h.concat(fill);\n    }\n    if (!_h || _h.length === 0) {\n      return Array(col_count[0]).fill(0).map((_, i) => {\n        const _id = make_id();\n        els[_id] = { cell: null, input: null };\n        return { id: _id, value: JSON.stringify(i + 1) };\n      });\n    }\n    return _h.map((h, i) => {\n      const _id = make_id();\n      els[_id] = { cell: null, input: null };\n      return { id: _id, value: h ?? \"\" };\n    });\n  }\n  function process_data(_values) {\n    const data_row_length = _values.length;\n    return Array(row_count[1] === \"fixed\" ? row_count[0] : data_row_length < row_count[0] ? row_count[0] : data_row_length).fill(0).map((_, i) => Array(col_count[1] === \"fixed\" ? col_count[0] : data_row_length > 0 ? _values[0].length : headers.length).fill(0).map((_2, j) => {\n      const id = make_id();\n      els[id] = els[id] || { input: null, cell: null };\n      const obj = { value: _values?.[i]?.[j] ?? \"\", id };\n      return obj;\n    }));\n  }\n  let _headers = make_headers(headers);\n  let old_headers;\n  function trigger_headers() {\n    _headers = make_headers(headers);\n    old_headers = headers.slice();\n    trigger_change();\n  }\n  let data = [[]];\n  let old_val = void 0;\n  async function trigger_change() {\n    dispatch(\"change\", {\n      data: data.map((r) => r.map(({ value }) => value)),\n      headers: _headers.map((h) => h.value),\n      metadata: editable ? null : { display_value, styling }\n    });\n  }\n  function get_sort_status(name, _sort, direction) {\n    return \"none\";\n  }\n  let clear_on_focus = false;\n  let sort_direction;\n  let sort_by;\n  let header_edit;\n  let select_on_focus = false;\n  let selected_header = false;\n  async function edit_header(i, _select = false) {\n    if (!editable || col_count[1] !== \"dynamic\" || header_edit === i)\n      return;\n    selected = false;\n    selected_header = i;\n    header_edit = i;\n    select_on_focus = _select;\n  }\n  async function add_row(index) {\n    parent.focus();\n    if (row_count[1] !== \"dynamic\")\n      return;\n    if (data.length === 0) {\n      values = [Array(headers.length).fill(\"\")];\n      return;\n    }\n    const new_row = Array(data[0].length).fill(0).map((_, i) => {\n      const _id = make_id();\n      els[_id] = { cell: null, input: null };\n      return { id: _id, value: \"\" };\n    });\n    if (index !== void 0 && index >= 0 && index <= data.length) {\n      data.splice(index, 0, new_row);\n    } else {\n      data.push(new_row);\n    }\n    data = data;\n    selected = [index !== void 0 ? index : data.length - 1, 0];\n  }\n  async function add_col(index) {\n    parent.focus();\n    if (col_count[1] !== \"dynamic\")\n      return;\n    const insert_index = index !== void 0 ? index : data[0].length;\n    for (let i = 0; i < data.length; i++) {\n      const _id = make_id();\n      els[_id] = { cell: null, input: null };\n      data[i].splice(insert_index, 0, { id: _id, value: \"\" });\n    }\n    headers.splice(insert_index, 0, `Header ${headers.length + 1}`);\n    data = data;\n    headers = headers;\n    await tick();\n    requestAnimationFrame(() => {\n      edit_header(insert_index, true);\n      const new_w = parent.querySelectorAll(\"tbody\")[1].offsetWidth;\n      parent.querySelectorAll(\"table\")[1].scrollTo({ left: new_w });\n    });\n  }\n  function handle_click_outside(event) {\n    if (active_cell_menu && !event.target.closest(\".cell-menu\") || active_header_menu && !event.target.closest(\".cell-menu\")) {\n      active_cell_menu = null;\n      active_header_menu = null;\n    }\n    event.stopImmediatePropagation();\n    const [trigger] = event.composedPath();\n    if (parent.contains(trigger)) {\n      return;\n    }\n    editing = false;\n    header_edit = false;\n    selected_header = false;\n    reset_selection();\n    active_cell_menu = null;\n    active_header_menu = null;\n  }\n  let dragging = false;\n  let cells = [];\n  let parent;\n  let table;\n  function set_cell_widths() {\n    const widths = cells.map((el, i) => {\n      return el?.clientWidth || 0;\n    });\n    if (widths.length === 0)\n      return;\n    for (let i = 0; i < widths.length; i++) {\n      parent.style.setProperty(`--cell-width-${i}`, `${widths[i] - scrollbar_width / widths.length}px`);\n    }\n  }\n  let table_height = values.slice(0, max_height / values.length * 37).length * 37 + 37;\n  let scrollbar_width = 0;\n  function sort_data(_data, _display_value, _styling, col, dir) {\n    if (selected && selected[0] in data && selected[1] in data[selected[0]]) {\n      data[selected[0]][selected[1]].id;\n    }\n    {\n      return;\n    }\n  }\n  let is_visible = false;\n  onMount(() => {\n    const observer = new IntersectionObserver((entries, observer2) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting && !is_visible) {\n          set_cell_widths();\n          data = data;\n        }\n        is_visible = entry.isIntersecting;\n      });\n    });\n    observer.observe(parent);\n    return () => {\n      observer.disconnect();\n    };\n  });\n  let active_cell_menu = null;\n  function add_row_at(index, position) {\n    const row_index = position === \"above\" ? index : index + 1;\n    add_row(row_index);\n    active_cell_menu = null;\n    active_header_menu = null;\n  }\n  function add_col_at(index, position) {\n    const col_index = position === \"left\" ? index : index + 1;\n    add_col(col_index);\n    active_cell_menu = null;\n    active_header_menu = null;\n  }\n  function handle_resize() {\n    active_cell_menu = null;\n    active_header_menu = null;\n    set_cell_widths();\n  }\n  onMount(() => {\n    document.addEventListener(\"click\", handle_click_outside);\n    window.addEventListener(\"resize\", handle_resize);\n    return () => {\n      document.removeEventListener(\"click\", handle_click_outside);\n      window.removeEventListener(\"resize\", handle_resize);\n    };\n  });\n  let active_header_menu = null;\n  function reset_selection() {\n    selected = false;\n    last_selected = null;\n  }\n  if ($$props.datatype === void 0 && $$bindings.datatype && datatype !== void 0)\n    $$bindings.datatype(datatype);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.headers === void 0 && $$bindings.headers && headers !== void 0)\n    $$bindings.headers(headers);\n  if ($$props.values === void 0 && $$bindings.values && values !== void 0)\n    $$bindings.values(values);\n  if ($$props.col_count === void 0 && $$bindings.col_count && col_count !== void 0)\n    $$bindings.col_count(col_count);\n  if ($$props.row_count === void 0 && $$bindings.row_count && row_count !== void 0)\n    $$bindings.row_count(row_count);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.wrap === void 0 && $$bindings.wrap && wrap !== void 0)\n    $$bindings.wrap(wrap);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.max_height === void 0 && $$bindings.max_height && max_height !== void 0)\n    $$bindings.max_height(max_height);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.column_widths === void 0 && $$bindings.column_widths && column_widths !== void 0)\n    $$bindings.column_widths(column_widths);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.stream_handler === void 0 && $$bindings.stream_handler && stream_handler !== void 0)\n    $$bindings.stream_handler(stream_handler);\n  if ($$props.display_value === void 0 && $$bindings.display_value && display_value !== void 0)\n    $$bindings.display_value(display_value);\n  if ($$props.styling === void 0 && $$bindings.styling && styling !== void 0)\n    $$bindings.styling(styling);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      if (!dequal(values, old_val)) {\n        data = process_data(values);\n        old_val = values;\n      }\n    }\n    {\n      {\n        if (selected !== false && !dequal(selected, last_selected)) {\n          const [row, col] = selected;\n          if (!isNaN(row) && !isNaN(col) && data[row]) {\n            dispatch(\"select\", {\n              index: [row, col],\n              value: get_data_at(row, col),\n              row_value: data[row].map((d) => d.value)\n            });\n            last_selected = selected;\n          }\n        }\n      }\n    }\n    {\n      {\n        if (!dequal(headers, old_headers)) {\n          trigger_headers();\n        }\n      }\n    }\n    (data || selected_header) && trigger_change();\n    max = get_max(data);\n    cells[0] && set_cell_widths();\n    {\n      sort_data();\n    }\n    selected_index = !!selected && selected[0];\n    $$rendered = ` <div class=\"${[\"svelte-1o4y3py\", label && label.length !== 0 ? \"label\" : \"\"].join(\" \").trim()}\">${label && label.length !== 0 && show_label ? `<p class=\"svelte-1o4y3py\">${escape(label)}</p>` : ``} <div class=\"${[\n      \"table-wrap svelte-1o4y3py\",\n      (dragging ? \"dragging\" : \"\") + \" \" + (!wrap ? \"no-wrap\" : \"\")\n    ].join(\" \").trim()}\" style=\"${\"height:\" + escape(table_height, true) + \"px\"}\" role=\"grid\" tabindex=\"0\"${add_attribute(\"this\", parent, 0)}><table class=\"${[\"svelte-1o4y3py\", column_widths.length != 0 ? \"fixed-layout\" : \"\"].join(\" \").trim()}\"${add_attribute(\"this\", table, 0)}>${label && label.length !== 0 ? `<caption class=\"sr-only\">${escape(label)}</caption>` : ``} <thead class=\"svelte-1o4y3py\"><tr class=\"svelte-1o4y3py\">${each(_headers, ({ value, id }, i) => {\n      return `<th${add_attribute(\"aria-sort\", get_sort_status(), 0)} class=\"${[\"svelte-1o4y3py\", header_edit === i ? \"editing\" : \"\"].join(\" \").trim()}\"${add_styles({\n        \"width\": column_widths.length ? column_widths[i] : void 0\n      })}><div class=\"cell-wrap svelte-1o4y3py\">${validate_component(EditableCell, \"EditableCell\").$$render(\n        $$result,\n        {\n          value,\n          latex_delimiters,\n          line_breaks,\n          header: true,\n          edit: false,\n          el: null,\n          root\n        },\n        {},\n        {}\n      )} <div class=\"${[\n        \"sort-button \" + escape(sort_direction, true) + \" svelte-1o4y3py\",\n        (sort_by === i ? \"sorted\" : \"\") + \" \" + (sort_by === i && sort_direction === \"des\" ? \"des\" : \"\")\n      ].join(\" \").trim()}\"><svg width=\"1em\" height=\"1em\" viewBox=\"0 0 9 7\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"svelte-1o4y3py\"><path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\"></path></svg> </div></div> </th>`;\n    })}</tr></thead> <tbody><tr class=\"svelte-1o4y3py\">${each(max, ({ value, id }, j) => {\n      return `<td tabindex=\"-1\" class=\"svelte-1o4y3py\"${add_attribute(\"this\", cells[j], 0)}><div class=\"cell-wrap svelte-1o4y3py\">${validate_component(EditableCell, \"EditableCell\").$$render(\n        $$result,\n        {\n          value,\n          latex_delimiters,\n          line_breaks,\n          datatype: Array.isArray(datatype) ? datatype[j] : datatype,\n          edit: false,\n          el: null,\n          root\n        },\n        {},\n        {}\n      )}</div> </td>`;\n    })}</tr></tbody></table> ${validate_component(Upload, \"Upload\").$$render(\n      $$result,\n      {\n        upload,\n        stream_handler,\n        flex: false,\n        center: false,\n        boundedheight: false,\n        disable_click: true,\n        root,\n        dragging\n      },\n      {\n        dragging: ($$value) => {\n          dragging = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${validate_component(VirtualTable, \"VirtualTable\").$$render(\n            $$result,\n            {\n              max_height,\n              selected: selected_index,\n              items: data,\n              actual_height: table_height,\n              table_scrollbar_width: scrollbar_width\n            },\n            {\n              items: ($$value) => {\n                data = $$value;\n                $$settled = false;\n              },\n              actual_height: ($$value) => {\n                table_height = $$value;\n                $$settled = false;\n              },\n              table_scrollbar_width: ($$value) => {\n                scrollbar_width = $$value;\n                $$settled = false;\n              }\n            },\n            {\n              tbody: ({ index, item }) => {\n                return `<tr slot=\"tbody\" class=\"${[\"svelte-1o4y3py\", index % 2 === 0 ? \"row_odd\" : \"\"].join(\" \").trim()}\">${each(item, ({ value, id }, j) => {\n                  return `<td tabindex=\"0\"${add_styles(merge_ssr_styles(escape(styling?.[index]?.[j] || \"\", true), { \"width\": `var(--cell-width-${j})` }))} class=\"${[\n                    \"svelte-1o4y3py\",\n                    (dequal(selected, [index, j]) ? \"focus\" : \"\") + \" \" + (active_cell_menu && active_cell_menu.row === index && active_cell_menu.col === j ? \"menu-active\" : \"\")\n                  ].join(\" \").trim()}\"><div class=\"cell-wrap svelte-1o4y3py\">${validate_component(EditableCell, \"EditableCell\").$$render(\n                    $$result,\n                    {\n                      display_value: display_value?.[index]?.[j],\n                      latex_delimiters,\n                      line_breaks,\n                      editable,\n                      edit: dequal(editing, [index, j]),\n                      datatype: Array.isArray(datatype) ? datatype[j] : datatype,\n                      clear_on_focus,\n                      root,\n                      value: data[index][j].value,\n                      el: els[id].input\n                    },\n                    {\n                      value: ($$value) => {\n                        data[index][j].value = $$value;\n                        $$settled = false;\n                      },\n                      el: ($$value) => {\n                        els[id].input = $$value;\n                        $$settled = false;\n                      }\n                    },\n                    {}\n                  )} ${editable ? `<button class=\"cell-menu-button svelte-1o4y3py\" data-svelte-h=\"svelte-b4o3eq\">⋮\n\t\t\t\t\t\t\t\t\t</button>` : ``}</div> </td>`;\n                })}</tr>`;\n              },\n              thead: () => {\n                return `<tr slot=\"thead\" class=\"svelte-1o4y3py\">${each(_headers, ({ value, id }, i) => {\n                  return `<th${add_attribute(\"aria-sort\", get_sort_status(), 0)} style=\"${\"width: var(--cell-width-\" + escape(i, true) + \");\"}\" class=\"${[\n                    \"svelte-1o4y3py\",\n                    header_edit === i || selected_header === i ? \"focus\" : \"\"\n                  ].join(\" \").trim()}\"><div class=\"cell-wrap svelte-1o4y3py\"><div class=\"header-content svelte-1o4y3py\">${validate_component(EditableCell, \"EditableCell\").$$render(\n                    $$result,\n                    {\n                      latex_delimiters,\n                      line_breaks,\n                      edit: header_edit === i,\n                      select_on_focus,\n                      header: true,\n                      root,\n                      value: _headers[i].value,\n                      el: els[id].input\n                    },\n                    {\n                      value: ($$value) => {\n                        _headers[i].value = $$value;\n                        $$settled = false;\n                      },\n                      el: ($$value) => {\n                        els[id].input = $$value;\n                        $$settled = false;\n                      }\n                    },\n                    {}\n                  )}    <div class=\"${[\n                    \"sort-button \" + escape(sort_direction, true) + \" svelte-1o4y3py\",\n                    (sort_by === i ? \"sorted\" : \"\") + \" \" + (sort_by === i && sort_direction === \"des\" ? \"des\" : \"\")\n                  ].join(\" \").trim()}\"><svg width=\"1em\" height=\"1em\" viewBox=\"0 0 9 7\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"svelte-1o4y3py\"><path d=\"M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z\"></path></svg> </div></div> ${editable ? `<button class=\"cell-menu-button svelte-1o4y3py\" data-svelte-h=\"svelte-1e92k9i\">⋮\n\t\t\t\t\t\t\t\t\t</button>` : ``}</div> </th>`;\n                })}</tr>`;\n              },\n              default: () => {\n                return `${label && label.length !== 0 ? `<caption class=\"sr-only\">${escape(label)}</caption>` : ``}`;\n              }\n            }\n          )}`;\n        }\n      }\n    )}</div></div> ${active_cell_menu !== null ? `${validate_component(CellMenu, \"CellMenu\").$$render(\n      $$result,\n      {\n        i18n,\n        x: active_cell_menu.x,\n        y: active_cell_menu.y,\n        row: active_cell_menu?.row ?? -1,\n        col_count,\n        row_count,\n        on_add_row_above: () => add_row_at(active_cell_menu?.row ?? -1, \"above\"),\n        on_add_row_below: () => add_row_at(active_cell_menu?.row ?? -1, \"below\"),\n        on_add_column_left: () => add_col_at(active_cell_menu?.col ?? -1, \"left\"),\n        on_add_column_right: () => add_col_at(active_cell_menu?.col ?? -1, \"right\")\n      },\n      {},\n      {}\n    )}` : ``} ${active_header_menu !== null ? `${validate_component(CellMenu, \"CellMenu\").$$render(\n      $$result,\n      {\n        i18n,\n        x: active_header_menu.x,\n        y: active_header_menu.y,\n        row: -1,\n        col_count,\n        row_count,\n        on_add_row_above: () => add_row_at(active_cell_menu?.row ?? -1, \"above\"),\n        on_add_row_below: () => add_row_at(active_cell_menu?.row ?? -1, \"below\"),\n        on_add_column_left: () => add_col_at(active_header_menu?.col ?? -1, \"left\"),\n        on_add_column_right: () => add_col_at(active_header_menu?.col ?? -1, \"right\")\n      },\n      {},\n      {}\n    )}` : ``}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst Table$1 = Table;\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { headers = [] } = $$props;\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value = {\n    data: [[\"\", \"\", \"\"]],\n    headers: [\"1\", \"2\", \"3\"],\n    metadata: null\n  } } = $$props;\n  let { value_is_output = false } = $$props;\n  let { col_count } = $$props;\n  let { row_count } = $$props;\n  let { label = null } = $$props;\n  let { show_label = true } = $$props;\n  let { wrap } = $$props;\n  let { datatype } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { root } = $$props;\n  let { line_breaks = true } = $$props;\n  let { column_widths = [] } = $$props;\n  let { gradio } = $$props;\n  let { latex_delimiters } = $$props;\n  let { max_height = void 0 } = $$props;\n  let { loading_status } = $$props;\n  let { interactive } = $$props;\n  let _headers;\n  let display_value;\n  let styling;\n  let values;\n  async function handle_change(data) {\n    let _data = value;\n    _headers = [..._data.headers || headers];\n    values = _data.data ? [..._data.data] : [];\n    display_value = _data?.metadata?.display_value ? [..._data?.metadata?.display_value] : null;\n    styling = !interactive && _data?.metadata?.styling ? [..._data?.metadata?.styling] : null;\n    await tick();\n    gradio.dispatch(\"change\");\n    if (!value_is_output) {\n      gradio.dispatch(\"input\");\n    }\n  }\n  handle_change();\n  afterUpdate(() => {\n    value_is_output = false;\n  });\n  if (Array.isArray(value) && value?.[0]?.length === 0 || value.data?.[0]?.length === 0) {\n    value = {\n      data: [Array(col_count?.[0] || 3).fill(\"\")],\n      headers: Array(col_count?.[0] || 3).fill(\"\").map((_, i) => `${i + 1}`),\n      metadata: null\n    };\n  }\n  if ($$props.headers === void 0 && $$bindings.headers && headers !== void 0)\n    $$bindings.headers(headers);\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.value_is_output === void 0 && $$bindings.value_is_output && value_is_output !== void 0)\n    $$bindings.value_is_output(value_is_output);\n  if ($$props.col_count === void 0 && $$bindings.col_count && col_count !== void 0)\n    $$bindings.col_count(col_count);\n  if ($$props.row_count === void 0 && $$bindings.row_count && row_count !== void 0)\n    $$bindings.row_count(row_count);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.wrap === void 0 && $$bindings.wrap && wrap !== void 0)\n    $$bindings.wrap(wrap);\n  if ($$props.datatype === void 0 && $$bindings.datatype && datatype !== void 0)\n    $$bindings.datatype(datatype);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.column_widths === void 0 && $$bindings.column_widths && column_widths !== void 0)\n    $$bindings.column_widths(column_widths);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.max_height === void 0 && $$bindings.max_height && max_height !== void 0)\n    $$bindings.max_height(max_height);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  return `${validate_component(Block, \"Block\").$$render(\n    $$result,\n    {\n      visible,\n      padding: false,\n      elem_id,\n      elem_classes,\n      container: false,\n      scale,\n      min_width,\n      allow_overflow: false\n    },\n    {},\n    {\n      default: () => {\n        return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${validate_component(Table$1, \"Table\").$$render(\n          $$result,\n          {\n            root,\n            label,\n            show_label,\n            row_count,\n            col_count,\n            values,\n            display_value,\n            styling,\n            headers: _headers,\n            wrap,\n            datatype,\n            latex_delimiters,\n            editable: interactive,\n            max_height,\n            i18n: gradio.i18n,\n            line_breaks,\n            column_widths,\n            upload: (...args) => gradio.client.upload(...args),\n            stream_handler: (...args) => gradio.client.stream(...args)\n          },\n          {},\n          {}\n        )}`;\n      }\n    }\n  )}`;\n});\nexport {\n  Table$1 as BaseDataFrame,\n  default2 as BaseExample,\n  Index as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAMA,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,8dAA8d;AACte,EAAE,GAAG,EAAE,qhGAAqhG;AAC5hG,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,cAAc,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;AACvB,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,EAAE,IAAI,EAAE,KAAK,KAAK,CAAC;AAC7D,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtB,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,MAAM,GAAG,KAAK,CAAC;AACjB,EAAE,OAAO,CAAC,EAAE,IAAI,GAAG,CAAC,2CAA2C,EAAE,CAAC,eAAe,EAAE,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,kCAAkC,EAAE,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,eAAe,EAAE,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,QAAQ,KAAK,MAAM,GAAG,CAAC,uBAAuB,EAAE,KAAK,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,QAAQ,KAAK,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAC5f,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO,EAAE,KAAK,CAAC,cAAc,EAAE;AACrC,MAAM,gBAAgB;AACtB,MAAM,WAAW;AACjB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,aAAa,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC3E,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,0kCAA0kC;AACllC,EAAE,GAAG,EAAE,sgUAAsgU;AAC7gU,CAAC,CAAC;AACF,IAAI,MAAM,GAAG,MAAM,CAAC;AACpB,MAAM,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,qBAAqB,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,cAAc,GAAG,EAAE,CAAC;AAC1B,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC;AACtB,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC;AAGtB,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACd,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,eAAe,GAAG,GAAG,CAAC;AAC5B,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;AACnB,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,MAAM,GAAG,GAAG,UAAU,GAAG,MAAM,CAAC,qBAAqB,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AA0CvE,EAAE,eAAe,iBAAiB,CAAC,CAAC,EAAE;AACtC,IAAI,GAAG,CAAC,YAAY;AACpB,MAAM,IAAI,OAAO,CAAC,KAAK,QAAQ;AAC/B,QAAQ,OAAO;AACf,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,QAAQ,GAAG,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACtE,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AAC9B,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,SAAS,KAAK,MAAM,EAAE;AAChC,QAAQ,MAAM,eAAe,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC;AAC1D,OAAO;AACP,MAAM,IAAI,SAAS,KAAK,UAAU,EAAE;AACpC,QAAQ,MAAM,eAAe,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;AAChE,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,CAAC,EAAE;AACzB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAmB,CAAC;AAC5C,IAAI,IAAgB,CAAC,GAAG,KAAK,EAAE;AAC/B,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,IAAgB,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE;AAClC,MAAM,OAAO,UAAU,CAAC;AACxB,KAAK;AACL,IAAI,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAC;AACnE,IAAI,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;AAC3E,IAAI,IAAI,IAAI,GAAG,YAAY,GAAG,EAAE,EAAE;AAClC,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,YAAY,GAAG,eAAe,EAAE;AAClD,MAAM,OAAO,UAAU,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,eAAe,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE;AACjE,IAAI,MAAM,IAAI,EAAE,CAAC;AACjB,IAAI,MAAM,WAAW,GAAG,cAAc,CAAC;AACvC,IAAI,IAAI,QAAQ,GAAG,KAAK,GAAG,WAAW,CAAC;AACvC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,QAAQ,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,GAAG,WAAW,CAAC;AACxE,KAAK;AACL,IAAI,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AAClD,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,GAAG,EAAE,QAAQ;AACnB,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,GAAG,IAAI;AACb,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC7B,GAAG;AAMH,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,qBAAqB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,qBAAqB,IAAI,qBAAqB,KAAK,KAAK,CAAC;AACtH,IAAI,UAAU,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC;AAC5D,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,eAAe,GAAG,GAAG,CAAC;AACxB,EAAE,WAAW,GAAG,KAAK,CAAC;AAEtB,EAAE;AACF,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,OAAO,GAAG,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;AACxE,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,EAAE,CAAC;AACtC,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,WAAW,CAAC,MAAM,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;AACnG,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,EAAE,CAAC;AACtC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,CAAC,0EAA0E,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,oBAAoB,GAAG,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,yBAAyB,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,4BAA4B,GAAG,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,4BAA4B,GAAG,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,+BAA+B,GAAG,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,qCAAqC,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,4CAA4C,EAAE,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,KAAK;AAC9pB,IAAI,OAAO,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC;AACnF;AACA,KAAK,CAAC,CAAC,CAAC,CAAC;AACT,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,6CAA6C,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,iDAAiD,CAAC,CAAC;AACjJ,CAAC,CAAC,CAAC;AAsDH,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,goJAAgoJ;AACxoJ,EAAE,GAAG,EAAE,wvpCAAwvpC;AAC/vpC,CAAC,CAAC;AACF,SAAS,OAAO,GAAG;AACnB,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACrD,CAAC;AACD,SAAS,OAAO,CAAC,EAAE,EAAE;AACrB,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AAC3B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE;AAClE,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,MAAM,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC;AACvB,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC;AAGtB,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE;AAC/B,IAAI,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACzB,IAAI,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE;AAC9D,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9F,MAAM,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,MAAM,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACvD,QAAQ,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;AAC9B,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAC/C,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;AACzD,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC5B,MAAM,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;AAC5B,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAC7C,MAAM,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;AACzC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,OAAO,EAAE;AACjC,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC;AAC3C,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;AACnR,MAAM,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC;AAC3B,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACvD,MAAM,MAAM,GAAG,GAAG,EAAE,KAAK,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC;AACzD,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACvC,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACrC,IAAI,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAClC,IAAI,cAAc,EAAE,CAAC;AACrB,GAAG;AACH,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;AAClB,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;AACvB,EAAE,eAAe,cAAc,GAAG;AAClC,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACvB,MAAM,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC;AACxD,MAAM,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAC3C,MAAM,QAAQ,EAAE,QAAQ,GAAG,IAAI,GAAG,EAAE,aAAa,EAAE,OAAO,EAAE;AAC5D,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;AACnD,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,cAAc,GAAG,KAAK,CAAC;AAC7B,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAC9B,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAmE9B,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC;AACvB,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;AACjB,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,KAAK,CAAC;AAWZ,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;AACvF,EAAE,IAAI,eAAe,GAAG,CAAC,CAAC;AAwD1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE;AACpC,QAAQ,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACpC,QAAQ,OAAO,GAAG,MAAM,CAAC;AACzB,OAAO;AACP,KAAK;AAgBL,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE;AAC3C,UAAU,eAAe,EAAE,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,CAAC,IAAI,IAAI,eAAe,KAAK,cAAc,EAAE,CAAC;AAClD,IAAI,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAKxB,IAAI,cAAc,GAAG,CAAC,CAAC,QAAQ,CAAe,CAAC;AAC/C,IAAI,UAAU,GAAG,CAAC,aAAa,EAAE,CAAC,gBAAgB,EAAE,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,GAAG,CAAC,0BAA0B,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE;AACtO,MAAM,2BAA2B;AACjC,MAAM,CAAC,QAAQ,GAAG,UAAU,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE,CAAC;AACnE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC,MAAM,IAAI,CAAC,GAAG,cAAc,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,0DAA0D,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK;AACnd,MAAM,OAAO,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC;AACpK,QAAQ,OAAO,EAAE,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACjE,OAAO,CAAC,CAAC,uCAAuC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAC3G,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,KAAK;AACf,UAAU,gBAAgB;AAC1B,UAAU,WAAW;AACrB,UAAU,MAAM,EAAE,IAAI;AACtB,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,EAAE,EAAE,IAAI;AAClB,UAAU,IAAI;AACd,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,aAAa,EAAE;AACvB,QAAQ,cAAc,GAAG,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,iBAAiB;AACzE,QAAQ,CAAC,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,OAAO,KAAK,CAAC,IAAI,cAAc,KAAK,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AACxG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,6MAA6M,CAAC,CAAC;AACxO,KAAK,CAAC,CAAC,gDAAgD,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK;AACzF,MAAM,OAAO,CAAC,wCAAwC,EAAE,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,uCAAuC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAC7L,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,KAAK;AACf,UAAU,gBAAgB;AAC1B,UAAU,WAAW;AACrB,UAAU,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ;AACpE,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,EAAE,EAAE,IAAI;AAClB,UAAU,IAAI;AACd,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,YAAY,CAAC,CAAC;AACtB,KAAK,CAAC,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC5E,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,aAAa,EAAE,IAAI;AAC3B,QAAQ,IAAI;AACZ,QAAQ,QAAQ;AAChB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAC7E,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,UAAU;AACxB,cAAc,QAAQ,EAAE,cAAc;AACtC,cAAc,KAAK,EAAE,IAAI;AACzB,cAAc,aAAa,EAAE,YAAY;AACzC,cAAc,qBAAqB,EAAE,eAAe;AACpD,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,CAAC,OAAO,KAAK;AAClC,gBAAgB,IAAI,GAAG,OAAO,CAAC;AAC/B,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,aAAa,EAAE,CAAC,OAAO,KAAK;AAC1C,gBAAgB,YAAY,GAAG,OAAO,CAAC;AACvC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,qBAAqB,EAAE,CAAC,OAAO,KAAK;AAClD,gBAAgB,eAAe,GAAG,OAAO,CAAC;AAC1C,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK;AAC1C,gBAAgB,OAAO,CAAC,wBAAwB,EAAE,CAAC,gBAAgB,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK;AAC7J,kBAAkB,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE;AACrK,oBAAoB,gBAAgB;AACpC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAuG,EAAE,CAAC;AACjL,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,wCAAwC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AACxI,oBAAoB,QAAQ;AAC5B,oBAAoB;AACpB,sBAAsB,aAAa,EAAE,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AAChE,sBAAsB,gBAAgB;AACtC,sBAAsB,WAAW;AACjC,sBAAsB,QAAQ;AAC9B,sBAAsB,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACvD,sBAAsB,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ;AAChF,sBAAsB,cAAc;AACpC,sBAAsB,IAAI;AAC1B,sBAAsB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;AACjD,sBAAsB,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACvC,qBAAqB;AACrB,oBAAoB;AACpB,sBAAsB,KAAK,EAAE,CAAC,OAAO,KAAK;AAC1C,wBAAwB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;AACvD,wBAAwB,SAAS,GAAG,KAAK,CAAC;AAC1C,uBAAuB;AACvB,sBAAsB,EAAE,EAAE,CAAC,OAAO,KAAK;AACvC,wBAAwB,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;AAChD,wBAAwB,SAAS,GAAG,KAAK,CAAC;AAC1C,uBAAuB;AACvB,qBAAqB;AACrB,oBAAoB,EAAE;AACtB,mBAAmB,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC;AACnC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;AACvC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1B,eAAe;AACf,cAAc,KAAK,EAAE,MAAM;AAC3B,gBAAgB,OAAO,CAAC,wCAAwC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK;AACvG,kBAAkB,OAAO,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE;AACzJ,oBAAoB,gBAAgB;AACpC,oBAAoB,WAAW,KAAK,CAAC,IAAI,eAAe,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE;AAC7E,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,mFAAmF,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AACnL,oBAAoB,QAAQ;AAC5B,oBAAoB;AACpB,sBAAsB,gBAAgB;AACtC,sBAAsB,WAAW;AACjC,sBAAsB,IAAI,EAAE,WAAW,KAAK,CAAC;AAC7C,sBAAsB,eAAe;AACrC,sBAAsB,MAAM,EAAE,IAAI;AAClC,sBAAsB,IAAI;AAC1B,sBAAsB,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK;AAC9C,sBAAsB,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK;AACvC,qBAAqB;AACrB,oBAAoB;AACpB,sBAAsB,KAAK,EAAE,CAAC,OAAO,KAAK;AAC1C,wBAAwB,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;AACpD,wBAAwB,SAAS,GAAG,KAAK,CAAC;AAC1C,uBAAuB;AACvB,sBAAsB,EAAE,EAAE,CAAC,OAAO,KAAK;AACvC,wBAAwB,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;AAChD,wBAAwB,SAAS,GAAG,KAAK,CAAC;AAC1C,uBAAuB;AACvB,qBAAqB;AACrB,oBAAoB,EAAE;AACtB,mBAAmB,CAAC,gBAAgB,EAAE;AACtC,oBAAoB,cAAc,GAAG,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,iBAAiB;AACrF,oBAAoB,CAAC,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,OAAO,KAAK,CAAC,IAAI,cAAc,KAAK,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AACpH,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,wMAAwM,EAAE,QAAQ,GAAG,CAAC;AAC3P,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;AACvC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1B,eAAe;AACf,cAAc,OAAO,EAAE,MAAM;AAC7B,gBAAgB,OAAO,CAAC,EAAE,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrH,eAAe;AACf,aAAa;AACb,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO;AACP,KAAK,CAAC,aAAa,EAgBT,CAAC,CAAC,CAAC,CAAC,EAgBJ,CAAC,CAAC,CAAC,CAAC,CAAC;AACf,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,OAAO,GAAG,MAAM;AACjB,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG;AAChB,IAAI,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACxB,IAAI,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC5B,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG,EAAE,GAAG,OAAO,CAAC;AAChB,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,eAAe,aAAa,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC;AACtB,IAAI,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC;AAC7C,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAC/C,IAAI,aAAa,GAAG,KAAK,EAAE,QAAQ,EAAE,aAAa,GAAG,CAAC,GAAG,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC,GAAG,IAAI,CAAC;AAChG,IAAI,OAAO,GAAG,CAAC,WAAW,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;AAC9F,IAAI,MAAM,IAAI,EAAE,CAAC;AACjB,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE,aAAa,EAAE,CAAC;AAIlB,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK,CAAC,EAAE;AACzF,IAAI,KAAK,GAAG;AACZ,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACjD,MAAM,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5E,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACvD,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO;AACb,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM,cAAc,EAAE,KAAK;AAC3B,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ;AACtO,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI;AAChB,YAAY,KAAK;AACjB,YAAY,UAAU;AACtB,YAAY,SAAS;AACrB,YAAY,SAAS;AACrB,YAAY,MAAM;AAClB,YAAY,aAAa;AACzB,YAAY,OAAO;AACnB,YAAY,OAAO,EAAE,QAAQ;AAC7B,YAAY,IAAI;AAChB,YAAY,QAAQ;AACpB,YAAY,gBAAgB;AAC5B,YAAY,QAAQ,EAAE,WAAW;AACjC,YAAY,UAAU;AACtB,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;AAC7B,YAAY,WAAW;AACvB,YAAY,aAAa;AACzB,YAAY,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC9D,YAAY,cAAc,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACtE,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,CAAC;AACZ,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN,CAAC;;;;"}