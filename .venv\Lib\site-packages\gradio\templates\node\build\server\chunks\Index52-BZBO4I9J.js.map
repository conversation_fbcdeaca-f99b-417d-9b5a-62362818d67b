{"version": 3, "file": "Index52-BZBO4I9J.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index52.js"], "sourcesContent": ["import { create_ssr_component, each, add_attribute, escape, add_styles, merge_ssr_styles, validate_component, globals } from \"svelte/internal\";\nimport { g as get_next_color } from \"./color.js\";\nimport { createEventDispatcher, onMount } from \"svelte\";\nimport { y as colors, B as Block, S as Static, e as BlockLabel, Y as TextHighlight, d as Empty } from \"./client.js\";\nfunction name_to_rgba(name, a, ctx) {\n  if (!ctx) {\n    var canvas = document.createElement(\"canvas\");\n    ctx = canvas.getContext(\"2d\");\n  }\n  ctx.fillStyle = name;\n  ctx.fillRect(0, 0, 1, 1);\n  const [r, g, b] = ctx.getImageData(0, 0, 1, 1).data;\n  ctx.clearRect(0, 0, 1, 1);\n  return `rgba(${r}, ${g}, ${b}, ${255 / a})`;\n}\nfunction correct_color_map(color_map, _color_map, browser, ctx) {\n  for (const col in color_map) {\n    const _c = color_map[col].trim();\n    if (_c in colors) {\n      _color_map[col] = colors[_c];\n    } else {\n      _color_map[col] = {\n        primary: browser ? name_to_rgba(color_map[col], 1, ctx) : color_map[col],\n        secondary: browser ? name_to_rgba(color_map[col], 0.5, ctx) : color_map[col]\n      };\n    }\n  }\n}\nfunction merge_elements(value, mergeMode) {\n  let result = [];\n  let tempStr = null;\n  let tempVal = null;\n  for (const val of value) {\n    if (tempVal === val.class_or_confidence) {\n      tempStr = tempStr ? tempStr + val.token : val.token;\n    } else {\n      if (tempStr !== null) {\n        result.push({\n          token: tempStr,\n          class_or_confidence: tempVal\n        });\n      }\n      tempStr = val.token;\n      tempVal = val.class_or_confidence;\n    }\n  }\n  if (tempStr !== null) {\n    result.push({\n      token: tempStr,\n      class_or_confidence: tempVal\n    });\n  }\n  return result;\n}\nconst css$2 = {\n  code: \".container.svelte-ju12zg.svelte-ju12zg{display:flex;flex-direction:column;gap:var(--spacing-sm);padding:var(--block-padding)}.hl.svelte-ju12zg+.hl.svelte-ju12zg{margin-left:var(--size-1)}.textspan.svelte-ju12zg:last-child>.label.svelte-ju12zg{margin-right:0}.category-legend.svelte-ju12zg.svelte-ju12zg{display:flex;flex-wrap:wrap;gap:var(--spacing-sm);color:black}.category-label.svelte-ju12zg.svelte-ju12zg{cursor:pointer;border-radius:var(--radius-xs);padding-right:var(--size-2);padding-left:var(--size-2);font-weight:var(--weight-semibold)}.color-legend.svelte-ju12zg.svelte-ju12zg{display:flex;justify-content:space-between;border-radius:var(--radius-xs);background:linear-gradient(\\n\t\t\tto right,\\n\t\t\tvar(--color-purple),\\n\t\t\trgba(255, 255, 255, 0),\\n\t\t\tvar(--color-red)\\n\t\t);padding:var(--size-1) var(--size-2);font-weight:var(--weight-semibold)}.textfield.svelte-ju12zg.svelte-ju12zg{box-sizing:border-box;border-radius:var(--radius-xs);background:var(--background-fill-primary);background-color:transparent;max-width:var(--size-full);line-height:var(--scale-4);word-break:break-all}.textspan.svelte-ju12zg.svelte-ju12zg{transition:150ms;border-radius:var(--radius-xs);padding-top:2.5px;padding-right:var(--size-1);padding-bottom:3.5px;padding-left:var(--size-1);color:black}.label.svelte-ju12zg.svelte-ju12zg{transition:150ms;margin-top:1px;border-radius:var(--radius-xs);padding:1px 5px;color:var(--body-text-color);color:white;font-weight:var(--weight-bold);font-size:var(--text-sm);text-transform:uppercase}.text.svelte-ju12zg.svelte-ju12zg{color:black;white-space:pre-wrap}.score-text.svelte-ju12zg .text.svelte-ju12zg{color:var(--body-text-color)}.score-text.svelte-ju12zg.svelte-ju12zg{margin-right:var(--size-1);padding:var(--size-1)}.no-cat.svelte-ju12zg.svelte-ju12zg{color:var(--body-text-color)}.no-label.svelte-ju12zg.svelte-ju12zg{color:var(--body-text-color)}.selectable.svelte-ju12zg.svelte-ju12zg{cursor:pointer}\",\n  map: '{\"version\":3,\"file\":\"StaticHighlightedtext.svelte\",\"sources\":[\"StaticHighlightedtext.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">const browser = typeof document !== \\\\\"undefined\\\\\";\\\\nimport { get_next_color } from \\\\\"@gradio/utils\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport { correct_color_map } from \\\\\"./utils\\\\\";\\\\nexport let value = [];\\\\nexport let show_legend = false;\\\\nexport let show_inline_category = true;\\\\nexport let color_map = {};\\\\nexport let selectable = false;\\\\nlet ctx;\\\\nlet _color_map = {};\\\\nlet active = \\\\\"\\\\\";\\\\nfunction splitTextByNewline(text) {\\\\n    return text.split(\\\\\"\\\\\\\\n\\\\\");\\\\n}\\\\nconst dispatch = createEventDispatcher();\\\\nlet mode;\\\\n$: {\\\\n    if (!color_map) {\\\\n        color_map = {};\\\\n    }\\\\n    if (value.length > 0) {\\\\n        for (let entry of value) {\\\\n            if (entry.class_or_confidence !== null) {\\\\n                if (typeof entry.class_or_confidence === \\\\\"string\\\\\") {\\\\n                    mode = \\\\\"categories\\\\\";\\\\n                    if (!(entry.class_or_confidence in color_map)) {\\\\n                        let color = get_next_color(Object.keys(color_map).length);\\\\n                        color_map[entry.class_or_confidence] = color;\\\\n                    }\\\\n                }\\\\n                else {\\\\n                    mode = \\\\\"scores\\\\\";\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    correct_color_map(color_map, _color_map, browser, ctx);\\\\n}\\\\nfunction handle_mouseover(label) {\\\\n    active = label;\\\\n}\\\\nfunction handle_mouseout() {\\\\n    active = \\\\\"\\\\\";\\\\n}\\\\n<\\/script>\\\\n\\\\n<!-- \\\\n\\\\t@todo victor: try reimplementing without flex (negative margins on container to avoid left margin on linebreak). \\\\n\\\\tIf not possible hijack the copy execution like this:\\\\n\\\\n<svelte:window\\\\n\\\\ton:copy|preventDefault={() => {\\\\n\\\\t\\\\tconst selection =.getSelection()?.toString();\\\\n\\\\t\\\\tconsole.log(selection?.replaceAll(\\\\\"\\\\\\\\n\\\\\", \\\\\" \\\\\"));\\\\n\\\\t}}\\\\n/>\\\\n-->\\\\n\\\\n<div class=\\\\\"container\\\\\">\\\\n\\\\t{#if mode === \\\\\"categories\\\\\"}\\\\n\\\\t\\\\t{#if show_legend}\\\\n\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"category-legend\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"highlighted-text:category-legend\\\\\"\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#each Object.entries(_color_map) as [category, color], i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<!-- TODO: fix -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-no-static-element-interactions -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseover={() => handle_mouseover(category)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:focus={() => handle_mouseover(category)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseout={() => handle_mouseout()}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:blur={() => handle_mouseout()}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"category-label\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle={\\\\\"background-color:\\\\\" + color.secondary}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{category}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<div class=\\\\\"textfield\\\\\">\\\\n\\\\t\\\\t\\\\t{#each value as v, i}\\\\n\\\\t\\\\t\\\\t\\\\t{#each splitTextByNewline(v.token) as line, j}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if line.trim() !== \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- TODO: fix -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-no-static-element-interactions -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-click-events-have-key-events-->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"textspan\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:background-color={v.class_or_confidence === null ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(active && active !== v.class_or_confidence)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? \\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: _color_map[v.class_or_confidence].secondary}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:no-cat={v.class_or_confidence === null ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(active && active !== v.class_or_confidence)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:hl={v.class_or_confidence !== null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:selectable\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"select\\\\\", {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tindex: i,\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue: [v.token, v.class_or_confidence]\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t});\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:no-label={v.class_or_confidence === null ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t!_color_map[v.class_or_confidence]}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"text\\\\\">{line}</span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if !show_legend && show_inline_category && v.class_or_confidence !== null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t&nbsp;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"label\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:background-color={v.class_or_confidence === null ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(active && active !== v.class_or_confidence)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? \\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: _color_map[v.class_or_confidence].primary}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{v.class_or_confidence}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if j < splitTextByNewline(v.token).length - 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<br />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t{#if show_legend}\\\\n\\\\t\\\\t\\\\t<div class=\\\\\"color-legend\\\\\" data-testid=\\\\\"highlighted-text:color-legend\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t<span>-1</span>\\\\n\\\\t\\\\t\\\\t\\\\t<span>0</span>\\\\n\\\\t\\\\t\\\\t\\\\t<span>+1</span>\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<div class=\\\\\"textfield\\\\\" data-testid=\\\\\"highlighted-text:textfield\\\\\">\\\\n\\\\t\\\\t\\\\t{#each value as v}\\\\n\\\\t\\\\t\\\\t\\\\t{@const score =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ttypeof v.class_or_confidence === \\\\\"string\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? parseInt(v.class_or_confidence)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: v.class_or_confidence}\\\\n\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"textspan score-text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstyle={\\\\\"background-color: rgba(\\\\\" +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(score && score < 0\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? \\\\\"128, 90, 213,\\\\\" + -score\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: \\\\\"239, 68, 60,\\\\\" + score) +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\")\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"text\\\\\">{v.token}</span>\\\\n\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tpadding: var(--block-padding);\\\\n\\\\t}\\\\n\\\\t.hl + .hl {\\\\n\\\\t\\\\tmargin-left: var(--size-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.textspan:last-child > .label {\\\\n\\\\t\\\\tmargin-right: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.category-legend {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tcolor: black;\\\\n\\\\t}\\\\n\\\\n\\\\t.category-label {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tpadding-right: var(--size-2);\\\\n\\\\t\\\\tpadding-left: var(--size-2);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t}\\\\n\\\\n\\\\t.color-legend {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tbackground: linear-gradient(\\\\n\\\\t\\\\t\\\\tto right,\\\\n\\\\t\\\\t\\\\tvar(--color-purple),\\\\n\\\\t\\\\t\\\\trgba(255, 255, 255, 0),\\\\n\\\\t\\\\t\\\\tvar(--color-red)\\\\n\\\\t\\\\t);\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t}\\\\n\\\\n\\\\t.textfield {\\\\n\\\\t\\\\tbox-sizing: border-box;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tbackground: var(--background-fill-primary);\\\\n\\\\t\\\\tbackground-color: transparent;\\\\n\\\\t\\\\tmax-width: var(--size-full);\\\\n\\\\t\\\\tline-height: var(--scale-4);\\\\n\\\\t\\\\tword-break: break-all;\\\\n\\\\t}\\\\n\\\\n\\\\t.textspan {\\\\n\\\\t\\\\ttransition: 150ms;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tpadding-top: 2.5px;\\\\n\\\\t\\\\tpadding-right: var(--size-1);\\\\n\\\\t\\\\tpadding-bottom: 3.5px;\\\\n\\\\t\\\\tpadding-left: var(--size-1);\\\\n\\\\t\\\\tcolor: black;\\\\n\\\\t}\\\\n\\\\n\\\\t.label {\\\\n\\\\t\\\\ttransition: 150ms;\\\\n\\\\t\\\\tmargin-top: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tpadding: 1px 5px;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t\\\\tfont-weight: var(--weight-bold);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\ttext-transform: uppercase;\\\\n\\\\t}\\\\n\\\\n\\\\t.text {\\\\n\\\\t\\\\tcolor: black;\\\\n\\\\t\\\\twhite-space: pre-wrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.score-text .text {\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.score-text {\\\\n\\\\t\\\\tmargin-right: var(--size-1);\\\\n\\\\t\\\\tpadding: var(--size-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.no-cat {\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.no-label {\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAiKC,sCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,OAAO,CAAE,IAAI,eAAe,CAC7B,CACA,iBAAG,CAAG,iBAAI,CACT,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,uBAAS,WAAW,CAAG,oBAAO,CAC7B,YAAY,CAAE,CACf,CAEA,4CAAiB,CAChB,OAAO,CAAE,IAAI,CACb,SAAS,CAAE,IAAI,CACf,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,KAAK,CAAE,KACR,CAEA,2CAAgB,CACf,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,WAAW,CAAE,IAAI,iBAAiB,CACnC,CAEA,yCAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE;AACd,GAAG,EAAE,CAAC,KAAK,CAAC;AACZ,GAAG,IAAI,cAAc,CAAC,CAAC;AACvB,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,GAAG,IAAI,WAAW,CAAC;AACnB,GAAG,CACD,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,WAAW,CAAE,IAAI,iBAAiB,CACnC,CAEA,sCAAW,CACV,UAAU,CAAE,UAAU,CACtB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,gBAAgB,CAAE,WAAW,CAC7B,SAAS,CAAE,IAAI,WAAW,CAAC,CAC3B,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,UAAU,CAAE,SACb,CAEA,qCAAU,CACT,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,cAAc,CAAE,KAAK,CACrB,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,KAAK,CAAE,KACR,CAEA,kCAAO,CACN,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,GAAG,CACf,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,IAAI,aAAa,CAAC,CAC/B,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,cAAc,CAAE,SACjB,CAEA,iCAAM,CACL,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,QACd,CAEA,yBAAW,CAAC,mBAAM,CACjB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,uCAAY,CACX,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,OAAO,CAAE,IAAI,QAAQ,CACtB,CAEA,mCAAQ,CACP,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,qCAAU,CACT,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,uCAAY,CACX,MAAM,CAAE,OACT\"}'\n};\nfunction splitTextByNewline$1(text) {\n  return text.split(\"\\n\");\n}\nconst StaticHighlightedtext = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  const browser = typeof document !== \"undefined\";\n  let { value = [] } = $$props;\n  let { show_legend = false } = $$props;\n  let { show_inline_category = true } = $$props;\n  let { color_map = {} } = $$props;\n  let { selectable = false } = $$props;\n  let ctx;\n  let _color_map = {};\n  let active = \"\";\n  createEventDispatcher();\n  let mode;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.show_legend === void 0 && $$bindings.show_legend && show_legend !== void 0)\n    $$bindings.show_legend(show_legend);\n  if ($$props.show_inline_category === void 0 && $$bindings.show_inline_category && show_inline_category !== void 0)\n    $$bindings.show_inline_category(show_inline_category);\n  if ($$props.color_map === void 0 && $$bindings.color_map && color_map !== void 0)\n    $$bindings.color_map(color_map);\n  if ($$props.selectable === void 0 && $$bindings.selectable && selectable !== void 0)\n    $$bindings.selectable(selectable);\n  $$result.css.add(css$2);\n  {\n    {\n      if (!color_map) {\n        color_map = {};\n      }\n      if (value.length > 0) {\n        for (let entry of value) {\n          if (entry.class_or_confidence !== null) {\n            if (typeof entry.class_or_confidence === \"string\") {\n              mode = \"categories\";\n              if (!(entry.class_or_confidence in color_map)) {\n                let color = get_next_color(Object.keys(color_map).length);\n                color_map[entry.class_or_confidence] = color;\n              }\n            } else {\n              mode = \"scores\";\n            }\n          }\n        }\n      }\n      correct_color_map(color_map, _color_map, browser, ctx);\n    }\n  }\n  return ` <div class=\"container svelte-ju12zg\">${mode === \"categories\" ? `${show_legend ? `<div class=\"category-legend svelte-ju12zg\" data-testid=\"highlighted-text:category-legend\">${each(Object.entries(_color_map), ([category, color], i) => {\n    return `  <div class=\"category-label svelte-ju12zg\"${add_attribute(\"style\", \"background-color:\" + color.secondary, 0)}>${escape(category)} </div>`;\n  })}</div>` : ``} <div class=\"textfield svelte-ju12zg\">${each(value, (v, i) => {\n    return `${each(splitTextByNewline$1(v.token), (line, j) => {\n      return `${line.trim() !== \"\" ? `   <span class=\"${[\n        \"textspan svelte-ju12zg\",\n        (v.class_or_confidence === null || active ? \"no-cat\" : \"\") + \" \" + (v.class_or_confidence !== null ? \"hl\" : \"\") + \" \" + (selectable ? \"selectable\" : \"\")\n      ].join(\" \").trim()}\"${add_styles({\n        \"background-color\": v.class_or_confidence === null || active ? \"\" : _color_map[v.class_or_confidence].secondary\n      })}><span class=\"${[\n        \"text svelte-ju12zg\",\n        v.class_or_confidence === null || !_color_map[v.class_or_confidence] ? \"no-label\" : \"\"\n      ].join(\" \").trim()}\">${escape(line)}</span> ${!show_legend && show_inline_category && v.class_or_confidence !== null ? ` \n\t\t\t\t\t\t\t\t<span class=\"label svelte-ju12zg\"${add_styles({\n        \"background-color\": v.class_or_confidence === null || active ? \"\" : _color_map[v.class_or_confidence].primary\n      })}>${escape(v.class_or_confidence)} </span>` : ``} </span>` : ``} ${j < splitTextByNewline$1(v.token).length - 1 ? `<br>` : ``}`;\n    })}`;\n  })}</div>` : `${show_legend ? `<div class=\"color-legend svelte-ju12zg\" data-testid=\"highlighted-text:color-legend\" data-svelte-h=\"svelte-mv3vmx\"><span>-1</span> <span>0</span> <span>+1</span></div>` : ``} <div class=\"textfield svelte-ju12zg\" data-testid=\"highlighted-text:textfield\">${each(value, (v) => {\n    let score = typeof v.class_or_confidence === \"string\" ? parseInt(v.class_or_confidence) : v.class_or_confidence;\n    return ` <span class=\"textspan score-text svelte-ju12zg\"${add_attribute(\n      \"style\",\n      \"background-color: rgba(\" + (score && score < 0 ? \"128, 90, 213,\" + -score : \"239, 68, 60,\" + score) + \")\",\n      0\n    )}><span class=\"text svelte-ju12zg\">${escape(v.token)}</span> </span>`;\n  })}</div>`} </div>`;\n});\nconst StaticHighlightedText = StaticHighlightedtext;\nconst css$1 = {\n  code: \".label-input.svelte-1cag2po{transition:150ms;margin-top:1px;margin-right:calc(var(--size-1));border-radius:var(--radius-xs);padding:1px 5px;color:black;font-weight:var(--weight-bold);font-size:var(--text-sm);text-transform:uppercase;line-height:1;color:white}.label-input.svelte-1cag2po::placeholder{color:rgba(1, 1, 1, 0.5)}\",\n  map: '{\"version\":3,\"file\":\"LabelInput.svelte\",\"sources\":[\"LabelInput.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let category;\\\\nexport let active;\\\\nexport let labelToEdit;\\\\nexport let indexOfLabel;\\\\nexport let text;\\\\nexport let handleValueChange;\\\\nexport let isScoresMode = false;\\\\nexport let _color_map;\\\\nlet _input_value = category;\\\\nfunction handleInput(e) {\\\\n    let target = e.target;\\\\n    if (target) {\\\\n        _input_value = target.value;\\\\n    }\\\\n}\\\\nfunction updateLabelValue(e, elementIndex, text2) {\\\\n    let target = e.target;\\\\n    value = [\\\\n        ...value.slice(0, elementIndex),\\\\n        {\\\\n            token: text2,\\\\n            class_or_confidence: target.value === \\\\\"\\\\\" ? null : isScoresMode ? Number(target.value) : target.value\\\\n        },\\\\n        ...value.slice(elementIndex + 1)\\\\n    ];\\\\n    handleValueChange();\\\\n}\\\\nfunction clearPlaceHolderOnFocus(e) {\\\\n    let target = e.target;\\\\n    if (target && target.placeholder)\\\\n        target.placeholder = \\\\\"\\\\\";\\\\n}\\\\n<\\/script>\\\\n\\\\n<!-- svelte-ignore a11y-autofocus -->\\\\n<!-- autofocus should not be disorienting for a screen reader users\\\\nas input is only rendered once a new label is created -->\\\\n{#if !isScoresMode}\\\\n\\\\t<input\\\\n\\\\t\\\\tclass=\\\\\"label-input\\\\\"\\\\n\\\\t\\\\tautofocus\\\\n\\\\t\\\\tid={`label-input-${indexOfLabel}`}\\\\n\\\\t\\\\ttype=\\\\\"text\\\\\"\\\\n\\\\t\\\\tplaceholder=\\\\\"label\\\\\"\\\\n\\\\t\\\\tvalue={category}\\\\n\\\\t\\\\tstyle:background-color={category === null || (active && active !== category)\\\\n\\\\t\\\\t\\\\t? \\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t: _color_map[category].primary}\\\\n\\\\t\\\\tstyle:width={_input_value\\\\n\\\\t\\\\t\\\\t? _input_value.toString()?.length + 4 + \\\\\"ch\\\\\"\\\\n\\\\t\\\\t\\\\t: \\\\\"8ch\\\\\"}\\\\n\\\\t\\\\ton:input={handleInput}\\\\n\\\\t\\\\ton:blur={(e) => updateLabelValue(e, indexOfLabel, text)}\\\\n\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\tif (e.key === \\\\\"Enter\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\tupdateLabelValue(e, indexOfLabel, text);\\\\n\\\\t\\\\t\\\\t\\\\tlabelToEdit = -1;\\\\n\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t}}\\\\n\\\\t\\\\ton:focus={clearPlaceHolderOnFocus}\\\\n\\\\t/>\\\\n{:else}\\\\n\\\\t<input\\\\n\\\\t\\\\tclass=\\\\\"label-input\\\\\"\\\\n\\\\t\\\\tautofocus\\\\n\\\\t\\\\ttype=\\\\\"number\\\\\"\\\\n\\\\t\\\\tstep=\\\\\"0.1\\\\\"\\\\n\\\\t\\\\tstyle={\\\\\"background-color: rgba(\\\\\" +\\\\n\\\\t\\\\t\\\\t(typeof category === \\\\\"number\\\\\" && category < 0\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"128, 90, 213,\\\\\" + -category\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"239, 68, 60,\\\\\" + category) +\\\\n\\\\t\\\\t\\\\t\\\\\")\\\\\"}\\\\n\\\\t\\\\tvalue={category}\\\\n\\\\t\\\\tstyle:width=\\\\\"7ch\\\\\"\\\\n\\\\t\\\\ton:input={handleInput}\\\\n\\\\t\\\\ton:blur={(e) => updateLabelValue(e, indexOfLabel, text)}\\\\n\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\tif (e.key === \\\\\"Enter\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\tupdateLabelValue(e, indexOfLabel, text);\\\\n\\\\t\\\\t\\\\t\\\\tlabelToEdit = -1;\\\\n\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t}}\\\\n\\\\t/>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.label-input {\\\\n\\\\t\\\\ttransition: 150ms;\\\\n\\\\t\\\\tmargin-top: 1px;\\\\n\\\\t\\\\tmargin-right: calc(var(--size-1));\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tpadding: 1px 5px;\\\\n\\\\t\\\\tcolor: black;\\\\n\\\\t\\\\tfont-weight: var(--weight-bold);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\ttext-transform: uppercase;\\\\n\\\\t\\\\tline-height: 1;\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t}\\\\n\\\\n\\\\t.label-input::placeholder {\\\\n\\\\t\\\\tcolor: rgba(1, 1, 1, 0.5);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAuFC,2BAAa,CACZ,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,GAAG,CACf,YAAY,CAAE,KAAK,IAAI,QAAQ,CAAC,CAAC,CACjC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,IAAI,aAAa,CAAC,CAC/B,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,cAAc,CAAE,SAAS,CACzB,WAAW,CAAE,CAAC,CACd,KAAK,CAAE,KACR,CAEA,2BAAY,aAAc,CACzB,KAAK,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CACzB\"}'\n};\nconst LabelInput = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { category } = $$props;\n  let { active } = $$props;\n  let { labelToEdit } = $$props;\n  let { indexOfLabel } = $$props;\n  let { text } = $$props;\n  let { handleValueChange } = $$props;\n  let { isScoresMode = false } = $$props;\n  let { _color_map } = $$props;\n  let _input_value = category;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.category === void 0 && $$bindings.category && category !== void 0)\n    $$bindings.category(category);\n  if ($$props.active === void 0 && $$bindings.active && active !== void 0)\n    $$bindings.active(active);\n  if ($$props.labelToEdit === void 0 && $$bindings.labelToEdit && labelToEdit !== void 0)\n    $$bindings.labelToEdit(labelToEdit);\n  if ($$props.indexOfLabel === void 0 && $$bindings.indexOfLabel && indexOfLabel !== void 0)\n    $$bindings.indexOfLabel(indexOfLabel);\n  if ($$props.text === void 0 && $$bindings.text && text !== void 0)\n    $$bindings.text(text);\n  if ($$props.handleValueChange === void 0 && $$bindings.handleValueChange && handleValueChange !== void 0)\n    $$bindings.handleValueChange(handleValueChange);\n  if ($$props.isScoresMode === void 0 && $$bindings.isScoresMode && isScoresMode !== void 0)\n    $$bindings.isScoresMode(isScoresMode);\n  if ($$props._color_map === void 0 && $$bindings._color_map && _color_map !== void 0)\n    $$bindings._color_map(_color_map);\n  $$result.css.add(css$1);\n  return `  ${!isScoresMode ? `<input class=\"label-input svelte-1cag2po\" autofocus${add_attribute(\"id\", `label-input-${indexOfLabel}`, 0)} type=\"text\" placeholder=\"label\"${add_attribute(\"value\", category, 0)}${add_styles({\n    \"background-color\": category === null || active && active !== category ? \"\" : _color_map[category].primary,\n    \"width\": _input_value ? _input_value.toString()?.length + 4 + \"ch\" : \"8ch\"\n  })}>` : `<input class=\"label-input svelte-1cag2po\" autofocus type=\"number\" step=\"0.1\"${add_styles(merge_ssr_styles(\n    escape(\n      \"background-color: rgba(\" + (typeof category === \"number\" && category < 0 ? \"128, 90, 213,\" + -category : \"239, 68, 60,\" + category) + \")\",\n      true\n    ),\n    { \"width\": `7ch` }\n  ))}${add_attribute(\"value\", category, 0)}>`}`;\n});\nconst css = {\n  code: \".label-clear-button.svelte-1ozsnjl.svelte-1ozsnjl{display:none;border-radius:var(--radius-xs);padding-top:2.5px;padding-right:var(--size-1);padding-bottom:3.5px;padding-left:var(--size-1);color:black;background-color:var(--background-fill-secondary);user-select:none;position:relative;left:-3px;border-radius:0 var(--radius-xs) var(--radius-xs) 0;color:var(--block-label-text-color)}.text-class_or_confidence-container.svelte-1ozsnjl:hover .label-clear-button.svelte-1ozsnjl,.text-class_or_confidence-container.svelte-1ozsnjl:focus-within .label-clear-button.svelte-1ozsnjl,.score-text-container.svelte-1ozsnjl:hover .label-clear-button.svelte-1ozsnjl,.score-text-container.svelte-1ozsnjl:focus-within .label-clear-button.svelte-1ozsnjl{display:inline}.text-class_or_confidence-container.svelte-1ozsnjl:hover .textspan.hl.svelte-1ozsnjl,.text-class_or_confidence-container.svelte-1ozsnjl:focus-within .textspan.hl.svelte-1ozsnjl,.score-text.svelte-1ozsnjl.svelte-1ozsnjl:hover{border-radius:var(--radius-xs) 0 0 var(--radius-xs)}.container.svelte-1ozsnjl.svelte-1ozsnjl{display:flex;flex-direction:column;gap:var(--spacing-sm);padding:var(--block-padding)}.hl.svelte-1ozsnjl.svelte-1ozsnjl{margin-left:var(--size-1);transition:background-color 0.3s;user-select:none}.textspan.svelte-1ozsnjl:last-child>.label.svelte-1ozsnjl{margin-right:0}.class_or_confidence-legend.svelte-1ozsnjl.svelte-1ozsnjl{display:flex;flex-wrap:wrap;gap:var(--spacing-sm);color:black}.class_or_confidence-label.svelte-1ozsnjl.svelte-1ozsnjl{cursor:pointer;border-radius:var(--radius-xs);padding-right:var(--size-2);padding-left:var(--size-2);font-weight:var(--weight-semibold)}.color-legend.svelte-1ozsnjl.svelte-1ozsnjl{display:flex;justify-content:space-between;border-radius:var(--radius-xs);background:linear-gradient(\\n\t\t\tto right,\\n\t\t\tvar(--color-purple),\\n\t\t\trgba(255, 255, 255, 0),\\n\t\t\tvar(--color-red)\\n\t\t);padding:var(--size-1) var(--size-2);font-weight:var(--weight-semibold)}.textfield.svelte-1ozsnjl.svelte-1ozsnjl{box-sizing:border-box;border-radius:var(--radius-xs);background:var(--background-fill-primary);background-color:transparent;max-width:var(--size-full);line-height:var(--scale-4);word-break:break-all}.textspan.svelte-1ozsnjl.svelte-1ozsnjl{transition:150ms;border-radius:var(--radius-xs);padding-top:2.5px;padding-right:var(--size-1);padding-bottom:3.5px;padding-left:var(--size-1);color:black;cursor:text}.label.svelte-1ozsnjl.svelte-1ozsnjl{transition:150ms;margin-top:1px;border-radius:var(--radius-xs);padding:1px 5px;color:var(--body-text-color);color:white;font-weight:var(--weight-bold);font-size:var(--text-sm);text-transform:uppercase;user-select:none}.text.svelte-1ozsnjl.svelte-1ozsnjl{color:black;white-space:pre-wrap}.textspan.hl.svelte-1ozsnjl.svelte-1ozsnjl{user-select:none}.score-text-container.svelte-1ozsnjl.svelte-1ozsnjl{margin-right:var(--size-1)}.score-text.svelte-1ozsnjl .text.svelte-1ozsnjl{color:var(--body-text-color)}.no-cat.svelte-1ozsnjl.svelte-1ozsnjl{color:var(--body-text-color)}.no-label.svelte-1ozsnjl.svelte-1ozsnjl{color:var(--body-text-color);user-select:text}.selectable.svelte-1ozsnjl.svelte-1ozsnjl{cursor:text;user-select:text}\",\n  map: '{\"version\":3,\"file\":\"InteractiveHighlightedtext.svelte\",\"sources\":[\"InteractiveHighlightedtext.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">const browser = typeof document !== \\\\\"undefined\\\\\";\\\\nimport { get_next_color } from \\\\\"@gradio/utils\\\\\";\\\\nimport { createEventDispatcher, onMount } from \\\\\"svelte\\\\\";\\\\nimport { correct_color_map, merge_elements } from \\\\\"./utils\\\\\";\\\\nimport LabelInput from \\\\\"./LabelInput.svelte\\\\\";\\\\nexport let value = [];\\\\nexport let show_legend = false;\\\\nexport let color_map = {};\\\\nexport let selectable = false;\\\\nlet activeElementIndex = -1;\\\\nlet ctx;\\\\nlet _color_map = {};\\\\nlet active = \\\\\"\\\\\";\\\\nlet selection;\\\\nlet labelToEdit = -1;\\\\nonMount(() => {\\\\n    const mouseUpHandler = () => {\\\\n        selection = window.getSelection();\\\\n        handleSelectionComplete();\\\\n        window.removeEventListener(\\\\\"mouseup\\\\\", mouseUpHandler);\\\\n    };\\\\n    window.addEventListener(\\\\\"mousedown\\\\\", () => {\\\\n        window.addEventListener(\\\\\"mouseup\\\\\", mouseUpHandler);\\\\n    });\\\\n});\\\\nasync function handleTextSelected(startIndex, endIndex) {\\\\n    if (selection?.toString() && activeElementIndex !== -1 && value[activeElementIndex].token.toString().includes(selection.toString())) {\\\\n        const tempFlag = Symbol();\\\\n        const str = value[activeElementIndex].token;\\\\n        const [before, selected, after] = [\\\\n            str.substring(0, startIndex),\\\\n            str.substring(startIndex, endIndex),\\\\n            str.substring(endIndex)\\\\n        ];\\\\n        let tempValue = [\\\\n            ...value.slice(0, activeElementIndex),\\\\n            { token: before, class_or_confidence: null },\\\\n            {\\\\n                token: selected,\\\\n                class_or_confidence: mode === \\\\\"scores\\\\\" ? 1 : \\\\\"label\\\\\",\\\\n                flag: tempFlag\\\\n            },\\\\n            // add a temp flag to the new highlighted text element\\\\n            { token: after, class_or_confidence: null },\\\\n            ...value.slice(activeElementIndex + 1)\\\\n        ];\\\\n        labelToEdit = tempValue.findIndex(({ flag }) => flag === tempFlag);\\\\n        tempValue = tempValue.filter((item) => item.token.trim() !== \\\\\"\\\\\");\\\\n        value = tempValue.map(({ flag, ...rest }) => rest);\\\\n        handleValueChange();\\\\n        document.getElementById(`label-input-${labelToEdit}`)?.focus();\\\\n    }\\\\n}\\\\nconst dispatch = createEventDispatcher();\\\\nfunction splitTextByNewline(text) {\\\\n    return text.split(\\\\\"\\\\\\\\n\\\\\");\\\\n}\\\\nfunction removeHighlightedText(index) {\\\\n    if (!value || index < 0 || index >= value.length)\\\\n        return;\\\\n    value[index].class_or_confidence = null;\\\\n    value = merge_elements(value, \\\\\"equal\\\\\");\\\\n    handleValueChange();\\\\n    window.getSelection()?.empty();\\\\n}\\\\nfunction handleValueChange() {\\\\n    dispatch(\\\\\"change\\\\\", value);\\\\n    labelToEdit = -1;\\\\n    if (show_legend) {\\\\n        color_map = {};\\\\n        _color_map = {};\\\\n    }\\\\n}\\\\nlet mode;\\\\n$: {\\\\n    if (!color_map) {\\\\n        color_map = {};\\\\n    }\\\\n    if (value.length > 0) {\\\\n        for (let entry of value) {\\\\n            if (entry.class_or_confidence !== null) {\\\\n                if (typeof entry.class_or_confidence === \\\\\"string\\\\\") {\\\\n                    mode = \\\\\"categories\\\\\";\\\\n                    if (!(entry.class_or_confidence in color_map)) {\\\\n                        let color = get_next_color(Object.keys(color_map).length);\\\\n                        color_map[entry.class_or_confidence] = color;\\\\n                    }\\\\n                }\\\\n                else {\\\\n                    mode = \\\\\"scores\\\\\";\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    correct_color_map(color_map, _color_map, browser, ctx);\\\\n}\\\\nfunction handle_mouseover(label) {\\\\n    active = label;\\\\n}\\\\nfunction handle_mouseout() {\\\\n    active = \\\\\"\\\\\";\\\\n}\\\\nasync function handleKeydownSelection(event) {\\\\n    selection = window.getSelection();\\\\n    if (event.key === \\\\\"Enter\\\\\") {\\\\n        handleSelectionComplete();\\\\n    }\\\\n}\\\\nfunction handleSelectionComplete() {\\\\n    if (selection && selection?.toString().trim() !== \\\\\"\\\\\") {\\\\n        const textBeginningIndex = selection.getRangeAt(0).startOffset;\\\\n        const textEndIndex = selection.getRangeAt(0).endOffset;\\\\n        handleTextSelected(textBeginningIndex, textEndIndex);\\\\n    }\\\\n}\\\\nfunction handleSelect(i, text, class_or_confidence) {\\\\n    dispatch(\\\\\"select\\\\\", {\\\\n        index: i,\\\\n        value: [text, class_or_confidence]\\\\n    });\\\\n}\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"container\\\\\">\\\\n\\\\t{#if mode === \\\\\"categories\\\\\"}\\\\n\\\\t\\\\t{#if show_legend}\\\\n\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"class_or_confidence-legend\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"highlighted-text:class_or_confidence-legend\\\\\"\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if _color_map}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each Object.entries(_color_map) as [class_or_confidence, color], i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-roledescription=\\\\\"Categories of highlighted text. Hover to see text with this class_or_confidence highlighted.\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseover={() => handle_mouseover(class_or_confidence)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:focus={() => handle_mouseover(class_or_confidence)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseout={() => handle_mouseout()}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:blur={() => handle_mouseout()}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"class_or_confidence-label\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle={\\\\\"background-color:\\\\\" + color.secondary}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{class_or_confidence}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t<div class=\\\\\"textfield\\\\\">\\\\n\\\\t\\\\t\\\\t{#each value as { token, class_or_confidence }, i}\\\\n\\\\t\\\\t\\\\t\\\\t{#each splitTextByNewline(token) as line, j}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if line.trim() !== \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"text-class_or_confidence-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"textspan\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:background-color={class_or_confidence === null ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(active && active !== class_or_confidence)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? \\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: class_or_confidence && _color_map[class_or_confidence]\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? _color_map[class_or_confidence].secondary\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:no-cat={class_or_confidence === null ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(active && active !== class_or_confidence)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:hl={class_or_confidence !== null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:selectable\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (class_or_confidence !== null) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandleSelect(i, token, class_or_confidence);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (class_or_confidence !== null) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabelToEdit = i;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandleSelect(i, token, class_or_confidence);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t} else {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandleKeydownSelection(e);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:focus={() => (activeElementIndex = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseover={() => (activeElementIndex = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:no-label={class_or_confidence === null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={(e) => handleKeydownSelection(e)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:focus={() => (activeElementIndex = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseover={() => (activeElementIndex = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => (labelToEdit = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\">{line}</span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if !show_legend && class_or_confidence !== null && labelToEdit !== i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tid={`label-tag-${i}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"label\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:background-color={class_or_confidence === null ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(active && active !== class_or_confidence)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? \\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: _color_map[class_or_confidence].primary}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => (labelToEdit = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={() => (labelToEdit = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{class_or_confidence}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if labelToEdit === i && class_or_confidence !== null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t&nbsp;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<LabelInput\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:value\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{labelToEdit}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tcategory={class_or_confidence}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{active}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_color_map}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tindexOfLabel={i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttext={token}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{handleValueChange}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if class_or_confidence !== null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"label-clear-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-roledescription=\\\\\"Remove label from text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => removeHighlightedText(i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={(event) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (event.key === \\\\\"Enter\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tremoveHighlightedText(i);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>×\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if j < splitTextByNewline(token).length - 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<br />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t{#if show_legend}\\\\n\\\\t\\\\t\\\\t<div class=\\\\\"color-legend\\\\\" data-testid=\\\\\"highlighted-text:color-legend\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t<span>-1</span>\\\\n\\\\t\\\\t\\\\t\\\\t<span>0</span>\\\\n\\\\t\\\\t\\\\t\\\\t<span>+1</span>\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t<div class=\\\\\"textfield\\\\\" data-testid=\\\\\"highlighted-text:textfield\\\\\">\\\\n\\\\t\\\\t\\\\t{#each value as { token, class_or_confidence }, i}\\\\n\\\\t\\\\t\\\\t\\\\t{@const score =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ttypeof class_or_confidence === \\\\\"string\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? parseInt(class_or_confidence)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: class_or_confidence}\\\\n\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"score-text-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"textspan score-text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:no-cat={class_or_confidence === null ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(active && active !== class_or_confidence)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:hl={class_or_confidence !== null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseover={() => (activeElementIndex = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:focus={() => (activeElementIndex = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => (labelToEdit = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (e.key === \\\\\"Enter\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabelToEdit = i;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle={\\\\\"background-color: rgba(\\\\\" +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(score && score < 0\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? \\\\\"128, 90, 213,\\\\\" + -score\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: \\\\\"239, 68, 60,\\\\\" + score) +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\")\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"text\\\\\">{token}</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if class_or_confidence && labelToEdit === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<LabelInput\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:value\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{labelToEdit}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_color_map}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tcategory={class_or_confidence}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{active}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tindexOfLabel={i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttext={token}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{handleValueChange}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tisScoresMode\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if class_or_confidence && activeElementIndex === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"label-clear-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-roledescription=\\\\\"Remove label from text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => removeHighlightedText(i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={(event) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (event.key === \\\\\"Enter\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tremoveHighlightedText(i);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>×\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.label-clear-button {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tpadding-top: 2.5px;\\\\n\\\\t\\\\tpadding-right: var(--size-1);\\\\n\\\\t\\\\tpadding-bottom: 3.5px;\\\\n\\\\t\\\\tpadding-left: var(--size-1);\\\\n\\\\t\\\\tcolor: black;\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tuser-select: none;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tleft: -3px;\\\\n\\\\t\\\\tborder-radius: 0 var(--radius-xs) var(--radius-xs) 0;\\\\n\\\\t\\\\tcolor: var(--block-label-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.text-class_or_confidence-container:hover .label-clear-button,\\\\n\\\\t.text-class_or_confidence-container:focus-within .label-clear-button,\\\\n\\\\t.score-text-container:hover .label-clear-button,\\\\n\\\\t.score-text-container:focus-within .label-clear-button {\\\\n\\\\t\\\\tdisplay: inline;\\\\n\\\\t}\\\\n\\\\n\\\\t.text-class_or_confidence-container:hover .textspan.hl,\\\\n\\\\t.text-class_or_confidence-container:focus-within .textspan.hl,\\\\n\\\\t.score-text:hover {\\\\n\\\\t\\\\tborder-radius: var(--radius-xs) 0 0 var(--radius-xs);\\\\n\\\\t}\\\\n\\\\n\\\\t.container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tpadding: var(--block-padding);\\\\n\\\\t}\\\\n\\\\n\\\\t.hl {\\\\n\\\\t\\\\tmargin-left: var(--size-1);\\\\n\\\\t\\\\ttransition: background-color 0.3s;\\\\n\\\\t\\\\tuser-select: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.textspan:last-child > .label {\\\\n\\\\t\\\\tmargin-right: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.class_or_confidence-legend {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tcolor: black;\\\\n\\\\t}\\\\n\\\\n\\\\t.class_or_confidence-label {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tpadding-right: var(--size-2);\\\\n\\\\t\\\\tpadding-left: var(--size-2);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t}\\\\n\\\\n\\\\t.color-legend {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tbackground: linear-gradient(\\\\n\\\\t\\\\t\\\\tto right,\\\\n\\\\t\\\\t\\\\tvar(--color-purple),\\\\n\\\\t\\\\t\\\\trgba(255, 255, 255, 0),\\\\n\\\\t\\\\t\\\\tvar(--color-red)\\\\n\\\\t\\\\t);\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t}\\\\n\\\\n\\\\t.textfield {\\\\n\\\\t\\\\tbox-sizing: border-box;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tbackground: var(--background-fill-primary);\\\\n\\\\t\\\\tbackground-color: transparent;\\\\n\\\\t\\\\tmax-width: var(--size-full);\\\\n\\\\t\\\\tline-height: var(--scale-4);\\\\n\\\\t\\\\tword-break: break-all;\\\\n\\\\t}\\\\n\\\\n\\\\t.textspan {\\\\n\\\\t\\\\ttransition: 150ms;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tpadding-top: 2.5px;\\\\n\\\\t\\\\tpadding-right: var(--size-1);\\\\n\\\\t\\\\tpadding-bottom: 3.5px;\\\\n\\\\t\\\\tpadding-left: var(--size-1);\\\\n\\\\t\\\\tcolor: black;\\\\n\\\\t\\\\tcursor: text;\\\\n\\\\t}\\\\n\\\\n\\\\t.label {\\\\n\\\\t\\\\ttransition: 150ms;\\\\n\\\\t\\\\tmargin-top: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-xs);\\\\n\\\\t\\\\tpadding: 1px 5px;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t\\\\tfont-weight: var(--weight-bold);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\ttext-transform: uppercase;\\\\n\\\\t\\\\tuser-select: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.text {\\\\n\\\\t\\\\tcolor: black;\\\\n\\\\t\\\\twhite-space: pre-wrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.textspan.hl {\\\\n\\\\t\\\\tuser-select: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.score-text-container {\\\\n\\\\t\\\\tmargin-right: var(--size-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.score-text .text {\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.no-cat {\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.no-label {\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tuser-select: text;\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: text;\\\\n\\\\t\\\\tuser-select: text;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAkUC,iDAAoB,CACnB,OAAO,CAAE,IAAI,CACb,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,cAAc,CAAE,KAAK,CACrB,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,KAAK,CAAE,KAAK,CACZ,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,WAAW,CAAE,IAAI,CACjB,QAAQ,CAAE,QAAQ,CAClB,IAAI,CAAE,IAAI,CACV,aAAa,CAAE,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CACpD,KAAK,CAAE,IAAI,wBAAwB,CACpC,CAEA,kDAAmC,MAAM,CAAC,kCAAmB,CAC7D,kDAAmC,aAAa,CAAC,kCAAmB,CACpE,oCAAqB,MAAM,CAAC,kCAAmB,CAC/C,oCAAqB,aAAa,CAAC,kCAAoB,CACtD,OAAO,CAAE,MACV,CAEA,kDAAmC,MAAM,CAAC,SAAS,kBAAG,CACtD,kDAAmC,aAAa,CAAC,SAAS,kBAAG,CAC7D,yCAAW,MAAO,CACjB,aAAa,CAAE,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CACpD,CAEA,wCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,OAAO,CAAE,IAAI,eAAe,CAC7B,CAEA,iCAAI,CACH,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,UAAU,CAAE,gBAAgB,CAAC,IAAI,CACjC,WAAW,CAAE,IACd,CAEA,wBAAS,WAAW,CAAG,qBAAO,CAC7B,YAAY,CAAE,CACf,CAEA,yDAA4B,CAC3B,OAAO,CAAE,IAAI,CACb,SAAS,CAAE,IAAI,CACf,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,KAAK,CAAE,KACR,CAEA,wDAA2B,CAC1B,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,WAAW,CAAE,IAAI,iBAAiB,CACnC,CAEA,2CAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE;AACd,GAAG,EAAE,CAAC,KAAK,CAAC;AACZ,GAAG,IAAI,cAAc,CAAC,CAAC;AACvB,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,GAAG,IAAI,WAAW,CAAC;AACnB,GAAG,CACD,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,WAAW,CAAE,IAAI,iBAAiB,CACnC,CAEA,wCAAW,CACV,UAAU,CAAE,UAAU,CACtB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,gBAAgB,CAAE,WAAW,CAC7B,SAAS,CAAE,IAAI,WAAW,CAAC,CAC3B,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,UAAU,CAAE,SACb,CAEA,uCAAU,CACT,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,cAAc,CAAE,KAAK,CACrB,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,KAAK,CAAE,KAAK,CACZ,MAAM,CAAE,IACT,CAEA,oCAAO,CACN,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,GAAG,CACf,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,IAAI,aAAa,CAAC,CAC/B,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,cAAc,CAAE,SAAS,CACzB,WAAW,CAAE,IACd,CAEA,mCAAM,CACL,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,QACd,CAEA,SAAS,iCAAI,CACZ,WAAW,CAAE,IACd,CAEA,mDAAsB,CACrB,YAAY,CAAE,IAAI,QAAQ,CAC3B,CAEA,0BAAW,CAAC,oBAAM,CACjB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,qCAAQ,CACP,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,uCAAU,CACT,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,WAAW,CAAE,IACd,CAEA,yCAAY,CACX,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,IACd\"}'\n};\nfunction splitTextByNewline(text) {\n  return text.split(\"\\n\");\n}\nconst InteractiveHighlightedtext = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  const browser = typeof document !== \"undefined\";\n  let { value = [] } = $$props;\n  let { show_legend = false } = $$props;\n  let { color_map = {} } = $$props;\n  let { selectable = false } = $$props;\n  let activeElementIndex = -1;\n  let ctx;\n  let _color_map = {};\n  let active = \"\";\n  let selection;\n  let labelToEdit = -1;\n  onMount(() => {\n    const mouseUpHandler = () => {\n      selection = window.getSelection();\n      handleSelectionComplete();\n      window.removeEventListener(\"mouseup\", mouseUpHandler);\n    };\n    window.addEventListener(\"mousedown\", () => {\n      window.addEventListener(\"mouseup\", mouseUpHandler);\n    });\n  });\n  async function handleTextSelected(startIndex, endIndex) {\n    if (selection?.toString() && activeElementIndex !== -1 && value[activeElementIndex].token.toString().includes(selection.toString())) {\n      const tempFlag = Symbol();\n      const str = value[activeElementIndex].token;\n      const [before, selected, after] = [\n        str.substring(0, startIndex),\n        str.substring(startIndex, endIndex),\n        str.substring(endIndex)\n      ];\n      let tempValue = [\n        ...value.slice(0, activeElementIndex),\n        { token: before, class_or_confidence: null },\n        {\n          token: selected,\n          class_or_confidence: mode === \"scores\" ? 1 : \"label\",\n          flag: tempFlag\n        },\n        // add a temp flag to the new highlighted text element\n        { token: after, class_or_confidence: null },\n        ...value.slice(activeElementIndex + 1)\n      ];\n      labelToEdit = tempValue.findIndex(({ flag }) => flag === tempFlag);\n      tempValue = tempValue.filter((item) => item.token.trim() !== \"\");\n      value = tempValue.map(({ flag, ...rest }) => rest);\n      handleValueChange();\n      document.getElementById(`label-input-${labelToEdit}`)?.focus();\n    }\n  }\n  const dispatch = createEventDispatcher();\n  function handleValueChange() {\n    dispatch(\"change\", value);\n    labelToEdit = -1;\n    if (show_legend) {\n      color_map = {};\n      _color_map = {};\n    }\n  }\n  let mode;\n  function handleSelectionComplete() {\n    if (selection && selection?.toString().trim() !== \"\") {\n      const textBeginningIndex = selection.getRangeAt(0).startOffset;\n      const textEndIndex = selection.getRangeAt(0).endOffset;\n      handleTextSelected(textBeginningIndex, textEndIndex);\n    }\n  }\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.show_legend === void 0 && $$bindings.show_legend && show_legend !== void 0)\n    $$bindings.show_legend(show_legend);\n  if ($$props.color_map === void 0 && $$bindings.color_map && color_map !== void 0)\n    $$bindings.color_map(color_map);\n  if ($$props.selectable === void 0 && $$bindings.selectable && selectable !== void 0)\n    $$bindings.selectable(selectable);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      {\n        if (!color_map) {\n          color_map = {};\n        }\n        if (value.length > 0) {\n          for (let entry of value) {\n            if (entry.class_or_confidence !== null) {\n              if (typeof entry.class_or_confidence === \"string\") {\n                mode = \"categories\";\n                if (!(entry.class_or_confidence in color_map)) {\n                  let color = get_next_color(Object.keys(color_map).length);\n                  color_map[entry.class_or_confidence] = color;\n                }\n              } else {\n                mode = \"scores\";\n              }\n            }\n          }\n        }\n        correct_color_map(color_map, _color_map, browser, ctx);\n      }\n    }\n    $$rendered = `<div class=\"container svelte-1ozsnjl\">${mode === \"categories\" ? `${show_legend ? `<div class=\"class_or_confidence-legend svelte-1ozsnjl\" data-testid=\"highlighted-text:class_or_confidence-legend\">${_color_map ? `${each(Object.entries(_color_map), ([class_or_confidence, color], i) => {\n      return `<div role=\"button\" aria-roledescription=\"Categories of highlighted text. Hover to see text with this class_or_confidence highlighted.\" tabindex=\"0\" class=\"class_or_confidence-label svelte-1ozsnjl\"${add_attribute(\"style\", \"background-color:\" + color.secondary, 0)}>${escape(class_or_confidence)} </div>`;\n    })}` : ``}</div>` : ``} <div class=\"textfield svelte-1ozsnjl\">${each(value, ({ token, class_or_confidence }, i) => {\n      return `${each(splitTextByNewline(token), (line, j) => {\n        return `${line.trim() !== \"\" ? `<span class=\"text-class_or_confidence-container svelte-1ozsnjl\"><span role=\"button\" tabindex=\"0\" class=\"${[\n          \"textspan svelte-1ozsnjl\",\n          (class_or_confidence === null || active ? \"no-cat\" : \"\") + \" \" + (class_or_confidence !== null ? \"hl\" : \"\") + \" \" + (selectable ? \"selectable\" : \"\")\n        ].join(\" \").trim()}\"${add_styles({\n          \"background-color\": class_or_confidence === null || active ? \"\" : class_or_confidence && _color_map[class_or_confidence] ? _color_map[class_or_confidence].secondary : \"\"\n        })}><span class=\"${[\n          \"text svelte-1ozsnjl\",\n          class_or_confidence === null ? \"no-label\" : \"\"\n        ].join(\" \").trim()}\" role=\"button\" tabindex=\"0\">${escape(line)}</span> ${!show_legend && class_or_confidence !== null && labelToEdit !== i ? `<span${add_attribute(\"id\", `label-tag-${i}`, 0)} class=\"label svelte-1ozsnjl\" role=\"button\" tabindex=\"0\"${add_styles({\n          \"background-color\": class_or_confidence === null || active ? \"\" : _color_map[class_or_confidence].primary\n        })}>${escape(class_or_confidence)} </span>` : ``} ${labelToEdit === i && class_or_confidence !== null ? ` \n\t\t\t\t\t\t\t\t\t${validate_component(LabelInput, \"LabelInput\").$$render(\n          $$result,\n          {\n            labelToEdit,\n            category: class_or_confidence,\n            active,\n            _color_map,\n            indexOfLabel: i,\n            text: token,\n            handleValueChange,\n            value\n          },\n          {\n            value: ($$value) => {\n              value = $$value;\n              $$settled = false;\n            }\n          },\n          {}\n        )}` : ``}</span> ${class_or_confidence !== null ? `<span class=\"label-clear-button svelte-1ozsnjl\" role=\"button\" aria-roledescription=\"Remove label from text\" tabindex=\"0\" data-svelte-h=\"svelte-1fuy4vv\">×\n\t\t\t\t\t\t\t\t</span>` : ``} </span>` : ``} ${j < splitTextByNewline(token).length - 1 ? `<br>` : ``}`;\n      })}`;\n    })}</div>` : `${show_legend ? `<div class=\"color-legend svelte-1ozsnjl\" data-testid=\"highlighted-text:color-legend\" data-svelte-h=\"svelte-mv3vmx\"><span>-1</span> <span>0</span> <span>+1</span></div>` : ``} <div class=\"textfield svelte-1ozsnjl\" data-testid=\"highlighted-text:textfield\">${each(value, ({ token, class_or_confidence }, i) => {\n      let score = typeof class_or_confidence === \"string\" ? parseInt(class_or_confidence) : class_or_confidence;\n      return ` <span class=\"score-text-container svelte-1ozsnjl\"><span class=\"${[\n        \"textspan score-text svelte-1ozsnjl\",\n        (class_or_confidence === null || active ? \"no-cat\" : \"\") + \" \" + (class_or_confidence !== null ? \"hl\" : \"\")\n      ].join(\" \").trim()}\" role=\"button\" tabindex=\"0\"${add_attribute(\n        \"style\",\n        \"background-color: rgba(\" + (score && score < 0 ? \"128, 90, 213,\" + -score : \"239, 68, 60,\" + score) + \")\",\n        0\n      )}><span class=\"text svelte-1ozsnjl\">${escape(token)}</span> ${class_or_confidence && labelToEdit === i ? `${validate_component(LabelInput, \"LabelInput\").$$render(\n        $$result,\n        {\n          labelToEdit,\n          _color_map,\n          category: class_or_confidence,\n          active,\n          indexOfLabel: i,\n          text: token,\n          handleValueChange,\n          isScoresMode: true,\n          value\n        },\n        {\n          value: ($$value) => {\n            value = $$value;\n            $$settled = false;\n          }\n        },\n        {}\n      )}` : ``}</span> ${class_or_confidence && activeElementIndex === i ? `<span class=\"label-clear-button svelte-1ozsnjl\" role=\"button\" aria-roledescription=\"Remove label from text\" tabindex=\"0\" data-svelte-h=\"svelte-hxhs1z\">×\n\t\t\t\t\t\t</span>` : ``} </span>`;\n    })}</div>`} </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst InteractiveHighlightedText = InteractiveHighlightedtext;\nconst { Object: Object_1 } = globals;\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { gradio } = $$props;\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value } = $$props;\n  let old_value;\n  let { show_legend } = $$props;\n  let { show_inline_category } = $$props;\n  let { color_map = {} } = $$props;\n  let { label = gradio.i18n(\"highlighted_text.highlighted_text\") } = $$props;\n  let { container = true } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { _selectable = false } = $$props;\n  let { combine_adjacent = false } = $$props;\n  let { interactive } = $$props;\n  let { loading_status } = $$props;\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.show_legend === void 0 && $$bindings.show_legend && show_legend !== void 0)\n    $$bindings.show_legend(show_legend);\n  if ($$props.show_inline_category === void 0 && $$bindings.show_inline_category && show_inline_category !== void 0)\n    $$bindings.show_inline_category(show_inline_category);\n  if ($$props.color_map === void 0 && $$bindings.color_map && color_map !== void 0)\n    $$bindings.color_map(color_map);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.container === void 0 && $$bindings.container && container !== void 0)\n    $$bindings.container(container);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props._selectable === void 0 && $$bindings._selectable && _selectable !== void 0)\n    $$bindings._selectable(_selectable);\n  if ($$props.combine_adjacent === void 0 && $$bindings.combine_adjacent && combine_adjacent !== void 0)\n    $$bindings.combine_adjacent(combine_adjacent);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      if (!color_map && Object.keys(color_map).length) {\n        color_map = color_map;\n      }\n    }\n    {\n      if (value && combine_adjacent) {\n        value = merge_elements(value);\n      }\n    }\n    {\n      {\n        if (value !== old_value) {\n          old_value = value;\n          gradio.dispatch(\"change\");\n        }\n      }\n    }\n    $$rendered = `${!interactive ? `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        variant: \"solid\",\n        test_id: \"highlighted-text\",\n        visible,\n        elem_id,\n        elem_classes,\n        padding: false,\n        container,\n        scale,\n        min_width\n      },\n      {},\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object_1.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${label ? `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n            $$result,\n            {\n              Icon: TextHighlight,\n              label,\n              float: false,\n              disable: container === false\n            },\n            {},\n            {}\n          )}` : ``} ${value ? `${validate_component(StaticHighlightedText, \"StaticHighlightedText\").$$render(\n            $$result,\n            {\n              selectable: _selectable,\n              value,\n              show_legend,\n              show_inline_category,\n              color_map\n            },\n            {},\n            {}\n          )}` : `${validate_component(Empty, \"Empty\").$$render($$result, {}, {}, {\n            default: () => {\n              return `${validate_component(TextHighlight, \"TextHighlight\").$$render($$result, {}, {}, {})}`;\n            }\n          })}`}`;\n        }\n      }\n    )}` : `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        variant: interactive ? \"dashed\" : \"solid\",\n        test_id: \"highlighted-text\",\n        visible,\n        elem_id,\n        elem_classes,\n        padding: false,\n        container,\n        scale,\n        min_width\n      },\n      {},\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object_1.assign({}, { autoscroll: gradio.autoscroll }, loading_status, { i18n: gradio.i18n }), {}, {})} ${label ? `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n            $$result,\n            {\n              Icon: TextHighlight,\n              label,\n              float: false,\n              disable: container === false\n            },\n            {},\n            {}\n          )}` : ``} ${value ? `${validate_component(InteractiveHighlightedText, \"InteractiveHighlightedText\").$$render(\n            $$result,\n            {\n              selectable: _selectable,\n              show_legend,\n              color_map,\n              value\n            },\n            {\n              value: ($$value) => {\n                value = $$value;\n                $$settled = false;\n              }\n            },\n            {}\n          )}` : `${validate_component(Empty, \"Empty\").$$render($$result, {}, {}, {\n            default: () => {\n              return `${validate_component(TextHighlight, \"TextHighlight\").$$render($$result, {}, {}, {})}`;\n            }\n          })}`}`;\n        }\n      }\n    )}`}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  InteractiveHighlightedText as BaseInteractiveHighlightedText,\n  StaticHighlightedText as BaseStaticHighlightedText,\n  Index as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAIA,SAAS,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE;AACpC,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,IAAI,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAClD,IAAI,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3B,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;AACtD,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;AACD,SAAS,iBAAiB,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE;AAChE,EAAE,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;AAC/B,IAAI,MAAM,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACrC,IAAI,IAAI,EAAE,IAAI,MAAM,EAAE;AACtB,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AACnC,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG;AACxB,QAAQ,OAAO,EAAE,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC;AAChF,QAAQ,SAAS,EAAE,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC;AACpF,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,CAAC;AACD,SAAS,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE;AAC1C,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC;AACrB,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC;AACrB,EAAE,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;AAC3B,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC,mBAAmB,EAAE;AAC7C,MAAM,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AAC1D,KAAK,MAAM;AACX,MAAM,IAAI,OAAO,KAAK,IAAI,EAAE;AAC5B,QAAQ,MAAM,CAAC,IAAI,CAAC;AACpB,UAAU,KAAK,EAAE,OAAO;AACxB,UAAU,mBAAmB,EAAE,OAAO;AACtC,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;AAC1B,MAAM,OAAO,GAAG,GAAG,CAAC,mBAAmB,CAAC;AACxC,KAAK;AACL,GAAG;AACH,EAAE,IAAI,OAAO,KAAK,IAAI,EAAE;AACxB,IAAI,MAAM,CAAC,IAAI,CAAC;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,mBAAmB,EAAE,OAAO;AAClC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,m5DAAm5D;AAC35D,EAAE,GAAG,EAAE,mqUAAmqU;AAC1qU,CAAC,CAAC;AACF,SAAS,oBAAoB,CAAC,IAAI,EAAE;AACpC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1B,CAAC;AACD,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7F,EAAE,MAAM,OAAO,GAAG,OAAO,QAAQ,KAAK,WAAW,CAAC;AAClD,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,oBAAoB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChD,EAAE,IAAI,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;AACtB,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE;AACF,IAAI;AACJ,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,SAAS,GAAG,EAAE,CAAC;AACvB,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AACjC,UAAU,IAAI,KAAK,CAAC,mBAAmB,KAAK,IAAI,EAAE;AAClD,YAAY,IAAI,OAAO,KAAK,CAAC,mBAAmB,KAAK,QAAQ,EAAE;AAC/D,cAAc,IAAI,GAAG,YAAY,CAAC;AAClC,cAAc,IAAI,EAAE,KAAK,CAAC,mBAAmB,IAAI,SAAS,CAAC,EAAE;AAC7D,gBAAgB,IAAI,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AAC1E,gBAAgB,SAAS,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,KAAK,CAAC;AAC7D,eAAe;AACf,aAAa,MAAM;AACnB,cAAc,IAAI,GAAG,QAAQ,CAAC;AAC9B,aAAa;AACb,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,iBAAiB,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AAC7D,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,sCAAsC,EAAE,IAAI,KAAK,YAAY,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,0FAA0F,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK;AACnP,IAAI,OAAO,CAAC,2CAA2C,EAAE,aAAa,CAAC,OAAO,EAAE,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;AACvJ,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AAChF,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AAC/D,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,gBAAgB,EAAE;AACxD,QAAQ,wBAAwB;AAChC,QAAQ,CAAC,CAAC,CAAC,mBAAmB,KAAK,IAAI,IAAI,MAAM,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,mBAAmB,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,UAAU,GAAG,YAAY,GAAG,EAAE,CAAC;AAChK,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC;AACvC,QAAQ,kBAAkB,EAAE,CAAC,CAAC,mBAAmB,KAAK,IAAI,IAAI,MAAM,GAAG,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,SAAS;AACvH,OAAO,CAAC,CAAC,cAAc,EAAE;AACzB,QAAQ,oBAAoB;AAC5B,QAAQ,CAAC,CAAC,mBAAmB,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,GAAG,UAAU,GAAG,EAAE;AAC9F,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,IAAI,oBAAoB,IAAI,CAAC,CAAC,mBAAmB,KAAK,IAAI,GAAG,CAAC;AAC9H,yCAAyC,EAAE,UAAU,CAAC;AACtD,QAAQ,kBAAkB,EAAE,CAAC,CAAC,mBAAmB,KAAK,IAAI,IAAI,MAAM,GAAG,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,OAAO;AACrH,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACxI,KAAK,CAAC,CAAC,CAAC,CAAC;AACT,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,sKAAsK,CAAC,GAAG,CAAC,CAAC,CAAC,+EAA+E,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK;AAClT,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,mBAAmB,KAAK,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,mBAAmB,CAAC;AACpH,IAAI,OAAO,CAAC,gDAAgD,EAAE,aAAa;AAC3E,MAAM,OAAO;AACb,MAAM,yBAAyB,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,KAAK,GAAG,cAAc,GAAG,KAAK,CAAC,GAAG,GAAG;AAChH,MAAM,CAAC;AACP,KAAK,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC;AAC3E,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;AACtB,CAAC,CAAC,CAAC;AACE,MAAC,qBAAqB,GAAG,sBAAsB;AACpD,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,uUAAuU;AAC/U,EAAE,GAAG,EAAE,iiHAAiiH;AACxiH,CAAC,CAAC;AACF,MAAM,UAAU,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAClF,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,YAAY,GAAG,QAAQ,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,YAAY,GAAG,CAAC,mDAAmD,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gCAAgC,EAAE,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;AAC7N,IAAI,kBAAkB,EAAE,QAAQ,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM,KAAK,QAAQ,GAAG,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO;AAC9G,IAAI,OAAO,EAAE,YAAY,GAAG,YAAY,CAAC,QAAQ,EAAE,EAAE,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK;AAC9E,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,4EAA4E,EAAE,UAAU,CAAC,gBAAgB;AACpH,IAAI,MAAM;AACV,MAAM,yBAAyB,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,QAAQ,GAAG,cAAc,GAAG,QAAQ,CAAC,GAAG,GAAG;AAChJ,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;AACtB,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC;AACH,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,smGAAsmG;AAC9mG,EAAE,GAAG,EAAE,o7mBAAo7mB;AAC37mB,CAAC,CAAC;AACF,SAAS,kBAAkB,CAAC,IAAI,EAAE;AAClC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1B,CAAC;AACD,MAAM,0BAA0B,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAClG,EAAE,MAAM,OAAO,GAAG,OAAO,QAAQ,KAAK,WAAW,CAAC;AAClD,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAC9B,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;AACtB,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAElB,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;AAuCvB,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC9B,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;AACrB,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,IAAI,CAAC;AAQX,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,UAAU,SAAS,GAAG,EAAE,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AACnC,YAAY,IAAI,KAAK,CAAC,mBAAmB,KAAK,IAAI,EAAE;AACpD,cAAc,IAAI,OAAO,KAAK,CAAC,mBAAmB,KAAK,QAAQ,EAAE;AACjE,gBAAgB,IAAI,GAAG,YAAY,CAAC;AACpC,gBAAgB,IAAI,EAAE,KAAK,CAAC,mBAAmB,IAAI,SAAS,CAAC,EAAE;AAC/D,kBAAkB,IAAI,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5E,kBAAkB,SAAS,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,KAAK,CAAC;AAC/D,iBAAiB;AACjB,eAAe,MAAM;AACrB,gBAAgB,IAAI,GAAG,QAAQ,CAAC;AAChC,eAAe;AACf,aAAa;AACb,WAAW;AACX,SAAS;AACT,QAAQ,iBAAiB,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AAC/D,OAAO;AACP,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,sCAAsC,EAAE,IAAI,KAAK,YAAY,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,iHAAiH,EAAE,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK;AAC7S,MAAM,OAAO,CAAC,oMAAoM,EAAE,aAAa,CAAC,OAAO,EAAE,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,CAAC;AAC7T,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,uCAAuC,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,CAAC,KAAK;AACvH,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AAC7D,QAAQ,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,wGAAwG,EAAE;AAClJ,UAAU,yBAAyB;AACnC,UAAU,CAAC,mBAAmB,KAAK,IAAI,IAAI,MAAM,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,mBAAmB,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,UAAU,GAAG,YAAY,GAAG,EAAE,CAAC;AAC9J,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC;AACzC,UAAU,kBAAkB,EAAE,mBAAmB,KAAK,IAAI,IAAI,MAAM,GAAG,EAAE,GAAG,mBAAmB,IAAI,UAAU,CAAC,mBAAmB,CAAC,GAAG,UAAU,CAAC,mBAAmB,CAAC,CAAC,SAAS,GAAG,EAAE;AACnL,SAAS,CAAC,CAAC,cAAc,EAAE;AAC3B,UAAU,qBAAqB;AAC/B,UAAU,mBAAmB,KAAK,IAAI,GAAG,UAAU,GAAG,EAAE;AACxD,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,6BAA6B,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,IAAI,mBAAmB,KAAK,IAAI,IAAI,WAAW,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,wDAAwD,EAAE,UAAU,CAAC;AAC3Q,UAAU,kBAAkB,EAAE,mBAAmB,KAAK,IAAI,IAAI,MAAM,GAAG,EAAE,GAAG,UAAU,CAAC,mBAAmB,CAAC,CAAC,OAAO;AACnH,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,KAAK,CAAC,IAAI,mBAAmB,KAAK,IAAI,GAAG,CAAC;AACjH,SAAS,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAChE,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,WAAW;AACvB,YAAY,QAAQ,EAAE,mBAAmB;AACzC,YAAY,MAAM;AAClB,YAAY,UAAU;AACtB,YAAY,YAAY,EAAE,CAAC;AAC3B,YAAY,IAAI,EAAE,KAAK;AACvB,YAAY,iBAAiB;AAC7B,YAAY,KAAK;AACjB,WAAW;AACX,UAAU;AACV,YAAY,KAAK,EAAE,CAAC,OAAO,KAAK;AAChC,cAAc,KAAK,GAAG,OAAO,CAAC;AAC9B,cAAc,SAAS,GAAG,KAAK,CAAC;AAChC,aAAa;AACb,WAAW;AACX,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,mBAAmB,KAAK,IAAI,GAAG,CAAC;AAC3D,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjG,OAAO,CAAC,CAAC,CAAC,CAAC;AACX,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,uKAAuK,CAAC,GAAG,CAAC,CAAC,CAAC,gFAAgF,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,CAAC,KAAK;AACtV,MAAM,IAAI,KAAK,GAAG,OAAO,mBAAmB,KAAK,QAAQ,GAAG,QAAQ,CAAC,mBAAmB,CAAC,GAAG,mBAAmB,CAAC;AAChH,MAAM,OAAO,CAAC,gEAAgE,EAAE;AAChF,QAAQ,oCAAoC;AAC5C,QAAQ,CAAC,mBAAmB,KAAK,IAAI,IAAI,MAAM,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,mBAAmB,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AACnH,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,4BAA4B,EAAE,aAAa;AACpE,QAAQ,OAAO;AACf,QAAQ,yBAAyB,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,KAAK,GAAG,cAAc,GAAG,KAAK,CAAC,GAAG,GAAG;AAClH,QAAQ,CAAC;AACT,OAAO,CAAC,mCAAmC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,mBAAmB,IAAI,WAAW,KAAK,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACxK,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,WAAW;AACrB,UAAU,UAAU;AACpB,UAAU,QAAQ,EAAE,mBAAmB;AACvC,UAAU,MAAM;AAChB,UAAU,YAAY,EAAE,CAAC;AACzB,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,iBAAiB;AAC3B,UAAU,YAAY,EAAE,IAAI;AAC5B,UAAU,KAAK;AACf,SAAS;AACT,QAAQ;AACR,UAAU,KAAK,EAAE,CAAC,OAAO,KAAK;AAC9B,YAAY,KAAK,GAAG,OAAO,CAAC;AAC5B,YAAY,SAAS,GAAG,KAAK,CAAC;AAC9B,WAAW;AACX,SAAS;AACT,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,mBAAmB,IAAI,kBAAkB,KAAK,CAAC,GAAG,CAAC;AAC5E,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC9B,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;AACxB,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,0BAA0B,GAAG,2BAA2B;AAC9D,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAChC,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,EAAE,GAAG,OAAO,CAAC;AAC7E,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,gBAAgB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE;AACvD,QAAQ,SAAS,GAAG,SAAS,CAAC;AAC9B,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,KAAK,IAAI,gBAAgB,EAAE;AACrC,QAAQ,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AACtC,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;AACjC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,UAAU,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACpC,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACjF,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,OAAO,EAAE,kBAAkB;AACnC,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC7P,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,IAAI,EAAE,aAAa;AACjC,cAAc,KAAK;AACnB,cAAc,KAAK,EAAE,KAAK;AAC1B,cAAc,OAAO,EAAE,SAAS,KAAK,KAAK;AAC1C,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,qBAAqB,EAAE,uBAAuB,CAAC,CAAC,QAAQ;AAC5G,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,UAAU,EAAE,WAAW;AACrC,cAAc,KAAK;AACnB,cAAc,WAAW;AACzB,cAAc,oBAAoB;AAClC,cAAc,SAAS;AACvB,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AACjF,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5G,aAAa;AACb,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACxD,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO,EAAE,WAAW,GAAG,QAAQ,GAAG,OAAO;AACjD,QAAQ,OAAO,EAAE,kBAAkB;AACnC,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,cAAc,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC7P,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,IAAI,EAAE,aAAa;AACjC,cAAc,KAAK;AACnB,cAAc,KAAK,EAAE,KAAK;AAC1B,cAAc,OAAO,EAAE,SAAS,KAAK,KAAK;AAC1C,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,0BAA0B,EAAE,4BAA4B,CAAC,CAAC,QAAQ;AACtH,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,UAAU,EAAE,WAAW;AACrC,cAAc,WAAW;AACzB,cAAc,SAAS;AACvB,cAAc,KAAK;AACnB,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,CAAC,OAAO,KAAK;AAClC,gBAAgB,KAAK,GAAG,OAAO,CAAC;AAChC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,aAAa;AACb,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AACjF,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5G,aAAa;AACb,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}