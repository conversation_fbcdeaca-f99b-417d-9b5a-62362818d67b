{"version": 3, "file": "Index-DCYRjwCt.js", "sources": ["../../../html/shared/HTML.svelte", "../../../html/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let elem_classes: string[] = [];\n\texport let value: string;\n\texport let visible = true;\n\n\tconst dispatch = createEventDispatcher<{ change: undefined }>();\n\n\t$: value, dispatch(\"change\");\n</script>\n\n<div class=\"prose {elem_classes.join(' ')}\" class:hide={!visible}>\n\t{@html value}\n</div>\n\n<style>\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport HTML from \"./shared/HTML.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { Code as CodeIcon } from \"@gradio/icons\";\n\timport { css_units } from \"@gradio/utils\";\n\n\texport let label: string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let show_label = false;\n\texport let min_height: number | undefined = undefined;\n\texport let max_height: number | undefined = undefined;\n\n\t$: label, gradio.dispatch(\"change\");\n</script>\n\n<Block {visible} {elem_id} {elem_classes} container={false}>\n\t{#if show_label}\n\t\t<span class=\"label-container\">\n\t\t\t<BlockLabel Icon={CodeIcon} {show_label} {label} float={true} />\n\t\t</span>\n\t{/if}\n\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tvariant=\"center\"\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<div\n\t\tclass:pending={loading_status?.status === \"pending\"}\n\t\tstyle:min-height={min_height && loading_status?.status !== \"pending\"\n\t\t\t? css_units(min_height)\n\t\t\t: undefined}\n\t\tstyle:max-height={max_height ? css_units(max_height) : undefined}\n\t>\n\t\t<HTML\n\t\t\t{value}\n\t\t\t{elem_classes}\n\t\t\t{visible}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\tdiv {\n\t\ttransition: 150ms;\n\t}\n\n\t.pending {\n\t\topacity: 0.2;\n\t}\n\n\t.label-container :global(label) {\n\t\ttop: -8px !important;\n\t\tposition: relative !important;\n\t\tleft: -8px !important;\n\t\tbackground: var(--block-background-fill) !important;\n\t\tborder-top: var(--block-label-border-width) solid\n\t\t\tvar(--border-color-primary) !important;\n\t\tborder-left: var(--block-label-border-width) solid\n\t\t\tvar(--border-color-primary) !important;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "elem_classes", "$$props", "value", "visible", "dispatch", "createEventDispatcher", "CodeIcon", "span", "create_if_block", "set_style", "css_units", "dirty", "label", "elem_id", "loading_status", "gradio", "show_label", "min_height", "max_height", "clear_status_handler"], "mappings": "yXAYmBA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gBAAA,cAAiBA,EAAO,CAAA,CAAA,UAAhEC,EAEKC,EAAAC,EAAAC,CAAA,cADGJ,EAAK,CAAA,8BAALA,EAAK,CAAA,wBADMA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,mDAAiBA,EAAO,CAAA,CAAA,4CATpD,GAAA,CAAA,aAAAK,EAAA,EAAA,EAAAC,EACA,CAAA,MAAAC,CAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,QAEfG,EAAWC,6JAEPD,EAAS,QAAQ,kZCoBPE,mCAAsC,6FADzDV,EAEMC,EAAAU,EAAAR,CAAA,qNAHFJ,EAAU,CAAA,GAAAa,EAAAb,CAAA,WAOF,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,kUAKHA,EAAc,CAAA,GAAE,SAAW,SAAS,EACjCc,EAAAX,EAAA,aAAAH,EAAc,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,UACxDe,EAAUf,EAAU,CAAA,CAAA,EACpB,MAAS,mBACMA,EAAU,CAAA,EAAGe,EAAUf,EAAU,CAAA,CAAA,EAAI,MAAS,iDALjEC,EAaKC,EAAAC,EAAAC,CAAA,2BA1BAJ,EAAU,CAAA,4HAOFgB,EAAA,IAAA,CAAA,WAAAhB,KAAO,UAAU,EACvBgB,EAAA,IAAA,CAAA,KAAAhB,KAAO,IAAI,UACbA,EAAc,CAAA,CAAA,2IAKHA,EAAc,CAAA,GAAE,SAAW,SAAS,SACjCc,EAAAX,EAAA,aAAAH,EAAc,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,UACxDe,EAAUf,EAAU,CAAA,CAAA,EACpB,MAAS,0BACMA,EAAU,CAAA,EAAGe,EAAUf,EAAU,CAAA,CAAA,EAAI,MAAS,wQAnBb,oUAjBzC,GAAA,CAAA,MAAAiB,CAAA,EAAAX,GACA,QAAAY,EAAU,EAAA,EAAAZ,EACV,CAAA,aAAAD,EAAA,EAAA,EAAAC,GACA,QAAAE,EAAU,EAAA,EAAAF,GACV,MAAAC,EAAQ,EAAA,EAAAD,EACR,CAAA,eAAAa,CAAA,EAAAb,EACA,CAAA,OAAAc,CAAA,EAAAd,GAIA,WAAAe,EAAa,EAAA,EAAAf,GACb,WAAAgB,EAAiC,MAAA,EAAAhB,GACjC,WAAAiB,EAAiC,MAAA,EAAAjB,EAiBpB,MAAAkB,EAAA,IAAAJ,EAAO,SAAS,eAAgBD,CAAc,QAanDC,EAAO,SAAS,QAAQ,sZA5BjCA,EAAO,SAAS,QAAQ"}