import{a as y,i as p,s as $,f as _,B as tt,c as v,m as k,t as b,b as m,d as B,I as H,a5 as J,x as et,l as st,y as it,z as lt,a6 as K,o as nt,Y as at,S as ut,a0 as ot,a4 as _t}from"../lite.js";import{T as ft}from"./Textbox-GhQQJgUa.js";import{default as Ct}from"./Example-BiOJtN-L.js";import"./BlockTitle-BlPSRItZ.js";import"./Info-2h-pcw07.js";import"./MarkdownCode-C6RHM0m6.js";import"./Check-ChD9RrF6.js";import"./Copy-DOOc0VFX.js";import"./Send-DY8minAZ.js";import"./Square-7hK0fZD1.js";import"./index-DCygRGWm.js";/* empty css                                              *//* empty css                                              */function L(i){let t,s;const n=[{autoscroll:i[2].autoscroll},{i18n:i[2].i18n},i[19]];let h={};for(let a=0;a<n.length;a+=1)h=at(h,n[a]);return t=new ut({props:h}),t.$on("clear_status",i[27]),{c(){v(t.$$.fragment)},m(a,o){k(t,a,o),s=!0},p(a,o){const c=o[0]&524292?ot(n,[o[0]&4&&{autoscroll:a[2].autoscroll},o[0]&4&&{i18n:a[2].i18n},o[0]&524288&&_t(a[19])]):{};t.$set(c)},i(a){s||(b(t.$$.fragment,a),s=!0)},o(a){m(t.$$.fragment,a),s=!1},d(a){B(t,a)}}}function ht(i){let t,s,n,h,a,o=i[19]&&L(i);function c(l){i[28](l)}function g(l){i[29](l)}let r={label:i[3],info:i[4],root:i[25],show_label:i[10],lines:i[8],type:i[12],rtl:i[20],text_align:i[21],max_lines:i[11]?i[11]:i[8]+1,placeholder:i[9],submit_btn:i[16],stop_btn:i[17],show_copy_button:i[18],autofocus:i[22],container:i[13],autoscroll:i[23],max_length:i[26],disabled:!i[24]};return i[0]!==void 0&&(r.value=i[0]),i[1]!==void 0&&(r.value_is_output=i[1]),s=new ft({props:r}),H.push(()=>J(s,"value",c)),H.push(()=>J(s,"value_is_output",g)),s.$on("change",i[30]),s.$on("input",i[31]),s.$on("submit",i[32]),s.$on("blur",i[33]),s.$on("select",i[34]),s.$on("focus",i[35]),s.$on("stop",i[36]),{c(){o&&o.c(),t=et(),v(s.$$.fragment)},m(l,u){o&&o.m(l,u),st(l,t,u),k(s,l,u),a=!0},p(l,u){l[19]?o?(o.p(l,u),u[0]&524288&&b(o,1)):(o=L(l),o.c(),b(o,1),o.m(t.parentNode,t)):o&&(it(),m(o,1,1,()=>{o=null}),lt());const f={};u[0]&8&&(f.label=l[3]),u[0]&16&&(f.info=l[4]),u[0]&33554432&&(f.root=l[25]),u[0]&1024&&(f.show_label=l[10]),u[0]&256&&(f.lines=l[8]),u[0]&4096&&(f.type=l[12]),u[0]&1048576&&(f.rtl=l[20]),u[0]&2097152&&(f.text_align=l[21]),u[0]&2304&&(f.max_lines=l[11]?l[11]:l[8]+1),u[0]&512&&(f.placeholder=l[9]),u[0]&65536&&(f.submit_btn=l[16]),u[0]&131072&&(f.stop_btn=l[17]),u[0]&262144&&(f.show_copy_button=l[18]),u[0]&4194304&&(f.autofocus=l[22]),u[0]&8192&&(f.container=l[13]),u[0]&8388608&&(f.autoscroll=l[23]),u[0]&67108864&&(f.max_length=l[26]),u[0]&16777216&&(f.disabled=!l[24]),!n&&u[0]&1&&(n=!0,f.value=l[0],K(()=>n=!1)),!h&&u[0]&2&&(h=!0,f.value_is_output=l[1],K(()=>h=!1)),s.$set(f)},i(l){a||(b(o),b(s.$$.fragment,l),a=!0)},o(l){m(o),m(s.$$.fragment,l),a=!1},d(l){l&&nt(t),o&&o.d(l),B(s,l)}}}function rt(i){let t,s;return t=new tt({props:{visible:i[7],elem_id:i[5],elem_classes:i[6],scale:i[14],min_width:i[15],allow_overflow:!1,padding:i[13],$$slots:{default:[ht]},$$scope:{ctx:i}}}),{c(){v(t.$$.fragment)},m(n,h){k(t,n,h),s=!0},p(n,h){const a={};h[0]&128&&(a.visible=n[7]),h[0]&32&&(a.elem_id=n[5]),h[0]&64&&(a.elem_classes=n[6]),h[0]&16384&&(a.scale=n[14]),h[0]&32768&&(a.min_width=n[15]),h[0]&8192&&(a.padding=n[13]),h[0]&134168351|h[1]&64&&(a.$$scope={dirty:h,ctx:n}),t.$set(a)},i(n){s||(b(t.$$.fragment,n),s=!0)},o(n){m(t.$$.fragment,n),s=!1},d(n){B(t,n)}}}function ct(i,t,s){let{gradio:n}=t,{label:h="Textbox"}=t,{info:a=void 0}=t,{elem_id:o=""}=t,{elem_classes:c=[]}=t,{visible:g=!0}=t,{value:r=""}=t,{lines:l}=t,{placeholder:u=""}=t,{show_label:f}=t,{max_lines:T}=t,{type:x="text"}=t,{container:S=!0}=t,{scale:I=null}=t,{min_width:j=void 0}=t,{submit_btn:q=null}=t,{stop_btn:z=null}=t,{show_copy_button:C=!1}=t,{loading_status:w=void 0}=t,{value_is_output:d=!1}=t,{rtl:E=!1}=t,{text_align:N=void 0}=t,{autofocus:Y=!1}=t,{autoscroll:A=!0}=t,{interactive:D}=t,{root:F}=t,{max_length:G=void 0}=t;const M=()=>n.dispatch("clear_status",w);function O(e){r=e,s(0,r)}function P(e){d=e,s(1,d)}const Q=()=>n.dispatch("change",r),R=()=>n.dispatch("input"),U=()=>n.dispatch("submit"),V=()=>n.dispatch("blur"),W=e=>n.dispatch("select",e.detail),X=()=>n.dispatch("focus"),Z=()=>n.dispatch("stop");return i.$$set=e=>{"gradio"in e&&s(2,n=e.gradio),"label"in e&&s(3,h=e.label),"info"in e&&s(4,a=e.info),"elem_id"in e&&s(5,o=e.elem_id),"elem_classes"in e&&s(6,c=e.elem_classes),"visible"in e&&s(7,g=e.visible),"value"in e&&s(0,r=e.value),"lines"in e&&s(8,l=e.lines),"placeholder"in e&&s(9,u=e.placeholder),"show_label"in e&&s(10,f=e.show_label),"max_lines"in e&&s(11,T=e.max_lines),"type"in e&&s(12,x=e.type),"container"in e&&s(13,S=e.container),"scale"in e&&s(14,I=e.scale),"min_width"in e&&s(15,j=e.min_width),"submit_btn"in e&&s(16,q=e.submit_btn),"stop_btn"in e&&s(17,z=e.stop_btn),"show_copy_button"in e&&s(18,C=e.show_copy_button),"loading_status"in e&&s(19,w=e.loading_status),"value_is_output"in e&&s(1,d=e.value_is_output),"rtl"in e&&s(20,E=e.rtl),"text_align"in e&&s(21,N=e.text_align),"autofocus"in e&&s(22,Y=e.autofocus),"autoscroll"in e&&s(23,A=e.autoscroll),"interactive"in e&&s(24,D=e.interactive),"root"in e&&s(25,F=e.root),"max_length"in e&&s(26,G=e.max_length)},[r,d,n,h,a,o,c,g,l,u,f,T,x,S,I,j,q,z,C,w,E,N,Y,A,D,F,G,M,O,P,Q,R,U,V,W,X,Z]}class jt extends y{constructor(t){super(),p(this,t,ct,rt,$,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,submit_btn:16,stop_btn:17,show_copy_button:18,loading_status:19,value_is_output:1,rtl:20,text_align:21,autofocus:22,autoscroll:23,interactive:24,root:25,max_length:26},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(t){this.$$set({gradio:t}),_()}get label(){return this.$$.ctx[3]}set label(t){this.$$set({label:t}),_()}get info(){return this.$$.ctx[4]}set info(t){this.$$set({info:t}),_()}get elem_id(){return this.$$.ctx[5]}set elem_id(t){this.$$set({elem_id:t}),_()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(t){this.$$set({elem_classes:t}),_()}get visible(){return this.$$.ctx[7]}set visible(t){this.$$set({visible:t}),_()}get value(){return this.$$.ctx[0]}set value(t){this.$$set({value:t}),_()}get lines(){return this.$$.ctx[8]}set lines(t){this.$$set({lines:t}),_()}get placeholder(){return this.$$.ctx[9]}set placeholder(t){this.$$set({placeholder:t}),_()}get show_label(){return this.$$.ctx[10]}set show_label(t){this.$$set({show_label:t}),_()}get max_lines(){return this.$$.ctx[11]}set max_lines(t){this.$$set({max_lines:t}),_()}get type(){return this.$$.ctx[12]}set type(t){this.$$set({type:t}),_()}get container(){return this.$$.ctx[13]}set container(t){this.$$set({container:t}),_()}get scale(){return this.$$.ctx[14]}set scale(t){this.$$set({scale:t}),_()}get min_width(){return this.$$.ctx[15]}set min_width(t){this.$$set({min_width:t}),_()}get submit_btn(){return this.$$.ctx[16]}set submit_btn(t){this.$$set({submit_btn:t}),_()}get stop_btn(){return this.$$.ctx[17]}set stop_btn(t){this.$$set({stop_btn:t}),_()}get show_copy_button(){return this.$$.ctx[18]}set show_copy_button(t){this.$$set({show_copy_button:t}),_()}get loading_status(){return this.$$.ctx[19]}set loading_status(t){this.$$set({loading_status:t}),_()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(t){this.$$set({value_is_output:t}),_()}get rtl(){return this.$$.ctx[20]}set rtl(t){this.$$set({rtl:t}),_()}get text_align(){return this.$$.ctx[21]}set text_align(t){this.$$set({text_align:t}),_()}get autofocus(){return this.$$.ctx[22]}set autofocus(t){this.$$set({autofocus:t}),_()}get autoscroll(){return this.$$.ctx[23]}set autoscroll(t){this.$$set({autoscroll:t}),_()}get interactive(){return this.$$.ctx[24]}set interactive(t){this.$$set({interactive:t}),_()}get root(){return this.$$.ctx[25]}set root(t){this.$$set({root:t}),_()}get max_length(){return this.$$.ctx[26]}set max_length(t){this.$$set({max_length:t}),_()}}export{Ct as BaseExample,ft as BaseTextbox,jt as default};
//# sourceMappingURL=Index-BSQqHN0m.js.map
