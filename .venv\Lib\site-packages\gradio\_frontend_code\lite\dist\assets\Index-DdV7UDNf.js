import{a as q,i as z,s as C,f,e as I,p as Y,x as A,q as h,r as m,$ as d,l as B,v as D,t as c,y as E,b as w,z as F,u as G,h as H,j as J,o as K,Y as L,S as M,c as N,m as O,a0 as P,a4 as Q,d as R}from"../lite.js";function S(i){let e,n;const r=[{autoscroll:i[8].autoscroll},{i18n:i[8].i18n},i[7],{status:i[7]?i[7].status=="pending"?"generating":i[7].status:null}];let o={};for(let s=0;s<r.length;s+=1)o=L(o,r[s]);return e=new M({props:o}),{c(){N(e.$$.fragment)},m(s,t){O(e,s,t),n=!0},p(s,t){const g=t&384?P(r,[t&256&&{autoscroll:s[8].autoscroll},t&256&&{i18n:s[8].i18n},t&128&&Q(s[7]),t&128&&{status:s[7]?s[7].status=="pending"?"generating":s[7].status:null}]):{};e.$set(g)},i(s){n||(c(e.$$.fragment,s),n=!0)},o(s){w(e.$$.fragment,s),n=!1},d(s){R(e,s)}}}function T(i){let e,n,r,o=`calc(min(${i[2]}px, 100%))`,s,t=i[7]&&i[9]&&i[8]&&S(i);const g=i[11].default,_=I(g,i,i[10],null);return{c(){e=Y("div"),t&&t.c(),n=A(),_&&_.c(),h(e,"id",i[3]),h(e,"class",r="column "+i[4].join(" ")+" svelte-vt1mxs"),m(e,"gap",i[1]),m(e,"compact",i[6]==="compact"),m(e,"panel",i[6]==="panel"),m(e,"hide",!i[5]),d(e,"flex-grow",i[0]),d(e,"min-width",o)},m(a,u){B(a,e,u),t&&t.m(e,null),D(e,n),_&&_.m(e,null),s=!0},p(a,[u]){a[7]&&a[9]&&a[8]?t?(t.p(a,u),u&896&&c(t,1)):(t=S(a),t.c(),c(t,1),t.m(e,n)):t&&(E(),w(t,1,1,()=>{t=null}),F()),_&&_.p&&(!s||u&1024)&&G(_,g,a,a[10],s?J(g,a[10],u,null):H(a[10]),null),(!s||u&8)&&h(e,"id",a[3]),(!s||u&16&&r!==(r="column "+a[4].join(" ")+" svelte-vt1mxs"))&&h(e,"class",r),(!s||u&18)&&m(e,"gap",a[1]),(!s||u&80)&&m(e,"compact",a[6]==="compact"),(!s||u&80)&&m(e,"panel",a[6]==="panel"),(!s||u&48)&&m(e,"hide",!a[5]),u&1&&d(e,"flex-grow",a[0]),u&4&&o!==(o=`calc(min(${a[2]}px, 100%))`)&&d(e,"min-width",o)},i(a){s||(c(t),c(_,a),s=!0)},o(a){w(t),w(_,a),s=!1},d(a){a&&K(e),t&&t.d(),_&&_.d(a)}}}function U(i,e,n){let{$$slots:r={},$$scope:o}=e,{scale:s=null}=e,{gap:t=!0}=e,{min_width:g=0}=e,{elem_id:_=""}=e,{elem_classes:a=[]}=e,{visible:u=!0}=e,{variant:v="default"}=e,{loading_status:b=void 0}=e,{gradio:k=void 0}=e,{show_progress:j=!1}=e;return i.$$set=l=>{"scale"in l&&n(0,s=l.scale),"gap"in l&&n(1,t=l.gap),"min_width"in l&&n(2,g=l.min_width),"elem_id"in l&&n(3,_=l.elem_id),"elem_classes"in l&&n(4,a=l.elem_classes),"visible"in l&&n(5,u=l.visible),"variant"in l&&n(6,v=l.variant),"loading_status"in l&&n(7,b=l.loading_status),"gradio"in l&&n(8,k=l.gradio),"show_progress"in l&&n(9,j=l.show_progress),"$$scope"in l&&n(10,o=l.$$scope)},[s,t,g,_,a,u,v,b,k,j,o,r]}class W extends q{constructor(e){super(),z(this,e,U,T,C,{scale:0,gap:1,min_width:2,elem_id:3,elem_classes:4,visible:5,variant:6,loading_status:7,gradio:8,show_progress:9})}get scale(){return this.$$.ctx[0]}set scale(e){this.$$set({scale:e}),f()}get gap(){return this.$$.ctx[1]}set gap(e){this.$$set({gap:e}),f()}get min_width(){return this.$$.ctx[2]}set min_width(e){this.$$set({min_width:e}),f()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),f()}get variant(){return this.$$.ctx[6]}set variant(e){this.$$set({variant:e}),f()}get loading_status(){return this.$$.ctx[7]}set loading_status(e){this.$$set({loading_status:e}),f()}get gradio(){return this.$$.ctx[8]}set gradio(e){this.$$set({gradio:e}),f()}get show_progress(){return this.$$.ctx[9]}set show_progress(e){this.$$set({show_progress:e}),f()}}export{W as default};
//# sourceMappingURL=Index-DdV7UDNf.js.map
