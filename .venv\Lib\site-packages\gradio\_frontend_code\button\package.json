{"name": "@gradio/button", "version": "0.3.6", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@gradio/client": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^"}, "devDependencies": {"@gradio/preview": "workspace:^"}, "main": "./Index.svelte", "main_changeset": true, "exports": {"./package.json": "./package.json", ".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/button"}}