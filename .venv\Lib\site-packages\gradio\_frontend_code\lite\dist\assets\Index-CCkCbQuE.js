import{a as y,i as E,s as L,f as _,W as q,c as $,m as w,t as h,b as d,d as v,a8 as O,x as D,l as C,o as I,B as M,D as P,Y as Q,S as V,E as X,a0 as Z,a4 as x,y as S,z as U,b7 as ee,I as te,a5 as se,a6 as ne}from"../lite.js";import{C as T}from"./Check-ChD9RrF6.js";import{C as z}from"./Copy-DOOc0VFX.js";import{D as N}from"./Download-CgLP-Xl6.js";import{D as ae}from"./DownloadLink-B8hI46-W.js";import{I as le}from"./IconButtonWrapper-Ck50MZwX.js";import{C as Y}from"./Code-BsaWGhrX.js";import{B as ie}from"./BlockLabel-B0HN-MOU.js";import{E as ue}from"./Empty-C76eC2zW.js";import{default as Oe}from"./Example-CSkYOt-V.js";import"./file-url-Co2ROWca.js";function re(l){let e,s;return e=new q({props:{Icon:l[0]?T:z}}),e.$on("click",l[1]),{c(){$(e.$$.fragment)},m(t,a){w(e,t,a),s=!0},p(t,[a]){const n={};a&1&&(n.Icon=t[0]?T:z),e.$set(n)},i(t){s||(h(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function oe(l,e,s){let t=!1,{value:a}=e,n;function f(){s(0,t=!0),n&&clearTimeout(n),n=setTimeout(()=>{s(0,t=!1)},2e3)}async function g(){"clipboard"in navigator&&(await navigator.clipboard.writeText(a),f())}return O(()=>{n&&clearTimeout(n)}),l.$$set=i=>{"value"in i&&s(2,a=i.value)},[t,g,a]}class fe extends y{constructor(e){super(),E(this,e,oe,re,L,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),_()}}function ce(l){let e,s;return e=new q({props:{Icon:l[0]?T:N}}),{c(){$(e.$$.fragment)},m(t,a){w(e,t,a),s=!0},p(t,a){const n={};a&1&&(n.Icon=t[0]?T:N),e.$set(n)},i(t){s||(h(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function _e(l){let e,s;return e=new ae({props:{download:"file."+l[2],href:l[1],$$slots:{default:[ce]},$$scope:{ctx:l}}}),e.$on("click",l[3]),{c(){$(e.$$.fragment)},m(t,a){w(e,t,a),s=!0},p(t,[a]){const n={};a&4&&(n.download="file."+t[2]),a&2&&(n.href=t[1]),a&129&&(n.$$scope={dirty:a,ctx:t}),e.$set(n)},i(t){s||(h(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function ge(l){return{py:"py",python:"py",md:"md",markdown:"md",json:"json",html:"html",css:"css",js:"js",javascript:"js",ts:"ts",typescript:"ts",yaml:"yaml",yml:"yml",dockerfile:"dockerfile",sh:"sh",shell:"sh",r:"r",c:"c",cpp:"cpp"}[l]||"txt"}function me(l,e,s){let t,a,{value:n}=e,{language:f}=e,g=!1,i;function c(){s(0,g=!0),i&&clearTimeout(i),i=setTimeout(()=>{s(0,g=!1)},2e3)}return O(()=>{i&&clearTimeout(i)}),l.$$set=o=>{"value"in o&&s(4,n=o.value),"language"in o&&s(5,f=o.language)},l.$$.update=()=>{l.$$.dirty&32&&s(2,t=ge(f)),l.$$.dirty&16&&s(1,a=URL.createObjectURL(new Blob([n])))},[g,a,t,c,n,f]}class he extends y{constructor(e){super(),E(this,e,me,_e,L,{value:4,language:5})}get value(){return this.$$.ctx[4]}set value(e){this.$$set({value:e}),_()}get language(){return this.$$.ctx[5]}set language(e){this.$$set({language:e}),_()}}function de(l){let e,s,t,a;return e=new he({props:{value:l[0],language:l[1]}}),t=new fe({props:{value:l[0]}}),{c(){$(e.$$.fragment),s=D(),$(t.$$.fragment)},m(n,f){w(e,n,f),C(n,s,f),w(t,n,f),a=!0},p(n,f){const g={};f&1&&(g.value=n[0]),f&2&&(g.language=n[1]),e.$set(g);const i={};f&1&&(i.value=n[0]),t.$set(i)},i(n){a||(h(e.$$.fragment,n),h(t.$$.fragment,n),a=!0)},o(n){d(e.$$.fragment,n),d(t.$$.fragment,n),a=!1},d(n){n&&I(s),v(e,n),v(t,n)}}}function be(l){let e,s;return e=new le({props:{$$slots:{default:[de]},$$scope:{ctx:l}}}),{c(){$(e.$$.fragment)},m(t,a){w(e,t,a),s=!0},p(t,[a]){const n={};a&7&&(n.$$scope={dirty:a,ctx:t}),e.$set(n)},i(t){s||(h(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function $e(l,e,s){let{value:t}=e,{language:a}=e;return l.$$set=n=>{"value"in n&&s(0,t=n.value),"language"in n&&s(1,a=n.language)},[t,a]}class we extends y{constructor(e){super(),E(this,e,$e,be,L,{value:0,language:1})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get language(){return this.$$.ctx[1]}set language(e){this.$$set({language:e}),_()}}function R(l){let e,s;return e=new ie({props:{Icon:Y,show_label:l[9],label:l[8],float:!1}}),{c(){$(e.$$.fragment)},m(t,a){w(e,t,a),s=!0},p(t,a){const n={};a&512&&(n.show_label=t[9]),a&256&&(n.label=t[8]),e.$set(n)},i(t){s||(h(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function ve(l){let e,s,t,a,n;e=new we({props:{language:l[2],value:l[0]}});function f(i){l[18](i)}let g={language:l[2],lines:l[3],max_lines:l[4],dark_mode:l[15],wrap_lines:l[13],readonly:!l[14]};return l[0]!==void 0&&(g.value=l[0]),t=new ee({props:g}),te.push(()=>se(t,"value",f)),t.$on("blur",l[19]),t.$on("focus",l[20]),{c(){$(e.$$.fragment),s=D(),$(t.$$.fragment)},m(i,c){w(e,i,c),C(i,s,c),w(t,i,c),n=!0},p(i,c){const o={};c&4&&(o.language=i[2]),c&1&&(o.value=i[0]),e.$set(o);const b={};c&4&&(b.language=i[2]),c&8&&(b.lines=i[3]),c&16&&(b.max_lines=i[4]),c&8192&&(b.wrap_lines=i[13]),c&16384&&(b.readonly=!i[14]),!a&&c&1&&(a=!0,b.value=i[0],ne(()=>a=!1)),t.$set(b)},i(i){n||(h(e.$$.fragment,i),h(t.$$.fragment,i),n=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),n=!1},d(i){i&&I(s),v(e,i),v(t,i)}}}function ke(l){let e,s;return e=new ue({props:{unpadded_box:!0,size:"large",$$slots:{default:[pe]},$$scope:{ctx:l}}}),{c(){$(e.$$.fragment)},m(t,a){w(e,t,a),s=!0},p(t,a){const n={};a&4194304&&(n.$$scope={dirty:a,ctx:t}),e.$set(n)},i(t){s||(h(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function pe(l){let e,s;return e=new Y({}),{c(){$(e.$$.fragment)},m(t,a){w(e,t,a),s=!0},i(t){s||(h(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function Be(l){let e,s,t,a,n,f,g;const i=[{autoscroll:l[1].autoscroll},{i18n:l[1].i18n},l[10]];let c={};for(let u=0;u<i.length;u+=1)c=Q(c,i[u]);e=new V({props:c}),e.$on("clear_status",l[17]);let o=l[9]&&R(l);const b=[ke,ve],k=[];function B(u,m){return!u[0]&&!u[14]?0:1}return a=B(l),n=k[a]=b[a](l),{c(){$(e.$$.fragment),s=D(),o&&o.c(),t=D(),n.c(),f=X()},m(u,m){w(e,u,m),C(u,s,m),o&&o.m(u,m),C(u,t,m),k[a].m(u,m),C(u,f,m),g=!0},p(u,m){const j=m&1026?Z(i,[m&2&&{autoscroll:u[1].autoscroll},m&2&&{i18n:u[1].i18n},m&1024&&x(u[10])]):{};e.$set(j),u[9]?o?(o.p(u,m),m&512&&h(o,1)):(o=R(u),o.c(),h(o,1),o.m(t.parentNode,t)):o&&(S(),d(o,1,1,()=>{o=null}),U());let p=a;a=B(u),a===p?k[a].p(u,m):(S(),d(k[p],1,1,()=>{k[p]=null}),U(),n=k[a],n?n.p(u,m):(n=k[a]=b[a](u),n.c()),h(n,1),n.m(f.parentNode,f))},i(u){g||(h(e.$$.fragment,u),h(o),h(n),g=!0)},o(u){d(e.$$.fragment,u),d(o),d(n),g=!1},d(u){u&&(I(s),I(t),I(f)),v(e,u),o&&o.d(u),k[a].d(u)}}}function Ce(l){let e,s;return e=new M({props:{height:l[4]&&"fit-content",variant:"solid",padding:!1,elem_id:l[5],elem_classes:l[6],visible:l[7],scale:l[11],min_width:l[12],$$slots:{default:[Be]},$$scope:{ctx:l}}}),{c(){$(e.$$.fragment)},m(t,a){w(e,t,a),s=!0},p(t,[a]){const n={};a&16&&(n.height=t[4]&&"fit-content"),a&32&&(n.elem_id=t[5]),a&64&&(n.elem_classes=t[6]),a&128&&(n.visible=t[7]),a&2048&&(n.scale=t[11]),a&4096&&(n.min_width=t[12]),a&4220703&&(n.$$scope={dirty:a,ctx:t}),e.$set(n)},i(t){s||(h(e.$$.fragment,t),s=!0)},o(t){d(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function Ie(l,e,s){let{gradio:t}=e,{value:a=""}=e,{value_is_output:n=!1}=e,{language:f=""}=e,{lines:g=5}=e,{max_lines:i=void 0}=e,{elem_id:c=""}=e,{elem_classes:o=[]}=e,{visible:b=!0}=e,{label:k=t.i18n("code.code")}=e,{show_label:B=!0}=e,{loading_status:u}=e,{scale:m=null}=e,{min_width:j=void 0}=e,{wrap_lines:p=!1}=e,{interactive:W}=e,A=t.theme==="dark";function F(){t.dispatch("change",a),n||t.dispatch("input")}P(()=>{s(16,n=!1)});const G=()=>t.dispatch("clear_status",u);function H(r){a=r,s(0,a)}const J=()=>t.dispatch("blur"),K=()=>t.dispatch("focus");return l.$$set=r=>{"gradio"in r&&s(1,t=r.gradio),"value"in r&&s(0,a=r.value),"value_is_output"in r&&s(16,n=r.value_is_output),"language"in r&&s(2,f=r.language),"lines"in r&&s(3,g=r.lines),"max_lines"in r&&s(4,i=r.max_lines),"elem_id"in r&&s(5,c=r.elem_id),"elem_classes"in r&&s(6,o=r.elem_classes),"visible"in r&&s(7,b=r.visible),"label"in r&&s(8,k=r.label),"show_label"in r&&s(9,B=r.show_label),"loading_status"in r&&s(10,u=r.loading_status),"scale"in r&&s(11,m=r.scale),"min_width"in r&&s(12,j=r.min_width),"wrap_lines"in r&&s(13,p=r.wrap_lines),"interactive"in r&&s(14,W=r.interactive)},l.$$.update=()=>{l.$$.dirty&1&&F()},[a,t,f,g,i,c,o,b,k,B,u,m,j,p,W,A,n,G,H,J,K]}class Ne extends y{constructor(e){super(),E(this,e,Ie,Ce,L,{gradio:1,value:0,value_is_output:16,language:2,lines:3,max_lines:4,elem_id:5,elem_classes:6,visible:7,label:8,show_label:9,loading_status:10,scale:11,min_width:12,wrap_lines:13,interactive:14})}get gradio(){return this.$$.ctx[1]}set gradio(e){this.$$set({gradio:e}),_()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get value_is_output(){return this.$$.ctx[16]}set value_is_output(e){this.$$set({value_is_output:e}),_()}get language(){return this.$$.ctx[2]}set language(e){this.$$set({language:e}),_()}get lines(){return this.$$.ctx[3]}set lines(e){this.$$set({lines:e}),_()}get max_lines(){return this.$$.ctx[4]}set max_lines(e){this.$$set({max_lines:e}),_()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),_()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),_()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),_()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),_()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),_()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),_()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),_()}get wrap_lines(){return this.$$.ctx[13]}set wrap_lines(e){this.$$set({wrap_lines:e}),_()}get interactive(){return this.$$.ctx[14]}set interactive(e){this.$$set({interactive:e}),_()}}export{ee as BaseCode,fe as BaseCopy,he as BaseDownload,Oe as BaseExample,we as BaseWidget,Ne as default};
//# sourceMappingURL=Index-CCkCbQuE.js.map
