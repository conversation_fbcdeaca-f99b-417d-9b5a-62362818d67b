import{a as c,i as d,s as h,Q as n,q as t,l as u,v as l,w as r,o as w}from"../lite.js";function f(i){let e,o,s;return{c(){e=n("svg"),o=n("polyline"),s=n("path"),t(o,"points","1 4 1 10 7 10"),t(s,"d","M3.51 15a9 9 0 1 0 2.13-9.36L1 10"),t(e,"aria-label","undo"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-rotate-ccw")},m(a,p){u(a,e,p),l(e,o),l(e,s)},p:r,i:r,o:r,d(a){a&&w(e)}}}class m extends c{constructor(e){super(),d(this,e,null,f,h,{})}}export{m as U};
//# sourceMappingURL=Undo-DitIvwTU.js.map
