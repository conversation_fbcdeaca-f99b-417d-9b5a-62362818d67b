import{aZ as v,a_ as z,a as I,i as N,s as P,f as g,p as q,q as H,r as b,l as S,w,o as X,a$ as B,D as F,I as K}from"../lite.js";var U=function(e,r,t){for(var n=t,l=0,s=e.length;n<r.length;){var h=r[n];if(l<=0&&r.slice(n,n+s)===e)return n;h==="\\"?n++:h==="{"?l++:h==="}"&&l--,n++}return-1},V=function(e){return e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},W=/^\\begin{/,Z=function(e,r){for(var t,n=[],l=new RegExp("("+r.map(f=>V(f.left)).join("|")+")");t=e.search(l),t!==-1;){t>0&&(n.push({type:"text",data:e.slice(0,t)}),e=e.slice(t));var s=r.findIndex(f=>e.startsWith(f.left));if(t=U(r[s].right,e,r[s].left.length),t===-1)break;var h=e.slice(0,t+r[s].right.length),c=W.test(h)?h:e.slice(r[s].left.length,t);n.push({type:"math",data:c,rawData:h,display:r[s].display}),e=e.slice(t+r[s].right.length)}return e!==""&&n.push({type:"text",data:e}),n},j=function(e,r){var t=Z(e,r.delimiters);if(t.length===1&&t[0].type==="text")return null;for(var n=document.createDocumentFragment(),l=0;l<t.length;l++)if(t[l].type==="text")n.appendChild(document.createTextNode(t[l].data));else{var s=document.createElement("span"),h=t[l].data;r.displayMode=t[l].display;try{r.preProcess&&(h=r.preProcess(h)),v.render(h,s,r)}catch(c){if(!(c instanceof v.ParseError))throw c;r.errorCallback("KaTeX auto-render: Failed to parse `"+t[l].data+"` with ",c),n.appendChild(document.createTextNode(t[l].rawData));continue}n.appendChild(s)}return n},G=function a(e,r){for(var t=0;t<e.childNodes.length;t++){var n=e.childNodes[t];if(n.nodeType===3){for(var l=n.textContent,s=n.nextSibling,h=0;s&&s.nodeType===Node.TEXT_NODE;)l+=s.textContent,s=s.nextSibling,h++;var c=j(l,r);if(c){for(var f=0;f<h;f++)n.nextSibling.remove();t+=c.childNodes.length-1,e.replaceChild(c,n)}else t+=h}else n.nodeType===1&&function(){var m=" "+n.className+" ",o=r.ignoredTags.indexOf(n.nodeName.toLowerCase())===-1&&r.ignoredClasses.every(_=>m.indexOf(" "+_+" ")===-1);o&&a(n,r)}()}},J=function(e,r){if(!e)throw new Error("No element provided to render");var t={};for(var n in r)r.hasOwnProperty(n)&&(t[n]=r[n]);t.delimiters=t.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],t.ignoredTags=t.ignoredTags||["script","noscript","style","textarea","pre","code","option"],t.ignoredClasses=t.ignoredClasses||[],t.errorCallback=t.errorCallback||console.error,t.macros=t.macros||{},G(e,t)};const Q=(a,e)=>{try{return!!a&&new URL(a).origin!==new URL(e).origin}catch{return!1}};function y(a,e){const r=new z,t=new DOMParser().parseFromString(a,"text/html");return T(t.body,"A",n=>{n instanceof HTMLElement&&"target"in n&&Q(n.getAttribute("href"),e)&&(n.setAttribute("target","_blank"),n.setAttribute("rel","noopener noreferrer"))}),r.sanitize(t).body.innerHTML}function T(a,e,r){a&&(a.nodeName===e||typeof e=="function")&&r(a);const t=a?.childNodes||[];for(let n=0;n<t.length;n++)T(t[n],e,r)}function Y(a){let e;return{c(){e=q("span"),H(e,"class","md svelte-7ddecg"),b(e,"chatbot",a[0]),b(e,"prose",a[1])},m(r,t){S(r,e,t),e.innerHTML=a[3],a[10](e)},p(r,[t]){t&8&&(e.innerHTML=r[3]),t&1&&b(e,"chatbot",r[0]),t&2&&b(e,"prose",r[1])},i:w,o:w,d(r){r&&X(e),a[10](null)}}}function E(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function $(a,e,r){let{chatbot:t=!0}=e,{message:n}=e,{sanitize_html:l=!0}=e,{latex_delimiters:s=[]}=e,{render_markdown:h=!0}=e,{line_breaks:c=!0}=e,{header_links:f=!1}=e,{root:m}=e,o,_;const M=B({header_links:f,line_breaks:c,latex_delimiters:s});function p(i){let d=i;if(h){const u=[];s.forEach((k,x)=>{const O=E(k.left),C=E(k.right),A=new RegExp(`${O}([\\s\\S]+?)${C}`,"g");d=d.replace(A,(R,ee)=>(u.push(R),`%%%LATEX_BLOCK_${u.length-1}%%%`))}),d=M.parse(d),d=d.replace(/%%%LATEX_BLOCK_(\d+)%%%/g,(k,x)=>u[parseInt(x,10)])}return l&&y&&(d=y(d,m)),d}async function L(i){s.length>0&&i&&s.some(u=>i.includes(u.left)&&i.includes(u.right))&&J(o,{delimiters:s,throwOnError:!1})}F(async()=>{o&&document.body.contains(o)?await L(n):console.error("Element is not in the DOM")});function D(i){K[i?"unshift":"push"](()=>{o=i,r(2,o)})}return a.$$set=i=>{"chatbot"in i&&r(0,t=i.chatbot),"message"in i&&r(4,n=i.message),"sanitize_html"in i&&r(5,l=i.sanitize_html),"latex_delimiters"in i&&r(6,s=i.latex_delimiters),"render_markdown"in i&&r(1,h=i.render_markdown),"line_breaks"in i&&r(7,c=i.line_breaks),"header_links"in i&&r(8,f=i.header_links),"root"in i&&r(9,m=i.root)},a.$$.update=()=>{a.$$.dirty&16&&(n&&n.trim()?r(3,_=p(n)):r(3,_=""))},[t,h,o,_,n,l,s,c,f,m,D]}class re extends I{constructor(e){super(),N(this,e,$,Y,P,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8,root:9})}get chatbot(){return this.$$.ctx[0]}set chatbot(e){this.$$set({chatbot:e}),g()}get message(){return this.$$.ctx[4]}set message(e){this.$$set({message:e}),g()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),g()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),g()}get render_markdown(){return this.$$.ctx[1]}set render_markdown(e){this.$$set({render_markdown:e}),g()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),g()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),g()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),g()}}export{re as M};
//# sourceMappingURL=MarkdownCode-C6RHM0m6.js.map
