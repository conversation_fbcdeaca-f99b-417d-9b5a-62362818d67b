{"version": 3, "file": "Example15-Dtw09g22.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example15.js"], "sourcesContent": ["import { create_ssr_component, validate_component } from \"svelte/internal\";\nimport \"./Index22.js\";\nimport { I as Image } from \"./Image.js\";\nconst css = {\n  code: \".container.svelte-jhlhb0 img{width:100%;height:100%}.container.selected.svelte-jhlhb0{border-color:var(--border-color-accent)}.container.table.svelte-jhlhb0{margin:0 auto;border:2px solid var(--border-color-primary);border-radius:var(--radius-lg);width:var(--size-20);height:var(--size-20);object-fit:cover}.container.gallery.svelte-jhlhb0{border:2px solid var(--border-color-primary);height:var(--size-20);max-height:var(--size-20);object-fit:cover}\",\n  map: '{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { BaseImage as Image } from \\\\\"@gradio/image\\\\\";\\\\nexport let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"container\\\\\"\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n>\\\\n\\\\t<Image src={value.composite?.url || value.background?.url} alt=\\\\\"\\\\\" />\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.container :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.selected {\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.container.table {\\\\n\\\\t\\\\tmargin: 0 auto;\\\\n\\\\t\\\\tborder: 2px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\theight: var(--size-20);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.gallery {\\\\n\\\\t\\\\tborder: 2px solid var(--border-color-primary);\\\\n\\\\t\\\\theight: var(--size-20);\\\\n\\\\t\\\\tmax-height: var(--size-20);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAgBC,wBAAU,CAAS,GAAK,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,UAAU,uBAAU,CACnB,YAAY,CAAE,IAAI,qBAAqB,CACxC,CAEA,UAAU,oBAAO,CAChB,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,KACb,CAEA,UAAU,sBAAS,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,UAAU,CAAE,KACb\"}'\n};\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  $$result.css.add(css);\n  return `<div class=\"${[\n    \"container svelte-jhlhb0\",\n    (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\")\n  ].join(\" \").trim()}\">${validate_component(Image, \"Image\").$$render(\n    $$result,\n    {\n      src: value.composite?.url || value.background?.url,\n      alt: \"\"\n    },\n    {},\n    {}\n  )} </div>`;\n});\nexport {\n  Example as default\n};\n"], "names": ["Image"], "mappings": ";;;;;;;;;;;AAGA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,ocAAoc;AAC5c,EAAE,GAAG,EAAE,skDAAskD;AAC7kD,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,yBAAyB;AAC7B,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC;AACxH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAACA,OAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACpE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,GAAG,EAAE,KAAK,CAAC,SAAS,EAAE,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE,GAAG;AACxD,MAAM,GAAG,EAAE,EAAE;AACb,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,OAAO,CAAC,CAAC;AACb,CAAC;;;;"}