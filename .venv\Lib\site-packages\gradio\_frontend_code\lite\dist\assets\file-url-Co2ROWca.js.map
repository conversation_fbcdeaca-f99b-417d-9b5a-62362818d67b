{"version": 3, "file": "file-url-Co2ROWca.js", "sources": ["../../../wasm/svelte/file-url.ts"], "sourcesContent": ["import { getWorkerProxyContext } from \"./context\";\nimport { is_self_host } from \"../network/host\";\nimport { getHeaderValue } from \"../src/http\";\nimport type { WorkerProxy } from \"../src/worker-proxy\";\n\ntype MediaSrc = string | undefined | null;\n\nexport function should_proxy_wasm_src(src: MediaSrc): boolean {\n\tconst is_browser = typeof window !== \"undefined\";\n\n\tif (src == null || !is_browser) {\n\t\treturn false;\n\t}\n\n\tconst url = new URL(src, window.location.href);\n\tif (!is_self_host(url)) {\n\t\t// `src` is not accessing a local server resource, so we don't need to proxy this request to the Wasm worker.\n\t\treturn false;\n\t}\n\tif (url.protocol !== \"http:\" && url.protocol !== \"https:\") {\n\t\t// `src` can be a data URL.\n\t\treturn false;\n\t}\n\n\treturn true;\n}\n\nlet maybeWorkerProxy: WorkerProxy | undefined;\n\nexport async function resolve_wasm_src(src: MediaSrc): Promise<MediaSrc> {\n\tconst is_browser = typeof window !== \"undefined\";\n\tif (src == null || !is_browser || !should_proxy_wasm_src(src)) {\n\t\treturn src;\n\t}\n\n\tif (maybeWorkerProxy == null) {\n\t\ttry {\n\t\t\tmaybeWorkerProxy = getWorkerProxyContext();\n\t\t} catch (e) {\n\t\t\t// We are not in the Wasm env. Just use the src as is.\n\t\t\treturn src;\n\t\t}\n\t}\n\n\tif (maybeWorkerProxy == null) {\n\t\t// We are not in the Wasm env. Just use the src as is.\n\t\treturn src;\n\t}\n\n\tconst url = new URL(src, window.location.href);\n\tconst path = url.pathname;\n\treturn maybeWorkerProxy\n\t\t.httpRequest({\n\t\t\tmethod: \"GET\",\n\t\t\tpath,\n\t\t\theaders: {},\n\t\t\tquery_string: \"\"\n\t\t})\n\t\t.then((response) => {\n\t\t\tif (response.status !== 200) {\n\t\t\t\tthrow new Error(`Failed to get file ${path} from the Wasm worker.`);\n\t\t\t}\n\t\t\tconst blob = new Blob([response.body], {\n\t\t\t\ttype: getHeaderValue(response.headers, \"content-type\")\n\t\t\t});\n\t\t\tconst blobUrl = URL.createObjectURL(blob);\n\t\t\treturn blobUrl;\n\t\t});\n}\n"], "names": ["should_proxy_wasm_src", "src", "is_browser", "url", "is_self_host", "maybeWorkerProxy", "resolve_wasm_src", "getWorkerProxyContext", "path", "response", "blob", "getHeaderValue"], "mappings": "6CAOO,SAASA,EAAsBC,EAAwB,CACvD,MAAAC,EAAa,OAAO,OAAW,IAEjC,GAAAD,GAAO,MAAQ,CAACC,EACZ,MAAA,GAGR,MAAMC,EAAM,IAAI,IAAIF,EAAK,OAAO,SAAS,IAAI,EAK7C,MAJI,GAACG,EAAaD,CAAG,GAIjBA,EAAI,WAAa,SAAWA,EAAI,WAAa,SAMlD,CAEA,IAAIE,EAEJ,eAAsBC,EAAiBL,EAAkC,CAClE,MAAAC,EAAa,OAAO,OAAW,IACrC,GAAID,GAAO,MAAQ,CAACC,GAAc,CAACF,EAAsBC,CAAG,EACpD,OAAAA,EAGR,GAAII,GAAoB,KACnB,GAAA,CACHA,EAAmBE,EAAsB,OAC9B,CAEJ,OAAAN,CACR,CAGD,GAAII,GAAoB,KAEhB,OAAAJ,EAIR,MAAMO,EADM,IAAI,IAAIP,EAAK,OAAO,SAAS,IAAI,EAC5B,SACjB,OAAOI,EACL,YAAY,CACZ,OAAQ,MACR,KAAAG,EACA,QAAS,CAAC,EACV,aAAc,EAAA,CACd,EACA,KAAMC,GAAa,CACf,GAAAA,EAAS,SAAW,IACvB,MAAM,IAAI,MAAM,sBAAsBD,CAAI,wBAAwB,EAEnE,MAAME,EAAO,IAAI,KAAK,CAACD,EAAS,IAAI,EAAG,CACtC,KAAME,EAAeF,EAAS,QAAS,cAAc,CAAA,CACrD,EAEM,OADS,IAAI,gBAAgBC,CAAI,CACjC,CACP,CACH"}