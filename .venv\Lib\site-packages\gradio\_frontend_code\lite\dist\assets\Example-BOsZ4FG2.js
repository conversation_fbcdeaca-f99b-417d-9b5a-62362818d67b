import{a as g,i as d,s as m,f as n,p as y,q as v,r as i,l as _,w as h,o as b}from"../lite.js";function x(a){let e;return{c(){e=y("div"),e.textContent=`${a[2]}`,v(e,"class","svelte-1ayixqk"),i(e,"table",a[0]==="table"),i(e,"gallery",a[0]==="gallery"),i(e,"selected",a[1])},m(t,l){_(t,e,l)},p(t,[l]){l&1&&i(e,"table",t[0]==="table"),l&1&&i(e,"gallery",t[0]==="gallery"),l&2&&i(e,"selected",t[1])},i:h,o:h,d(t){t&&b(e)}}}function q(a,e,t){let{value:l}=e,{type:f}=e,{selected:u=!1}=e,{choices:c}=e,r=l.map(s=>c.find(o=>o[1]===s)?.[0]).filter(s=>s!==void 0).join(", ");return a.$$set=s=>{"value"in s&&t(3,l=s.value),"type"in s&&t(0,f=s.type),"selected"in s&&t(1,u=s.selected),"choices"in s&&t(4,c=s.choices)},[f,u,r,l,c]}class k extends g{constructor(e){super(),d(this,e,q,x,m,{value:3,type:0,selected:1,choices:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),n()}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),n()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),n()}get choices(){return this.$$.ctx[4]}set choices(e){this.$$set({choices:e}),n()}}export{k as default};
//# sourceMappingURL=Example-BOsZ4FG2.js.map
