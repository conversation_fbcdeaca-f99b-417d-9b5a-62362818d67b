{"version": 3, "file": "webworker-C_cbzHXU.js", "sources": ["../../wasm/dist/webworker/webworker.js"], "sourcesContent": ["var D=Object.defineProperty;var F=(o,e,t)=>e in o?D(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var w=(o,e,t)=>(F(o,typeof e!=\"symbol\"?e+\"\":e,t),t);function H(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,\"default\")?o.default:o}function f(o){if(typeof o!=\"string\")throw new TypeError(\"Path must be a string. Received \"+JSON.stringify(o))}function O(o,e){for(var t=\"\",s=0,r=-1,l=0,n,a=0;a<=o.length;++a){if(a<o.length)n=o.charCodeAt(a);else{if(n===47)break;n=47}if(n===47){if(!(r===a-1||l===1))if(r!==a-1&&l===2){if(t.length<2||s!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){var i=t.lastIndexOf(\"/\");if(i!==t.length-1){i===-1?(t=\"\",s=0):(t=t.slice(0,i),s=t.length-1-t.lastIndexOf(\"/\")),r=a,l=0;continue}}else if(t.length===2||t.length===1){t=\"\",s=0,r=a,l=0;continue}}e&&(t.length>0?t+=\"/..\":t=\"..\",s=2)}else t.length>0?t+=\"/\"+o.slice(r+1,a):t=o.slice(r+1,a),s=a-r-1;r=a,l=0}else n===46&&l!==-1?++l:l=-1}return t}function U(o,e){var t=e.dir||e.root,s=e.base||(e.name||\"\")+(e.ext||\"\");return t?t===e.root?t+s:t+o+s:s}var g={resolve:function(){for(var e=\"\",t=!1,s,r=arguments.length-1;r>=-1&&!t;r--){var l;r>=0?l=arguments[r]:(s===void 0&&(s=process.cwd()),l=s),f(l),l.length!==0&&(e=l+\"/\"+e,t=l.charCodeAt(0)===47)}return e=O(e,!t),t?e.length>0?\"/\"+e:\"/\":e.length>0?e:\".\"},normalize:function(e){if(f(e),e.length===0)return\".\";var t=e.charCodeAt(0)===47,s=e.charCodeAt(e.length-1)===47;return e=O(e,!t),e.length===0&&!t&&(e=\".\"),e.length>0&&s&&(e+=\"/\"),t?\"/\"+e:e},isAbsolute:function(e){return f(e),e.length>0&&e.charCodeAt(0)===47},join:function(){if(arguments.length===0)return\".\";for(var e,t=0;t<arguments.length;++t){var s=arguments[t];f(s),s.length>0&&(e===void 0?e=s:e+=\"/\"+s)}return e===void 0?\".\":g.normalize(e)},relative:function(e,t){if(f(e),f(t),e===t||(e=g.resolve(e),t=g.resolve(t),e===t))return\"\";for(var s=1;s<e.length&&e.charCodeAt(s)===47;++s);for(var r=e.length,l=r-s,n=1;n<t.length&&t.charCodeAt(n)===47;++n);for(var a=t.length,i=a-n,p=l<i?l:i,d=-1,c=0;c<=p;++c){if(c===p){if(i>p){if(t.charCodeAt(n+c)===47)return t.slice(n+c+1);if(c===0)return t.slice(n+c)}else l>p&&(e.charCodeAt(s+c)===47?d=c:c===0&&(d=0));break}var u=e.charCodeAt(s+c),_=t.charCodeAt(n+c);if(u!==_)break;u===47&&(d=c)}var m=\"\";for(c=s+d+1;c<=r;++c)(c===r||e.charCodeAt(c)===47)&&(m.length===0?m+=\"..\":m+=\"/..\");return m.length>0?m+t.slice(n+d):(n+=d,t.charCodeAt(n)===47&&++n,t.slice(n))},_makeLong:function(e){return e},dirname:function(e){if(f(e),e.length===0)return\".\";for(var t=e.charCodeAt(0),s=t===47,r=-1,l=!0,n=e.length-1;n>=1;--n)if(t=e.charCodeAt(n),t===47){if(!l){r=n;break}}else l=!1;return r===-1?s?\"/\":\".\":s&&r===1?\"//\":e.slice(0,r)},basename:function(e,t){if(t!==void 0&&typeof t!=\"string\")throw new TypeError('\"ext\" argument must be a string');f(e);var s=0,r=-1,l=!0,n;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return\"\";var a=t.length-1,i=-1;for(n=e.length-1;n>=0;--n){var p=e.charCodeAt(n);if(p===47){if(!l){s=n+1;break}}else i===-1&&(l=!1,i=n+1),a>=0&&(p===t.charCodeAt(a)?--a===-1&&(r=n):(a=-1,r=i))}return s===r?r=i:r===-1&&(r=e.length),e.slice(s,r)}else{for(n=e.length-1;n>=0;--n)if(e.charCodeAt(n)===47){if(!l){s=n+1;break}}else r===-1&&(l=!1,r=n+1);return r===-1?\"\":e.slice(s,r)}},extname:function(e){f(e);for(var t=-1,s=0,r=-1,l=!0,n=0,a=e.length-1;a>=0;--a){var i=e.charCodeAt(a);if(i===47){if(!l){s=a+1;break}continue}r===-1&&(l=!1,r=a+1),i===46?t===-1?t=a:n!==1&&(n=1):t!==-1&&(n=-1)}return t===-1||r===-1||n===0||n===1&&t===r-1&&t===s+1?\"\":e.slice(t,r)},format:function(e){if(e===null||typeof e!=\"object\")throw new TypeError('The \"pathObject\" argument must be of type Object. Received type '+typeof e);return U(\"/\",e)},parse:function(e){f(e);var t={root:\"\",dir:\"\",base:\"\",ext:\"\",name:\"\"};if(e.length===0)return t;var s=e.charCodeAt(0),r=s===47,l;r?(t.root=\"/\",l=1):l=0;for(var n=-1,a=0,i=-1,p=!0,d=e.length-1,c=0;d>=l;--d){if(s=e.charCodeAt(d),s===47){if(!p){a=d+1;break}continue}i===-1&&(p=!1,i=d+1),s===46?n===-1?n=d:c!==1&&(c=1):n!==-1&&(c=-1)}return n===-1||i===-1||c===0||c===1&&n===i-1&&n===a+1?i!==-1&&(a===0&&r?t.base=t.name=e.slice(1,i):t.base=t.name=e.slice(a,i)):(a===0&&r?(t.name=e.slice(1,n),t.base=e.slice(1,i)):(t.name=e.slice(a,n),t.base=e.slice(a,i)),t.ext=e.slice(n,i)),a>0?t.dir=e.slice(0,a-1):r&&(t.dir=\"/\"),t},sep:\"/\",delimiter:\":\",win32:null,posix:null};g.posix=g;var W=g;const A=H(W),x=\"/home/<USER>\",S=o=>`${x}/${o}`,b=(o,e)=>(A.normalize(e),A.resolve(S(o),e));function R(o,e){const t=A.normalize(e),r=A.dirname(t).split(\"/\"),l=[];for(const n of r){l.push(n);const a=l.join(\"/\");if(o.FS.analyzePath(a).exists){if(o.FS.isDir(a))throw new Error(`\"${a}\" already exists and is not a directory.`);continue}try{o.FS.mkdir(a)}catch(i){throw console.error(`Failed to create a directory \"${a}\"`),i}}}function N(o,e,t,s){R(o,e),o.FS.writeFile(e,t,s)}function z(o,e,t){R(o,t),o.FS.rename(e,t)}function G(o){o.forEach(e=>{let t;try{t=new URL(e)}catch{return}if(t.protocol===\"emfs:\"||t.protocol===\"file:\")throw new Error(`\"emfs:\" and \"file:\" protocols are not allowed for the requirement (${e})`)})}class q{constructor(){w(this,\"_buffer\",[]);w(this,\"_promise\");w(this,\"_resolve\");this._resolve=null,this._promise=null,this._notifyAll()}async _wait(){await this._promise}_notifyAll(){this._resolve&&this._resolve(),this._promise=new Promise(e=>this._resolve=e)}async dequeue(){for(;this._buffer.length===0;)await this._wait();return this._buffer.shift()}enqueue(e){this._buffer.push(e),this._notifyAll()}}function Y(o,e,t){const s=new q;t.addEventListener(\"message\",n=>{s.enqueue(n.data)}),t.start();async function r(){return await s.dequeue()}async function l(n){const a=Object.fromEntries(n.toJs());t.postMessage(a)}return o(e,r,l)}const k=\"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789\";function B(o){return Array.from(Array(o)).map(()=>k[Math.floor(Math.random()*k.length)]).join(\"\")}const j=`import ast\nimport os\nimport sys\nimport tokenize\nimport types\nfrom inspect import CO_COROUTINE\n\nfrom gradio.wasm_utils import app_id_context\n\n# BSD 3-Clause License\n#\n# - Copyright (c) 2008-Present, IPython Development Team\n# <AUTHOR> <EMAIL>\n# <AUTHOR> <EMAIL>\n# <AUTHOR> <EMAIL>\n#\n# All rights reserved.\n#\n# Redistribution and use in source and binary forms, with or without\n# modification, are permitted provided that the following conditions are met:\n#\n# * Redistributions of source code must retain the above copyright notice, this\n#   list of conditions and the following disclaimer.\n\n# * Redistributions in binary form must reproduce the above copyright notice,\n#   this list of conditions and the following disclaimer in the documentation\n#   and/or other materials provided with the distribution.\n\n# * Neither the name of the copyright holder nor the names of its\n#   contributors may be used to endorse or promote products derived from\n#   this software without specific prior written permission.\n\n# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\n# Code modified from IPython (BSD license)\n# Source: https://github.com/ipython/ipython/blob/master/IPython/utils/syspathcontext.py#L42\nclass modified_sys_path:  # noqa: N801\n    \"\"\"A context for prepending a directory to sys.path for a second.\"\"\"\n\n    def __init__(self, script_path: str):\n        self._script_path = script_path\n        self._added_path = False\n\n    def __enter__(self):\n        if self._script_path not in sys.path:\n            sys.path.insert(0, self._script_path)\n            self._added_path = True\n\n    def __exit__(self, type, value, traceback):\n        if self._added_path:\n            try:\n                sys.path.remove(self._script_path)\n            except ValueError:\n                # It's already removed.\n                pass\n\n        # Returning False causes any exceptions to be re-raised.\n        return False\n\n\n# Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022)\n# Copyright (c) Yuichiro Tachibana (2023)\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this file except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#     http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\ndef _new_module(name: str) -> types.ModuleType:\n    \"\"\"Create a new module with the given name.\"\"\"\n    return types.ModuleType(name)\n\n\ndef set_home_dir(home_dir: str) -> None:\n    os.environ[\"HOME\"] = home_dir\n    os.chdir(home_dir)\n\n\nasync def _run_script(app_id: str, home_dir: str, script_path: str) -> None:\n    # This function is based on the following code from Streamlit:\n    # https://github.com/streamlit/streamlit/blob/1.24.0/lib/streamlit/runtime/scriptrunner/script_runner.py#L519-L554\n    # with modifications to support top-level await.\n    set_home_dir(home_dir)\n\n    with tokenize.open(script_path) as f:\n        filebody = f.read()\n\n    await _run_code(app_id, home_dir, filebody, script_path)\n\n\nasync def _run_code(\n        app_id: str,\n        home_dir: str,\n        filebody: str,\n        script_path: str = '<string>'  # This default value follows the convention. Ref: https://docs.python.org/3/library/functions.html#compile\n    ) -> None:\n    set_home_dir(home_dir)\n\n    # NOTE: In Streamlit, the bytecode caching mechanism has been introduced.\n    # However, we skipped it here for simplicity and because Gradio doesn't need to rerun the script so frequently,\n    # while we may do it in the future.\n    bytecode = compile(  # type: ignore\n        filebody,\n        # Pass in the file path so it can show up in exceptions.\n        script_path,\n        # We're compiling entire blocks of Python, so we need \"exec\"\n        # mode (as opposed to \"eval\" or \"single\").\n        mode=\"exec\",\n        # Don't inherit any flags or \"future\" statements.\n        flags=ast.PyCF_ALLOW_TOP_LEVEL_AWAIT, # Allow top-level await. Ref: https://github.com/whitphx/streamlit/commit/277dc580efb315a3e9296c9a0078c602a0904384\n        dont_inherit=1,\n        # Use the default optimization options.\n        optimize=-1,\n    )\n\n    module = _new_module(\"__main__\")\n\n    # Install the fake module as the __main__ module. This allows\n    # the pickle module to work inside the user's code, since it now\n    # can know the module where the pickled objects stem from.\n    # IMPORTANT: This means we can't use \"if __name__ == '__main__'\" in\n    # our code, as it will point to the wrong module!!!\n    sys.modules[\"__main__\"] = module\n\n    # Add special variables to the module's globals dict.\n    module.__dict__[\"__file__\"] = script_path\n\n    with modified_sys_path(script_path), modified_sys_path(home_dir), app_id_context(app_id):\n        # Allow top-level await. Ref: https://github.com/whitphx/streamlit/commit/277dc580efb315a3e9296c9a0078c602a0904384\n        if bytecode.co_flags & CO_COROUTINE:\n            # The source code includes top-level awaits, so the compiled code object is a coroutine.\n            await eval(bytecode, module.__dict__)\n        else:\n            exec(bytecode, module.__dict__)\n`,$=`# Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022)\n# Copyright (c) Yuichiro Tachibana (2023)\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this file except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#     http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n\nimport fnmatch\nimport logging\nimport os\nimport sys\nimport types\nfrom typing import Optional, Set\n\nLOGGER = logging.getLogger(__name__)\n\n#\n# Copied from https://github.com/streamlit/streamlit/blob/1.24.0/lib/streamlit/file_util.py\n#\n\ndef file_is_in_folder_glob(filepath, folderpath_glob) -> bool:\n    \"\"\"Test whether a file is in some folder with globbing support.\n\n    Parameters\n    ----------\n    filepath : str\n        A file path.\n    folderpath_glob: str\n        A path to a folder that may include globbing.\n\n    \"\"\"\n    # Make the glob always end with \"/*\" so we match files inside subfolders of\n    # folderpath_glob.\n    if not folderpath_glob.endswith(\"*\"):\n        if folderpath_glob.endswith(\"/\"):\n            folderpath_glob += \"*\"\n        else:\n            folderpath_glob += \"/*\"\n\n    file_dir = os.path.dirname(filepath) + \"/\"\n    return fnmatch.fnmatch(file_dir, folderpath_glob)\n\n\ndef get_directory_size(directory: str) -> int:\n    \"\"\"Return the size of a directory in bytes.\"\"\"\n    total_size = 0\n    for dirpath, _, filenames in os.walk(directory):\n        for f in filenames:\n            fp = os.path.join(dirpath, f)\n            total_size += os.path.getsize(fp)\n    return total_size\n\n\ndef file_in_pythonpath(filepath) -> bool:\n    \"\"\"Test whether a filepath is in the same folder of a path specified in the PYTHONPATH env variable.\n\n\n    Parameters\n    ----------\n    filepath : str\n        An absolute file path.\n\n    Returns\n    -------\n    boolean\n        True if contained in PYTHONPATH, False otherwise. False if PYTHONPATH is not defined or empty.\n\n    \"\"\"\n    pythonpath = os.environ.get(\"PYTHONPATH\", \"\")\n    if len(pythonpath) == 0:\n        return False\n\n    absolute_paths = [os.path.abspath(path) for path in pythonpath.split(os.pathsep)]\n    return any(\n        file_is_in_folder_glob(os.path.normpath(filepath), path)\n        for path in absolute_paths\n    )\n\n#\n# Copied from https://github.com/streamlit/streamlit/blob/1.24.0/lib/streamlit/watcher/local_sources_watcher.py\n#\n\ndef get_module_paths(module: types.ModuleType) -> Set[str]:\n    paths_extractors = [\n        # https://docs.python.org/3/reference/datamodel.html\n        # __file__ is the pathname of the file from which the module was loaded\n        # if it was loaded from a file.\n        # The __file__ attribute may be missing for certain types of modules\n        lambda m: [m.__file__],\n        # https://docs.python.org/3/reference/import.html#__spec__\n        # The __spec__ attribute is set to the module spec that was used\n        # when importing the module. one exception is __main__,\n        # where __spec__ is set to None in some cases.\n        # https://www.python.org/dev/peps/pep-0451/#id16\n        # \"origin\" in an import context means the system\n        # (or resource within a system) from which a module originates\n        # ... It is up to the loader to decide on how to interpret\n        # and use a module's origin, if at all.\n        lambda m: [m.__spec__.origin],\n        # https://www.python.org/dev/peps/pep-0420/\n        # Handling of \"namespace packages\" in which the __path__ attribute\n        # is a _NamespacePath object with a _path attribute containing\n        # the various paths of the package.\n        lambda m: list(m.__path__._path),\n    ]\n\n    all_paths = set()\n    for extract_paths in paths_extractors:\n        potential_paths = []\n        try:\n            potential_paths = extract_paths(module)\n        except AttributeError:\n            # Some modules might not have __file__ or __spec__ attributes.\n            pass\n        except Exception as e:\n            LOGGER.warning(f\"Examining the path of {module.__name__} raised: {e}\")\n\n        all_paths.update(\n            [os.path.abspath(str(p)) for p in potential_paths if _is_valid_path(p)]\n        )\n    return all_paths\n\n\ndef _is_valid_path(path: Optional[str]) -> bool:\n    return isinstance(path, str) and (os.path.isfile(path) or os.path.isdir(path))\n\n\n#\n# Original code\n#\n\ndef unload_local_modules(target_dir_path: str = \".\"):\n    \"\"\" Unload all modules that are in the target directory or in a subdirectory of it.\n    It is necessary to unload modules before re-executing a script that imports the modules,\n    so that the new version of the modules is loaded.\n    The module unloading feature is extracted from Streamlit's LocalSourcesWatcher (https://github.com/streamlit/streamlit/blob/1.24.0/lib/streamlit/watcher/local_sources_watcher.py)\n    and packaged as a standalone function.\n    \"\"\"\n    target_dir_path = os.path.abspath(target_dir_path)\n    loaded_modules = {} # filepath -> module_name\n\n    # Copied from \\`LocalSourcesWatcher.update_watched_modules()\\`\n    module_paths = {\n        name: get_module_paths(module)\n        for name, module in dict(sys.modules).items()\n    }\n\n    # Copied from \\`LocalSourcesWatcher._register_necessary_watchers()\\`\n    for name, paths in module_paths.items():\n        for path in paths:\n            if file_is_in_folder_glob(path, target_dir_path) or file_in_pythonpath(path):\n                loaded_modules[path] = name\n\n    # Copied from \\`LocalSourcesWatcher.on_file_changed()\\`\n    for module_name in loaded_modules.values():\n        if module_name is not None and module_name in sys.modules:\n            del sys.modules[module_name]\n`;importScripts(\"https://cdn.jsdelivr.net/pyodide/v0.26.1/full/pyodide.js\");let h,v,L,C,M,E;async function V(o,e){console.debug(\"Loading Pyodide.\"),e(\"Loading Pyodide\"),h=await loadPyodide({stdout:console.debug,stderr:console.error}),console.debug(\"Pyodide is loaded.\"),console.debug(\"Loading micropip\"),e(\"Loading micropip\"),await h.loadPackage(\"micropip\"),v=h.pyimport(\"micropip\"),console.debug(\"micropip is loaded.\");const t=[o.gradioWheelUrl,o.gradioClientWheelUrl];console.debug(\"Loading Gradio wheels.\",t),e(\"Loading Gradio wheels\"),await h.loadPackage([\"ssl\",\"setuptools\"]),await v.add_mock_package(\"ffmpy\",\"0.3.0\"),await v.install.callKwargs(t,{keep_going:!0}),console.debug(\"Gradio wheels are loaded.\"),console.debug(\"Mocking os module methods.\"),e(\"Mock os module methods\"),await h.runPythonAsync(`\nimport os\n\nos.link = lambda src, dst: None\n`),console.debug(\"os module methods are mocked.\"),console.debug(\"Importing gradio package.\"),e(\"Importing gradio package\"),await h.runPythonAsync(\"import gradio\"),console.debug(\"gradio package is imported.\"),console.debug(\"Defining a ASGI wrapper function.\"),e(\"Defining a ASGI wrapper function\"),await h.runPythonAsync(`\n# Based on Shiny's App.call_pyodide().\n# https://github.com/rstudio/py-shiny/blob/v0.3.3/shiny/_app.py#L224-L258\nasync def _call_asgi_app_from_js(app_id, scope, receive, send):\n\t# TODO: Pretty sure there are objects that need to be destroy()'d here?\n\tscope = scope.to_py()\n\n\t# ASGI requires some values to be byte strings, not character strings. Those are\n\t# not that easy to create in JavaScript, so we let the JS side pass us strings\n\t# and we convert them to bytes here.\n\tif \"headers\" in scope:\n\t\t\t# JS doesn't have \\`bytes\\` so we pass as strings and convert here\n\t\t\tscope[\"headers\"] = [\n\t\t\t\t\t[value.encode(\"latin-1\") for value in header]\n\t\t\t\t\tfor header in scope[\"headers\"]\n\t\t\t]\n\tif \"query_string\" in scope and scope[\"query_string\"]:\n\t\t\tscope[\"query_string\"] = scope[\"query_string\"].encode(\"latin-1\")\n\tif \"raw_path\" in scope and scope[\"raw_path\"]:\n\t\t\tscope[\"raw_path\"] = scope[\"raw_path\"].encode(\"latin-1\")\n\n\tasync def rcv():\n\t\t\tevent = await receive()\n\t\t\tpy_event = event.to_py()\n\t\t\tif \"body\" in py_event:\n\t\t\t\t\tif isinstance(py_event[\"body\"], memoryview):\n\t\t\t\t\t\t\tpy_event[\"body\"] = py_event[\"body\"].tobytes()\n\t\t\treturn py_event\n\n\tasync def snd(event):\n\t\t\tawait send(event)\n\n\tapp = gradio.wasm_utils.get_registered_app(app_id)\n\tif app is None:\n\t\traise RuntimeError(\"Gradio app has not been launched.\")\n\n\tawait app(scope, rcv, snd)\n`),L=h.globals.get(\"_call_asgi_app_from_js\"),console.debug(\"The ASGI wrapper function is defined.\"),console.debug(\"Mocking async libraries.\"),e(\"Mocking async libraries\"),await h.runPythonAsync(`\nasync def mocked_anyio_to_thread_run_sync(func, *args, cancellable=False, limiter=None):\n\treturn func(*args)\n\nimport anyio.to_thread\nanyio.to_thread.run_sync = mocked_anyio_to_thread_run_sync\n\t`),console.debug(\"Async libraries are mocked.\"),console.debug(\"Setting up Python utility functions.\"),e(\"Setting up Python utility functions\"),await h.runPythonAsync(j),C=h.globals.get(\"_run_code\"),M=h.globals.get(\"_run_script\"),await h.runPythonAsync($),E=h.globals.get(\"unload_local_modules\"),console.debug(\"Python utility functions are set up.\"),e(\"Initialization completed\")}async function J(o,e,t,s){const r=S(o);console.debug(\"Creating a home directory for the app.\",{appId:o,appHomeDir:r}),h.FS.mkdir(r),console.debug(\"Mounting files.\",e.files),t(\"Mounting files\");const l=[];await Promise.all(Object.keys(e.files).map(async d=>{const c=e.files[d];let u;\"url\"in c?(console.debug(`Fetch a file from ${c.url}`),u=await fetch(c.url).then(I=>I.arrayBuffer()).then(I=>new Uint8Array(I))):u=c.data;const{opts:_}=e.files[d],m=b(o,d);console.debug(`Write a file \"${m}\"`),N(h,m,u,_),typeof u==\"string\"&&d.endsWith(\".py\")&&l.push(u)})),console.debug(\"Files are mounted.\"),console.debug(\"Installing packages.\",e.requirements),t(\"Installing packages\"),await v.install.callKwargs(e.requirements,{keep_going:!0}),console.debug(\"Packages are installed.\"),console.debug(\"Auto-loading modules.\");const n=await Promise.all(l.map(d=>h.loadPackagesFromImports(d))),a=new Set(n.flat()),i=Array.from(a);i.length>0&&s(i);const p=i.map(d=>d.name);console.debug(\"Modules are auto-loaded.\",i),(e.requirements.includes(\"matplotlib\")||p.includes(\"matplotlib\"))&&(console.debug(\"Setting matplotlib backend.\"),t(\"Setting matplotlib backend\"),await h.runPythonAsync(`\ntry:\n\timport matplotlib\n\tmatplotlib.use(\"agg\")\nexcept ImportError:\n\tpass\n`),console.debug(\"matplotlib backend is set.\")),t(\"App is now loaded\")}const T=self;\"postMessage\"in T?P(T):T.onconnect=o=>{const e=o.ports[0];P(e),e.start()};let y;function P(o){const e=B(8);console.debug(\"Set up a new app.\",{appId:e});const t=l=>{const n={type:\"progress-update\",data:{log:l}};o.postMessage(n)},s=l=>{const n={type:\"modules-auto-loaded\",data:{packages:l}};o.postMessage(n)};let r;o.onmessage=async function(l){const n=l.data;console.debug(\"worker.onmessage\",n);const a=l.ports[0];try{if(n.type===\"init-env\"){y==null?y=V(n.data,t):t(\"Pyodide environment initialization is ongoing in another session\"),y.then(()=>{const i={type:\"reply:success\",data:null};a.postMessage(i)}).catch(i=>{const p={type:\"reply:error\",error:i};a.postMessage(p)});return}if(y==null)throw new Error(\"Pyodide Initialization is not started.\");if(await y,n.type===\"init-app\"){r=J(e,n.data,t,s);const i={type:\"reply:success\",data:null};a.postMessage(i);return}if(r==null)throw new Error(\"App initialization is not started.\");switch(await r,n.type){case\"echo\":{const i={type:\"reply:success\",data:n.data};a.postMessage(i);break}case\"run-python-code\":{E(),console.debug(\"Auto install the requirements\");const i=await h.loadPackagesFromImports(n.data.code);i.length>0&&s(i),console.debug(\"Modules are auto-loaded.\",i),await C(e,S(e),n.data.code);const p={type:\"reply:success\",data:null};a.postMessage(p);break}case\"run-python-file\":{E(),await M(e,S(e),n.data.path);const i={type:\"reply:success\",data:null};a.postMessage(i);break}case\"asgi-request\":{console.debug(\"ASGI request\",n.data),Y(L.bind(null,e),n.data.scope,a);break}case\"file:write\":{const{path:i,data:p,opts:d}=n.data;if(typeof p==\"string\"&&i.endsWith(\".py\")){console.debug(`Auto install the requirements in ${i}`);const _=await h.loadPackagesFromImports(p);_.length>0&&s(_),console.debug(\"Modules are auto-loaded.\",_)}const c=b(e,i);console.debug(`Write a file \"${c}\"`),N(h,c,p,d);const u={type:\"reply:success\",data:null};a.postMessage(u);break}case\"file:rename\":{const{oldPath:i,newPath:p}=n.data,d=b(e,i),c=b(e,p);console.debug(`Rename \"${d}\" to ${c}`),z(h,d,c);const u={type:\"reply:success\",data:null};a.postMessage(u);break}case\"file:unlink\":{const{path:i}=n.data,p=b(e,i);console.debug(`Remove \"${p}`),h.FS.unlink(p);const d={type:\"reply:success\",data:null};a.postMessage(d);break}case\"install\":{const{requirements:i}=n.data,p=h.pyimport(\"micropip\");console.debug(\"Install the requirements:\",i),G(i),await p.install.callKwargs(i,{keep_going:!0}).then(()=>{if(i.includes(\"matplotlib\"))return h.runPythonAsync(`\ntry:\n\timport matplotlib\n\tmatplotlib.use(\"agg\")\nexcept ImportError:\n\tpass\n`)}).then(()=>{console.debug(\"Successfully installed\");const d={type:\"reply:success\",data:null};a.postMessage(d)});break}}}catch(i){if(console.error(i),!(i instanceof Error))throw i;const p=new Error(i.message);p.name=i.name,p.stack=i.stack;const d={type:\"reply:error\",error:p};a.postMessage(d)}}}\n"], "names": ["D", "F", "o", "e", "t", "w", "H", "O", "r", "l", "n", "a", "i", "U", "g", "s", "p", "u", "_", "W", "A", "x", "S", "b", "R", "z", "G", "Y", "k", "B", "j", "h", "v", "L", "C", "M", "E", "d", "c", "I", "m"], "mappings": "yBAAA,IAAIA,EAAE,OAAO,eAAmBC,EAAE,CAACC,EAAEC,EAAEC,IAAID,KAAKD,EAAEF,EAAEE,EAAEC,EAAE,CAAC,WAAW,GAAG,aAAa,GAAG,SAAS,GAAG,MAAMC,CAAC,CAAC,EAAEF,EAAEC,CAAC,EAAEC,EAAMC,EAAE,CAACH,EAAEC,EAAEC,KAAKH,EAAEC,EAAE,OAAOC,GAAG,SAASA,EAAE,GAAGA,EAAEC,CAAC,EAAEA,GAAG,SAASE,EAAEJ,EAAE,CAAC,OAAOA,GAAGA,EAAE,YAAY,OAAO,UAAU,eAAe,KAAKA,EAAE,SAAS,EAAEA,EAAE,QAAQA,CAAC,CAAC,SAAS,EAAEA,EAAE,CAAC,GAAG,OAAOA,GAAG,SAAS,MAAM,IAAI,UAAU,mCAAmC,KAAK,UAAUA,CAAC,CAAC,CAAC,CAAC,SAASK,EAAEL,EAAEC,EAAE,CAAC,QAAQC,EAAE,GAAG,EAAE,EAAEI,EAAE,GAAGC,EAAE,EAAEC,EAAEC,EAAE,EAAEA,GAAGT,EAAE,OAAO,EAAES,EAAE,CAAC,GAAGA,EAAET,EAAE,OAAOQ,EAAER,EAAE,WAAWS,CAAC,MAAM,CAAC,GAAGD,IAAI,GAAG,MAAMA,EAAE,EAAE,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,EAAEF,IAAIG,EAAE,GAAGF,IAAI,GAAG,GAAGD,IAAIG,EAAE,GAAGF,IAAI,EAAE,CAAC,GAAGL,EAAE,OAAO,GAAG,IAAI,GAAGA,EAAE,WAAWA,EAAE,OAAO,CAAC,IAAI,IAAIA,EAAE,WAAWA,EAAE,OAAO,CAAC,IAAI,IAAI,GAAGA,EAAE,OAAO,EAAE,CAAC,IAAIQ,EAAER,EAAE,YAAY,GAAG,EAAE,GAAGQ,IAAIR,EAAE,OAAO,EAAE,CAACQ,IAAI,IAAIR,EAAE,GAAG,EAAE,IAAIA,EAAEA,EAAE,MAAM,EAAEQ,CAAC,EAAE,EAAER,EAAE,OAAO,EAAEA,EAAE,YAAY,GAAG,GAAGI,EAAEG,EAAEF,EAAE,EAAE,QAAQ,CAAC,SAASL,EAAE,SAAS,GAAGA,EAAE,SAAS,EAAE,CAACA,EAAE,GAAG,EAAE,EAAEI,EAAEG,EAAEF,EAAE,EAAE,QAAQ,EAAEN,IAAIC,EAAE,OAAO,EAAEA,GAAG,MAAMA,EAAE,KAAK,EAAE,EAAE,MAAMA,EAAE,OAAO,EAAEA,GAAG,IAAIF,EAAE,MAAMM,EAAE,EAAEG,CAAC,EAAEP,EAAEF,EAAE,MAAMM,EAAE,EAAEG,CAAC,EAAE,EAAEA,EAAEH,EAAE,EAAEA,EAAEG,EAAEF,EAAE,CAAC,MAAMC,IAAI,IAAID,IAAI,GAAG,EAAEA,EAAEA,EAAE,EAAE,CAAC,OAAOL,CAAC,CAAC,SAASS,EAAEX,EAAEC,EAAE,CAAC,IAAIC,EAAED,EAAE,KAAKA,EAAE,KAAK,EAAEA,EAAE,OAAOA,EAAE,MAAM,KAAKA,EAAE,KAAK,IAAI,OAAOC,EAAEA,IAAID,EAAE,KAAKC,EAAE,EAAEA,EAAEF,EAAE,EAAE,CAAC,CAAC,IAAIY,EAAE,CAAC,QAAQ,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAGC,EAAEP,EAAE,UAAU,OAAO,EAAEA,GAAG,IAAI,CAAC,EAAEA,IAAI,CAAC,IAAI,EAAEA,GAAG,EAAE,EAAE,UAAUA,CAAC,GAAGO,IAAI,SAASA,EAAE,QAAQ,IAAK,GAAE,EAAEA,GAAG,EAAE,CAAC,EAAE,EAAE,SAAS,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,WAAW,CAAC,IAAI,GAAG,CAAC,OAAO,EAAER,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,UAAU,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,IAAI,IAAI,EAAE,EAAE,WAAW,CAAC,IAAI,GAAGQ,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,EAAER,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,GAAGQ,IAAI,GAAG,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,WAAW,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,WAAW,CAAC,IAAI,EAAE,EAAE,KAAK,UAAU,CAAC,GAAG,UAAU,SAAS,EAAE,MAAM,IAAI,QAAQ,EAAE,EAAE,EAAE,EAAE,UAAU,OAAO,EAAE,EAAE,CAAC,IAAIA,EAAE,UAAU,CAAC,EAAE,EAAEA,CAAC,EAAEA,EAAE,OAAO,IAAI,IAAI,OAAO,EAAEA,EAAE,GAAG,IAAIA,EAAE,CAAC,OAAO,IAAI,OAAO,IAAID,EAAE,UAAU,CAAC,CAAC,EAAE,SAAS,SAAS,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,IAAI,EAAEA,EAAE,QAAQ,CAAC,EAAE,EAAEA,EAAE,QAAQ,CAAC,EAAE,IAAI,GAAG,MAAM,GAAG,QAAQC,EAAE,EAAEA,EAAE,EAAE,QAAQ,EAAE,WAAWA,CAAC,IAAI,GAAG,EAAEA,EAAE,CAAC,QAAQP,EAAE,EAAE,OAAO,EAAEA,EAAEO,EAAEL,EAAE,EAAEA,EAAE,EAAE,QAAQ,EAAE,WAAWA,CAAC,IAAI,GAAG,EAAEA,EAAE,CAAC,QAAQ,EAAE,EAAE,OAAOE,EAAE,EAAEF,EAAEM,EAAE,EAAEJ,EAAE,EAAEA,EAAE,EAAE,GAAG,EAAE,EAAE,GAAGI,EAAE,EAAE,EAAE,CAAC,GAAG,IAAIA,EAAE,CAAC,GAAGJ,EAAEI,EAAE,CAAC,GAAG,EAAE,WAAWN,EAAE,CAAC,IAAI,GAAG,OAAO,EAAE,MAAMA,EAAE,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,MAAMA,EAAE,CAAC,CAAC,MAAM,EAAEM,IAAI,EAAE,WAAWD,EAAE,CAAC,IAAI,GAAG,EAAE,EAAE,IAAI,IAAI,EAAE,IAAI,KAAK,CAAC,IAAIE,EAAE,EAAE,WAAWF,EAAE,CAAC,EAAEG,EAAE,EAAE,WAAWR,EAAE,CAAC,EAAE,GAAGO,IAAIC,EAAE,MAAMD,IAAI,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEF,EAAE,EAAE,EAAE,GAAGP,EAAE,EAAE,GAAG,IAAIA,GAAG,EAAE,WAAW,CAAC,IAAI,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,GAAG,OAAO,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,MAAME,EAAE,CAAC,GAAGA,GAAG,EAAE,EAAE,WAAWA,CAAC,IAAI,IAAI,EAAEA,EAAE,EAAE,MAAMA,CAAC,EAAE,EAAE,UAAU,SAAS,EAAE,CAAC,OAAO,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,IAAI,QAAQ,EAAE,EAAE,WAAW,CAAC,EAAEK,EAAE,IAAI,GAAGP,EAAE,GAAG,EAAE,GAAGE,EAAE,EAAE,OAAO,EAAEA,GAAG,EAAE,EAAEA,EAAE,GAAG,EAAE,EAAE,WAAWA,CAAC,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE,CAACF,EAAEE,EAAE,KAAK,OAAO,EAAE,GAAG,OAAOF,IAAI,GAAGO,EAAE,IAAI,IAAIA,GAAGP,IAAI,EAAE,KAAK,EAAE,MAAM,EAAEA,CAAC,CAAC,EAAE,SAAS,SAAS,EAAE,EAAE,CAAC,GAAG,IAAI,QAAQ,OAAO,GAAG,SAAS,MAAM,IAAI,UAAU,iCAAiC,EAAE,EAAE,CAAC,EAAE,IAAIO,EAAE,EAAEP,EAAE,GAAG,EAAE,GAAGE,EAAE,GAAG,IAAI,QAAQ,EAAE,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,EAAE,OAAO,EAAEE,EAAE,GAAG,IAAIF,EAAE,EAAE,OAAO,EAAEA,GAAG,EAAE,EAAEA,EAAE,CAAC,IAAIM,EAAE,EAAE,WAAWN,CAAC,EAAE,GAAGM,IAAI,IAAI,GAAG,CAAC,EAAE,CAACD,EAAEL,EAAE,EAAE,KAAK,OAAOE,IAAI,KAAK,EAAE,GAAGA,EAAEF,EAAE,GAAG,GAAG,IAAIM,IAAI,EAAE,WAAW,CAAC,EAAE,EAAE,IAAI,KAAKR,EAAEE,IAAI,EAAE,GAAGF,EAAEI,GAAG,CAAC,OAAOG,IAAIP,EAAEA,EAAEI,EAAEJ,IAAI,KAAKA,EAAE,EAAE,QAAQ,EAAE,MAAMO,EAAEP,CAAC,CAAC,KAAK,CAAC,IAAIE,EAAE,EAAE,OAAO,EAAEA,GAAG,EAAE,EAAEA,EAAE,GAAG,EAAE,WAAWA,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,CAACK,EAAEL,EAAE,EAAE,KAAK,OAAOF,IAAI,KAAK,EAAE,GAAGA,EAAEE,EAAE,GAAG,OAAOF,IAAI,GAAG,GAAG,EAAE,MAAMO,EAAEP,CAAC,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAGO,EAAE,EAAEP,EAAE,GAAG,EAAE,GAAGE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,IAAIE,EAAE,EAAE,WAAW,CAAC,EAAE,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAACG,EAAE,EAAE,EAAE,KAAK,CAAC,QAAQ,CAACP,IAAI,KAAK,EAAE,GAAGA,EAAE,EAAE,GAAGI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAEF,IAAI,IAAIA,EAAE,GAAG,IAAI,KAAKA,EAAE,GAAG,CAAC,OAAO,IAAI,IAAIF,IAAI,IAAIE,IAAI,GAAGA,IAAI,GAAG,IAAIF,EAAE,GAAG,IAAIO,EAAE,EAAE,GAAG,EAAE,MAAM,EAAEP,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE,CAAC,GAAG,IAAI,MAAM,OAAO,GAAG,SAAS,MAAM,IAAI,UAAU,mEAAmE,OAAO,CAAC,EAAE,OAAOK,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,IAAIE,EAAE,EAAE,WAAW,CAAC,EAAEP,EAAEO,IAAI,GAAG,EAAEP,GAAG,EAAE,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,QAAQE,EAAE,GAAG,EAAE,EAAEE,EAAE,GAAGI,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,GAAGD,EAAE,EAAE,WAAW,CAAC,EAAEA,IAAI,GAAG,CAAC,GAAG,CAACC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,QAAQ,CAACJ,IAAI,KAAKI,EAAE,GAAGJ,EAAE,EAAE,GAAGG,IAAI,GAAGL,IAAI,GAAGA,EAAE,EAAE,IAAI,IAAI,EAAE,GAAGA,IAAI,KAAK,EAAE,GAAG,CAAC,OAAOA,IAAI,IAAIE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGF,IAAIE,EAAE,GAAGF,IAAI,EAAE,EAAEE,IAAI,KAAK,IAAI,GAAGJ,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAEI,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAEA,CAAC,IAAI,IAAI,GAAGJ,GAAG,EAAE,KAAK,EAAE,MAAM,EAAEE,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAEE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAEF,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAEE,CAAC,GAAG,EAAE,IAAI,EAAE,MAAMF,EAAEE,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC,EAAEJ,IAAI,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,UAAU,IAAI,MAAM,KAAK,MAAM,IAAI,EAAEM,EAAE,MAAMA,EAAE,IAAIK,EAAEL,EAAE,MAAMM,EAAEd,EAAEa,CAAC,EAAEE,EAAE,gBAAgBC,EAAEpB,GAAG,GAAGmB,CAAC,IAAInB,CAAC,GAAGqB,EAAE,CAACrB,EAAEC,KAAKiB,EAAE,UAAUjB,CAAC,EAAEiB,EAAE,QAAQE,EAAEpB,CAAC,EAAEC,CAAC,GAAG,SAASqB,EAAEtB,EAAEC,EAAE,CAAC,MAAMC,EAAEgB,EAAE,UAAUjB,CAAC,EAAEK,EAAEY,EAAE,QAAQhB,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,UAAUM,KAAKF,EAAE,CAAC,EAAE,KAAKE,CAAC,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG,EAAE,GAAGR,EAAE,GAAG,YAAY,CAAC,EAAE,OAAO,CAAC,GAAGA,EAAE,GAAG,MAAM,CAAC,EAAE,MAAM,IAAI,MAAM,IAAI,CAAC,0CAA0C,EAAE,QAAQ,CAAC,GAAG,CAACA,EAAE,GAAG,MAAM,CAAC,CAAC,OAAOU,EAAE,CAAC,MAAM,QAAQ,MAAM,iCAAiC,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,SAAS,EAAEV,EAAEC,EAAEC,EAAE,EAAE,CAACoB,EAAEtB,EAAEC,CAAC,EAAED,EAAE,GAAG,UAAUC,EAAEC,EAAE,CAAC,CAAC,CAAC,SAASqB,EAAEvB,EAAEC,EAAEC,EAAE,CAACoB,EAAEtB,EAAEE,CAAC,EAAEF,EAAE,GAAG,OAAOC,EAAEC,CAAC,CAAC,CAAC,SAASsB,EAAExB,EAAE,CAACA,EAAE,QAAQC,GAAG,CAAC,IAAIC,EAAE,GAAG,CAACA,EAAE,IAAI,IAAID,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAGC,EAAE,WAAW,SAASA,EAAE,WAAW,QAAQ,MAAM,IAAI,MAAM,sEAAsED,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,CAACE,EAAE,KAAK,UAAU,CAAE,CAAA,EAAEA,EAAE,KAAK,UAAU,EAAEA,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK,WAAU,CAAE,CAAC,MAAM,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,YAAY,CAAC,KAAK,UAAU,KAAK,SAAQ,EAAG,KAAK,SAAS,IAAI,QAAQF,GAAG,KAAK,SAASA,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,KAAK,KAAK,QAAQ,SAAS,GAAG,MAAM,KAAK,MAAO,EAAC,OAAO,KAAK,QAAQ,OAAO,CAAC,QAAQA,EAAE,CAAC,KAAK,QAAQ,KAAKA,CAAC,EAAE,KAAK,WAAU,CAAE,CAAC,CAAC,SAASwB,EAAEzB,EAAEC,EAAEC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAEA,EAAE,iBAAiB,UAAUM,GAAG,CAAC,EAAE,QAAQA,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,MAAO,EAAC,eAAeI,GAAG,CAAC,OAAO,MAAM,EAAE,QAAS,CAAA,CAAC,eAAeC,EAAEC,EAAE,CAAC,MAAMC,EAAE,OAAO,YAAYD,EAAE,KAAI,CAAE,EAAEN,EAAE,YAAYO,CAAC,CAAC,CAAC,OAAOT,EAAEC,EAAEK,EAAEC,CAAC,CAAC,CAAC,MAAMmB,EAAE,iEAAiE,SAASC,EAAE3B,EAAE,CAAC,OAAO,MAAM,KAAK,MAAMA,CAAC,CAAC,EAAE,IAAI,IAAI0B,EAAE,KAAK,MAAM,KAAK,OAAM,EAAGA,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,MAAME,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqJ5zL,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqKF,cAAc,0DAA0D,EAAE,IAAIC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,eAAe,EAAElC,EAAEC,EAAE,CAAC,QAAQ,MAAM,kBAAkB,EAAEA,EAAE,iBAAiB,EAAE4B,EAAE,MAAM,YAAY,CAAC,OAAO,QAAQ,MAAM,OAAO,QAAQ,KAAK,CAAC,EAAE,QAAQ,MAAM,oBAAoB,EAAE,QAAQ,MAAM,kBAAkB,EAAE5B,EAAE,kBAAkB,EAAE,MAAM4B,EAAE,YAAY,UAAU,EAAEC,EAAED,EAAE,SAAS,UAAU,EAAE,QAAQ,MAAM,qBAAqB,EAAE,MAAM3B,EAAE,CAACF,EAAE,eAAeA,EAAE,oBAAoB,EAAE,QAAQ,MAAM,yBAAyBE,CAAC,EAAED,EAAE,uBAAuB,EAAE,MAAM4B,EAAE,YAAY,CAAC,MAAM,YAAY,CAAC,EAAE,MAAMC,EAAE,iBAAiB,QAAQ,OAAO,EAAE,MAAMA,EAAE,QAAQ,WAAW5B,EAAE,CAAC,WAAW,EAAE,CAAC,EAAE,QAAQ,MAAM,2BAA2B,EAAE,QAAQ,MAAM,4BAA4B,EAAED,EAAE,wBAAwB,EAAE,MAAM4B,EAAE,eAAe;AAAA;AAAA;AAAA;AAAA,CAItyB,EAAE,QAAQ,MAAM,+BAA+B,EAAE,QAAQ,MAAM,2BAA2B,EAAE5B,EAAE,0BAA0B,EAAE,MAAM4B,EAAE,eAAe,eAAe,EAAE,QAAQ,MAAM,6BAA6B,EAAE,QAAQ,MAAM,mCAAmC,EAAE5B,EAAE,kCAAkC,EAAE,MAAM4B,EAAE,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAqC/T,EAAEE,EAAEF,EAAE,QAAQ,IAAI,wBAAwB,EAAE,QAAQ,MAAM,uCAAuC,EAAE,QAAQ,MAAM,0BAA0B,EAAE5B,EAAE,yBAAyB,EAAE,MAAM4B,EAAE,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhM,EAAE,QAAQ,MAAM,6BAA6B,EAAE,QAAQ,MAAM,sCAAsC,EAAE5B,EAAE,qCAAqC,EAAE,MAAM4B,EAAE,eAAeD,CAAC,EAAEI,EAAEH,EAAE,QAAQ,IAAI,WAAW,EAAEI,EAAEJ,EAAE,QAAQ,IAAI,aAAa,EAAE,MAAMA,EAAE,eAAe,CAAC,EAAEK,EAAEL,EAAE,QAAQ,IAAI,sBAAsB,EAAE,QAAQ,MAAM,sCAAsC,EAAE5B,EAAE,0BAA0B,CAAC,CAAC,eAAe,EAAED,EAAEC,EAAEC,EAAE,EAAE,CAAC,MAAMI,EAAEc,EAAEpB,CAAC,EAAE,QAAQ,MAAM,yCAAyC,CAAC,MAAMA,EAAE,WAAWM,CAAC,CAAC,EAAEuB,EAAE,GAAG,MAAMvB,CAAC,EAAE,QAAQ,MAAM,kBAAkBL,EAAE,KAAK,EAAEC,EAAE,gBAAgB,EAAE,MAAMK,EAAE,CAAE,EAAC,MAAM,QAAQ,IAAI,OAAO,KAAKN,EAAE,KAAK,EAAE,IAAI,MAAMkC,GAAG,CAAC,MAAMC,EAAEnC,EAAE,MAAMkC,CAAC,EAAE,IAAI,EAAE,QAAQC,GAAG,QAAQ,MAAM,qBAAqBA,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,MAAMA,EAAE,GAAG,EAAE,KAAKC,GAAGA,EAAE,YAAa,CAAA,EAAE,KAAKA,GAAG,IAAI,WAAWA,CAAC,CAAC,GAAG,EAAED,EAAE,KAAK,KAAK,CAAC,KAAKpB,CAAC,EAAEf,EAAE,MAAMkC,CAAC,EAAEG,EAAEjB,EAAErB,EAAEmC,CAAC,EAAE,QAAQ,MAAM,iBAAiBG,CAAC,GAAG,EAAE,EAAET,EAAES,EAAE,EAAEtB,CAAC,EAAE,OAAO,GAAG,UAAUmB,EAAE,SAAS,KAAK,GAAG5B,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,oBAAoB,EAAE,QAAQ,MAAM,uBAAuBN,EAAE,YAAY,EAAEC,EAAE,qBAAqB,EAAE,MAAM4B,EAAE,QAAQ,WAAW7B,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,EAAE,QAAQ,MAAM,yBAAyB,EAAE,QAAQ,MAAM,uBAAuB,EAAE,MAAMO,EAAE,MAAM,QAAQ,IAAID,EAAE,IAAI4B,GAAGN,EAAE,wBAAwBM,CAAC,CAAC,CAAC,EAAE1B,EAAE,IAAI,IAAID,EAAE,KAAI,CAAE,EAAEE,EAAE,MAAM,KAAKD,CAAC,EAAEC,EAAE,OAAO,GAAG,EAAEA,CAAC,EAAE,MAAMI,EAAEJ,EAAE,IAAIyB,GAAGA,EAAE,IAAI,EAAE,QAAQ,MAAM,2BAA2BzB,CAAC,GAAGT,EAAE,aAAa,SAAS,YAAY,GAAGa,EAAE,SAAS,YAAY,KAAK,QAAQ,MAAM,6BAA6B,EAAEZ,EAAE,4BAA4B,EAAE,MAAM2B,EAAE,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAMtgD,EAAE,QAAQ,MAAM,4BAA4B,GAAG3B,EAAE,mBAAmB,CAAC,CAAC,MAAM,EAAE,KAAK,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,UAAUF,GAAG,CAAC,MAAMC,EAAED,EAAE,MAAM,CAAC,EAAE,EAAEC,CAAC,EAAEA,EAAE,MAAK,CAAE,EAAE,IAAI,EAAE,SAAS,EAAED,EAAE,CAAC,MAAMC,EAAE0B,EAAE,CAAC,EAAE,QAAQ,MAAM,oBAAoB,CAAC,MAAM1B,CAAC,CAAC,EAAE,MAAMC,EAAEK,GAAG,CAAC,MAAMC,EAAE,CAAC,KAAK,kBAAkB,KAAK,CAAC,IAAID,CAAC,CAAC,EAAEP,EAAE,YAAYQ,CAAC,CAAC,EAAE,EAAED,GAAG,CAAC,MAAMC,EAAE,CAAC,KAAK,sBAAsB,KAAK,CAAC,SAASD,CAAC,CAAC,EAAEP,EAAE,YAAYQ,CAAC,CAAC,EAAE,IAAIF,EAAEN,EAAE,UAAU,eAAeO,EAAE,CAAC,MAAMC,EAAED,EAAE,KAAK,QAAQ,MAAM,mBAAmBC,CAAC,EAAE,MAAMC,EAAEF,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,GAAGC,EAAE,OAAO,WAAW,CAAC,GAAG,KAAK,EAAE,EAAEA,EAAE,KAAKN,CAAC,EAAEA,EAAE,kEAAkE,EAAE,EAAE,KAAK,IAAI,CAAC,MAAMQ,EAAE,CAAC,KAAK,gBAAgB,KAAK,IAAI,EAAED,EAAE,YAAYC,CAAC,CAAC,CAAC,EAAE,MAAMA,GAAG,CAAC,MAAMI,EAAE,CAAC,KAAK,cAAc,MAAMJ,CAAC,EAAED,EAAE,YAAYK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,GAAG,KAAK,MAAM,IAAI,MAAM,wCAAwC,EAAE,GAAG,MAAM,EAAEN,EAAE,OAAO,WAAW,CAACF,EAAE,EAAEL,EAAEO,EAAE,KAAKN,EAAE,CAAC,EAAE,MAAMQ,EAAE,CAAC,KAAK,gBAAgB,KAAK,IAAI,EAAED,EAAE,YAAYC,CAAC,EAAE,MAAM,CAAC,GAAGJ,GAAG,KAAK,MAAM,IAAI,MAAM,oCAAoC,EAAE,OAAO,MAAMA,EAAEE,EAAE,KAAI,CAAE,IAAI,OAAO,CAAC,MAAME,EAAE,CAAC,KAAK,gBAAgB,KAAKF,EAAE,IAAI,EAAEC,EAAE,YAAYC,CAAC,EAAE,KAAK,CAAC,IAAI,kBAAkB,CAACwB,EAAG,EAAC,QAAQ,MAAM,+BAA+B,EAAE,MAAMxB,EAAE,MAAMmB,EAAE,wBAAwBrB,EAAE,KAAK,IAAI,EAAEE,EAAE,OAAO,GAAG,EAAEA,CAAC,EAAE,QAAQ,MAAM,2BAA2BA,CAAC,EAAE,MAAMsB,EAAE/B,EAAEmB,EAAEnB,CAAC,EAAEO,EAAE,KAAK,IAAI,EAAE,MAAMM,EAAE,CAAC,KAAK,gBAAgB,KAAK,IAAI,EAAEL,EAAE,YAAYK,CAAC,EAAE,KAAK,CAAC,IAAI,kBAAkB,CAACoB,EAAC,EAAG,MAAMD,EAAEhC,EAAEmB,EAAEnB,CAAC,EAAEO,EAAE,KAAK,IAAI,EAAE,MAAME,EAAE,CAAC,KAAK,gBAAgB,KAAK,IAAI,EAAED,EAAE,YAAYC,CAAC,EAAE,KAAK,CAAC,IAAI,eAAe,CAAC,QAAQ,MAAM,eAAeF,EAAE,IAAI,EAAEiB,EAAEM,EAAE,KAAK,KAAK9B,CAAC,EAAEO,EAAE,KAAK,MAAMC,CAAC,EAAE,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,KAAKC,EAAE,KAAKI,EAAE,KAAKqB,CAAC,EAAE3B,EAAE,KAAK,GAAG,OAAOM,GAAG,UAAUJ,EAAE,SAAS,KAAK,EAAE,CAAC,QAAQ,MAAM,oCAAoCA,CAAC,EAAE,EAAE,MAAMM,EAAE,MAAMa,EAAE,wBAAwBf,CAAC,EAAEE,EAAE,OAAO,GAAG,EAAEA,CAAC,EAAE,QAAQ,MAAM,2BAA2BA,CAAC,CAAC,CAAC,MAAMoB,EAAEf,EAAEpB,EAAES,CAAC,EAAE,QAAQ,MAAM,iBAAiB0B,CAAC,GAAG,EAAE,EAAEP,EAAEO,EAAEtB,EAAEqB,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,gBAAgB,KAAK,IAAI,EAAE1B,EAAE,YAAY,CAAC,EAAE,KAAK,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,QAAQC,EAAE,QAAQI,CAAC,EAAEN,EAAE,KAAK2B,EAAEd,EAAEpB,EAAES,CAAC,EAAE0B,EAAEf,EAAEpB,EAAEa,CAAC,EAAE,QAAQ,MAAM,WAAWqB,CAAC,QAAQC,CAAC,EAAE,EAAEb,EAAEM,EAAEM,EAAEC,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,gBAAgB,KAAK,IAAI,EAAE3B,EAAE,YAAY,CAAC,EAAE,KAAK,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,KAAKC,CAAC,EAAEF,EAAE,KAAKM,EAAEO,EAAEpB,EAAES,CAAC,EAAE,QAAQ,MAAM,WAAWI,CAAC,EAAE,EAAEe,EAAE,GAAG,OAAOf,CAAC,EAAE,MAAMqB,EAAE,CAAC,KAAK,gBAAgB,KAAK,IAAI,EAAE1B,EAAE,YAAY0B,CAAC,EAAE,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,aAAazB,CAAC,EAAEF,EAAE,KAAKM,EAAEe,EAAE,SAAS,UAAU,EAAE,QAAQ,MAAM,4BAA4BnB,CAAC,EAAEc,EAAEd,CAAC,EAAE,MAAMI,EAAE,QAAQ,WAAWJ,EAAE,CAAC,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,GAAGA,EAAE,SAAS,YAAY,EAAE,OAAOmB,EAAE,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAM3/E,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,MAAM,wBAAwB,EAAE,MAAMM,EAAE,CAAC,KAAK,gBAAgB,KAAK,IAAI,EAAE1B,EAAE,YAAY0B,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,OAAOzB,EAAE,CAAC,GAAG,QAAQ,MAAMA,CAAC,EAAE,EAAEA,aAAa,OAAO,MAAMA,EAAE,MAAMI,EAAE,IAAI,MAAMJ,EAAE,OAAO,EAAEI,EAAE,KAAKJ,EAAE,KAAKI,EAAE,MAAMJ,EAAE,MAAM,MAAMyB,EAAE,CAAC,KAAK,cAAc,MAAMrB,CAAC,EAAEL,EAAE,YAAY0B,CAAC,CAAC,CAAC,CAAC"}