const __vite__fileDeps=["./PlotlyPlot-Btt7SCjQ.js","../lite.js","../lite.css","./BokehPlot-DYj1mWXo.js","./BokehPlot-Cd-I2ErV.css","./AltairPlot-D__T9RXF.js","./color-Dz5ygqaR.js","./vega-embed.module-CnIc3T1i.js","./dsv-DB8NKgIY.js","./AltairPlot-CSe9xcFj.css","./MatplotlibPlot-CHXrnGyV.js","./MatplotlibPlot-AF_QcUtc.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{a as j,i as q,s as M,Q as b,q as s,l as C,v as d,w as v,o as T,f,E as O,y as A,b as m,z as R,t as h,ay as p,H,c as y,m as P,d as E,as as N}from"../lite.js";import{E as F}from"./Empty-C76eC2zW.js";function G(a){let e,o,l,n,c,t,_;return{c(){e=b("svg"),o=b("circle"),l=b("circle"),n=b("circle"),c=b("circle"),t=b("circle"),_=b("path"),s(o,"cx","20"),s(o,"cy","4"),s(o,"r","2"),s(o,"fill","currentColor"),s(l,"cx","8"),s(l,"cy","16"),s(l,"r","2"),s(l,"fill","currentColor"),s(n,"cx","28"),s(n,"cy","12"),s(n,"r","2"),s(n,"fill","currentColor"),s(c,"cx","11"),s(c,"cy","7"),s(c,"r","2"),s(c,"fill","currentColor"),s(t,"cx","16"),s(t,"cy","24"),s(t,"r","2"),s(t,"fill","currentColor"),s(_,"fill","currentColor"),s(_,"d","M30 3.413L28.586 2L4 26.585V2H2v26a2 2 0 0 0 2 2h26v-2H5.413Z"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),s(e,"aria-hidden","true"),s(e,"role","img"),s(e,"class","iconify iconify--carbon"),s(e,"width","100%"),s(e,"height","100%"),s(e,"preserveAspectRatio","xMidYMid meet"),s(e,"viewBox","0 0 32 32")},m(i,u){C(i,e,u),d(e,o),d(e,l),d(e,n),d(e,c),d(e,t),d(e,_)},p:v,i:v,o:v,d(i){i&&T(e)}}}let J=class extends j{constructor(e){super(),q(this,e,null,G,M,{})}};function K(a){let e,o;return e=new F({props:{unpadded_box:!0,size:"large",$$slots:{default:[W]},$$scope:{ctx:a}}}),{c(){y(e.$$.fragment)},m(l,n){P(e,l,n),o=!0},p(l,n){const c={};n&524288&&(c.$$scope={dirty:n,ctx:l}),e.$set(c)},i(l){o||(h(e.$$.fragment,l),o=!0)},o(l){m(e.$$.fragment,l),o=!1},d(l){E(e,l)}}}function U(a){let e=a[11],o,l,n=S(a);return{c(){n.c(),o=O()},m(c,t){n.m(c,t),C(c,o,t),l=!0},p(c,t){t&2048&&M(e,e=c[11])?(A(),m(n,1,1,v),R(),n=S(c),n.c(),h(n,1),n.m(o.parentNode,o)):n.p(c,t)},i(c){l||(h(n),l=!0)},o(c){m(n),l=!1},d(c){c&&T(o),n.d(c)}}}function W(a){let e,o;return e=new J({}),{c(){y(e.$$.fragment)},m(l,n){P(e,l,n),o=!0},i(l){o||(h(e.$$.fragment,l),o=!0)},o(l){m(e.$$.fragment,l),o=!1},d(l){E(e,l)}}}function S(a){let e,o,l;var n=a[10];function c(t,_){return{props:{value:t[0],colors:t[1],theme_mode:t[3],show_label:t[2],caption:t[4],bokeh_version:t[5],show_actions_button:t[6],gradio:t[7],_selectable:t[9],x_lim:t[8]}}}return n&&(e=N(n,c(a)),e.$on("load",a[15]),e.$on("select",a[16])),{c(){e&&y(e.$$.fragment),o=O()},m(t,_){e&&P(e,t,_),C(t,o,_),l=!0},p(t,_){if(_&1024&&n!==(n=t[10])){if(e){A();const i=e;m(i.$$.fragment,1,0,()=>{E(i,1)}),R()}n?(e=N(n,c(t)),e.$on("load",t[15]),e.$on("select",t[16]),y(e.$$.fragment),h(e.$$.fragment,1),P(e,o.parentNode,o)):e=null}else if(n){const i={};_&1&&(i.value=t[0]),_&2&&(i.colors=t[1]),_&8&&(i.theme_mode=t[3]),_&4&&(i.show_label=t[2]),_&16&&(i.caption=t[4]),_&32&&(i.bokeh_version=t[5]),_&64&&(i.show_actions_button=t[6]),_&128&&(i.gradio=t[7]),_&512&&(i._selectable=t[9]),_&256&&(i.x_lim=t[8]),e.$set(i)}},i(t){l||(e&&h(e.$$.fragment,t),l=!0)},o(t){e&&m(e.$$.fragment,t),l=!1},d(t){t&&T(o),e&&E(e,t)}}}function X(a){let e,o,l,n;const c=[U,K],t=[];function _(i,u){return i[0]&&i[10]?0:1}return e=_(a),o=t[e]=c[e](a),{c(){o.c(),l=O()},m(i,u){t[e].m(i,u),C(i,l,u),n=!0},p(i,[u]){let g=e;e=_(i),e===g?t[e].p(i,u):(A(),m(t[g],1,1,()=>{t[g]=null}),R(),o=t[e],o?o.p(i,u):(o=t[e]=c[e](i),o.c()),h(o,1),o.m(l.parentNode,l))},i(i){n||(h(o),n=!0)},o(i){m(o),n=!1},d(i){i&&T(l),t[e].d(i)}}}function $(a,e,o){let{value:l}=e,n,{colors:c=[]}=e,{show_label:t}=e,{theme_mode:_}=e,{caption:i}=e,{bokeh_version:u}=e,{show_actions_button:g}=e,{gradio:V}=e,{x_lim:x=null}=e,{_selectable:D}=e,w=null,L=l?.type;const I={plotly:()=>p(()=>import("./PlotlyPlot-Btt7SCjQ.js"),__vite__mapDeps([0,1,2]),import.meta.url),bokeh:()=>p(()=>import("./BokehPlot-DYj1mWXo.js"),__vite__mapDeps([3,1,2,4]),import.meta.url),altair:()=>p(()=>import("./AltairPlot-D__T9RXF.js"),__vite__mapDeps([5,1,2,6,7,8,9]),import.meta.url),matplotlib:()=>p(()=>import("./MatplotlibPlot-CHXrnGyV.js"),__vite__mapDeps([10,1,2,11]),import.meta.url)};let k={};const B=typeof window<"u";let z=0;function Q(r){H.call(this,a,r)}function Y(r){H.call(this,a,r)}return a.$$set=r=>{"value"in r&&o(0,l=r.value),"colors"in r&&o(1,c=r.colors),"show_label"in r&&o(2,t=r.show_label),"theme_mode"in r&&o(3,_=r.theme_mode),"caption"in r&&o(4,i=r.caption),"bokeh_version"in r&&o(5,u=r.bokeh_version),"show_actions_button"in r&&o(6,g=r.show_actions_button),"gradio"in r&&o(7,V=r.gradio),"x_lim"in r&&o(8,x=r.x_lim),"_selectable"in r&&o(9,D=r._selectable)},a.$$.update=()=>{if(a.$$.dirty&31745&&l!==n){o(11,z+=1);let r=l?.type;r!==L&&o(10,w=null),r&&r in I&&B&&(k[r]?o(10,w=k[r]):I[r]().then(Z=>{o(10,w=Z.default),o(14,k[r]=w,k)})),o(12,n=l),o(13,L=r)}},[l,c,t,_,i,u,g,V,x,D,w,z,n,L,k,Q,Y]}class ee extends j{constructor(e){super(),q(this,e,$,X,M,{value:0,colors:1,show_label:2,theme_mode:3,caption:4,bokeh_version:5,show_actions_button:6,gradio:7,x_lim:8,_selectable:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get colors(){return this.$$.ctx[1]}set colors(e){this.$$set({colors:e}),f()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),f()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),f()}get caption(){return this.$$.ctx[4]}set caption(e){this.$$set({caption:e}),f()}get bokeh_version(){return this.$$.ctx[5]}set bokeh_version(e){this.$$set({bokeh_version:e}),f()}get show_actions_button(){return this.$$.ctx[6]}set show_actions_button(e){this.$$set({show_actions_button:e}),f()}get gradio(){return this.$$.ctx[7]}set gradio(e){this.$$set({gradio:e}),f()}get x_lim(){return this.$$.ctx[8]}set x_lim(e){this.$$set({x_lim:e}),f()}get _selectable(){return this.$$.ctx[9]}set _selectable(e){this.$$set({_selectable:e}),f()}}const ne=Object.freeze(Object.defineProperty({__proto__:null,default:ee},Symbol.toStringTag,{value:"Module"}));export{J as P,ee as a,ne as b};
//# sourceMappingURL=Plot-DVlm3BYd.js.map
