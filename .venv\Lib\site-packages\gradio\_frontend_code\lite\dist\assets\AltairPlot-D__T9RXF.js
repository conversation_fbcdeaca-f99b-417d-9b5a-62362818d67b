import{b6 as k,a as J,i as N,s as R,f as _,p as x,x as B,q as F,l as E,v as C,w as V,o as M,a3 as G,a8 as H,k as K,n as Q,I as q}from"../lite.js";import{g as U}from"./color-Dz5ygqaR.js";import X from"./vega-embed.module-CnIc3T1i.js";import"./dsv-DB8NKgIY.js";function Y(l,t,o,a){let e=t.getPropertyValue("--color-accent"),n=t.getPropertyValue("--body-text-color"),s=t.getPropertyValue("--border-color-primary"),f=t.fontFamily,w=t.getPropertyValue("--block-title-text-weight");const h=c=>c.endsWith("px")?parseFloat(c.slice(0,-2)):12;let b=h(t.getPropertyValue("--text-md")),d=h(t.getPropertyValue("--text-sm")),u={autosize:{type:"fit",contains:"padding"},axis:{labelFont:f,labelColor:n,titleFont:f,titleColor:n,tickColor:s,labelFontSize:d,gridColor:s,titleFontWeight:"normal",titleFontSize:d,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:n,labelFont:f,titleColor:n,titleFont:f,titleFontWeight:"normal",titleFontSize:d,labelFontWeight:"normal",offset:2},title:{color:n,font:f,fontSize:b,fontWeight:w,anchor:"middle"},view:{stroke:s}};l.config=u;let r=l.encoding,m=l.layer;switch(o){case"scatter":l.config.mark={stroke:e},r.color&&r.color.type=="nominal"?r.color.scale.range=r.color.scale.range.map((c,g)=>v(a,g)):r.color&&r.color.type=="quantitative"&&(r.color.scale.range=["#eff6ff","#1e3a8a"],r.color.scale.range.interpolate="hsl");break;case"line":l.config.mark={stroke:e,cursor:"crosshair"},m.forEach(c=>{c.encoding.color&&(c.encoding.color.scale.range=c.encoding.color.scale.range.map((g,p)=>v(a,p)))});break;case"bar":l.config.mark={opacity:.8,fill:e},r.color&&(r.color.scale.range=r.color.scale.range.map((c,g)=>v(a,g)));break}return l}function v(l,t){let o=l[t%l.length];return o&&o in k?k[o]?.primary:o||k[U(t)].primary}function O(l){let t,o;return{c(){t=x("div"),o=K(l[0]),F(t,"class","caption layout svelte-1qhqpn7")},m(a,e){E(a,t,e),C(t,o)},p(a,e){e&1&&Q(o,a[0])},d(a){a&&M(t)}}}function Z(l){let t,o,a,e=l[0]&&O(l);return{c(){t=x("div"),o=x("div"),a=B(),e&&e.c(),F(t,"data-testid","altair"),F(t,"class","altair layout svelte-1qhqpn7")},m(n,s){E(n,t,s),C(t,o),l[11](o),C(t,a),e&&e.m(t,null),l[12](t)},p(n,[s]){n[0]?e?e.p(n,s):(e=O(n),e.c(),e.m(t,null)):e&&(e.d(1),e=null)},i:V,o:V,d(n){n&&M(t),l[11](null),e&&e.d(),l[12](null)}}}function $(l,t,o){let a,e,n,{value:s}=t,{colors:f=[]}=t,{caption:w}=t,{show_actions_button:h}=t,{gradio:b}=t,d,u,r,{_selectable:m}=t,c=window.getComputedStyle(document.body),g,p;const z=()=>Math.min(u.offsetWidth,p||u.offsetWidth);let P=()=>{};const j=()=>{n&&o(9,e.width=z(),e),X(d,e,{actions:h}).then(function(i){if(r=i.view,P=()=>{r.signal("width",z()).run()},!m)return;const W=(tt,et)=>{const y=r.signal("brush");if(y)if(Object.keys(y).length===0)b.dispatch("select",{value:null,index:null,selected:!1});else{const T=Object.keys(y)[0];let D=y[T].map(I=>I/1e3);b.dispatch("select",{value:y,index:D,selected:!0})}};r.addEventListener("mouseup",W),r.addEventListener("touchup",W)})};let S=new ResizeObserver(()=>{n&&e.width!==u.offsetWidth&&P()});G(()=>{j(),S.observe(u)}),H(()=>{S.disconnect()});function A(i){q[i?"unshift":"push"](()=>{d=i,o(1,d)})}function L(i){q[i?"unshift":"push"](()=>{u=i,o(2,u)})}return l.$$set=i=>{"value"in i&&o(3,s=i.value),"colors"in i&&o(4,f=i.colors),"caption"in i&&o(0,w=i.caption),"show_actions_button"in i&&o(5,h=i.show_actions_button),"gradio"in i&&o(6,b=i.gradio),"_selectable"in i&&o(7,m=i._selectable)},l.$$.update=()=>{l.$$.dirty&8&&o(10,a=s?.plot),l.$$.dirty&1024&&o(9,e=JSON.parse(a)),l.$$.dirty&640&&e&&e.params&&!m&&o(9,e.params=e.params.filter(i=>i.name!=="brush"),e),l.$$.dirty&536&&s.chart&&o(9,e=Y(e,c,s.chart,f)),l.$$.dirty&768&&g!==e&&(o(8,g=e),p=e.width),l.$$.dirty&520&&(n=!(e.encoding?.column?.field||e.encoding?.row?.field||s.chart===void 0))},[w,d,u,s,f,h,b,m,g,e,a,A,L]}class rt extends J{constructor(t){super(),N(this,t,$,Z,R,{value:3,colors:4,caption:0,show_actions_button:5,gradio:6,_selectable:7})}get value(){return this.$$.ctx[3]}set value(t){this.$$set({value:t}),_()}get colors(){return this.$$.ctx[4]}set colors(t){this.$$set({colors:t}),_()}get caption(){return this.$$.ctx[0]}set caption(t){this.$$set({caption:t}),_()}get show_actions_button(){return this.$$.ctx[5]}set show_actions_button(t){this.$$set({show_actions_button:t}),_()}get gradio(){return this.$$.ctx[6]}set gradio(t){this.$$set({gradio:t}),_()}get _selectable(){return this.$$.ctx[7]}set _selectable(t){this.$$set({_selectable:t}),_()}}export{rt as default};
//# sourceMappingURL=AltairPlot-D__T9RXF.js.map
