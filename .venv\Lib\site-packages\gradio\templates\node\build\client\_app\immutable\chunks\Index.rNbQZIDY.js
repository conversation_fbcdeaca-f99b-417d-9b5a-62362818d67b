import { SvelteComponent, init, safe_not_equal, create_component, claim_component, mount_component, transition_in, transition_out, destroy_component, afterUpdate, assign, space, element, claim_space, claim_element, children, detach, attr, toggle_class, insert_hydration, append_hydration, set_input_value, listen, get_spread_update, get_spread_object, to_number, run_all, tick, text, claim_text, set_data } from "../../../svelte/svelte.js";
import "../../../svelte/svelte-submodules.js";
import { B as Block, S as Static, f as BlockTitle } from "./2.BqWhUxOo.js";
function create_default_slot_1(ctx) {
  let t;
  return {
    c() {
      t = text(
        /*label*/
        ctx[2]
      );
    },
    l(nodes) {
      t = claim_text(
        nodes,
        /*label*/
        ctx[2]
      );
    },
    m(target, anchor) {
      insert_hydration(target, t, anchor);
    },
    p(ctx2, dirty) {
      if (dirty & /*label*/
      4)
        set_data(
          t,
          /*label*/
          ctx2[2]
        );
    },
    d(detaching) {
      if (detaching) {
        detach(t);
      }
    }
  };
}
function create_default_slot(ctx) {
  let statustracker;
  let t0;
  let label_1;
  let blocktitle;
  let t1;
  let input;
  let current;
  let mounted;
  let dispose;
  const statustracker_spread_levels = [
    { autoscroll: (
      /*gradio*/
      ctx[1].autoscroll
    ) },
    { i18n: (
      /*gradio*/
      ctx[1].i18n
    ) },
    /*loading_status*/
    ctx[13]
  ];
  let statustracker_props = {};
  for (let i = 0; i < statustracker_spread_levels.length; i += 1) {
    statustracker_props = assign(statustracker_props, statustracker_spread_levels[i]);
  }
  statustracker = new Static({ props: statustracker_props });
  statustracker.$on(
    "clear_status",
    /*clear_status_handler*/
    ctx[20]
  );
  blocktitle = new BlockTitle({
    props: {
      root: (
        /*root*/
        ctx[15]
      ),
      show_label: (
        /*show_label*/
        ctx[10]
      ),
      info: (
        /*info*/
        ctx[3]
      ),
      $$slots: { default: [create_default_slot_1] },
      $$scope: { ctx }
    }
  });
  return {
    c() {
      create_component(statustracker.$$.fragment);
      t0 = space();
      label_1 = element("label");
      create_component(blocktitle.$$.fragment);
      t1 = space();
      input = element("input");
      this.h();
    },
    l(nodes) {
      claim_component(statustracker.$$.fragment, nodes);
      t0 = claim_space(nodes);
      label_1 = claim_element(nodes, "LABEL", { class: true });
      var label_1_nodes = children(label_1);
      claim_component(blocktitle.$$.fragment, label_1_nodes);
      t1 = claim_space(label_1_nodes);
      input = claim_element(label_1_nodes, "INPUT", {
        "aria-label": true,
        type: true,
        min: true,
        max: true,
        step: true,
        class: true
      });
      label_1_nodes.forEach(detach);
      this.h();
    },
    h() {
      attr(
        input,
        "aria-label",
        /*label*/
        ctx[2]
      );
      attr(input, "type", "number");
      attr(
        input,
        "min",
        /*minimum*/
        ctx[11]
      );
      attr(
        input,
        "max",
        /*maximum*/
        ctx[12]
      );
      attr(
        input,
        "step",
        /*step*/
        ctx[14]
      );
      input.disabled = /*disabled*/
      ctx[16];
      attr(input, "class", "svelte-7ha85a");
      attr(label_1, "class", "block svelte-7ha85a");
      toggle_class(
        label_1,
        "container",
        /*container*/
        ctx[7]
      );
    },
    m(target, anchor) {
      mount_component(statustracker, target, anchor);
      insert_hydration(target, t0, anchor);
      insert_hydration(target, label_1, anchor);
      mount_component(blocktitle, label_1, null);
      append_hydration(label_1, t1);
      append_hydration(label_1, input);
      set_input_value(
        input,
        /*value*/
        ctx[0]
      );
      current = true;
      if (!mounted) {
        dispose = [
          listen(
            input,
            "input",
            /*input_input_handler*/
            ctx[21]
          ),
          listen(
            input,
            "keypress",
            /*handle_keypress*/
            ctx[17]
          ),
          listen(
            input,
            "blur",
            /*blur_handler*/
            ctx[22]
          ),
          listen(
            input,
            "focus",
            /*focus_handler*/
            ctx[23]
          )
        ];
        mounted = true;
      }
    },
    p(ctx2, dirty) {
      const statustracker_changes = dirty & /*gradio, loading_status*/
      8194 ? get_spread_update(statustracker_spread_levels, [
        dirty & /*gradio*/
        2 && { autoscroll: (
          /*gradio*/
          ctx2[1].autoscroll
        ) },
        dirty & /*gradio*/
        2 && { i18n: (
          /*gradio*/
          ctx2[1].i18n
        ) },
        dirty & /*loading_status*/
        8192 && get_spread_object(
          /*loading_status*/
          ctx2[13]
        )
      ]) : {};
      statustracker.$set(statustracker_changes);
      const blocktitle_changes = {};
      if (dirty & /*root*/
      32768)
        blocktitle_changes.root = /*root*/
        ctx2[15];
      if (dirty & /*show_label*/
      1024)
        blocktitle_changes.show_label = /*show_label*/
        ctx2[10];
      if (dirty & /*info*/
      8)
        blocktitle_changes.info = /*info*/
        ctx2[3];
      if (dirty & /*$$scope, label*/
      33554436) {
        blocktitle_changes.$$scope = { dirty, ctx: ctx2 };
      }
      blocktitle.$set(blocktitle_changes);
      if (!current || dirty & /*label*/
      4) {
        attr(
          input,
          "aria-label",
          /*label*/
          ctx2[2]
        );
      }
      if (!current || dirty & /*minimum*/
      2048) {
        attr(
          input,
          "min",
          /*minimum*/
          ctx2[11]
        );
      }
      if (!current || dirty & /*maximum*/
      4096) {
        attr(
          input,
          "max",
          /*maximum*/
          ctx2[12]
        );
      }
      if (!current || dirty & /*step*/
      16384) {
        attr(
          input,
          "step",
          /*step*/
          ctx2[14]
        );
      }
      if (!current || dirty & /*disabled*/
      65536) {
        input.disabled = /*disabled*/
        ctx2[16];
      }
      if (dirty & /*value*/
      1 && to_number(input.value) !== /*value*/
      ctx2[0]) {
        set_input_value(
          input,
          /*value*/
          ctx2[0]
        );
      }
      if (!current || dirty & /*container*/
      128) {
        toggle_class(
          label_1,
          "container",
          /*container*/
          ctx2[7]
        );
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(statustracker.$$.fragment, local);
      transition_in(blocktitle.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(statustracker.$$.fragment, local);
      transition_out(blocktitle.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(t0);
        detach(label_1);
      }
      destroy_component(statustracker, detaching);
      destroy_component(blocktitle);
      mounted = false;
      run_all(dispose);
    }
  };
}
function create_fragment(ctx) {
  let block;
  let current;
  block = new Block({
    props: {
      visible: (
        /*visible*/
        ctx[6]
      ),
      elem_id: (
        /*elem_id*/
        ctx[4]
      ),
      elem_classes: (
        /*elem_classes*/
        ctx[5]
      ),
      padding: (
        /*container*/
        ctx[7]
      ),
      allow_overflow: false,
      scale: (
        /*scale*/
        ctx[8]
      ),
      min_width: (
        /*min_width*/
        ctx[9]
      ),
      $$slots: { default: [create_default_slot] },
      $$scope: { ctx }
    }
  });
  return {
    c() {
      create_component(block.$$.fragment);
    },
    l(nodes) {
      claim_component(block.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(block, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const block_changes = {};
      if (dirty & /*visible*/
      64)
        block_changes.visible = /*visible*/
        ctx2[6];
      if (dirty & /*elem_id*/
      16)
        block_changes.elem_id = /*elem_id*/
        ctx2[4];
      if (dirty & /*elem_classes*/
      32)
        block_changes.elem_classes = /*elem_classes*/
        ctx2[5];
      if (dirty & /*container*/
      128)
        block_changes.padding = /*container*/
        ctx2[7];
      if (dirty & /*scale*/
      256)
        block_changes.scale = /*scale*/
        ctx2[8];
      if (dirty & /*min_width*/
      512)
        block_changes.min_width = /*min_width*/
        ctx2[9];
      if (dirty & /*$$scope, container, label, minimum, maximum, step, disabled, value, gradio, root, show_label, info, loading_status*/
      33684623) {
        block_changes.$$scope = { dirty, ctx: ctx2 };
      }
      block.$set(block_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(block.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(block.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(block, detaching);
    }
  };
}
function instance($$self, $$props, $$invalidate) {
  let disabled;
  let { gradio } = $$props;
  let { label = gradio.i18n("number.number") } = $$props;
  let { info = void 0 } = $$props;
  let { elem_id = "" } = $$props;
  let { elem_classes = [] } = $$props;
  let { visible = true } = $$props;
  let { container = true } = $$props;
  let { scale = null } = $$props;
  let { min_width = void 0 } = $$props;
  let { value = 0 } = $$props;
  let { show_label } = $$props;
  let { minimum = void 0 } = $$props;
  let { maximum = void 0 } = $$props;
  let { loading_status } = $$props;
  let { value_is_output = false } = $$props;
  let { step = null } = $$props;
  let { interactive } = $$props;
  let { root } = $$props;
  function handle_change() {
    if (!isNaN(value) && value !== null) {
      gradio.dispatch("change");
      if (!value_is_output) {
        gradio.dispatch("input");
      }
    }
  }
  afterUpdate(() => {
    $$invalidate(18, value_is_output = false);
  });
  async function handle_keypress(e) {
    await tick();
    if (e.key === "Enter") {
      e.preventDefault();
      gradio.dispatch("submit");
    }
  }
  const clear_status_handler = () => gradio.dispatch("clear_status", loading_status);
  function input_input_handler() {
    value = to_number(this.value);
    $$invalidate(0, value);
  }
  const blur_handler = () => gradio.dispatch("blur");
  const focus_handler = () => gradio.dispatch("focus");
  $$self.$$set = ($$props2) => {
    if ("gradio" in $$props2)
      $$invalidate(1, gradio = $$props2.gradio);
    if ("label" in $$props2)
      $$invalidate(2, label = $$props2.label);
    if ("info" in $$props2)
      $$invalidate(3, info = $$props2.info);
    if ("elem_id" in $$props2)
      $$invalidate(4, elem_id = $$props2.elem_id);
    if ("elem_classes" in $$props2)
      $$invalidate(5, elem_classes = $$props2.elem_classes);
    if ("visible" in $$props2)
      $$invalidate(6, visible = $$props2.visible);
    if ("container" in $$props2)
      $$invalidate(7, container = $$props2.container);
    if ("scale" in $$props2)
      $$invalidate(8, scale = $$props2.scale);
    if ("min_width" in $$props2)
      $$invalidate(9, min_width = $$props2.min_width);
    if ("value" in $$props2)
      $$invalidate(0, value = $$props2.value);
    if ("show_label" in $$props2)
      $$invalidate(10, show_label = $$props2.show_label);
    if ("minimum" in $$props2)
      $$invalidate(11, minimum = $$props2.minimum);
    if ("maximum" in $$props2)
      $$invalidate(12, maximum = $$props2.maximum);
    if ("loading_status" in $$props2)
      $$invalidate(13, loading_status = $$props2.loading_status);
    if ("value_is_output" in $$props2)
      $$invalidate(18, value_is_output = $$props2.value_is_output);
    if ("step" in $$props2)
      $$invalidate(14, step = $$props2.step);
    if ("interactive" in $$props2)
      $$invalidate(19, interactive = $$props2.interactive);
    if ("root" in $$props2)
      $$invalidate(15, root = $$props2.root);
  };
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*value*/
    1) {
      handle_change();
    }
    if ($$self.$$.dirty & /*interactive*/
    524288) {
      $$invalidate(16, disabled = !interactive);
    }
  };
  return [
    value,
    gradio,
    label,
    info,
    elem_id,
    elem_classes,
    visible,
    container,
    scale,
    min_width,
    show_label,
    minimum,
    maximum,
    loading_status,
    step,
    root,
    disabled,
    handle_keypress,
    value_is_output,
    interactive,
    clear_status_handler,
    input_input_handler,
    blur_handler,
    focus_handler
  ];
}
class Index extends SvelteComponent {
  constructor(options) {
    super();
    init(this, options, instance, create_fragment, safe_not_equal, {
      gradio: 1,
      label: 2,
      info: 3,
      elem_id: 4,
      elem_classes: 5,
      visible: 6,
      container: 7,
      scale: 8,
      min_width: 9,
      value: 0,
      show_label: 10,
      minimum: 11,
      maximum: 12,
      loading_status: 13,
      value_is_output: 18,
      step: 14,
      interactive: 19,
      root: 15
    });
  }
}
export {
  Index as default
};
