import{a as ee,i as te,s as le,Q as ae,q as g,l as B,v as y,w as G,o as j,p as M,c as T,x as F,k as ie,$ as oe,m as U,F as x,t as b,b as k,d as N,A as me,f as W,r as Q,ap as ze,y as V,z as X,a3 as Le,I as Z,E as de,J as Ae,K as He,aw as Pe,N as Fe,P as Oe,av as Ge,ao as De,aq as Je,M as Ie,n as fe,W as Ke,ax as Qe,a5 as ce,a6 as ue,G as Ve,H as _e,e as Xe,u as Ye,h as Ze,j as $e}from"../lite.js";import{B as xe}from"./BlockLabel-B0HN-MOU.js";import{I as et}from"./Image-Cvn5Jo_E.js";import{W as tt,a as lt,S as it}from"./SelectSource-CfNi7fcQ.js";import{g as nt}from"./utils-Gtzs_Zla.js";import{D as Te}from"./DropdownArrow-B85Vabzi.js";import{S as rt}from"./Square-7hK0fZD1.js";import{f as st}from"./index-DCygRGWm.js";import{S as at}from"./StreamingBar-C8nPcBp-.js";import{U as ot}from"./Upload-TGDabKXH.js";import{I as ct}from"./IconButtonWrapper-Ck50MZwX.js";import{I as ut}from"./Image-Bd-jnd8M.js";function ft(i){let e,t,l;return{c(){e=ae("svg"),t=ae("path"),l=ae("circle"),g(t,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),g(l,"cx","12"),g(l,"cy","13"),g(l,"r","4"),g(e,"xmlns","http://www.w3.org/2000/svg"),g(e,"width","100%"),g(e,"height","100%"),g(e,"viewBox","0 0 24 24"),g(e,"fill","none"),g(e,"stroke","currentColor"),g(e,"stroke-width","1.5"),g(e,"stroke-linecap","round"),g(e,"stroke-linejoin","round"),g(e,"class","feather feather-camera")},m(n,a){B(n,e,a),y(e,t),y(e,l)},p:G,i:G,o:G,d(n){n&&j(e)}}}class _t extends ee{constructor(e){super(),te(this,e,null,ft,le,{})}}function mt(i){let e,t;return{c(){e=ae("svg"),t=ae("circle"),g(t,"cx","12"),g(t,"cy","12"),g(t,"r","10"),g(e,"xmlns","http://www.w3.org/2000/svg"),g(e,"width","100%"),g(e,"height","100%"),g(e,"viewBox","0 0 24 24"),g(e,"stroke-width","1.5"),g(e,"stroke-linecap","round"),g(e,"stroke-linejoin","round"),g(e,"class","feather feather-circle")},m(l,n){B(l,e,n),y(e,t)},p:G,i:G,o:G,d(l){l&&j(e)}}}class dt extends ee{constructor(e){super(),te(this,e,null,mt,le,{})}}function gt(i){let e,t,l,n,a,r="Click to Access Webcam",o,s,_,u;return n=new tt({}),{c(){e=M("button"),t=M("div"),l=M("span"),T(n.$$.fragment),a=F(),o=ie(r),g(l,"class","icon-wrap svelte-qbrfs"),g(t,"class","wrap svelte-qbrfs"),g(e,"class","svelte-qbrfs"),oe(e,"height","100%")},m(m,S){B(m,e,S),y(e,t),y(t,l),U(n,l,null),y(t,a),y(t,o),s=!0,_||(u=x(e,"click",i[1]),_=!0)},p:G,i(m){s||(b(n.$$.fragment,m),s=!0)},o(m){k(n.$$.fragment,m),s=!1},d(m){m&&j(e),N(n),_=!1,u()}}}function ht(i){const e=me();return[e,()=>e("click")]}class bt extends ee{constructor(e){super(),te(this,e,ht,gt,le,{})}}function pt(){return navigator.mediaDevices.enumerateDevices()}function vt(i,e){e.srcObject=i,e.muted=!0,e.play()}async function Me(i,e,t){const l={width:{ideal:1920},height:{ideal:1440}},n={video:t?{deviceId:{exact:t},...l}:l,audio:i};return navigator.mediaDevices.getUserMedia(n).then(a=>(vt(a,e),a))}function wt(i){return i.filter(t=>t.kind==="videoinput")}function Se(i,e,t){const l=i.slice();return l[37]=e[t],l}function kt(i){let e,t,l,n,a,r,o,s,_,u,m;const S=[Wt,It],z=[];function E(w,I){return w[2]==="video"||w[1]?0:1}l=E(i),n=z[l]=S[l](i);let f=!i[11]&&Be(i),v=i[13]&&i[8]&&je(i);return{c(){e=M("div"),t=M("button"),n.c(),r=F(),f&&f.c(),o=F(),v&&v.c(),s=de(),g(t,"aria-label",a=i[2]==="image"?"capture photo":"start recording"),g(t,"class","svelte-s8feoe"),g(e,"class","button-wrap svelte-s8feoe")},m(w,I){B(w,e,I),y(e,t),z[l].m(t,null),y(e,r),f&&f.m(e,null),B(w,o,I),v&&v.m(w,I),B(w,s,I),_=!0,u||(m=x(t,"click",i[16]),u=!0)},p(w,I){let O=l;l=E(w),l===O?z[l].p(w,I):(V(),k(z[O],1,1,()=>{z[O]=null}),X(),n=z[l],n?n.p(w,I):(n=z[l]=S[l](w),n.c()),b(n,1),n.m(t,null)),(!_||I[0]&4&&a!==(a=w[2]==="image"?"capture photo":"start recording"))&&g(t,"aria-label",a),w[11]?f&&(V(),k(f,1,1,()=>{f=null}),X()):f?(f.p(w,I),I[0]&2048&&b(f,1)):(f=Be(w),f.c(),b(f,1),f.m(e,null)),w[13]&&w[8]?v?(v.p(w,I),I[0]&8448&&b(v,1)):(v=je(w),v.c(),b(v,1),v.m(s.parentNode,s)):v&&(V(),k(v,1,1,()=>{v=null}),X())},i(w){_||(b(n),b(f),b(v),_=!0)},o(w){k(n),k(f),k(v),_=!1},d(w){w&&(j(e),j(o),j(s)),z[l].d(),f&&f.d(),v&&v.d(w),u=!1,m()}}}function yt(i){let e,t,l,n;return t=new bt({}),t.$on("click",i[26]),{c(){e=M("div"),T(t.$$.fragment),g(e,"title","grant webcam access"),oe(e,"height","100%")},m(a,r){B(a,e,r),U(t,e,null),n=!0},p:G,i(a){n||(b(t.$$.fragment,a),a&&(l||Ae(()=>{l=He(e,st,{delay:100,duration:200}),l.start()})),n=!0)},o(a){k(t.$$.fragment,a),n=!1},d(a){a&&j(e),N(t)}}}function It(i){let e,t,l;return t=new _t({}),{c(){e=M("div"),T(t.$$.fragment),g(e,"class","icon svelte-s8feoe"),g(e,"title","capture photo")},m(n,a){B(n,e,a),U(t,e,null),l=!0},p:G,i(n){l||(b(t.$$.fragment,n),l=!0)},o(n){k(t.$$.fragment,n),l=!1},d(n){n&&j(e),N(t)}}}function Wt(i){let e,t,l,n;const a=[Mt,Dt,zt],r=[];function o(s,_){return s[1]&&s[10]==="waiting"?0:s[1]&&s[10]==="open"||!s[1]&&s[11]?1:2}return e=o(i),t=r[e]=a[e](i),{c(){t.c(),l=de()},m(s,_){r[e].m(s,_),B(s,l,_),n=!0},p(s,_){let u=e;e=o(s),e===u?r[e].p(s,_):(V(),k(r[u],1,1,()=>{r[u]=null}),X(),t=r[e],t?t.p(s,_):(t=r[e]=a[e](s),t.c()),b(t,1),t.m(l.parentNode,l))},i(s){n||(b(t),n=!0)},o(s){k(t),n=!1},d(s){s&&j(l),r[e].d(s)}}}function zt(i){let e,t,l,n,a=i[4]("audio.record")+"",r,o;return l=new dt({}),{c(){e=M("div"),t=M("div"),T(l.$$.fragment),n=F(),r=ie(a),g(t,"class","icon color-primary svelte-s8feoe"),g(t,"title","start recording"),g(e,"class","icon-with-text svelte-s8feoe")},m(s,_){B(s,e,_),y(e,t),U(l,t,null),y(e,n),y(e,r),o=!0},p(s,_){(!o||_[0]&16)&&a!==(a=s[4]("audio.record")+"")&&fe(r,a)},i(s){o||(b(l.$$.fragment,s),o=!0)},o(s){k(l.$$.fragment,s),o=!1},d(s){s&&j(e),N(l)}}}function Dt(i){let e,t,l,n,a=i[4]("audio.stop")+"",r,o;return l=new rt({}),{c(){e=M("div"),t=M("div"),T(l.$$.fragment),n=F(),r=ie(a),g(t,"class","icon color-primary svelte-s8feoe"),g(t,"title","stop recording"),g(e,"class","icon-with-text svelte-s8feoe")},m(s,_){B(s,e,_),y(e,t),U(l,t,null),y(e,n),y(e,r),o=!0},p(s,_){(!o||_[0]&16)&&a!==(a=s[4]("audio.stop")+"")&&fe(r,a)},i(s){o||(b(l.$$.fragment,s),o=!0)},o(s){k(l.$$.fragment,s),o=!1},d(s){s&&j(e),N(l)}}}function Mt(i){let e,t,l,n,a=i[4]("audio.waiting")+"",r,o;return l=new lt({}),{c(){e=M("div"),t=M("div"),T(l.$$.fragment),n=F(),r=ie(a),g(t,"class","icon color-primary svelte-s8feoe"),g(t,"title","spinner"),g(e,"class","icon-with-text svelte-s8feoe"),oe(e,"width","var(--size-24)")},m(s,_){B(s,e,_),y(e,t),U(l,t,null),y(e,n),y(e,r),o=!0},p(s,_){(!o||_[0]&16)&&a!==(a=s[4]("audio.waiting")+"")&&fe(r,a)},i(s){o||(b(l.$$.fragment,s),o=!0)},o(s){k(l.$$.fragment,s),o=!1},d(s){s&&j(e),N(l)}}}function Be(i){let e,t,l,n,a;return t=new Te({}),{c(){e=M("button"),T(t.$$.fragment),g(e,"class","icon svelte-s8feoe"),g(e,"aria-label","select input source")},m(r,o){B(r,e,o),U(t,e,null),l=!0,n||(a=x(e,"click",i[27]),n=!0)},p:G,i(r){l||(b(t.$$.fragment,r),l=!0)},o(r){k(t.$$.fragment,r),l=!1},d(r){r&&j(e),N(t),n=!1,a()}}}function je(i){let e,t,l,n,a,r,o;l=new Te({});function s(m,S){return m[7].length===0?Bt:St}let _=s(i),u=_(i);return{c(){e=M("select"),t=M("button"),T(l.$$.fragment),n=F(),u.c(),g(t,"class","inset-icon svelte-s8feoe"),g(e,"class","select-wrap svelte-s8feoe"),g(e,"aria-label","select source")},m(m,S){B(m,e,S),y(e,t),U(l,t,null),y(t,n),u.m(e,null),a=!0,r||(o=[x(t,"click",Pe(i[28])),Fe(We.call(null,e,i[17])),x(e,"change",i[14])],r=!0)},p(m,S){_===(_=s(m))&&u?u.p(m,S):(u.d(1),u=_(m),u&&(u.c(),u.m(e,null)))},i(m){a||(b(l.$$.fragment,m),a=!0)},o(m){k(l.$$.fragment,m),a=!1},d(m){m&&j(e),N(l),u.d(),r=!1,Oe(o)}}}function St(i){let e,t=De(i[7]),l=[];for(let n=0;n<t.length;n+=1)l[n]=qe(Se(i,t,n));return{c(){for(let n=0;n<l.length;n+=1)l[n].c();e=de()},m(n,a){for(let r=0;r<l.length;r+=1)l[r]&&l[r].m(n,a);B(n,e,a)},p(n,a){if(a[0]&384){t=De(n[7]);let r;for(r=0;r<t.length;r+=1){const o=Se(n,t,r);l[r]?l[r].p(o,a):(l[r]=qe(o),l[r].c(),l[r].m(e.parentNode,e))}for(;r<l.length;r+=1)l[r].d(1);l.length=t.length}},d(n){n&&j(e),Je(l,n)}}}function Bt(i){let e,t=i[4]("common.no_devices")+"",l;return{c(){e=M("option"),l=ie(t),e.__value="",Ie(e,e.__value),g(e,"class","svelte-s8feoe")},m(n,a){B(n,e,a),y(e,l)},p(n,a){a[0]&16&&t!==(t=n[4]("common.no_devices")+"")&&fe(l,t)},d(n){n&&j(e)}}}function qe(i){let e,t=i[37].label+"",l,n,a,r;return{c(){e=M("option"),l=ie(t),n=F(),e.__value=a=i[37].deviceId,Ie(e,e.__value),e.selected=r=i[8].deviceId===i[37].deviceId,g(e,"class","svelte-s8feoe")},m(o,s){B(o,e,s),y(e,l),y(e,n)},p(o,s){s[0]&128&&t!==(t=o[37].label+"")&&fe(l,t),s[0]&128&&a!==(a=o[37].deviceId)&&(e.__value=a,Ie(e,e.__value)),s[0]&384&&r!==(r=o[8].deviceId===o[37].deviceId)&&(e.selected=r)},d(o){o&&j(e)}}}function jt(i){let e,t,l,n,a,r,o,s,_,u,m;t=new at({props:{time_limit:i[9]}});const S=[yt,kt],z=[];function E(f,v){return f[12]?1:0}return _=E(i),u=z[_]=S[_](i),{c(){e=M("div"),T(t.$$.fragment),l=F(),n=M("video"),a=F(),r=M("img"),s=F(),u.c(),g(n,"class","svelte-s8feoe"),Q(n,"flip",i[3]),Q(n,"hide",!i[12]||i[12]&&!!i[0]),ze(r.src,o=i[0]?.url)||g(r,"src",o),g(r,"class","svelte-s8feoe"),Q(r,"hide",!i[12]||i[12]&&!i[0]),g(e,"class","wrap svelte-s8feoe")},m(f,v){B(f,e,v),U(t,e,null),y(e,l),y(e,n),i[25](n),y(e,a),y(e,r),y(e,s),z[_].m(e,null),m=!0},p(f,v){const w={};v[0]&512&&(w.time_limit=f[9]),t.$set(w),(!m||v[0]&8)&&Q(n,"flip",f[3]),(!m||v[0]&4097)&&Q(n,"hide",!f[12]||f[12]&&!!f[0]),(!m||v[0]&1&&!ze(r.src,o=f[0]?.url))&&g(r,"src",o),(!m||v[0]&4097)&&Q(r,"hide",!f[12]||f[12]&&!f[0]);let I=_;_=E(f),_===I?z[_].p(f,v):(V(),k(z[I],1,1,()=>{z[I]=null}),X(),u=z[_],u?u.p(f,v):(u=z[_]=S[_](f),u.c()),b(u,1),u.m(e,null))},i(f){m||(b(t.$$.fragment,f),b(u),m=!0)},o(f){k(t.$$.fragment,f),k(u),m=!1},d(f){f&&j(e),N(t),i[25](null),z[_].d()}}}function We(i,e){const t=l=>{i&&!i.contains(l.target)&&!l.defaultPrevented&&e(l)};return document.addEventListener("click",t,!0),{destroy(){document.removeEventListener("click",t,!0)}}}function qt(i,e,t){let l,n=[],a=null,r=null,o="closed";const s=h=>{h==="closed"?(t(9,r=null),t(10,o="closed"),t(0,q=null)):h==="waiting"?t(10,o="waiting"):t(10,o="open")},_=h=>{A&&t(9,r=h)};let u,{streaming:m=!1}=e,{pending:S=!1}=e,{root:z=""}=e,{stream_every:E=1}=e,{mode:f="image"}=e,{mirror_webcam:v}=e,{include_audio:w}=e,{i18n:I}=e,{upload:O}=e,{value:q=null}=e;const L=me();Le(()=>{u=document.createElement("canvas"),m&&f==="image"&&window.setInterval(()=>{l&&!S&&p()},E*1e3)});const D=async h=>{const K=h.target.value;await Me(w,l,K).then(async re=>{H=re,t(8,a=n.find(se=>se.deviceId===K)||null),t(13,Y=!1)})};async function d(){try{Me(w,l).then(async h=>{t(12,ne=!0),t(7,n=await pt()),H=h}).then(()=>wt(n)).then(h=>{t(7,n=h);const R=H.getTracks().map(K=>K.getSettings()?.deviceId)[0];t(8,a=R&&h.find(K=>K.deviceId===R)||n[0])}),(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)&&L("error",I("image.no_webcam_support"))}catch(h){if(h instanceof DOMException&&h.name=="NotAllowedError")L("error",I("image.allow_webcam_access"));else throw h}}function p(){var h=u.getContext("2d");if((!m||m&&A)&&l.videoWidth&&l.videoHeight){if(u.width=l.videoWidth,u.height=l.videoHeight,h.drawImage(l,0,0,l.videoWidth,l.videoHeight),v&&(h.scale(-1,1),h.drawImage(l,-l.videoWidth,0)),m&&(!A||o==="waiting"))return;if(m){const R=u.toDataURL("image/jpeg");L("stream",R);return}u.toBlob(R=>{L(m?"stream":"capture",R)},`image/${m?"jpeg":"png"}`,.8)}}let A=!1,C=[],H,P,J;function ge(){if(A){J.stop();let h=new Blob(C,{type:P}),R=new FileReader;R.onload=async function(K){if(K.target){let re=new File([h],"sample."+P.substring(6));const se=await Ge([re]);let ye=(await O(se,z))?.filter(Boolean)[0];L("capture",ye),L("stop_recording")}},R.readAsDataURL(h)}else{L("start_recording"),C=[];let h=["video/webm","video/mp4"];for(let R of h)if(MediaRecorder.isTypeSupported(R)){P=R;break}if(P===null){console.error("No supported MediaRecorder mimeType");return}J=new MediaRecorder(H,{mimeType:P}),J.addEventListener("dataavailable",function(R){C.push(R.data)}),J.start(200)}t(11,A=!A)}let ne=!1;function he(){f==="image"&&m&&t(11,A=!A),f==="image"?p():ge(),!A&&H&&(L("close_stream"),H.getTracks().forEach(h=>h.stop()),t(6,l.srcObject=null,l),t(12,ne=!1),window.setTimeout(()=>{t(0,q=null)},500),t(0,q=null))}let Y=!1;function be(h){h.preventDefault(),h.stopPropagation(),t(13,Y=!1)}function pe(h){Z[h?"unshift":"push"](()=>{l=h,t(6,l)})}const ve=async()=>d(),we=()=>t(13,Y=!0),ke=()=>t(13,Y=!1);return i.$$set=h=>{"streaming"in h&&t(1,m=h.streaming),"pending"in h&&t(20,S=h.pending),"root"in h&&t(21,z=h.root),"stream_every"in h&&t(22,E=h.stream_every),"mode"in h&&t(2,f=h.mode),"mirror_webcam"in h&&t(3,v=h.mirror_webcam),"include_audio"in h&&t(23,w=h.include_audio),"i18n"in h&&t(4,I=h.i18n),"upload"in h&&t(24,O=h.upload),"value"in h&&t(0,q=h.value)},[q,m,f,v,I,We,l,n,a,r,o,A,ne,Y,D,d,he,be,s,_,S,z,E,w,O,pe,ve,we,ke]}class Et extends ee{constructor(e){super(),te(this,e,qt,jt,le,{modify_stream:18,set_time_limit:19,streaming:1,pending:20,root:21,stream_every:22,mode:2,mirror_webcam:3,include_audio:23,i18n:4,upload:24,value:0,click_outside:5},null,[-1,-1])}get modify_stream(){return this.$$.ctx[18]}get set_time_limit(){return this.$$.ctx[19]}get streaming(){return this.$$.ctx[1]}set streaming(e){this.$$set({streaming:e}),W()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),W()}get root(){return this.$$.ctx[21]}set root(e){this.$$set({root:e}),W()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),W()}get mode(){return this.$$.ctx[2]}set mode(e){this.$$set({mode:e}),W()}get mirror_webcam(){return this.$$.ctx[3]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),W()}get include_audio(){return this.$$.ctx[23]}set include_audio(e){this.$$set({include_audio:e}),W()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),W()}get upload(){return this.$$.ctx[24]}set upload(e){this.$$set({upload:e}),W()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),W()}get click_outside(){return We}}const Ct=Et;function Rt(i){let e,t;return e=new Ke({props:{Icon:Qe,label:"Remove Image"}}),e.$on("click",i[1]),{c(){T(e.$$.fragment)},m(l,n){U(e,l,n),t=!0},p:G,i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){k(e.$$.fragment,l),t=!1},d(l){N(e,l)}}}function Tt(i){let e,t;return e=new ct({props:{$$slots:{default:[Rt]},$$scope:{ctx:i}}}),{c(){T(e.$$.fragment)},m(l,n){U(e,l,n),t=!0},p(l,[n]){const a={};n&4&&(a.$$scope={dirty:n,ctx:l}),e.$set(a)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){k(e.$$.fragment,l),t=!1},d(l){N(e,l)}}}function Ut(i){const e=me();return[e,l=>{e("remove_image"),l.stopPropagation()}]}class Nt extends ee{constructor(e){super(),te(this,e,Ut,Tt,le,{})}}function Ee(i){let e,t;return e=new Nt({}),e.$on("remove_image",i[28]),{c(){T(e.$$.fragment)},m(l,n){U(e,l,n),t=!0},p:G,i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){k(e.$$.fragment,l),t=!1},d(l){N(e,l)}}}function Ce(i){let e;const t=i[27].default,l=Xe(t,i,i[42],null);return{c(){l&&l.c()},m(n,a){l&&l.m(n,a),e=!0},p(n,a){l&&l.p&&(!e||a[1]&2048)&&Ye(l,t,n,n[42],e?$e(t,n[42],a,null):Ze(n[42]),null)},i(n){e||(b(l,n),e=!0)},o(n){k(l,n),e=!1},d(n){l&&l.d(n)}}}function Lt(i){let e,t,l=i[3]===null&&Ce(i);return{c(){l&&l.c(),e=de()},m(n,a){l&&l.m(n,a),B(n,e,a),t=!0},p(n,a){n[3]===null?l?(l.p(n,a),a[0]&8&&b(l,1)):(l=Ce(n),l.c(),b(l,1),l.m(e.parentNode,e)):l&&(V(),k(l,1,1,()=>{l=null}),X())},i(n){t||(b(l),t=!0)},o(n){k(l),t=!1},d(n){n&&j(e),l&&l.d(n)}}}function At(i){let e,t,l,n,a;return t=new ut({props:{src:i[3].url,alt:i[3].alt_text}}),{c(){e=M("div"),T(t.$$.fragment),g(e,"class","image-frame svelte-1ti4ehe"),Q(e,"selectable",i[11])},m(r,o){B(r,e,o),U(t,e,null),l=!0,n||(a=x(e,"click",i[24]),n=!0)},p(r,o){const s={};o[0]&8&&(s.src=r[3].url),o[0]&8&&(s.alt=r[3].alt_text),t.$set(s),(!l||o[0]&2048)&&Q(e,"selectable",r[11])},i(r){l||(b(t.$$.fragment,r),l=!0)},o(r){k(t.$$.fragment,r),l=!1},d(r){r&&j(e),N(t),n=!1,a()}}}function Ht(i){let e,t,l,n;function a(s){i[33](s)}function r(s){i[34](s)}let o={root:i[12],value:i[3],mirror_webcam:i[10],stream_every:i[17],streaming:i[9],mode:"image",include_audio:!1,i18n:i[13],upload:i[15]};return i[4]!==void 0&&(o.modify_stream=i[4]),i[5]!==void 0&&(o.set_time_limit=i[5]),e=new Ct({props:o}),Z.push(()=>ce(e,"modify_stream",a)),Z.push(()=>ce(e,"set_time_limit",r)),e.$on("capture",i[35]),e.$on("stream",i[36]),e.$on("error",i[37]),e.$on("drag",i[38]),e.$on("upload",i[39]),e.$on("close_stream",i[40]),{c(){T(e.$$.fragment)},m(s,_){U(e,s,_),n=!0},p(s,_){const u={};_[0]&4096&&(u.root=s[12]),_[0]&8&&(u.value=s[3]),_[0]&1024&&(u.mirror_webcam=s[10]),_[0]&131072&&(u.stream_every=s[17]),_[0]&512&&(u.streaming=s[9]),_[0]&8192&&(u.i18n=s[13]),_[0]&32768&&(u.upload=s[15]),!t&&_[0]&16&&(t=!0,u.modify_stream=s[4],ue(()=>t=!1)),!l&&_[0]&32&&(l=!0,u.set_time_limit=s[5],ue(()=>l=!1)),e.$set(u)},i(s){n||(b(e.$$.fragment,s),n=!0)},o(s){k(e.$$.fragment,s),n=!1},d(s){N(e,s)}}}function Re(i){let e,t,l;function n(r){i[41](r)}let a={sources:i[8],handle_clear:i[21],handle_select:i[25]};return i[1]!==void 0&&(a.active_source=i[1]),e=new it({props:a}),Z.push(()=>ce(e,"active_source",n)),{c(){T(e.$$.fragment)},m(r,o){U(e,r,o),l=!0},p(r,o){const s={};o[0]&256&&(s.sources=r[8]),!t&&o[0]&2&&(t=!0,s.active_source=r[1],ue(()=>t=!1)),e.$set(s)},i(r){l||(b(e.$$.fragment,r),l=!0)},o(r){k(e.$$.fragment,r),l=!1},d(r){N(e,r)}}}function Pt(i){let e,t,l,n,a,r,o,s,_,u,m,S,z=i[8].length>1||i[8].includes("clipboard"),E;e=new xe({props:{show_label:i[7],Icon:et,label:i[6]||"Image"}});let f=i[3]?.url&&!i[18]&&Ee(i);function v(d){i[30](d)}function w(d){i[31](d)}let I={hidden:i[3]!==null||i[1]==="webcam",filetype:i[1]==="clipboard"?"clipboard":"image/*",root:i[12],max_file_size:i[14],disable_click:!i[8].includes("upload")||i[3]!==null,upload:i[15],stream_handler:i[16],$$slots:{default:[Lt]},$$scope:{ctx:i}};i[0]!==void 0&&(I.uploading=i[0]),i[2]!==void 0&&(I.dragging=i[2]),r=new ot({props:I}),i[29](r),Z.push(()=>ce(r,"uploading",v)),Z.push(()=>ce(r,"dragging",w)),r.$on("load",i[20]),r.$on("error",i[32]);const O=[Ht,At],q=[];function L(d,p){return d[1]==="webcam"&&(d[9]||!d[9]&&!d[3])?0:d[3]!==null&&!d[9]?1:-1}~(u=L(i))&&(m=q[u]=O[u](i));let D=z&&Re(i);return{c(){T(e.$$.fragment),t=F(),l=M("div"),f&&f.c(),n=F(),a=M("div"),T(r.$$.fragment),_=F(),m&&m.c(),S=F(),D&&D.c(),g(a,"class","upload-container svelte-1ti4ehe"),Q(a,"reduced-height",i[8].length>1),oe(a,"width",i[3]?"auto":"100%"),g(l,"data-testid","image"),g(l,"class","image-container svelte-1ti4ehe")},m(d,p){U(e,d,p),B(d,t,p),B(d,l,p),f&&f.m(l,null),y(l,n),y(l,a),U(r,a,null),y(a,_),~u&&q[u].m(a,null),y(l,S),D&&D.m(l,null),E=!0},p(d,p){const A={};p[0]&128&&(A.show_label=d[7]),p[0]&64&&(A.label=d[6]||"Image"),e.$set(A),d[3]?.url&&!d[18]?f?(f.p(d,p),p[0]&262152&&b(f,1)):(f=Ee(d),f.c(),b(f,1),f.m(l,n)):f&&(V(),k(f,1,1,()=>{f=null}),X());const C={};p[0]&10&&(C.hidden=d[3]!==null||d[1]==="webcam"),p[0]&2&&(C.filetype=d[1]==="clipboard"?"clipboard":"image/*"),p[0]&4096&&(C.root=d[12]),p[0]&16384&&(C.max_file_size=d[14]),p[0]&264&&(C.disable_click=!d[8].includes("upload")||d[3]!==null),p[0]&32768&&(C.upload=d[15]),p[0]&65536&&(C.stream_handler=d[16]),p[0]&8|p[1]&2048&&(C.$$scope={dirty:p,ctx:d}),!o&&p[0]&1&&(o=!0,C.uploading=d[0],ue(()=>o=!1)),!s&&p[0]&4&&(s=!0,C.dragging=d[2],ue(()=>s=!1)),r.$set(C);let H=u;u=L(d),u===H?~u&&q[u].p(d,p):(m&&(V(),k(q[H],1,1,()=>{q[H]=null}),X()),~u?(m=q[u],m?m.p(d,p):(m=q[u]=O[u](d),m.c()),b(m,1),m.m(a,null)):m=null),(!E||p[0]&256)&&Q(a,"reduced-height",d[8].length>1),p[0]&8&&oe(a,"width",d[3]?"auto":"100%"),p[0]&256&&(z=d[8].length>1||d[8].includes("clipboard")),z?D?(D.p(d,p),p[0]&256&&b(D,1)):(D=Re(d),D.c(),b(D,1),D.m(l,null)):D&&(V(),k(D,1,1,()=>{D=null}),X())},i(d){E||(b(e.$$.fragment,d),b(f),b(r.$$.fragment,d),b(m),b(D),E=!0)},o(d){k(e.$$.fragment,d),k(f),k(r.$$.fragment,d),k(m),k(D),E=!1},d(d){d&&(j(t),j(l)),N(e,d),f&&f.d(),i[29](null),N(r),~u&&q[u].d(),D&&D.d()}}}function Ft(i,e,t){let l,{$$slots:n={},$$scope:a}=e,{value:r=null}=e,{label:o=void 0}=e,{show_label:s}=e,{sources:_=["upload","clipboard","webcam"]}=e,{streaming:u=!1}=e,{pending:m=!1}=e,{mirror_webcam:S}=e,{selectable:z=!1}=e,{root:E}=e,{i18n:f}=e,{max_file_size:v=null}=e,{upload:w}=e,{stream_handler:I}=e,{stream_every:O}=e,{modify_stream:q}=e,{set_time_limit:L}=e,D,{uploading:d=!1}=e,{active_source:p=null}=e;function A({detail:c}){u||(t(3,r=c),P("upload"))}function C(){t(3,r=null),P("clear"),P("change",null)}async function H(c,$){if($==="stream"){P("stream",{value:{url:c},is_value_data:!0});return}t(26,m=!0);const Ne=await D.load_files([new File([c],`image/${u?"jpeg":"png"}`)]);($==="change"||$==="upload")&&(t(3,r=Ne?.[0]||null),await Ve(),P("change")),t(26,m=!1)}const P=me();let{dragging:J=!1}=e;function ge(c){let $=nt(c);$&&P("select",{index:$,value:null})}async function ne(c){switch(c){case"clipboard":D.paste_clipboard();break}}const he=()=>{t(3,r=null),P("clear")};function Y(c){Z[c?"unshift":"push"](()=>{D=c,t(19,D)})}function be(c){d=c,t(0,d)}function pe(c){J=c,t(2,J)}function ve(c){_e.call(this,i,c)}function we(c){q=c,t(4,q)}function ke(c){L=c,t(5,L)}const h=c=>H(c.detail,"change"),R=c=>H(c.detail,"stream");function K(c){_e.call(this,i,c)}function re(c){_e.call(this,i,c)}const se=c=>H(c.detail,"upload");function ye(c){_e.call(this,i,c)}function Ue(c){p=c,t(1,p),t(8,_)}return i.$$set=c=>{"value"in c&&t(3,r=c.value),"label"in c&&t(6,o=c.label),"show_label"in c&&t(7,s=c.show_label),"sources"in c&&t(8,_=c.sources),"streaming"in c&&t(9,u=c.streaming),"pending"in c&&t(26,m=c.pending),"mirror_webcam"in c&&t(10,S=c.mirror_webcam),"selectable"in c&&t(11,z=c.selectable),"root"in c&&t(12,E=c.root),"i18n"in c&&t(13,f=c.i18n),"max_file_size"in c&&t(14,v=c.max_file_size),"upload"in c&&t(15,w=c.upload),"stream_handler"in c&&t(16,I=c.stream_handler),"stream_every"in c&&t(17,O=c.stream_every),"modify_stream"in c&&t(4,q=c.modify_stream),"set_time_limit"in c&&t(5,L=c.set_time_limit),"uploading"in c&&t(0,d=c.uploading),"active_source"in c&&t(1,p=c.active_source),"dragging"in c&&t(2,J=c.dragging),"$$scope"in c&&t(42,a=c.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&258&&!p&&_&&t(1,p=_[0]),i.$$.dirty[0]&514&&t(18,l=u&&p==="webcam"),i.$$.dirty[0]&262145&&d&&!l&&t(3,r=null),i.$$.dirty[0]&4&&P("drag",J)},[d,p,J,r,q,L,o,s,_,u,S,z,E,f,v,w,I,O,l,D,A,C,H,P,ge,ne,m,n,he,Y,be,pe,ve,we,ke,h,R,K,re,se,ye,Ue,a]}class Ot extends ee{constructor(e){super(),te(this,e,Ft,Pt,le,{value:3,label:6,show_label:7,sources:8,streaming:9,pending:26,mirror_webcam:10,selectable:11,root:12,i18n:13,max_file_size:14,upload:15,stream_handler:16,stream_every:17,modify_stream:4,set_time_limit:5,uploading:0,active_source:1,dragging:2},null,[-1,-1])}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),W()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),W()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),W()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),W()}get streaming(){return this.$$.ctx[9]}set streaming(e){this.$$set({streaming:e}),W()}get pending(){return this.$$.ctx[26]}set pending(e){this.$$set({pending:e}),W()}get mirror_webcam(){return this.$$.ctx[10]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),W()}get selectable(){return this.$$.ctx[11]}set selectable(e){this.$$set({selectable:e}),W()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),W()}get i18n(){return this.$$.ctx[13]}set i18n(e){this.$$set({i18n:e}),W()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),W()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),W()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),W()}get stream_every(){return this.$$.ctx[17]}set stream_every(e){this.$$set({stream_every:e}),W()}get modify_stream(){return this.$$.ctx[4]}set modify_stream(e){this.$$set({modify_stream:e}),W()}get set_time_limit(){return this.$$.ctx[5]}set set_time_limit(e){this.$$set({set_time_limit:e}),W()}get uploading(){return this.$$.ctx[0]}set uploading(e){this.$$set({uploading:e}),W()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),W()}get dragging(){return this.$$.ctx[2]}set dragging(e){this.$$set({dragging:e}),W()}}const ll=Ot;export{ll as I,Ct as W};
//# sourceMappingURL=ImageUploader-DjVwxXyF.js.map
