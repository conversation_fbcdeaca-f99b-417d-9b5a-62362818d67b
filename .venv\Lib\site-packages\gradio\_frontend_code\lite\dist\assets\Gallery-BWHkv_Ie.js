import{a as xe,i as et,s as tt,f as L,J as lt,x as A,E as Be,l as B,F as Y,t as m,y as R,b as d,z as M,o as T,A as nt,a3 as it,aA as rt,c as z,m as I,d as E,G as ot,H as _e,I as re,ao as te,p as U,q,$ as V,r as D,v as N,aq as Te,O as st,P as ft,k as Ae,n as De,W as le,w as Re,ax as ut}from"../lite.js";import{B as at}from"./BlockLabel-B0HN-MOU.js";import{E as ct}from"./Empty-C76eC2zW.js";import{S as _t}from"./ShareButton-Bn_rnIUK.js";import{D as mt}from"./Download-CgLP-Xl6.js";import{a as oe,M as me}from"./Minimize-DJwpjnSa.js";import{I as Me}from"./Image-Cvn5Jo_E.js";import{P as Ne}from"./Play-Cd5lLtoD.js";import{I as ht}from"./IconButtonWrapper-Ck50MZwX.js";/* empty css                                             */import{M as gt}from"./ModifyUpload-CWo4TCq1.js";import{I as se}from"./Image-Bd-jnd8M.js";/* empty css                                                   */import{V as fe}from"./Video-B2DSnbGg.js";import{u as bt}from"./utils-BsGrhMNe.js";import"./Community-_qL8Iyvr.js";import"./Undo-DitIvwTU.js";import"./DownloadLink-B8hI46-W.js";import"./file-url-Co2ROWca.js";import"./hls-CnVhpNcu.js";var he=Object.prototype.hasOwnProperty;function ge(n,e,l){for(l of n.keys())if(Q(l,e))return l}function Q(n,e){var l,t,i;if(n===e)return!0;if(n&&e&&(l=n.constructor)===e.constructor){if(l===Date)return n.getTime()===e.getTime();if(l===RegExp)return n.toString()===e.toString();if(l===Array){if((t=n.length)===e.length)for(;t--&&Q(n[t],e[t]););return t===-1}if(l===Set){if(n.size!==e.size)return!1;for(t of n)if(i=t,i&&typeof i=="object"&&(i=ge(e,i),!i)||!e.has(i))return!1;return!0}if(l===Map){if(n.size!==e.size)return!1;for(t of n)if(i=t[0],i&&typeof i=="object"&&(i=ge(e,i),!i)||!Q(t[1],e.get(i)))return!1;return!0}if(l===ArrayBuffer)n=new Uint8Array(n),e=new Uint8Array(e);else if(l===DataView){if((t=n.byteLength)===e.byteLength)for(;t--&&n.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(n)){if((t=n.byteLength)===e.byteLength)for(;t--&&n[t]===e[t];);return t===-1}if(!l||typeof n=="object"){t=0;for(l in n)if(he.call(n,l)&&++t&&!he.call(e,l)||!(l in e)||!Q(n[l],e[l]))return!1;return Object.keys(e).length===t}}return n!==n&&e!==e}async function dt(n){return n?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(n.map(async([l,t])=>l===null||!l.url?"":await bt(l.url)))).map(l=>`<img src="${l}" style="height: 400px" />`).join("")}</div>`:""}const{window:Ue}=rt;function be(n,e,l){const t=n.slice();return t[47]=e[l],t[49]=l,t}function de(n,e,l){const t=n.slice();return t[50]=e[l],t[51]=e,t[49]=l,t}function pe(n){let e,l;return e=new at({props:{show_label:n[2],Icon:Me,label:n[3]||"Gallery"}}),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p(t,i){const r={};i[0]&4&&(r.show_label=t[2]),i[0]&8&&(r.label=t[3]||"Gallery"),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function pt(n){let e,l,t,i,r,c,a=n[21]&&n[7]&&we(n),s=n[12]&&Ie(n),b=te(n[15]),_=[];for(let o=0;o<b.length;o+=1)_[o]=Le(be(n,b,o));const g=o=>d(_[o],1,1,()=>{_[o]=null});return{c(){e=U("div"),a&&a.c(),l=A(),t=U("div"),i=U("div"),s&&s.c(),r=A();for(let o=0;o<_.length;o+=1)_[o].c();q(i,"class","grid-container svelte-17j4qub"),V(i,"--grid-cols",n[4]),V(i,"--grid-rows",n[5]),V(i,"--object-fit",n[8]),V(i,"height",n[6]),D(i,"pt-6",n[2]),q(t,"class","grid-wrap svelte-17j4qub"),D(t,"minimal",n[13]==="minimal"),D(t,"fixed-height",n[13]!=="minimal"&&(!n[6]||n[6]=="auto")),D(t,"hidden",n[16]),q(e,"class","gallery-container")},m(o,f){B(o,e,f),a&&a.m(e,null),N(e,l),N(e,t),N(t,i),s&&s.m(i,null),N(i,r);for(let p=0;p<_.length;p+=1)_[p]&&_[p].m(i,null);n[42](e),c=!0},p(o,f){if(o[21]&&o[7]?a?(a.p(o,f),f[0]&2097280&&m(a,1)):(a=we(o),a.c(),m(a,1),a.m(e,l)):a&&(R(),d(a,1,1,()=>{a=null}),M()),o[12]?s?(s.p(o,f),f[0]&4096&&m(s,1)):(s=Ie(o),s.c(),m(s,1),s.m(i,r)):s&&(R(),d(s,1,1,()=>{s=null}),M()),f[0]&32770){b=te(o[15]);let p;for(p=0;p<b.length;p+=1){const j=be(o,b,p);_[p]?(_[p].p(j,f),m(_[p],1)):(_[p]=Le(j),_[p].c(),m(_[p],1),_[p].m(i,null))}for(R(),p=b.length;p<_.length;p+=1)g(p);M()}(!c||f[0]&16)&&V(i,"--grid-cols",o[4]),(!c||f[0]&32)&&V(i,"--grid-rows",o[5]),(!c||f[0]&256)&&V(i,"--object-fit",o[8]),(!c||f[0]&64)&&V(i,"height",o[6]),(!c||f[0]&4)&&D(i,"pt-6",o[2]),(!c||f[0]&8192)&&D(t,"minimal",o[13]==="minimal"),(!c||f[0]&8256)&&D(t,"fixed-height",o[13]!=="minimal"&&(!o[6]||o[6]=="auto")),(!c||f[0]&65536)&&D(t,"hidden",o[16])},i(o){if(!c){m(a),m(s);for(let f=0;f<b.length;f+=1)m(_[f]);c=!0}},o(o){d(a),d(s),_=_.filter(Boolean);for(let f=0;f<_.length;f+=1)d(_[f]);c=!1},d(o){o&&T(e),a&&a.d(),s&&s.d(),Te(_,o),n[42](null)}}}function wt(n){let e,l;return e=new ct({props:{unpadded_box:!0,size:"large",$$slots:{default:[It]},$$scope:{ctx:n}}}),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p(t,i){const r={};i[1]&2097152&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function we(n){let e,l,t,i,r,c,a,s,b,_,g,o;l=new ht({props:{$$slots:{default:[kt]},$$scope:{ctx:n}}});const f=[$t,vt],p=[];function j(k,v){return"image"in k[21]?0:1}r=j(n),c=p[r]=f[r](n);let w=n[21]?.caption&&qe(n),$=te(n[15]),h=[];for(let k=0;k<$.length;k+=1)h[k]=ze(de(n,$,k));const Z=k=>d(h[k],1,1,()=>{h[k]=null});return{c(){e=U("button"),z(l.$$.fragment),t=A(),i=U("button"),c.c(),a=A(),w&&w.c(),s=A(),b=U("div");for(let k=0;k<h.length;k+=1)h[k].c();q(i,"class","media-button svelte-17j4qub"),V(i,"height","calc(100% - "+(n[21].caption?"80px":"60px")+")"),q(i,"aria-label","detailed view of selected image"),q(b,"class","thumbnails scroll-hide svelte-17j4qub"),q(b,"data-testid","container_el"),q(e,"class","preview svelte-17j4qub"),D(e,"minimal",n[13]==="minimal")},m(k,v){B(k,e,v),I(l,e,null),N(e,t),N(e,i),p[r].m(i,null),N(e,a),w&&w.m(e,null),N(e,s),N(e,b);for(let O=0;O<h.length;O+=1)h[O]&&h[O].m(b,null);n[39](b),_=!0,g||(o=[Y(i,"click",function(){st("image"in n[21]?n[36]:null)&&("image"in n[21]?n[36]:null).apply(this,arguments)}),Y(e,"keydown",n[23])],g=!0)},p(k,v){n=k;const O={};v[0]&2215426|v[1]&2097152&&(O.$$scope={dirty:v,ctx:n}),l.$set(O);let G=r;if(r=j(n),r===G?p[r].p(n,v):(R(),d(p[G],1,1,()=>{p[G]=null}),M(),c=p[r],c?c.p(n,v):(c=p[r]=f[r](n),c.c()),m(c,1),c.m(i,null)),(!_||v[0]&2097152)&&V(i,"height","calc(100% - "+(n[21].caption?"80px":"60px")+")"),n[21]?.caption?w?w.p(n,v):(w=qe(n),w.c(),w.m(e,s)):w&&(w.d(1),w=null),v[0]&303106){$=te(n[15]);let y;for(y=0;y<$.length;y+=1){const X=de(n,$,y);h[y]?(h[y].p(X,v),m(h[y],1)):(h[y]=ze(X),h[y].c(),m(h[y],1),h[y].m(b,null))}for(R(),y=$.length;y<h.length;y+=1)Z(y);M()}(!_||v[0]&8192)&&D(e,"minimal",n[13]==="minimal")},i(k){if(!_){m(l.$$.fragment,k),m(c);for(let v=0;v<$.length;v+=1)m(h[v]);_=!0}},o(k){d(l.$$.fragment,k),d(c),h=h.filter(Boolean);for(let v=0;v<h.length;v+=1)d(h[v]);_=!1},d(k){k&&T(e),E(l),p[r].d(),w&&w.d(),Te(h,k),n[39](null),g=!1,ft(o)}}}function ke(n){let e,l;return e=new le({props:{Icon:mt,label:n[11]("common.download")}}),e.$on("click",n[32]),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p(t,i){const r={};i[0]&2048&&(r.label=t[11]("common.download")),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function ve(n){let e,l;return e=new le({props:{Icon:n[16]?oe:me,label:n[16]?"Exit full screen":"View in full screen"}}),e.$on("click",n[25]),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p(t,i){const r={};i[0]&65536&&(r.Icon=t[16]?oe:me),i[0]&65536&&(r.label=t[16]?"Exit full screen":"View in full screen"),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function $e(n){let e,l;return e=new le({props:{Icon:oe,label:"Exit full screen"}}),e.$on("click",n[25]),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p:Re,i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function je(n){let e,l,t;return l=new _t({props:{i18n:n[11],value:n[15],formatter:dt}}),l.$on("share",n[33]),l.$on("error",n[34]),{c(){e=U("div"),z(l.$$.fragment),q(e,"class","icon-button svelte-17j4qub")},m(i,r){B(i,e,r),I(l,e,null),t=!0},p(i,r){const c={};r[0]&2048&&(c.i18n=i[11]),r[0]&32768&&(c.value=i[15]),l.$set(c)},i(i){t||(m(l.$$.fragment,i),t=!0)},o(i){d(l.$$.fragment,i),t=!1},d(i){i&&T(e),E(l)}}}function ye(n){let e,l;return e=new le({props:{Icon:ut,label:"Close"}}),e.$on("click",n[35]),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p:Re,i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function kt(n){let e,l,t,i,r,c,a=n[10]&&ke(n),s=n[14]&&!n[16]&&ve(n),b=n[14]&&n[16]&&$e(n),_=n[9]&&je(n),g=!n[16]&&ye(n);return{c(){a&&a.c(),e=A(),s&&s.c(),l=A(),b&&b.c(),t=A(),_&&_.c(),i=A(),g&&g.c(),r=Be()},m(o,f){a&&a.m(o,f),B(o,e,f),s&&s.m(o,f),B(o,l,f),b&&b.m(o,f),B(o,t,f),_&&_.m(o,f),B(o,i,f),g&&g.m(o,f),B(o,r,f),c=!0},p(o,f){o[10]?a?(a.p(o,f),f[0]&1024&&m(a,1)):(a=ke(o),a.c(),m(a,1),a.m(e.parentNode,e)):a&&(R(),d(a,1,1,()=>{a=null}),M()),o[14]&&!o[16]?s?(s.p(o,f),f[0]&81920&&m(s,1)):(s=ve(o),s.c(),m(s,1),s.m(l.parentNode,l)):s&&(R(),d(s,1,1,()=>{s=null}),M()),o[14]&&o[16]?b?(b.p(o,f),f[0]&81920&&m(b,1)):(b=$e(o),b.c(),m(b,1),b.m(t.parentNode,t)):b&&(R(),d(b,1,1,()=>{b=null}),M()),o[9]?_?(_.p(o,f),f[0]&512&&m(_,1)):(_=je(o),_.c(),m(_,1),_.m(i.parentNode,i)):_&&(R(),d(_,1,1,()=>{_=null}),M()),o[16]?g&&(R(),d(g,1,1,()=>{g=null}),M()):g?(g.p(o,f),f[0]&65536&&m(g,1)):(g=ye(o),g.c(),m(g,1),g.m(r.parentNode,r))},i(o){c||(m(a),m(s),m(b),m(_),m(g),c=!0)},o(o){d(a),d(s),d(b),d(_),d(g),c=!1},d(o){o&&(T(e),T(l),T(t),T(i),T(r)),a&&a.d(o),s&&s.d(o),b&&b.d(o),_&&_.d(o),g&&g.d(o)}}}function vt(n){let e,l;return e=new fe({props:{src:n[21].video.url,"data-testid":"detailed-video",alt:n[21].caption||"",loading:"lazy",loop:!1,is_stream:!1,muted:!1,controls:!0}}),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p(t,i){const r={};i[0]&2097152&&(r.src=t[21].video.url),i[0]&2097152&&(r.alt=t[21].caption||""),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function $t(n){let e,l;return e=new se({props:{"data-testid":"detailed-image",src:n[21].image.url,alt:n[21].caption||"",title:n[21].caption||null,class:n[21].caption&&"with-caption",loading:"lazy"}}),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p(t,i){const r={};i[0]&2097152&&(r.src=t[21].image.url),i[0]&2097152&&(r.alt=t[21].caption||""),i[0]&2097152&&(r.title=t[21].caption||null),i[0]&2097152&&(r.class=t[21].caption&&"with-caption"),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function qe(n){let e,l=n[21].caption+"",t;return{c(){e=U("caption"),t=Ae(l),q(e,"class","caption svelte-17j4qub")},m(i,r){B(i,e,r),N(e,t)},p(i,r){r[0]&2097152&&l!==(l=i[21].caption+"")&&De(t,l)},d(i){i&&T(e)}}}function jt(n){let e,l,t,i;return e=new Ne({}),t=new fe({props:{src:n[50].video.url,title:n[50].caption||null,is_stream:!1,"data-testid":"thumbnail "+(n[49]+1),alt:"",loading:"lazy",loop:!1}}),{c(){z(e.$$.fragment),l=A(),z(t.$$.fragment)},m(r,c){I(e,r,c),B(r,l,c),I(t,r,c),i=!0},p(r,c){const a={};c[0]&32768&&(a.src=r[50].video.url),c[0]&32768&&(a.title=r[50].caption||null),t.$set(a)},i(r){i||(m(e.$$.fragment,r),m(t.$$.fragment,r),i=!0)},o(r){d(e.$$.fragment,r),d(t.$$.fragment,r),i=!1},d(r){r&&T(l),E(e,r),E(t,r)}}}function yt(n){let e,l;return e=new se({props:{src:n[50].image.url,title:n[50].caption||null,"data-testid":"thumbnail "+(n[49]+1),alt:"",loading:"lazy"}}),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p(t,i){const r={};i[0]&32768&&(r.src=t[50].image.url),i[0]&32768&&(r.title=t[50].caption||null),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function ze(n){let e,l,t,i,r,c=n[49],a,s,b;const _=[yt,jt],g=[];function o(w,$){return"image"in w[50]?0:1}l=o(n),t=g[l]=_[l](n);const f=()=>n[37](e,c),p=()=>n[37](null,c);function j(){return n[38](n[49])}return{c(){e=U("button"),t.c(),i=A(),q(e,"class","thumbnail-item thumbnail-small svelte-17j4qub"),q(e,"aria-label",r="Thumbnail "+(n[49]+1)+" of "+n[15].length),D(e,"selected",n[1]===n[49]&&n[13]!=="minimal")},m(w,$){B(w,e,$),g[l].m(e,null),N(e,i),f(),a=!0,s||(b=Y(e,"click",j),s=!0)},p(w,$){n=w;let h=l;l=o(n),l===h?g[l].p(n,$):(R(),d(g[h],1,1,()=>{g[h]=null}),M(),t=g[l],t?t.p(n,$):(t=g[l]=_[l](n),t.c()),m(t,1),t.m(e,i)),(!a||$[0]&32768&&r!==(r="Thumbnail "+(n[49]+1)+" of "+n[15].length))&&q(e,"aria-label",r),c!==n[49]&&(p(),c=n[49],f()),(!a||$[0]&8194)&&D(e,"selected",n[1]===n[49]&&n[13]!=="minimal")},i(w){a||(m(t),a=!0)},o(w){d(t),a=!1},d(w){w&&T(e),g[l].d(),p(),s=!1,b()}}}function Ie(n){let e,l,t;return l=new gt({props:{i18n:n[11]}}),l.$on("clear",n[40]),{c(){e=U("div"),z(l.$$.fragment),q(e,"class","icon-button svelte-17j4qub")},m(i,r){B(i,e,r),I(l,e,null),t=!0},p(i,r){const c={};r[0]&2048&&(c.i18n=i[11]),l.$set(c)},i(i){t||(m(l.$$.fragment,i),t=!0)},o(i){d(l.$$.fragment,i),t=!1},d(i){i&&T(e),E(l)}}}function qt(n){let e,l,t,i;return e=new Ne({}),t=new fe({props:{src:n[47].video.url,title:n[47].caption||null,is_stream:!1,"data-testid":"thumbnail "+(n[49]+1),alt:"",loading:"lazy",loop:!1}}),{c(){z(e.$$.fragment),l=A(),z(t.$$.fragment)},m(r,c){I(e,r,c),B(r,l,c),I(t,r,c),i=!0},p(r,c){const a={};c[0]&32768&&(a.src=r[47].video.url),c[0]&32768&&(a.title=r[47].caption||null),t.$set(a)},i(r){i||(m(e.$$.fragment,r),m(t.$$.fragment,r),i=!0)},o(r){d(e.$$.fragment,r),d(t.$$.fragment,r),i=!1},d(r){r&&T(l),E(e,r),E(t,r)}}}function zt(n){let e,l;return e=new se({props:{alt:n[47].caption||"",src:typeof n[47].image=="string"?n[47].image:n[47].image.url,loading:"lazy"}}),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},p(t,i){const r={};i[0]&32768&&(r.alt=t[47].caption||""),i[0]&32768&&(r.src=typeof t[47].image=="string"?t[47].image:t[47].image.url),e.$set(r)},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function Ee(n){let e,l=n[47].caption+"",t;return{c(){e=U("div"),t=Ae(l),q(e,"class","caption-label svelte-17j4qub")},m(i,r){B(i,e,r),N(e,t)},p(i,r){r[0]&32768&&l!==(l=i[47].caption+"")&&De(t,l)},d(i){i&&T(e)}}}function Le(n){let e,l,t,i,r,c,a,s,b;const _=[zt,qt],g=[];function o(j,w){return"image"in j[47]?0:1}l=o(n),t=g[l]=_[l](n);let f=n[47].caption&&Ee(n);function p(){return n[41](n[49])}return{c(){e=U("button"),t.c(),i=A(),f&&f.c(),r=A(),q(e,"class","thumbnail-item thumbnail-lg svelte-17j4qub"),q(e,"aria-label",c="Thumbnail "+(n[49]+1)+" of "+n[15].length),D(e,"selected",n[1]===n[49])},m(j,w){B(j,e,w),g[l].m(e,null),N(e,i),f&&f.m(e,null),N(e,r),a=!0,s||(b=Y(e,"click",p),s=!0)},p(j,w){n=j;let $=l;l=o(n),l===$?g[l].p(n,w):(R(),d(g[$],1,1,()=>{g[$]=null}),M(),t=g[l],t?t.p(n,w):(t=g[l]=_[l](n),t.c()),m(t,1),t.m(e,i)),n[47].caption?f?f.p(n,w):(f=Ee(n),f.c(),f.m(e,r)):f&&(f.d(1),f=null),(!a||w[0]&32768&&c!==(c="Thumbnail "+(n[49]+1)+" of "+n[15].length))&&q(e,"aria-label",c),(!a||w[0]&2)&&D(e,"selected",n[1]===n[49])},i(j){a||(m(t),a=!0)},o(j){d(t),a=!1},d(j){j&&T(e),g[l].d(),f&&f.d(),s=!1,b()}}}function It(n){let e,l;return e=new Me({}),{c(){z(e.$$.fragment)},m(t,i){I(e,t,i),l=!0},i(t){l||(m(e.$$.fragment,t),l=!0)},o(t){d(e.$$.fragment,t),l=!1},d(t){E(e,t)}}}function Et(n){let e,l,t,i,r,c,a;lt(n[31]);let s=n[2]&&pe(n);const b=[wt,pt],_=[];function g(o,f){return o[0]==null||o[15]==null||o[15].length===0?0:1}return l=g(n),t=_[l]=b[l](n),{c(){s&&s.c(),e=A(),t.c(),i=Be()},m(o,f){s&&s.m(o,f),B(o,e,f),_[l].m(o,f),B(o,i,f),r=!0,c||(a=Y(Ue,"resize",n[31]),c=!0)},p(o,f){o[2]?s?(s.p(o,f),f[0]&4&&m(s,1)):(s=pe(o),s.c(),m(s,1),s.m(e.parentNode,e)):s&&(R(),d(s,1,1,()=>{s=null}),M());let p=l;l=g(o),l===p?_[l].p(o,f):(R(),d(_[p],1,1,()=>{_[p]=null}),M(),t=_[l],t?t.p(o,f):(t=_[l]=b[l](o),t.c()),m(t,1),t.m(i.parentNode,i))},i(o){r||(m(s),m(t),r=!0)},o(o){d(s),d(t),r=!1},d(o){o&&(T(e),T(i)),s&&s.d(o),_[l].d(o),c=!1,a()}}}function Lt(n,e,l){let t,i,r,{show_label:c=!0}=e,{label:a}=e,{value:s=null}=e,{columns:b=[2]}=e,{rows:_=void 0}=e,{height:g="auto"}=e,{preview:o}=e,{allow_preview:f=!0}=e,{object_fit:p="cover"}=e,{show_share_button:j=!1}=e,{show_download_button:w=!1}=e,{i18n:$}=e,{selected_index:h=null}=e,{interactive:Z}=e,{_fetch:k}=e,{mode:v="normal"}=e,{show_fullscreen_button:O=!0}=e,G=!1,y;const X=nt();let J=!0,C=null,ne=s;h==null&&o&&s?.length&&(h=0);let ie=h;function ue(u){const S=u.target,F=u.offsetX,W=S.offsetWidth/2;F<W?l(1,h=t):l(1,h=i)}function Oe(u){switch(u.code){case"Escape":u.preventDefault(),l(1,h=null);break;case"ArrowLeft":u.preventDefault(),l(1,h=t);break;case"ArrowRight":u.preventDefault(),l(1,h=i);break}}let H=[],P;async function Se(u){if(typeof u!="number"||(await ot(),H[u]===void 0))return;H[u]?.focus();const{left:S,width:F}=P.getBoundingClientRect(),{left:x,width:W}=H[u].getBoundingClientRect(),K=x-S+W/2-F/2+P.scrollLeft;P&&typeof P.scrollTo=="function"&&P.scrollTo({left:K<0?0:K,behavior:"smooth"})}let ae=0;async function ce(u,S){let F;try{F=await k(u)}catch(K){if(K instanceof TypeError){window.open(u,"_blank","noreferrer");return}throw K}const x=await F.blob(),W=URL.createObjectURL(x),ee=document.createElement("a");ee.href=W,ee.download=S,ee.click(),URL.revokeObjectURL(W)}it(()=>{document.addEventListener("fullscreenchange",()=>{l(16,G=!!document.fullscreenElement)})});const Ve=async()=>{G?await document.exitFullscreen():await y.requestFullscreen()};function Ce(){l(20,ae=Ue.innerHeight)}const Pe=()=>{const u="image"in r?r?.image:r?.video;if(u==null)return;const{url:S,orig_name:F}=u;S&&ce(S,F??"image")};function Fe(u){_e.call(this,n,u)}function Ge(u){_e.call(this,n,u)}const He=()=>l(1,h=null),We=u=>ue(u);function Xe(u,S){re[u?"unshift":"push"](()=>{H[S]=u,l(18,H)})}const Je=u=>l(1,h=u);function Ke(u){re[u?"unshift":"push"](()=>{P=u,l(19,P)})}const Qe=()=>l(0,s=[]),Ye=u=>l(1,h=u);function Ze(u){re[u?"unshift":"push"](()=>{y=u,l(17,y)})}return n.$$set=u=>{"show_label"in u&&l(2,c=u.show_label),"label"in u&&l(3,a=u.label),"value"in u&&l(0,s=u.value),"columns"in u&&l(4,b=u.columns),"rows"in u&&l(5,_=u.rows),"height"in u&&l(6,g=u.height),"preview"in u&&l(26,o=u.preview),"allow_preview"in u&&l(7,f=u.allow_preview),"object_fit"in u&&l(8,p=u.object_fit),"show_share_button"in u&&l(9,j=u.show_share_button),"show_download_button"in u&&l(10,w=u.show_download_button),"i18n"in u&&l(11,$=u.i18n),"selected_index"in u&&l(1,h=u.selected_index),"interactive"in u&&l(12,Z=u.interactive),"_fetch"in u&&l(27,k=u._fetch),"mode"in u&&l(13,v=u.mode),"show_fullscreen_button"in u&&l(14,O=u.show_fullscreen_button)},n.$$.update=()=>{n.$$.dirty[0]&268435457&&l(28,J=s==null||s.length===0?!0:J),n.$$.dirty[0]&1&&l(15,C=s==null?null:s.map(u=>"video"in u?{video:u.video,caption:u.caption}:"image"in u?{image:u.image,caption:u.caption}:{})),n.$$.dirty[0]&872415235&&(Q(ne,s)||(J?(l(1,h=o&&s?.length?0:null),l(28,J=!1)):l(1,h=h!=null&&s!=null&&h<s.length?h:null),X("change"),l(29,ne=s))),n.$$.dirty[0]&32770&&(t=((h??0)+(C?.length??0)-1)%(C?.length??0)),n.$$.dirty[0]&32770&&(i=((h??0)+1)%(C?.length??0)),n.$$.dirty[0]&1073774594&&h!==ie&&(l(30,ie=h),h!==null&&X("select",{index:h,value:C?.[h]})),n.$$.dirty[0]&130&&f&&Se(h),n.$$.dirty[0]&32770&&l(21,r=h!=null&&C!=null?C[h]:null)},[s,h,c,a,b,_,g,f,p,j,w,$,Z,v,O,C,G,y,H,P,ae,r,ue,Oe,ce,Ve,o,k,J,ne,ie,Ce,Pe,Fe,Ge,He,We,Xe,Je,Ke,Qe,Ye,Ze]}class Qt extends xe{constructor(e){super(),et(this,e,Lt,Et,tt,{show_label:2,label:3,value:0,columns:4,rows:5,height:6,preview:26,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,i18n:11,selected_index:1,interactive:12,_fetch:27,mode:13,show_fullscreen_button:14},null,[-1,-1])}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),L()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),L()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),L()}get columns(){return this.$$.ctx[4]}set columns(e){this.$$set({columns:e}),L()}get rows(){return this.$$.ctx[5]}set rows(e){this.$$set({rows:e}),L()}get height(){return this.$$.ctx[6]}set height(e){this.$$set({height:e}),L()}get preview(){return this.$$.ctx[26]}set preview(e){this.$$set({preview:e}),L()}get allow_preview(){return this.$$.ctx[7]}set allow_preview(e){this.$$set({allow_preview:e}),L()}get object_fit(){return this.$$.ctx[8]}set object_fit(e){this.$$set({object_fit:e}),L()}get show_share_button(){return this.$$.ctx[9]}set show_share_button(e){this.$$set({show_share_button:e}),L()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),L()}get i18n(){return this.$$.ctx[11]}set i18n(e){this.$$set({i18n:e}),L()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),L()}get interactive(){return this.$$.ctx[12]}set interactive(e){this.$$set({interactive:e}),L()}get _fetch(){return this.$$.ctx[27]}set _fetch(e){this.$$set({_fetch:e}),L()}get mode(){return this.$$.ctx[13]}set mode(e){this.$$set({mode:e}),L()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),L()}}export{Qt as default};
//# sourceMappingURL=Gallery-BWHkv_Ie.js.map
