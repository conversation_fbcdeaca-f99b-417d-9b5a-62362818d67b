import{a as i,i as v,s as p,Q as l,q as a,l as h,v as c,w as o,o as m}from"../lite.js";function w(n){let t,e;return{c(){t=l("svg"),e=l("path"),a(e,"fill","currentColor"),a(e,"d","M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z"),a(t,"xmlns","http://www.w3.org/2000/svg"),a(t,"width","100%"),a(t,"height","100%"),a(t,"viewBox","0 0 32 32")},m(s,r){h(s,t,r),c(t,e)},p:o,i:o,o,d(s){s&&m(t)}}}class g extends i{constructor(t){super(),v(this,t,null,w,p,{})}}export{g as D};
//# sourceMappingURL=Download-CgLP-Xl6.js.map
