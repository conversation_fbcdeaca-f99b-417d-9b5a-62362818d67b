import{a as l,i as v,s as c,Q as n,q as e,l as h,v as p,w as i,o as m}from"../lite.js";function w(r){let t,s;return{c(){t=n("svg"),s=n("path"),e(s,"fill","currentColor"),e(s,"d","M4 2H2v26a2 2 0 0 0 2 2h26v-2H4v-3h22v-8H4v-4h14V5H4Zm20 17v4H4v-4ZM16 7v4H4V7Z"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"xmlns:xlink","http://www.w3.org/1999/xlink"),e(t,"aria-hidden","true"),e(t,"role","img"),e(t,"class","iconify iconify--carbon"),e(t,"width","100%"),e(t,"height","100%"),e(t,"preserveAspectRatio","xMidYMid meet"),e(t,"viewBox","0 0 32 32")},m(a,o){h(a,t,o),p(t,s)},p:i,i,o:i,d(a){a&&m(t)}}}class g extends l{constructor(t){super(),v(this,t,null,w,c,{})}}export{g as L};
//# sourceMappingURL=LineChart-BgrmtJD8.js.map
