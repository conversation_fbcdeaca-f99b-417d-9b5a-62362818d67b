{"version": 3, "file": "Image-DFqHtuJN.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Image.js"], "sourcesContent": ["import { create_ssr_component, compute_rest_props, spread, escape_attribute_value, escape_object } from \"svelte/internal\";\nimport \"svelte\";\nimport { r as resolve_wasm_src } from \"./DownloadLink.js\";\nconst css = {\n  code: \"img.svelte-1pijsyv{object-fit:cover}\",\n  map: '{\"version\":3,\"file\":\"Image.svelte\",\"sources\":[\"Image.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { resolve_wasm_src } from \\\\\"@gradio/wasm/svelte\\\\\";\\\\nexport let src = void 0;\\\\nlet resolved_src;\\\\nlet latest_src;\\\\n$: {\\\\n    resolved_src = src;\\\\n    latest_src = src;\\\\n    const resolving_src = src;\\\\n    resolve_wasm_src(resolving_src).then((s) => {\\\\n        if (latest_src === resolving_src) {\\\\n            resolved_src = s;\\\\n        }\\\\n    });\\\\n}\\\\n<\\/script>\\\\n\\\\n<!-- svelte-ignore a11y-missing-attribute -->\\\\n<img src={resolved_src} {...$$restProps} on:load />\\\\n\\\\n<style>\\\\n\\\\timg {\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAoBC,kBAAI,CACH,UAAU,CAAE,KACb\"}'\n};\nconst Image = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let $$restProps = compute_rest_props($$props, [\"src\"]);\n  let { src = void 0 } = $$props;\n  let resolved_src;\n  let latest_src;\n  if ($$props.src === void 0 && $$bindings.src && src !== void 0)\n    $$bindings.src(src);\n  $$result.css.add(css);\n  {\n    {\n      resolved_src = src;\n      latest_src = src;\n      const resolving_src = src;\n      resolve_wasm_src(resolving_src).then((s) => {\n        if (latest_src === resolving_src) {\n          resolved_src = s;\n        }\n      });\n    }\n  }\n  return ` <img${spread(\n    [\n      {\n        src: escape_attribute_value(resolved_src)\n      },\n      escape_object($$restProps)\n    ],\n    { classes: \"svelte-1pijsyv\" }\n  )}>`;\n});\nconst Image$1 = Image;\nexport {\n  Image$1 as I\n};\n"], "names": [], "mappings": ";;;AAGA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,sCAAsC;AAC9C,EAAE,GAAG,EAAE,itBAAitB;AACxtB,CAAC,CAAC;AACF,MAAM,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,WAAW,GAAG,kBAAkB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACzD,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE;AACF,IAAI;AACJ,MAAM,YAAY,GAAG,GAAG,CAAC;AACzB,MAAM,UAAU,GAAG,GAAG,CAAC;AACvB,MAAM,MAAM,aAAa,GAAG,GAAG,CAAC;AAChC,MAAM,gBAAgB,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;AAClD,QAAQ,IAAI,UAAU,KAAK,aAAa,EAAE;AAC1C,UAAU,YAAY,GAAG,CAAC,CAAC;AAC3B,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM;AACvB,IAAI;AACJ,MAAM;AACN,QAAQ,GAAG,EAAE,sBAAsB,CAAC,YAAY,CAAC;AACjD,OAAO;AACP,MAAM,aAAa,CAAC,WAAW,CAAC;AAChC,KAAK;AACL,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAE;AACjC,GAAG,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AACE,MAAC,OAAO,GAAG;;;;"}