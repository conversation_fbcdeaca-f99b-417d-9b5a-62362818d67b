import{a as Y,i as Z,s as x,f as m,ao as le,p as G,q,$ as te,l as U,v as A,y as S,b4 as pe,b9 as we,z as J,t as w,b as k,o as C,A as fe,k as j,x as L,r as ne,F as V,n as X,P as ke,w as ie,c as I,m as E,d as y,bb as ve,E as _e,H as N,G as $e,I as K,a5 as O,a6 as R,e as ze,u as Fe,h as Ae,j as qe,W as se,ax as Be}from"../lite.js";import{B as ce}from"./BlockLabel-B0HN-MOU.js";import{E as Ie}from"./Empty-C76eC2zW.js";import{F as ee}from"./File-Kbo-bXuF.js";import{U as he}from"./Upload-TGDabKXH.js";import{U as Ee}from"./Upload-D_TzP4YC.js";import{I as ye}from"./IconButtonWrapper-Ck50MZwX.js";import{D as Pe}from"./DownloadLink-B8hI46-W.js";const ae=t=>{let e=["B","KB","MB","GB","PB"],n=0;for(;t>1024;)t/=1024,n++;let l=e[n];return t.toFixed(1)+"&nbsp;"+l};function oe(t,e,n){const l=t.slice();return l[14]=e[n],l[16]=n,l}function Ue(t){let e=t[2]("file.uploading")+"",n;return{c(){n=j(e)},m(l,i){U(l,n,i)},p(l,i){i&4&&e!==(e=l[2]("file.uploading")+"")&&X(n,e)},i:ie,o:ie,d(l){l&&C(n)}}}function Ce(t){let e,n;function l(){return t[9](t[14])}return e=new Pe({props:{href:t[14].url,download:t[7]&&window.__is_colab__?null:t[14].orig_name,$$slots:{default:[De]},$$scope:{ctx:t}}}),e.$on("click",l),{c(){I(e.$$.fragment)},m(i,s){E(e,i,s),n=!0},p(i,s){t=i;const r={};s&8&&(r.href=t[14].url),s&8&&(r.download=t[7]&&window.__is_colab__?null:t[14].orig_name),s&131080&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(i){n||(w(e.$$.fragment,i),n=!0)},o(i){k(e.$$.fragment,i),n=!1},d(i){y(e,i)}}}function De(t){let e,n=(t[14].size!=null?ae(t[14].size):"(size unknown)")+"",l;return{c(){e=new ve(!1),l=j(" ⇣"),e.a=l},m(i,s){e.m(n,i,s),U(i,l,s)},p(i,s){s&8&&n!==(n=(i[14].size!=null?ae(i[14].size):"(size unknown)")+"")&&e.p(n)},d(i){i&&(e.d(),C(l))}}}function ue(t){let e,n,l,i;function s(){return t[10](t[16])}function r(...c){return t[11](t[16],...c)}return{c(){e=G("td"),n=G("button"),n.textContent="×",q(n,"class","label-clear-button svelte-18wv37q"),q(n,"aria-label","Remove this file"),q(e,"class","svelte-18wv37q")},m(c,o){U(c,e,o),A(e,n),l||(i=[V(n,"click",s),V(n,"keydown",r)],l=!0)},p(c,o){t=c},d(c){c&&C(e),l=!1,ke(i)}}}function re(t,e){let n,l,i,s=e[14].filename_stem+"",r,c,o,u=e[14].filename_ext+"",f,a,g,p,d,v,M,h,b,z,P;const H=[Ce,Ue],D=[];function W(F,B){return F[14].url?0:1}d=W(e),v=D[d]=H[d](e);let $=e[3].length>1&&ue(e);function Q(...F){return e[12](e[16],...F)}return{key:t,first:null,c(){n=G("tr"),l=G("td"),i=G("span"),r=j(s),c=L(),o=G("span"),f=j(u),g=L(),p=G("td"),v.c(),M=L(),$&&$.c(),h=L(),q(i,"class","stem svelte-18wv37q"),q(o,"class","ext svelte-18wv37q"),q(l,"class","filename svelte-18wv37q"),q(l,"aria-label",a=e[14].orig_name),q(p,"class","download svelte-18wv37q"),q(n,"class","file svelte-18wv37q"),ne(n,"selectable",e[0]),this.first=n},m(F,B){U(F,n,B),A(n,l),A(l,i),A(i,r),A(l,c),A(l,o),A(o,f),A(n,g),A(n,p),D[d].m(p,null),A(n,M),$&&$.m(n,null),A(n,h),b=!0,z||(P=V(n,"click",Q),z=!0)},p(F,B){e=F,(!b||B&8)&&s!==(s=e[14].filename_stem+"")&&X(r,s),(!b||B&8)&&u!==(u=e[14].filename_ext+"")&&X(f,u),(!b||B&8&&a!==(a=e[14].orig_name))&&q(l,"aria-label",a);let T=d;d=W(e),d===T?D[d].p(e,B):(S(),k(D[T],1,1,()=>{D[T]=null}),J(),v=D[d],v?v.p(e,B):(v=D[d]=H[d](e),v.c()),w(v,1),v.m(p,null)),e[3].length>1?$?$.p(e,B):($=ue(e),$.c(),$.m(n,h)):$&&($.d(1),$=null),(!b||B&1)&&ne(n,"selectable",e[0])},i(F){b||(w(v),b=!0)},o(F){k(v),b=!1},d(F){F&&C(n),D[d].d(),$&&$.d(),z=!1,P()}}}function Ge(t){let e,n,l,i=[],s=new Map,r,c=le(t[3]);const o=u=>u[14];for(let u=0;u<c.length;u+=1){let f=oe(t,c,u),a=o(f);s.set(a,i[u]=re(a,f))}return{c(){e=G("div"),n=G("table"),l=G("tbody");for(let u=0;u<i.length;u+=1)i[u].c();q(l,"class","svelte-18wv37q"),q(n,"class","file-preview svelte-18wv37q"),q(e,"class","file-preview-holder svelte-18wv37q"),te(e,"max-height",typeof t[1]===void 0?"auto":t[1]+"px")},m(u,f){U(u,e,f),A(e,n),A(n,l);for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(l,null);r=!0},p(u,[f]){f&253&&(c=le(u[3]),S(),i=pe(i,f,o,1,u,c,s,l,we,re,null,oe),J()),(!r||f&2)&&te(e,"max-height",typeof u[1]===void 0?"auto":u[1]+"px")},i(u){if(!r){for(let f=0;f<c.length;f+=1)w(i[f]);r=!0}},o(u){for(let f=0;f<i.length;f+=1)k(i[f]);r=!1},d(u){u&&C(e);for(let f=0;f<i.length;f+=1)i[f].d()}}}function He(t){const e=t.lastIndexOf(".");return e===-1?[t,""]:[t.slice(0,e),t.slice(e)]}function Le(t,e,n){let l;const i=fe();let{value:s}=e,{selectable:r=!1}=e,{height:c=void 0}=e,{i18n:o}=e;function u(h,b){const z=h.currentTarget;(h.target===z||z&&z.firstElementChild&&h.composedPath().includes(z.firstElementChild))&&i("select",{value:l[b].orig_name,index:b})}function f(h){const b=l.splice(h,1);n(3,l=[...l]),n(8,s=l),i("delete",b[0]),i("change",l)}function a(h){i("download",h)}const g=typeof window<"u",p=h=>a(h),d=h=>{f(h)},v=(h,b)=>{b.key==="Enter"&&f(h)},M=(h,b)=>{u(b,h)};return t.$$set=h=>{"value"in h&&n(8,s=h.value),"selectable"in h&&n(0,r=h.selectable),"height"in h&&n(1,c=h.height),"i18n"in h&&n(2,o=h.i18n)},t.$$.update=()=>{t.$$.dirty&256&&n(3,l=(Array.isArray(s)?s:[s]).map(h=>{const[b,z]=He(h.orig_name??"");return{...h,filename_stem:b,filename_ext:z}}))},[r,c,o,l,u,f,a,g,s,p,d,v,M]}class Me extends Y{constructor(e){super(),Z(this,e,Le,Ge,x,{value:8,selectable:0,height:1,i18n:2})}get value(){return this.$$.ctx[8]}set value(e){this.$$set({value:e}),m()}get selectable(){return this.$$.ctx[0]}set selectable(e){this.$$set({selectable:e}),m()}get height(){return this.$$.ctx[1]}set height(e){this.$$set({height:e}),m()}get i18n(){return this.$$.ctx[2]}set i18n(e){this.$$set({i18n:e}),m()}}const ge=Me;function Ne(t){let e,n;return e=new Ie({props:{unpadded_box:!0,size:"large",$$slots:{default:[We]},$$scope:{ctx:t}}}),{c(){I(e.$$.fragment)},m(l,i){E(e,l,i),n=!0},p(l,i){const s={};i&256&&(s.$$scope={dirty:i,ctx:l}),e.$set(s)},i(l){n||(w(e.$$.fragment,l),n=!0)},o(l){k(e.$$.fragment,l),n=!1},d(l){y(e,l)}}}function Te(t){let e,n;return e=new ge({props:{i18n:t[5],selectable:t[3],value:t[0],height:t[4]}}),e.$on("select",t[6]),e.$on("download",t[7]),{c(){I(e.$$.fragment)},m(l,i){E(e,l,i),n=!0},p(l,i){const s={};i&32&&(s.i18n=l[5]),i&8&&(s.selectable=l[3]),i&1&&(s.value=l[0]),i&16&&(s.height=l[4]),e.$set(s)},i(l){n||(w(e.$$.fragment,l),n=!0)},o(l){k(e.$$.fragment,l),n=!1},d(l){y(e,l)}}}function We(t){let e,n;return e=new ee({}),{c(){I(e.$$.fragment)},m(l,i){E(e,l,i),n=!0},i(l){n||(w(e.$$.fragment,l),n=!0)},o(l){k(e.$$.fragment,l),n=!1},d(l){y(e,l)}}}function je(t){let e,n,l,i,s,r,c;e=new ce({props:{show_label:t[2],float:t[0]===null,Icon:ee,label:t[1]||"File"}});const o=[Te,Ne],u=[];function f(a,g){return g&1&&(l=null),l==null&&(l=!!(a[0]&&(!Array.isArray(a[0])||a[0].length>0))),l?0:1}return i=f(t,-1),s=u[i]=o[i](t),{c(){I(e.$$.fragment),n=L(),s.c(),r=_e()},m(a,g){E(e,a,g),U(a,n,g),u[i].m(a,g),U(a,r,g),c=!0},p(a,[g]){const p={};g&4&&(p.show_label=a[2]),g&1&&(p.float=a[0]===null),g&2&&(p.label=a[1]||"File"),e.$set(p);let d=i;i=f(a,g),i===d?u[i].p(a,g):(S(),k(u[d],1,1,()=>{u[d]=null}),J(),s=u[i],s?s.p(a,g):(s=u[i]=o[i](a),s.c()),w(s,1),s.m(r.parentNode,r))},i(a){c||(w(e.$$.fragment,a),w(s),c=!0)},o(a){k(e.$$.fragment,a),k(s),c=!1},d(a){a&&(C(n),C(r)),y(e,a),u[i].d(a)}}}function Ke(t,e,n){let{value:l=null}=e,{label:i}=e,{show_label:s=!0}=e,{selectable:r=!1}=e,{height:c=void 0}=e,{i18n:o}=e;function u(a){N.call(this,t,a)}function f(a){N.call(this,t,a)}return t.$$set=a=>{"value"in a&&n(0,l=a.value),"label"in a&&n(1,i=a.label),"show_label"in a&&n(2,s=a.show_label),"selectable"in a&&n(3,r=a.selectable),"height"in a&&n(4,c=a.height),"i18n"in a&&n(5,o=a.i18n)},[l,i,s,r,c,o,u,f]}class Oe extends Y{constructor(e){super(),Z(this,e,Ke,je,x,{value:0,label:1,show_label:2,selectable:3,height:4,i18n:5})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),m()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),m()}get selectable(){return this.$$.ctx[3]}set selectable(e){this.$$set({selectable:e}),m()}get height(){return this.$$.ctx[4]}set height(e){this.$$set({height:e}),m()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),m()}}const ol=Oe;function Re(t){let e,n,l,i;function s(o){t[25](o)}function r(o){t[26](o)}let c={filetype:t[5],file_count:t[4],max_file_size:t[10],root:t[7],stream_handler:t[12],upload:t[11],$$slots:{default:[Je]},$$scope:{ctx:t}};return t[13]!==void 0&&(c.dragging=t[13]),t[1]!==void 0&&(c.uploading=t[1]),e=new he({props:c}),K.push(()=>O(e,"dragging",s)),K.push(()=>O(e,"uploading",r)),e.$on("load",t[14]),e.$on("error",t[27]),{c(){I(e.$$.fragment)},m(o,u){E(e,o,u),i=!0},p(o,u){const f={};u&32&&(f.filetype=o[5]),u&16&&(f.file_count=o[4]),u&1024&&(f.max_file_size=o[10]),u&128&&(f.root=o[7]),u&4096&&(f.stream_handler=o[12]),u&2048&&(f.upload=o[11]),u&268435456&&(f.$$scope={dirty:u,ctx:o}),!n&&u&8192&&(n=!0,f.dragging=o[13],R(()=>n=!1)),!l&&u&2&&(l=!0,f.uploading=o[1],R(()=>l=!1)),e.$set(f)},i(o){i||(w(e.$$.fragment,o),i=!0)},o(o){k(e.$$.fragment,o),i=!1},d(o){y(e,o)}}}function Se(t){let e,n,l,i;return e=new ye({props:{$$slots:{default:[Ve]},$$scope:{ctx:t}}}),l=new ge({props:{i18n:t[9],selectable:t[6],value:t[0],height:t[8]}}),l.$on("select",t[22]),l.$on("change",t[23]),l.$on("delete",t[24]),{c(){I(e.$$.fragment),n=L(),I(l.$$.fragment)},m(s,r){E(e,s,r),U(s,n,r),E(l,s,r),i=!0},p(s,r){const c={};r&268451506&&(c.$$scope={dirty:r,ctx:s}),e.$set(c);const o={};r&512&&(o.i18n=s[9]),r&64&&(o.selectable=s[6]),r&1&&(o.value=s[0]),r&256&&(o.height=s[8]),l.$set(o)},i(s){i||(w(e.$$.fragment,s),w(l.$$.fragment,s),i=!0)},o(s){k(e.$$.fragment,s),k(l.$$.fragment,s),i=!1},d(s){s&&C(n),y(e,s),y(l,s)}}}function Je(t){let e;const n=t[17].default,l=ze(n,t,t[28],null);return{c(){l&&l.c()},m(i,s){l&&l.m(i,s),e=!0},p(i,s){l&&l.p&&(!e||s&268435456)&&Fe(l,n,i,i[28],e?qe(n,i[28],s,null):Ae(i[28]),null)},i(i){e||(w(l,i),e=!0)},o(i){k(l,i),e=!1},d(i){l&&l.d(i)}}}function Qe(t){let e,n,l,i;function s(o){t[18](o)}function r(o){t[19](o)}let c={icon_upload:!0,filetype:t[5],file_count:t[4],max_file_size:t[10],root:t[7],stream_handler:t[12],upload:t[11]};return t[13]!==void 0&&(c.dragging=t[13]),t[1]!==void 0&&(c.uploading=t[1]),e=new he({props:c}),K.push(()=>O(e,"dragging",s)),K.push(()=>O(e,"uploading",r)),e.$on("load",t[14]),e.$on("error",t[20]),{c(){I(e.$$.fragment)},m(o,u){E(e,o,u),i=!0},p(o,u){const f={};u&32&&(f.filetype=o[5]),u&16&&(f.file_count=o[4]),u&1024&&(f.max_file_size=o[10]),u&128&&(f.root=o[7]),u&4096&&(f.stream_handler=o[12]),u&2048&&(f.upload=o[11]),!n&&u&8192&&(n=!0,f.dragging=o[13],R(()=>n=!1)),!l&&u&2&&(l=!0,f.uploading=o[1],R(()=>l=!1)),e.$set(f)},i(o){i||(w(e.$$.fragment,o),i=!0)},o(o){k(e.$$.fragment,o),i=!1},d(o){y(e,o)}}}function Ve(t){let e,n,l,i;return e=new se({props:{Icon:Ee,label:t[9]("common.upload"),$$slots:{default:[Qe]},$$scope:{ctx:t}}}),l=new se({props:{Icon:Be,label:t[9]("common.clear")}}),l.$on("click",t[21]),{c(){I(e.$$.fragment),n=L(),I(l.$$.fragment)},m(s,r){E(e,s,r),U(s,n,r),E(l,s,r),i=!0},p(s,r){const c={};r&512&&(c.label=s[9]("common.upload")),r&268450994&&(c.$$scope={dirty:r,ctx:s}),e.$set(c);const o={};r&512&&(o.label=s[9]("common.clear")),l.$set(o)},i(s){i||(w(e.$$.fragment,s),w(l.$$.fragment,s),i=!0)},o(s){k(e.$$.fragment,s),k(l.$$.fragment,s),i=!1},d(s){s&&C(n),y(e,s),y(l,s)}}}function Xe(t){let e,n,l,i,s,r,c;e=new ce({props:{show_label:t[3],Icon:ee,float:!t[0],label:t[2]||"File"}});const o=[Se,Re],u=[];function f(a,g){return g&1&&(l=null),l==null&&(l=!!(a[0]&&(!Array.isArray(a[0])||a[0].length>0))),l?0:1}return i=f(t,-1),s=u[i]=o[i](t),{c(){I(e.$$.fragment),n=L(),s.c(),r=_e()},m(a,g){E(e,a,g),U(a,n,g),u[i].m(a,g),U(a,r,g),c=!0},p(a,[g]){const p={};g&8&&(p.show_label=a[3]),g&1&&(p.float=!a[0]),g&4&&(p.label=a[2]||"File"),e.$set(p);let d=i;i=f(a,g),i===d?u[i].p(a,g):(S(),k(u[d],1,1,()=>{u[d]=null}),J(),s=u[i],s?s.p(a,g):(s=u[i]=o[i](a),s.c()),w(s,1),s.m(r.parentNode,r))},i(a){c||(w(e.$$.fragment,a),w(s),c=!0)},o(a){k(e.$$.fragment,a),k(s),c=!1},d(a){a&&(C(n),C(r)),y(e,a),u[i].d(a)}}}function Ye(t,e,n){let{$$slots:l={},$$scope:i}=e,{value:s}=e,{label:r}=e,{show_label:c=!0}=e,{file_count:o="single"}=e,{file_types:u=null}=e,{selectable:f=!1}=e,{root:a}=e,{height:g=void 0}=e,{i18n:p}=e,{max_file_size:d=null}=e,{upload:v}=e,{stream_handler:M}=e,{uploading:h=!1}=e;async function b({detail:_}){Array.isArray(s)?n(0,s=[...s,...Array.isArray(_)?_:[_]]):s?n(0,s=[s,...Array.isArray(_)?_:[_]]):n(0,s=_),await $e(),P("change",s),P("upload",_)}function z(){n(0,s=null),P("change",null),P("clear")}const P=fe();let H=!1;function D(_){H=_,n(13,H)}function W(_){h=_,n(1,h)}function $(_){N.call(this,t,_)}const Q=_=>{P("clear"),_.stopPropagation(),z()};function F(_){N.call(this,t,_)}function B(_){N.call(this,t,_)}function T(_){N.call(this,t,_)}function de(_){H=_,n(13,H)}function me(_){h=_,n(1,h)}function be(_){N.call(this,t,_)}return t.$$set=_=>{"value"in _&&n(0,s=_.value),"label"in _&&n(2,r=_.label),"show_label"in _&&n(3,c=_.show_label),"file_count"in _&&n(4,o=_.file_count),"file_types"in _&&n(5,u=_.file_types),"selectable"in _&&n(6,f=_.selectable),"root"in _&&n(7,a=_.root),"height"in _&&n(8,g=_.height),"i18n"in _&&n(9,p=_.i18n),"max_file_size"in _&&n(10,d=_.max_file_size),"upload"in _&&n(11,v=_.upload),"stream_handler"in _&&n(12,M=_.stream_handler),"uploading"in _&&n(1,h=_.uploading),"$$scope"in _&&n(28,i=_.$$scope)},t.$$.update=()=>{t.$$.dirty&8192&&P("drag",H)},[s,h,r,c,o,u,f,a,g,p,d,v,M,H,b,z,P,l,D,W,$,Q,F,B,T,de,me,be,i]}class Ze extends Y{constructor(e){super(),Z(this,e,Ye,Xe,x,{value:0,label:2,show_label:3,file_count:4,file_types:5,selectable:6,root:7,height:8,i18n:9,max_file_size:10,upload:11,stream_handler:12,uploading:1})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),m()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),m()}get file_count(){return this.$$.ctx[4]}set file_count(e){this.$$set({file_count:e}),m()}get file_types(){return this.$$.ctx[5]}set file_types(e){this.$$set({file_types:e}),m()}get selectable(){return this.$$.ctx[6]}set selectable(e){this.$$set({selectable:e}),m()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),m()}get height(){return this.$$.ctx[8]}set height(e){this.$$set({height:e}),m()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),m()}get max_file_size(){return this.$$.ctx[10]}set max_file_size(e){this.$$set({max_file_size:e}),m()}get upload(){return this.$$.ctx[11]}set upload(e){this.$$set({upload:e}),m()}get stream_handler(){return this.$$.ctx[12]}set stream_handler(e){this.$$set({stream_handler:e}),m()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),m()}}const ul=Ze;export{ul as B,ol as F,ge as a};
//# sourceMappingURL=FileUpload-CRh22YvE.js.map
