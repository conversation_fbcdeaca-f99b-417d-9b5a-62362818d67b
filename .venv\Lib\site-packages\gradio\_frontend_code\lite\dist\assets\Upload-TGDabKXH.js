import{a as ie,i as le,s as ne,f as w,p as W,k as N,x as G,q as b,r as p,l as S,v as A,n as H,w as $,o as j,A as se,a3 as Ue,a8 as Ce,$ as I,E as re,y as ae,b as L,z as oe,t as T,av as De,H as U,I as Ie,e as ue,F as z,aw as C,a1 as D,u as fe,h as de,j as _e,P as Le,c as Oe,m as Ne,d as Se,G as je}from"../lite.js";/* empty css                                             */function ee(t){let e,l,i,s,d=q(t[2])+"",o,u,r,a,y=t[2].orig_name+"",_;return{c(){e=W("div"),l=W("span"),i=W("div"),s=W("progress"),o=N(d),r=G(),a=W("span"),_=N(y),I(s,"visibility","hidden"),I(s,"height","0"),I(s,"width","0"),s.value=u=q(t[2]),b(s,"max","100"),b(s,"class","svelte-1vsfomn"),b(i,"class","progress-bar svelte-1vsfomn"),b(a,"class","file-name svelte-1vsfomn"),b(e,"class","file svelte-1vsfomn")},m(h,c){S(h,e,c),A(e,l),A(l,i),A(i,s),A(s,o),A(e,r),A(e,a),A(a,_)},p(h,c){c&4&&d!==(d=q(h[2])+"")&&H(o,d),c&4&&u!==(u=q(h[2]))&&(s.value=u),c&4&&y!==(y=h[2].orig_name+"")&&H(_,y)},d(h){h&&j(e)}}}function qe(t){let e,l,i,s=t[0].length+"",d,o,u=t[0].length>1?"files":"file",r,a,y,_=t[2]&&ee(t);return{c(){e=W("div"),l=W("span"),i=N("Uploading "),d=N(s),o=G(),r=N(u),a=N("..."),y=G(),_&&_.c(),b(l,"class","uploading svelte-1vsfomn"),b(e,"class","wrap svelte-1vsfomn"),p(e,"progress",t[1])},m(h,c){S(h,e,c),A(e,l),A(l,i),A(l,d),A(l,o),A(l,r),A(l,a),A(e,y),_&&_.m(e,null)},p(h,[c]){c&1&&s!==(s=h[0].length+"")&&H(d,s),c&1&&u!==(u=h[0].length>1?"files":"file")&&H(r,u),h[2]?_?_.p(h,c):(_=ee(h),_.c(),_.m(e,null)):_&&(_.d(1),_=null),c&2&&p(e,"progress",h[1])},i:$,o:$,d(h){h&&j(e),_&&_.d()}}}function q(t){return t.progress*100/(t.size||0)||0}function Me(t){let e=0;return t.forEach(l=>{e+=q(l)}),document.documentElement.style.setProperty("--upload-progress-width",(e/t.length).toFixed(2)+"%"),e/t.length}function Ge(t,e,l){let{upload_id:i}=e,{root:s}=e,{files:d}=e,{stream_handler:o}=e,u,r=!1,a,y,_=d.map(f=>({...f,progress:0}));const h=se();function c(f,g){l(0,_=_.map(F=>(F.orig_name===f&&(F.progress+=g),F)))}return Ue(async()=>{if(u=await o(new URL(`${s}/gradio_api/upload_progress?upload_id=${i}`)),u==null)throw new Error("Event source is not defined");u.onmessage=async function(f){const g=JSON.parse(f.data);r||l(1,r=!0),g.msg==="done"?(u?.close(),h("done")):(l(7,a=g),c(g.orig_name,g.chunk_size))}}),Ce(()=>{(u!=null||u!=null)&&u.close()}),t.$$set=f=>{"upload_id"in f&&l(3,i=f.upload_id),"root"in f&&l(4,s=f.root),"files"in f&&l(5,d=f.files),"stream_handler"in f&&l(6,o=f.stream_handler)},t.$$.update=()=>{t.$$.dirty&1&&Me(_),t.$$.dirty&129&&l(2,y=a||_[0])},[_,r,y,i,s,d,o,a]}class He extends ie{constructor(e){super(),le(this,e,Ge,qe,ne,{upload_id:3,root:4,files:5,stream_handler:6})}get upload_id(){return this.$$.ctx[3]}set upload_id(e){this.$$set({upload_id:e}),w()}get root(){return this.$$.ctx[4]}set root(e){this.$$set({root:e}),w()}get files(){return this.$$.ctx[5]}set files(e){this.$$set({files:e}),w()}get stream_handler(){return this.$$.ctx[6]}set stream_handler(e){this.$$set({stream_handler:e}),w()}}function Je(t){let e,l,i,s,d,o,u,r,a,y,_;const h=t[28].default,c=ue(h,t,t[27],null);return{c(){e=W("button"),c&&c.c(),l=G(),i=W("input"),b(i,"aria-label","file upload"),b(i,"data-testid","file-upload"),b(i,"type","file"),b(i,"accept",s=t[17]||void 0),i.multiple=d=t[6]==="multiple"||void 0,b(i,"webkitdirectory",o=t[6]==="directory"||void 0),b(i,"mozdirectory",u=t[6]==="directory"||void 0),b(i,"class","svelte-1b742ao"),b(e,"tabindex",r=t[9]?-1:0),b(e,"class","svelte-1b742ao"),p(e,"hidden",t[9]),p(e,"center",t[4]),p(e,"boundedheight",t[3]),p(e,"flex",t[5]),p(e,"disable_click",t[7]),p(e,"icon-mode",t[12]),I(e,"height",t[12]?"":"100%")},m(f,g){S(f,e,g),c&&c.m(e,null),A(e,l),A(e,i),t[36](i),a=!0,y||(_=[z(i,"change",t[19]),z(e,"drag",C(D(t[29]))),z(e,"dragstart",C(D(t[30]))),z(e,"dragend",C(D(t[31]))),z(e,"dragover",C(D(t[32]))),z(e,"dragenter",C(D(t[33]))),z(e,"dragleave",C(D(t[34]))),z(e,"drop",C(D(t[35]))),z(e,"click",t[14]),z(e,"drop",t[20]),z(e,"dragenter",t[18]),z(e,"dragleave",t[18])],y=!0)},p(f,g){c&&c.p&&(!a||g[0]&134217728)&&fe(c,h,f,f[27],a?_e(h,f[27],g,null):de(f[27]),null),(!a||g[0]&131072&&s!==(s=f[17]||void 0))&&b(i,"accept",s),(!a||g[0]&64&&d!==(d=f[6]==="multiple"||void 0))&&(i.multiple=d),(!a||g[0]&64&&o!==(o=f[6]==="directory"||void 0))&&b(i,"webkitdirectory",o),(!a||g[0]&64&&u!==(u=f[6]==="directory"||void 0))&&b(i,"mozdirectory",u),(!a||g[0]&512&&r!==(r=f[9]?-1:0))&&b(e,"tabindex",r),(!a||g[0]&512)&&p(e,"hidden",f[9]),(!a||g[0]&16)&&p(e,"center",f[4]),(!a||g[0]&8)&&p(e,"boundedheight",f[3]),(!a||g[0]&32)&&p(e,"flex",f[5]),(!a||g[0]&128)&&p(e,"disable_click",f[7]),(!a||g[0]&4096)&&p(e,"icon-mode",f[12]),g[0]&4096&&I(e,"height",f[12]?"":"100%")},i(f){a||(T(c,f),a=!0)},o(f){L(c,f),a=!1},d(f){f&&j(e),c&&c.d(f),t[36](null),y=!1,Le(_)}}}function Re(t){let e,l,i=!t[9]&&te(t);return{c(){i&&i.c(),e=re()},m(s,d){i&&i.m(s,d),S(s,e,d),l=!0},p(s,d){s[9]?i&&(ae(),L(i,1,1,()=>{i=null}),oe()):i?(i.p(s,d),d[0]&512&&T(i,1)):(i=te(s),i.c(),T(i,1),i.m(e.parentNode,e))},i(s){l||(T(i),l=!0)},o(s){L(i),l=!1},d(s){s&&j(e),i&&i.d(s)}}}function Be(t){let e,l,i,s,d;const o=t[28].default,u=ue(o,t,t[27],null);return{c(){e=W("button"),u&&u.c(),b(e,"tabindex",l=t[9]?-1:0),b(e,"class","svelte-1b742ao"),p(e,"hidden",t[9]),p(e,"center",t[4]),p(e,"boundedheight",t[3]),p(e,"flex",t[5]),p(e,"icon-mode",t[12]),I(e,"height",t[12]?"":"100%")},m(r,a){S(r,e,a),u&&u.m(e,null),i=!0,s||(d=z(e,"click",t[13]),s=!0)},p(r,a){u&&u.p&&(!i||a[0]&134217728)&&fe(u,o,r,r[27],i?_e(o,r[27],a,null):de(r[27]),null),(!i||a[0]&512&&l!==(l=r[9]?-1:0))&&b(e,"tabindex",l),(!i||a[0]&512)&&p(e,"hidden",r[9]),(!i||a[0]&16)&&p(e,"center",r[4]),(!i||a[0]&8)&&p(e,"boundedheight",r[3]),(!i||a[0]&32)&&p(e,"flex",r[5]),(!i||a[0]&4096)&&p(e,"icon-mode",r[12]),a[0]&4096&&I(e,"height",r[12]?"":"100%")},i(r){i||(T(u,r),i=!0)},o(r){L(u,r),i=!1},d(r){r&&j(e),u&&u.d(r),s=!1,d()}}}function te(t){let e,l;return e=new He({props:{root:t[8],upload_id:t[15],files:t[16],stream_handler:t[11]}}),{c(){Oe(e.$$.fragment)},m(i,s){Ne(e,i,s),l=!0},p(i,s){const d={};s[0]&256&&(d.root=i[8]),s[0]&32768&&(d.upload_id=i[15]),s[0]&65536&&(d.files=i[16]),s[0]&2048&&(d.stream_handler=i[11]),e.$set(d)},i(i){l||(T(e.$$.fragment,i),l=!0)},o(i){L(e.$$.fragment,i),l=!1},d(i){Se(e,i)}}}function Ke(t){let e,l,i,s;const d=[Be,Re,Je],o=[];function u(r,a){return r[0]==="clipboard"?0:r[1]&&r[10]?1:2}return e=u(t),l=o[e]=d[e](t),{c(){l.c(),i=re()},m(r,a){o[e].m(r,a),S(r,i,a),s=!0},p(r,a){let y=e;e=u(r),e===y?o[e].p(r,a):(ae(),L(o[y],1,1,()=>{o[y]=null}),oe(),l=o[e],l?l.p(r,a):(l=o[e]=d[e](r),l.c()),T(l,1),l.m(i.parentNode,i))},i(r){s||(T(l),s=!0)},o(r){L(l),s=!1},d(r){r&&j(i),o[e].d(r)}}}function Qe(t,e,l){if(!t||t==="*"||t==="file/*"||Array.isArray(t)&&t.some(s=>s==="*"||s==="file/*"))return!0;let i;if(typeof t=="string")i=t.split(",").map(s=>s.trim());else if(Array.isArray(t))i=t;else return!1;return i.includes(e)||i.some(s=>{const[d]=s.split("/").map(o=>o.trim());return s.endsWith("/*")&&l.startsWith(d+"/")})}function Ve(t,e,l){let i,{$$slots:s={},$$scope:d}=e,{filetype:o=null}=e,{dragging:u=!1}=e,{boundedheight:r=!0}=e,{center:a=!0}=e,{flex:y=!0}=e,{file_count:_="single"}=e,{disable_click:h=!1}=e,{root:c}=e,{hidden:f=!1}=e,{format:g="file"}=e,{uploading:F=!1}=e,{hidden_upload:P=null}=e,{show_progress:X=!0}=e,{max_file_size:J=null}=e,{upload:R}=e,{stream_handler:Y}=e,{icon_upload:Z=!1}=e,B,K,O,x=null;const ce=()=>{if(typeof navigator<"u"){const n=navigator.userAgent.toLowerCase();return n.indexOf("iphone")>-1||n.indexOf("ipad")>-1}return!1},E=se(),he=["image","video","audio","text","file"],Q=n=>i&&n.startsWith(".")?(x=!0,n):i&&n.includes("file/*")?"*":n.startsWith(".")||n.endsWith("/*")?n:he.includes(n)?n+"/*":"."+n;function ge(){l(21,u=!u)}function me(){navigator.clipboard.read().then(async n=>{for(let m=0;m<n.length;m++){const k=n[m].types.find(v=>v.startsWith("image/"));if(k){n[m].getType(k).then(async v=>{const V=new File([v],`clipboard.${k.replace("image/","")}`);await M([V])});break}}})}function pe(){h||P&&(l(2,P.value="",P),P.click())}async function be(n){await je(),l(15,B=Math.random().toString(36).substring(2,15)),l(1,F=!0);try{const m=await R(n,c,B,J??1/0);return E("load",_==="single"?m?.[0]:m),l(1,F=!1),m||[]}catch(m){return E("error",m.message),l(1,F=!1),[]}}async function M(n){if(!n.length)return;let m=n.map(k=>new File([k],k instanceof File?k.name:"file",{type:k.type}));return i&&x&&(m=m.filter(k=>ye(k)?!0:(E("error",`Invalid file type: ${k.name}. Only ${o} allowed.`),!1)),m.length===0)?[]:(l(16,K=await De(m)),await be(K))}function ye(n){return o?(Array.isArray(o)?o:[o]).some(k=>{const v=Q(k);if(v.startsWith("."))return n.name.toLowerCase().endsWith(v.toLowerCase());if(v==="*")return!0;if(v.endsWith("/*")){const[V]=v.split("/");return n.type.startsWith(V+"/")}return n.type===v}):!0}async function we(n){const m=n.target;if(m.files)if(g!="blob")await M(Array.from(m.files));else{if(_==="single"){E("load",m.files[0]);return}E("load",m.files)}}async function ke(n){if(l(21,u=!1),!n.dataTransfer?.files)return;const m=Array.from(n.dataTransfer.files).filter(k=>{const v="."+k.name.split(".").pop();return v&&Qe(O,v,k.type)||(v&&Array.isArray(o)?o.includes(v):v===o)?!0:(E("error",`Invalid file type only ${o} allowed.`),!1)});if(g!="blob")await M(m);else{if(_==="single"){E("load",m[0]);return}E("load",m)}}function ve(n){U.call(this,t,n)}function Ae(n){U.call(this,t,n)}function ze(n){U.call(this,t,n)}function We(n){U.call(this,t,n)}function Fe(n){U.call(this,t,n)}function Pe(n){U.call(this,t,n)}function Ee(n){U.call(this,t,n)}function Te(n){Ie[n?"unshift":"push"](()=>{P=n,l(2,P)})}return t.$$set=n=>{"filetype"in n&&l(0,o=n.filetype),"dragging"in n&&l(21,u=n.dragging),"boundedheight"in n&&l(3,r=n.boundedheight),"center"in n&&l(4,a=n.center),"flex"in n&&l(5,y=n.flex),"file_count"in n&&l(6,_=n.file_count),"disable_click"in n&&l(7,h=n.disable_click),"root"in n&&l(8,c=n.root),"hidden"in n&&l(9,f=n.hidden),"format"in n&&l(22,g=n.format),"uploading"in n&&l(1,F=n.uploading),"hidden_upload"in n&&l(2,P=n.hidden_upload),"show_progress"in n&&l(10,X=n.show_progress),"max_file_size"in n&&l(23,J=n.max_file_size),"upload"in n&&l(24,R=n.upload),"stream_handler"in n&&l(11,Y=n.stream_handler),"icon_upload"in n&&l(12,Z=n.icon_upload),"$$scope"in n&&l(27,d=n.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&67108865&&(o==null?l(17,O=null):typeof o=="string"?l(17,O=Q(o)):i&&o.includes("file/*")?l(17,O="*"):(l(0,o=o.map(Q)),l(17,O=o.join(", "))))},l(26,i=ce()),[o,F,P,r,a,y,_,h,c,f,X,Y,Z,me,pe,B,K,O,ge,we,ke,u,g,J,R,M,i,d,s,ve,Ae,ze,We,Fe,Pe,Ee,Te]}class Ze extends ie{constructor(e){super(),le(this,e,Ve,Ke,ne,{filetype:0,dragging:21,boundedheight:3,center:4,flex:5,file_count:6,disable_click:7,root:8,hidden:9,format:22,uploading:1,hidden_upload:2,show_progress:10,max_file_size:23,upload:24,stream_handler:11,icon_upload:12,paste_clipboard:13,open_file_upload:14,load_files:25},null,[-1,-1])}get filetype(){return this.$$.ctx[0]}set filetype(e){this.$$set({filetype:e}),w()}get dragging(){return this.$$.ctx[21]}set dragging(e){this.$$set({dragging:e}),w()}get boundedheight(){return this.$$.ctx[3]}set boundedheight(e){this.$$set({boundedheight:e}),w()}get center(){return this.$$.ctx[4]}set center(e){this.$$set({center:e}),w()}get flex(){return this.$$.ctx[5]}set flex(e){this.$$set({flex:e}),w()}get file_count(){return this.$$.ctx[6]}set file_count(e){this.$$set({file_count:e}),w()}get disable_click(){return this.$$.ctx[7]}set disable_click(e){this.$$set({disable_click:e}),w()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),w()}get hidden(){return this.$$.ctx[9]}set hidden(e){this.$$set({hidden:e}),w()}get format(){return this.$$.ctx[22]}set format(e){this.$$set({format:e}),w()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),w()}get hidden_upload(){return this.$$.ctx[2]}set hidden_upload(e){this.$$set({hidden_upload:e}),w()}get show_progress(){return this.$$.ctx[10]}set show_progress(e){this.$$set({show_progress:e}),w()}get max_file_size(){return this.$$.ctx[23]}set max_file_size(e){this.$$set({max_file_size:e}),w()}get upload(){return this.$$.ctx[24]}set upload(e){this.$$set({upload:e}),w()}get stream_handler(){return this.$$.ctx[11]}set stream_handler(e){this.$$set({stream_handler:e}),w()}get icon_upload(){return this.$$.ctx[12]}set icon_upload(e){this.$$set({icon_upload:e}),w()}get paste_clipboard(){return this.$$.ctx[13]}get open_file_upload(){return this.$$.ctx[14]}get load_files(){return this.$$.ctx[25]}}export{Ze as U};
//# sourceMappingURL=Upload-TGDabKXH.js.map
