{"version": 3, "file": "Index-CukgftOj.js", "sources": ["../../../icons/src/Chat.svelte", "../../../icons/src/Retry.svelte", "../../../icons/src/ScrollDownArrow.svelte", "../../../chatbot/shared/utils.ts", "../../../chatbot/shared/Component.svelte", "../../../chatbot/shared/MessageBox.svelte", "../../../chatbot/shared/ThumbDownActive.svelte", "../../../chatbot/shared/ThumbDownDefault.svelte", "../../../chatbot/shared/ThumbUpActive.svelte", "../../../chatbot/shared/ThumbUpDefault.svelte", "../../../chatbot/shared/LikeDislike.svelte", "../../../chatbot/shared/Copy.svelte", "../../../chatbot/shared/Download.svelte", "../../../chatbot/shared/ButtonPanel.svelte", "../../../chatbot/shared/Message.svelte", "../../../chatbot/shared/Pending.svelte", "../../../chatbot/shared/CopyAll.svelte", "../../../chatbot/shared/ChatBot.svelte", "../../../chatbot/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z\"\n\t/>\n\t<path fill=\"currentColor\" d=\"M8 10h16v2H8zm0 6h10v2H8z\" />\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tstroke-width=\"1.5\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tcolor=\"currentColor\"\n>\n\t<path\n\t\td=\"M19.1679 9C18.0247 6.46819 15.3006 4.5 11.9999 4.5C8.31459 4.5 5.05104 7.44668 4.54932 11\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M16 9H19.4C19.7314 9 20 8.73137 20 8.4V5\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M4.88146 15C5.92458 17.5318 8.64874 19.5 12.0494 19.5C15.7347 19.5 18.9983 16.5533 19.5 13\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M8.04932 15H4.64932C4.31795 15 4.04932 15.2686 4.04932 15.6V19\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M12 20L12 4M12 20L7 15M12 20L17 15\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"2\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n</svg>\n", "import type { FileData } from \"@gradio/client\";\nimport type { ComponentType, SvelteComponent } from \"svelte\";\nimport { uploadToHuggingFace } from \"@gradio/utils\";\nimport type {\n\tTupleFormat,\n\tComponentMessage,\n\tComponentData,\n\tTextMessage,\n\tNormalisedMessage,\n\tMessage,\n\tMessageRole\n} from \"../types\";\nimport type { LoadedComponent } from \"../../core/src/types\";\nimport { Gradio } from \"@gradio/utils\";\nexport const format_chat_for_sharing = async (\n\tchat: [string | FileData | null, string | FileData | null][]\n): Promise<string> => {\n\tlet messages = await Promise.all(\n\t\tchat.map(async (message_pair) => {\n\t\t\treturn await Promise.all(\n\t\t\t\tmessage_pair.map(async (message, i) => {\n\t\t\t\t\tif (message === null) return \"\";\n\t\t\t\t\tlet speaker_emoji = i === 0 ? \"😃\" : \"🤖\";\n\t\t\t\t\tlet html_content = \"\";\n\n\t\t\t\t\tif (typeof message === \"string\") {\n\t\t\t\t\t\tconst regexPatterns = {\n\t\t\t\t\t\t\taudio: /<audio.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\tvideo: /<video.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\timage: /<img.*?src=\"(\\/file=.*?)\".*?\\/>|!\\[.*?\\]\\((\\/file=.*?)\\)/g\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\thtml_content = message;\n\n\t\t\t\t\t\tfor (let [_, regex] of Object.entries(regexPatterns)) {\n\t\t\t\t\t\t\tlet match;\n\n\t\t\t\t\t\t\twhile ((match = regex.exec(message)) !== null) {\n\t\t\t\t\t\t\t\tconst fileUrl = match[1] || match[2];\n\t\t\t\t\t\t\t\tconst newUrl = await uploadToHuggingFace(fileUrl, \"url\");\n\t\t\t\t\t\t\t\thtml_content = html_content.replace(fileUrl, newUrl);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (!message?.url) return \"\";\n\t\t\t\t\t\tconst file_url = await uploadToHuggingFace(message.url, \"url\");\n\t\t\t\t\t\tif (message.mime_type?.includes(\"audio\")) {\n\t\t\t\t\t\t\thtml_content = `<audio controls src=\"${file_url}\"></audio>`;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"video\")) {\n\t\t\t\t\t\t\thtml_content = file_url;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"image\")) {\n\t\t\t\t\t\t\thtml_content = `<img src=\"${file_url}\" />`;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn `${speaker_emoji}: ${html_content}`;\n\t\t\t\t})\n\t\t\t);\n\t\t})\n\t);\n\treturn messages\n\t\t.map((message_pair) =>\n\t\t\tmessage_pair.join(\n\t\t\t\tmessage_pair[0] !== \"\" && message_pair[1] !== \"\" ? \"\\n\" : \"\"\n\t\t\t)\n\t\t)\n\t\t.join(\"\\n\");\n};\n\nexport interface UndoRetryData {\n\tindex: number | [number, number];\n\tvalue: string | FileData | ComponentData;\n}\n\nconst redirect_src_url = (src: string, root: string): string =>\n\tsrc.replace('src=\"/file', `src=\"${root}file`);\n\nfunction get_component_for_mime_type(\n\tmime_type: string | null | undefined\n): string {\n\tif (!mime_type) return \"file\";\n\tif (mime_type.includes(\"audio\")) return \"audio\";\n\tif (mime_type.includes(\"video\")) return \"video\";\n\tif (mime_type.includes(\"image\")) return \"image\";\n\treturn \"file\";\n}\n\nfunction convert_file_message_to_component_message(\n\tmessage: any\n): ComponentData {\n\tconst _file = Array.isArray(message.file) ? message.file[0] : message.file;\n\treturn {\n\t\tcomponent: get_component_for_mime_type(_file?.mime_type),\n\t\tvalue: message.file,\n\t\talt_text: message.alt_text,\n\t\tconstructor_args: {},\n\t\tprops: {}\n\t} as ComponentData;\n}\n\nexport function normalise_messages(\n\tmessages: Message[] | null,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\treturn messages.map((message, i) => {\n\t\tif (typeof message.content === \"string\") {\n\t\t\treturn {\n\t\t\t\trole: message.role,\n\t\t\t\tmetadata: message.metadata,\n\t\t\t\tcontent: redirect_src_url(message.content, root),\n\t\t\t\ttype: \"text\",\n\t\t\t\tindex: i\n\t\t\t};\n\t\t} else if (\"file\" in message.content) {\n\t\t\treturn {\n\t\t\t\tcontent: convert_file_message_to_component_message(message.content),\n\t\t\t\tmetadata: message.metadata,\n\t\t\t\trole: message.role,\n\t\t\t\ttype: \"component\",\n\t\t\t\tindex: i\n\t\t\t};\n\t\t}\n\t\treturn { type: \"component\", ...message } as ComponentMessage;\n\t});\n}\n\nexport function normalise_tuples(\n\tmessages: TupleFormat,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\tconst msg = messages.flatMap((message_pair, i) => {\n\t\treturn message_pair.map((message, index) => {\n\t\t\tif (message == null) return null;\n\t\t\tconst role = index == 0 ? \"user\" : \"assistant\";\n\n\t\t\tif (typeof message === \"string\") {\n\t\t\t\treturn {\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"text\",\n\t\t\t\t\tcontent: redirect_src_url(message, root),\n\t\t\t\t\tmetadata: { title: null },\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as TextMessage;\n\t\t\t}\n\n\t\t\tif (\"file\" in message) {\n\t\t\t\treturn {\n\t\t\t\t\tcontent: convert_file_message_to_component_message(message),\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"component\",\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as ComponentMessage;\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\trole: role,\n\t\t\t\tcontent: message,\n\t\t\t\ttype: \"component\",\n\t\t\t\tindex: [i, index]\n\t\t\t} as ComponentMessage;\n\t\t});\n\t});\n\treturn msg.filter((message) => message != null) as NormalisedMessage[];\n}\n\nexport function is_component_message(\n\tmessage: NormalisedMessage\n): message is ComponentMessage {\n\treturn message.type === \"component\";\n}\n\nexport function is_last_bot_message(\n\tmessages: NormalisedMessage[],\n\tall_messages: NormalisedMessage[]\n): boolean {\n\tconst is_bot = messages[messages.length - 1].role === \"assistant\";\n\tconst last_index = messages[messages.length - 1].index;\n\t// use JSON.stringify to handle both the number and tuple cases\n\t// when msg_format is tuples, last_index is an array and when it is messages, it is a number\n\tconst is_last =\n\t\tJSON.stringify(last_index) ===\n\t\tJSON.stringify(all_messages[all_messages.length - 1].index);\n\treturn is_last && is_bot;\n}\n\nexport function group_messages(\n\tmessages: NormalisedMessage[],\n\tmsg_format: \"messages\" | \"tuples\"\n): NormalisedMessage[][] {\n\tconst groupedMessages: NormalisedMessage[][] = [];\n\tlet currentGroup: NormalisedMessage[] = [];\n\tlet currentRole: MessageRole | null = null;\n\n\tfor (const message of messages) {\n\t\tif (msg_format === \"tuples\") {\n\t\t\tcurrentRole = null;\n\t\t}\n\n\t\tif (!(message.role === \"assistant\" || message.role === \"user\")) {\n\t\t\tcontinue;\n\t\t}\n\t\tif (message.role === currentRole) {\n\t\t\tcurrentGroup.push(message);\n\t\t} else {\n\t\t\tif (currentGroup.length > 0) {\n\t\t\t\tgroupedMessages.push(currentGroup);\n\t\t\t}\n\t\t\tcurrentGroup = [message];\n\t\t\tcurrentRole = message.role;\n\t\t}\n\t}\n\n\tif (currentGroup.length > 0) {\n\t\tgroupedMessages.push(currentGroup);\n\t}\n\n\treturn groupedMessages;\n}\n\nexport async function load_components(\n\tcomponent_names: string[],\n\t_components: Record<string, ComponentType<SvelteComponent>>,\n\tload_component: Gradio[\"load_component\"]\n): Promise<Record<string, ComponentType<SvelteComponent>>> {\n\tlet names: string[] = [];\n\tlet components: ReturnType<typeof load_component>[\"component\"][] = [];\n\n\tcomponent_names.forEach((component_name) => {\n\t\tif (_components[component_name] || component_name === \"file\") {\n\t\t\treturn;\n\t\t}\n\n\t\tconst { name, component } = load_component(component_name, \"base\");\n\t\tnames.push(name);\n\t\tcomponents.push(component);\n\t\tcomponent_name;\n\t});\n\n\tconst loaded_components: LoadedComponent[] = await Promise.all(components);\n\tloaded_components.forEach((component, i) => {\n\t\t_components[names[i]] = component.default;\n\t});\n\n\treturn _components;\n}\n\nexport function get_components_from_messages(\n\tmessages: NormalisedMessage[] | null\n): string[] {\n\tif (!messages) return [];\n\tlet components: Set<string> = new Set();\n\tmessages.forEach((message) => {\n\t\tif (message.type === \"component\") {\n\t\t\tcomponents.add(message.content.component);\n\t\t}\n\t});\n\treturn Array.from(components);\n}\n", "<script lang=\"ts\">\n\texport let type: \"gallery\" | \"plot\" | \"audio\" | \"video\" | \"image\" | string;\n\texport let components;\n\texport let value;\n\texport let target;\n\texport let theme_mode;\n\texport let props;\n\texport let i18n;\n\texport let upload;\n\texport let _fetch;\n</script>\n\n{#if type === \"gallery\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\t{_fetch}\n\t\tallow_preview={false}\n\t\tinteractive={false}\n\t\tmode=\"minimal\"\n\t\tfixed_height={1}\n\t\ton:load\n\t/>\n{:else if type === \"plot\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\t{target}\n\t\t{theme_mode}\n\t\tbokeh_version={props.bokeh_version}\n\t\tcaption=\"\"\n\t\tshow_actions_button={true}\n\t\ton:load\n\t/>\n{:else if type === \"audio\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\twaveform_settings={{}}\n\t\twaveform_options={{}}\n\t\tshow_download_button={false}\n\t\ton:load\n\t/>\n{:else if type === \"video\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\tautoplay={true}\n\t\tvalue={value.video || value}\n\t\tshow_label={false}\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\t{upload}\n\t\tshow_download_button={false}\n\t\ton:load\n\t>\n\t\t<track kind=\"captions\" />\n\t</svelte:component>\n{:else if type === \"image\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_download_button={false}\n\t\ton:load\n\t\t{i18n}\n\t/>\n{:else if type === \"html\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\tgradio={{ dispatch: () => {} }}\n\t\ton:load\n\t/>\n{/if}\n", "<script lang=\"ts\">\n\texport let expanded = false;\n\texport let title: string;\n\n\tfunction toggleExpanded(): void {\n\t\texpanded = !expanded;\n\t}\n</script>\n\n<button class=\"box\" on:click={toggleExpanded}>\n\t<div class=\"title\">\n\t\t<span class=\"title-text\">{title}</span>\n\t\t<span\n\t\t\tstyle:transform={expanded ? \"rotate(0)\" : \"rotate(90deg)\"}\n\t\t\tclass=\"arrow\"\n\t\t>\n\t\t\t▼\n\t\t</span>\n\t</div>\n\t{#if expanded}\n\t\t<div class=\"content\">\n\t\t\t<slot></slot>\n\t\t</div>\n\t{/if}\n</button>\n\n<style>\n\t.box {\n\t\tborder-radius: 4px;\n\t\tcursor: pointer;\n\t\tmax-width: max-content;\n\t\tbackground: var(--color-accent-soft);\n\t\tborder: 1px solid var(--border-color-accent-subdued);\n\t\tfont-size: 0.8em;\n\t}\n\n\t.title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 3px 6px;\n\t\tcolor: var(--body-text-color);\n\t\topacity: 0.8;\n\t}\n\n\t.content {\n\t\tpadding: 4px 8px;\n\t}\n\n\t.content :global(*) {\n\t\tfont-size: 0.8em;\n\t}\n\n\t.title-text {\n\t\tpadding-right: var(--spacing-lg);\n\t}\n\n\t.arrow {\n\t\tmargin-left: auto;\n\t\topacity: 0.8;\n\t}\n</style>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { IconButton } from \"@gradio/atoms\";\n\timport ThumbDownActive from \"./ThumbDownActive.svelte\";\n\timport ThumbDownDefault from \"./ThumbDownDefault.svelte\";\n\timport ThumbUpActive from \"./ThumbUpActive.svelte\";\n\timport ThumbUpDefault from \"./ThumbUpDefault.svelte\";\n\n\texport let handle_action: (selected: string | null) => void;\n\n\tlet selected: \"like\" | \"dislike\" | null = null;\n</script>\n\n<IconButton\n\tIcon={selected === \"dislike\" ? ThumbDownActive : ThumbDownDefault}\n\tlabel={selected === \"dislike\" ? \"clicked dislike\" : \"dislike\"}\n\tcolor={selected === \"dislike\"\n\t\t? \"var(--color-accent)\"\n\t\t: \"var(--block-label-text-color)\"}\n\ton:click={() => {\n\t\tselected = \"dislike\";\n\t\thandle_action(selected);\n\t}}\n/>\n\n<IconButton\n\tIcon={selected === \"like\" ? ThumbUpActive : ThumbUpDefault}\n\tlabel={selected === \"like\" ? \"clicked like\" : \"like\"}\n\tcolor={selected === \"like\"\n\t\t? \"var(--color-accent)\"\n\t\t: \"var(--block-label-text-color)\"}\n\ton:click={() => {\n\t\tselected = \"like\";\n\t\thandle_action(selected);\n\t}}\n/>\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t} else {\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\ttextArea.value = value;\n\n\t\t\ttextArea.style.position = \"absolute\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\n\t\t\tdocument.body.prepend(textArea);\n\t\t\ttextArea.select();\n\n\t\t\ttry {\n\t\t\t\tdocument.execCommand(\"copy\");\n\t\t\t\tcopy_feedback();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(error);\n\t\t\t} finally {\n\t\t\t\ttextArea.remove();\n\t\t\t}\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton\n\ton:click={handle_copy}\n\tlabel={copied ? \"Copied message\" : \"Copy message\"}\n\tIcon={copied ? Check : Copy}\n/>\n", "<svg\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M6.27701 8.253C6.24187 8.29143 6.19912 8.32212 6.15147 8.34311C6.10383 8.36411 6.05233 8.37495 6.00026 8.37495C5.94819 8.37495 5.89669 8.36411 5.84905 8.34311C5.8014 8.32212 5.75865 8.29143 5.72351 8.253L3.72351 6.0655C3.65798 5.99185 3.62408 5.89536 3.62916 5.79691C3.63424 5.69846 3.67788 5.60596 3.75064 5.53945C3.8234 5.47293 3.91943 5.43774 4.01794 5.44149C4.11645 5.44525 4.20952 5.48764 4.27701 5.5595L5.62501 7.0345V1.5C5.62501 1.40054 5.66452 1.30516 5.73485 1.23483C5.80517 1.16451 5.90055 1.125 6.00001 1.125C6.09947 1.125 6.19485 1.16451 6.26517 1.23483C6.3355 1.30516 6.37501 1.40054 6.37501 1.5V7.034L7.72351 5.559C7.79068 5.4856 7.88425 5.44189 7.98364 5.43748C8.08304 5.43308 8.18011 5.46833 8.25351 5.5355C8.32691 5.60267 8.37062 5.69624 8.37503 5.79563C8.37943 5.89503 8.34418 5.9921 8.27701 6.0655L6.27701 8.253Z\"\n\t\tfill=\"currentColor\"\n\t/>\n\t<path\n\t\td=\"M1.875 7.39258C1.875 7.29312 1.83549 7.19774 1.76517 7.12741C1.69484 7.05709 1.59946 7.01758 1.5 7.01758C1.40054 7.01758 1.30516 7.05709 1.23483 7.12741C1.16451 7.19774 1.125 7.29312 1.125 7.39258V7.42008C1.125 8.10358 1.125 8.65508 1.1835 9.08858C1.2435 9.53858 1.3735 9.91758 1.674 10.2186C1.975 10.5196 2.354 10.6486 2.804 10.7096C3.2375 10.7676 3.789 10.7676 4.4725 10.7676H7.5275C8.211 10.7676 8.7625 10.7676 9.196 10.7096C9.646 10.6486 10.025 10.5196 10.326 10.2186C10.627 9.91758 10.756 9.53858 10.817 9.08858C10.875 8.65508 10.875 8.10358 10.875 7.42008V7.39258C10.875 7.29312 10.8355 7.19774 10.7652 7.12741C10.6948 7.05709 10.5995 7.01758 10.5 7.01758C10.4005 7.01758 10.3052 7.05709 10.2348 7.12741C10.1645 7.19774 10.125 7.29312 10.125 7.39258C10.125 8.11008 10.124 8.61058 10.0735 8.98858C10.024 9.35558 9.9335 9.54958 9.7955 9.68808C9.657 9.82658 9.463 9.91658 9.0955 9.96608C8.718 10.0166 8.2175 10.0176 7.5 10.0176H4.5C3.7825 10.0176 3.2815 10.0166 2.904 9.96608C2.537 9.91658 2.343 9.82608 2.2045 9.68808C2.066 9.54958 1.976 9.35558 1.9265 8.98808C1.876 8.61058 1.875 8.11008 1.875 7.39258Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport LikeDislike from \"./LikeDislike.svelte\";\n\timport Copy from \"./Copy.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport DownloadIcon from \"./Download.svelte\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport type { NormalisedMessage, TextMessage } from \"../types\";\n\timport { is_component_message } from \"./utils\";\n\timport { Retry, Undo } from \"@gradio/icons\";\n\timport { IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\n\texport let likeable: boolean;\n\texport let show_retry: boolean;\n\texport let show_undo: boolean;\n\texport let show_copy_button: boolean;\n\texport let show: boolean;\n\texport let message: NormalisedMessage | NormalisedMessage[];\n\texport let position: \"right\" | \"left\";\n\texport let avatar: FileData | null;\n\texport let generating: boolean;\n\n\texport let handle_action: (selected: string | null) => void;\n\texport let layout: \"bubble\" | \"panel\";\n\n\tfunction is_all_text(\n\t\tmessage: NormalisedMessage[] | NormalisedMessage\n\t): message is TextMessage[] | TextMessage {\n\t\treturn (\n\t\t\t(Array.isArray(message) &&\n\t\t\t\tmessage.every((m) => typeof m.content === \"string\")) ||\n\t\t\t(!Array.isArray(message) && typeof message.content === \"string\")\n\t\t);\n\t}\n\n\tfunction all_text(message: TextMessage[] | TextMessage): string {\n\t\tif (Array.isArray(message)) {\n\t\t\treturn message.map((m) => m.content).join(\"\\n\");\n\t\t}\n\t\treturn message.content;\n\t}\n\n\t$: message_text = is_all_text(message) ? all_text(message) : \"\";\n\n\t$: show_copy = show_copy_button && message && is_all_text(message);\n\t$: show_download =\n\t\t!Array.isArray(message) &&\n\t\tis_component_message(message) &&\n\t\tmessage.content.value?.url;\n</script>\n\n{#if show}\n\t<div\n\t\tclass=\"message-buttons-{position} {layout} message-buttons {avatar !==\n\t\t\tnull && 'with-avatar'}\"\n\t>\n\t\t<IconButtonWrapper top_panel={false}>\n\t\t\t{#if show_copy}\n\t\t\t\t<Copy value={message_text} />\n\t\t\t{/if}\n\t\t\t{#if show_download && !Array.isArray(message) && is_component_message(message)}\n\t\t\t\t<DownloadLink\n\t\t\t\t\thref={message?.content?.value.url}\n\t\t\t\t\tdownload={message.content.value.orig_name || \"image\"}\n\t\t\t\t>\n\t\t\t\t\t<IconButton Icon={DownloadIcon} />\n\t\t\t\t</DownloadLink>\n\t\t\t{/if}\n\t\t\t{#if show_retry}\n\t\t\t\t<IconButton\n\t\t\t\t\tIcon={Retry}\n\t\t\t\t\tlabel=\"Retry\"\n\t\t\t\t\ton:click={() => handle_action(\"retry\")}\n\t\t\t\t\tdisabled={generating}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t\t{#if show_undo}\n\t\t\t\t<IconButton\n\t\t\t\t\tlabel=\"Undo\"\n\t\t\t\t\tIcon={Undo}\n\t\t\t\t\ton:click={() => handle_action(\"undo\")}\n\t\t\t\t\tdisabled={generating}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t\t{#if likeable}\n\t\t\t\t<LikeDislike {handle_action} />\n\t\t\t{/if}\n\t\t</IconButtonWrapper>\n\t</div>\n{/if}\n\n<style>\n\t.bubble :global(.icon-button-wrapper) {\n\t\tmargin: 0px calc(var(--spacing-xl) * 2);\n\t}\n\n\t.message-buttons-left {\n\t\talign-self: flex-start;\n\t}\n\n\t.bubble.message-buttons-right {\n\t\talign-self: flex-end;\n\t}\n\n\t.message-buttons-right :global(.icon-button-wrapper) {\n\t\tmargin-left: auto;\n\t}\n\n\t.bubble.with-avatar {\n\t\tmargin-left: calc(var(--spacing-xl) * 5);\n\t\tmargin-right: calc(var(--spacing-xl) * 5);\n\t}\n\n\t.panel {\n\t\tdisplay: flex;\n\t\talign-self: flex-start;\n\t\tpadding: 0 var(--spacing-xl);\n\t\tz-index: var(--layer-1);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { is_component_message, is_last_bot_message } from \"../shared/utils\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport Component from \"./Component.svelte\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport MessageBox from \"./MessageBox.svelte\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { ComponentType, SvelteComponent } from \"svelte\";\n\timport ButtonPanel from \"./ButtonPanel.svelte\";\n\n\texport let value: NormalisedMessage[];\n\texport let avatar_img: FileData | null;\n\texport let opposite_avatar_img: FileData | null = null;\n\texport let role = \"user\";\n\texport let messages: NormalisedMessage[] = [];\n\texport let layout: \"bubble\" | \"panel\";\n\texport let bubble_full_width: boolean;\n\texport let render_markdown: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let sanitize_html: boolean;\n\texport let selectable: boolean;\n\texport let _fetch: typeof fetch;\n\texport let rtl: boolean;\n\texport let dispatch: any;\n\texport let i18n: I18nFormatter;\n\texport let line_breaks: boolean;\n\texport let upload: Client[\"upload\"];\n\texport let target: HTMLElement | null;\n\texport let root: string;\n\texport let theme_mode: \"light\" | \"dark\" | \"system\";\n\texport let _components: Record<string, ComponentType<SvelteComponent>>;\n\texport let i: number;\n\texport let show_copy_button: boolean;\n\texport let generating: boolean;\n\texport let show_like: boolean;\n\texport let show_retry: boolean;\n\texport let show_undo: boolean;\n\texport let msg_format: \"tuples\" | \"messages\";\n\texport let handle_action: (selected: string | null) => void;\n\texport let scroll: () => void;\n\n\tfunction handle_select(i: number, message: NormalisedMessage): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: message.index,\n\t\t\tvalue: message.content\n\t\t});\n\t}\n\n\tfunction get_message_label_data(message: NormalisedMessage): string {\n\t\tif (message.type === \"text\") {\n\t\t\treturn message.content;\n\t\t} else if (\n\t\t\tmessage.type === \"component\" &&\n\t\t\tmessage.content.component === \"file\"\n\t\t) {\n\t\t\tif (Array.isArray(message.content.value)) {\n\t\t\t\treturn `file of extension type: ${message.content.value[0].orig_name?.split(\".\").pop()}`;\n\t\t\t}\n\t\t\treturn (\n\t\t\t\t`file of extension type: ${message.content.value?.orig_name?.split(\".\").pop()}` +\n\t\t\t\t(message.content.value?.orig_name ?? \"\")\n\t\t\t);\n\t\t}\n\t\treturn `a component of type ${message.content.component ?? \"unknown\"}`;\n\t}\n\n\ttype ButtonPanelProps = {\n\t\tshow: boolean;\n\t\thandle_action: (selected: string | null) => void;\n\t\tlikeable: boolean;\n\t\tshow_retry: boolean;\n\t\tshow_undo: boolean;\n\t\tgenerating: boolean;\n\t\tshow_copy_button: boolean;\n\t\tmessage: NormalisedMessage[] | NormalisedMessage;\n\t\tposition: \"left\" | \"right\";\n\t\tlayout: \"bubble\" | \"panel\";\n\t\tavatar: FileData | null;\n\t};\n\n\tlet button_panel_props: ButtonPanelProps;\n\t$: button_panel_props = {\n\t\tshow: show_like || show_retry || show_undo || show_copy_button,\n\t\thandle_action,\n\t\tlikeable: show_like,\n\t\tshow_retry,\n\t\tshow_undo,\n\t\tgenerating,\n\t\tshow_copy_button,\n\t\tmessage: msg_format === \"tuples\" ? messages[0] : messages,\n\t\tposition: role === \"user\" ? \"right\" : \"left\",\n\t\tavatar: avatar_img,\n\t\tlayout\n\t};\n</script>\n\n<div\n\tclass=\"message-row {layout} {role}-row\"\n\tclass:with_avatar={avatar_img !== null}\n\tclass:with_opposite_avatar={opposite_avatar_img !== null}\n>\n\t{#if avatar_img !== null}\n\t\t<div class=\"avatar-container\">\n\t\t\t<Image class=\"avatar-image\" src={avatar_img?.url} alt=\"{role} avatar\" />\n\t\t</div>\n\t{/if}\n\t<div\n\t\tclass:role\n\t\tclass=\"flex-wrap\"\n\t\tclass:component-wrap={messages[0].type === \"component\"}\n\t>\n\t\t{#each messages as message, thought_index}\n\t\t\t<div\n\t\t\t\tclass=\"message {role} {is_component_message(message)\n\t\t\t\t\t? message?.content.component\n\t\t\t\t\t: ''}\"\n\t\t\t\tclass:message-fit={layout === \"bubble\" && !bubble_full_width}\n\t\t\t\tclass:panel-full-width={true}\n\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\tstyle:text-align={rtl && role === \"user\" ? \"left\" : \"right\"}\n\t\t\t\tclass:component={message.type === \"component\"}\n\t\t\t\tclass:html={is_component_message(message) &&\n\t\t\t\t\tmessage.content.component === \"html\"}\n\t\t\t\tclass:thought={thought_index > 0}\n\t\t\t>\n\t\t\t\t<button\n\t\t\t\t\tdata-testid={role}\n\t\t\t\t\tclass:latest={i === value.length - 1}\n\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\tstyle:user-select=\"text\"\n\t\t\t\t\tclass:selectable\n\t\t\t\t\tstyle:cursor={selectable ? \"pointer\" : \"default\"}\n\t\t\t\t\tstyle:text-align={rtl ? \"right\" : \"left\"}\n\t\t\t\t\ton:click={() => handle_select(i, message)}\n\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\thandle_select(i, message);\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\taria-label={role + \"'s message: \" + get_message_label_data(message)}\n\t\t\t\t>\n\t\t\t\t\t{#if message.type === \"text\"}\n\t\t\t\t\t\t{#if message.metadata.title}\n\t\t\t\t\t\t\t<MessageBox\n\t\t\t\t\t\t\t\ttitle={message.metadata.title}\n\t\t\t\t\t\t\t\texpanded={is_last_bot_message([message], value)}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<Markdown\n\t\t\t\t\t\t\t\t\tmessage={message.content}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</MessageBox>\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t<Markdown\n\t\t\t\t\t\t\t\tmessage={message.content}\n\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t{:else if message.type === \"component\" && message.content.component in _components}\n\t\t\t\t\t\t<Component\n\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\tprops={message.content.props}\n\t\t\t\t\t\t\ttype={message.content.component}\n\t\t\t\t\t\t\tcomponents={_components}\n\t\t\t\t\t\t\tvalue={message.content.value}\n\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\ton:load={() => scroll()}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{:else if message.type === \"component\" && message.content.component === \"file\"}\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\tdata-testid=\"chatbot-file\"\n\t\t\t\t\t\t\tclass=\"file-pil\"\n\t\t\t\t\t\t\thref={message.content.value.url}\n\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t\t: message.content.value?.orig_name ||\n\t\t\t\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\t\t\t\"file\"}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{message.content.value?.orig_name ||\n\t\t\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\t\t\"file\"}\n\t\t\t\t\t\t</a>\n\t\t\t\t\t{/if}\n\t\t\t\t</button>\n\t\t\t</div>\n\n\t\t\t{#if layout === \"panel\"}\n\t\t\t\t<ButtonPanel {...button_panel_props} />\n\t\t\t{/if}\n\t\t{/each}\n\t</div>\n</div>\n\n{#if layout === \"bubble\"}\n\t<ButtonPanel {...button_panel_props} />\n{/if}\n\n<style>\n\t.message {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t}\n\n\t/* avatar styles */\n\t.avatar-container {\n\t\tflex-shrink: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\toverflow: hidden;\n\t}\n\n\t.avatar-container :global(img) {\n\t\tobject-fit: cover;\n\t}\n\n\t/* message wrapper */\n\t.flex-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: calc(100% - var(--spacing-xxl));\n\t\tmax-width: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-text-size);\n\t\toverflow-wrap: break-word;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.component {\n\t\tpadding: 0;\n\t\tborder-radius: var(--radius-md);\n\t\twidth: fit-content;\n\t\toverflow: hidden;\n\t}\n\n\t.component.gallery {\n\t\tborder: none;\n\t}\n\n\t.message-row :not(.avatar-container) :global(img) {\n\t\tmargin: var(--size-2);\n\t\tmax-height: 300px;\n\t}\n\n\t.file-pil {\n\t\tdisplay: block;\n\t\twidth: fit-content;\n\t\tpadding: var(--spacing-sm) var(--spacing-lg);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t\ttext-decoration: none;\n\t\tmargin: 0;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\t.file {\n\t\twidth: auto !important;\n\t\tmax-width: fit-content !important;\n\t}\n\n\t@media (max-width: 600px) or (max-width: 480px) {\n\t\t.component {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.message :global(.prose) {\n\t\tfont-size: var(--chatbot-text-size);\n\t}\n\n\t.message-bubble-border {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t.message-fit {\n\t\twidth: fit-content !important;\n\t}\n\n\t.panel-full-width {\n\t\twidth: 100%;\n\t}\n\t.message-markdown-disabled {\n\t\twhite-space: pre-line;\n\t}\n\n\t.user {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t\talign-self: flex-start;\n\t\tborder-bottom-right-radius: 0;\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\ttext-align: right;\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t\tborder-color: var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.bot {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder-bottom-left-radius: 0;\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\ttext-align: right;\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* Colors */\n\t.bubble .bot {\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\t.message-row {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t}\n\n\t/* bubble mode styles */\n\t.bubble {\n\t\tmargin: calc(var(--spacing-xl) * 2);\n\t\tmargin-bottom: var(--spacing-xl);\n\t}\n\n\t.bubble.user-row {\n\t\talign-self: flex-end;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.bubble.bot-row {\n\t\talign-self: flex-start;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.bubble .user-row {\n\t\tflex-direction: row;\n\t\tjustify-content: flex-end;\n\t}\n\n\t.bubble .with_avatar.user-row {\n\t\tmargin-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.bubble .with_avatar.bot-row {\n\t\tmargin-left: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.bubble .with_opposite_avatar.user-row {\n\t\tmargin-left: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\n\t}\n\n\t.bubble .message-fit {\n\t\twidth: fit-content !important;\n\t}\n\n\t/* panel mode styles */\n\t.panel {\n\t\tmargin: 0;\n\t\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\n\t}\n\n\t.panel.bot-row {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.panel .with_avatar {\n\t\tpadding-left: calc(var(--spacing-xl) * 2) !important;\n\t\tpadding-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.panel .panel-full-width {\n\t\twidth: 100%;\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* message content */\n\t.flex-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmax-width: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-text-size);\n\t\toverflow-wrap: break-word;\n\t}\n\n\t.user {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t\talign-self: flex-start;\n\t\tborder-bottom-right-radius: 0;\n\t\tbox-shadow: var(--shadow-drop);\n\t\ttext-align: right;\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t\tborder-color: var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\t@media (max-width: 480px) {\n\t\t.user-row.bubble {\n\t\t\talign-self: flex-end;\n\t\t}\n\n\t\t.bot-row.bubble {\n\t\t\talign-self: flex-start;\n\t\t}\n\t\t.message {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.avatar-container {\n\t\talign-self: flex-start;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\t.user-row > .avatar-container {\n\t\torder: 2;\n\t}\n\n\t.user-row.bubble > .avatar-container {\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\n\t.panel.user-row > .avatar-container {\n\t\torder: 0;\n\t}\n\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-right: var(--spacing-xxl);\n\t\tmargin-left: 0;\n\t}\n\n\t.avatar-container:not(.thumbnail-item) :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t\tpadding: 6px;\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\t@keyframes dot-flashing {\n\t\t0% {\n\t\t\topacity: 0.8;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\n\t/* Image preview */\n\t.message :global(.preview) {\n\t\tobject-fit: contain;\n\t\twidth: 95%;\n\t\tmax-height: 93%;\n\t}\n\t.image-preview {\n\t\tposition: absolute;\n\t\tz-index: 999;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: auto;\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.image-preview :global(svg) {\n\t\tstroke: white;\n\t}\n\t.image-preview-close-button {\n\t\tposition: absolute;\n\t\ttop: 10px;\n\t\tright: 10px;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tfont-size: 1.5em;\n\t\tcursor: pointer;\n\t\theight: 30px;\n\t\twidth: 30px;\n\t\tpadding: 3px;\n\t\tbackground: var(--bg-color);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.message > button {\n\t\twidth: 100%;\n\t}\n\t.html {\n\t\tpadding: 0;\n\t\tborder: none;\n\t\tbackground: none;\n\t}\n\n\t.thought {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.panel .bot,\n\t.panel .user {\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.panel.user-row {\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.panel .user-row,\n\t.panel .bot-row {\n\t\talign-self: flex-start;\n\t}\n\n\t.panel .user :global(*),\n\t.panel .bot :global(*) {\n\t\ttext-align: left;\n\t}\n\n\t.panel .user {\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.panel .user-row {\n\t\tbackground-color: var(--color-accent-soft);\n\t\talign-self: flex-start;\n\t}\n\n\t.panel .message {\n\t\tmargin-bottom: var(--spacing-md);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let layout = \"bubble\";\n</script>\n\n<div\n\tclass=\"message pending\"\n\trole=\"status\"\n\taria-label=\"Loading response\"\n\taria-live=\"polite\"\n\tstyle:border-radius={layout === \"bubble\" ? \"var(--radius-xxl)\" : \"none\"}\n>\n\t<span class=\"sr-only\">Loading content</span>\n\t<div class=\"dot-flashing\" />\n\t&nbsp;\n\t<div class=\"dot-flashing\" />\n\t&nbsp;\n\t<div class=\"dot-flashing\" />\n</div>\n\n<style>\n\t.pending {\n\t\tbackground: var(--color-accent-soft);\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\talign-self: center;\n\t\tgap: 2px;\n\t\twidth: 100%;\n\t\theight: var(--size-16);\n\t}\n\t.dot-flashing {\n\t\tanimation: flash 1s infinite ease-in-out;\n\t\tborder-radius: 5px;\n\t\tbackground-color: var(--body-text-color);\n\t\twidth: 7px;\n\t\theight: 7px;\n\t\tcolor: var(--body-text-color);\n\t}\n\t@keyframes flash {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 0;\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.dot-flashing:nth-child(1) {\n\t\tanimation-delay: 0s;\n\t}\n\n\t.dot-flashing:nth-child(2) {\n\t\tanimation-delay: 0.33s;\n\t}\n\t.dot-flashing:nth-child(3) {\n\t\tanimation-delay: 0.66s;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\tlet copied = false;\n\texport let value: NormalisedMessage[] | null;\n\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tconst copy_conversation = (): void => {\n\t\tif (value) {\n\t\t\tconst conversation_value = value\n\t\t\t\t.map((message) => {\n\t\t\t\t\tif (message.type === \"text\") {\n\t\t\t\t\t\treturn `${message.role}: ${message.content}`;\n\t\t\t\t\t}\n\t\t\t\t\treturn `${message.role}: ${message.content.value.url}`;\n\t\t\t\t})\n\t\t\t\t.join(\"\\n\\n\");\n\n\t\t\tnavigator.clipboard.writeText(conversation_value).catch((err) => {\n\t\t\t\tconsole.error(\"Failed to copy conversation: \", err);\n\t\t\t});\n\t\t}\n\t};\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tcopy_conversation();\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton\n\tIcon={copied ? Check : Copy}\n\ton:click={handle_copy}\n\tlabel={copied ? \"Copied conversation\" : \"Copy conversation\"}\n></IconButton>\n", "<script lang=\"ts\">\n\timport {\n\t\tformat_chat_for_sharing,\n\t\ttype UndoRetryData,\n\t\tis_last_bot_message,\n\t\tgroup_messages,\n\t\tload_components,\n\t\tget_components_from_messages\n\t} from \"./utils\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport { copy } from \"@gradio/utils\";\n\timport Message from \"./Message.svelte\";\n\n\timport { dequal } from \"dequal/lite\";\n\timport {\n\t\tafterUpdate,\n\t\tcreateEventDispatcher,\n\t\ttype SvelteComponent,\n\t\ttype ComponentType,\n\t\ttick,\n\t\tonMount\n\t} from \"svelte\";\n\timport { Image } from \"@gradio/image/shared\";\n\n\timport { Clear, Trash, Community, ScrollDownArrow } from \"@gradio/icons\";\n\timport { IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\timport type { SelectData, LikeData } from \"@gradio/utils\";\n\timport type { ExampleMessage } from \"../types\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport Pending from \"./Pending.svelte\";\n\timport { ShareError } from \"@gradio/utils\";\n\timport { Gradio } from \"@gradio/utils\";\n\n\texport let value: NormalisedMessage[] | null = [];\n\tlet old_value: NormalisedMessage[] | null = null;\n\n\timport CopyAll from \"./CopyAll.svelte\";\n\n\texport let _fetch: typeof fetch;\n\texport let load_component: Gradio[\"load_component\"];\n\n\tlet _components: Record<string, ComponentType<SvelteComponent>> = {};\n\n\tconst is_browser = typeof window !== \"undefined\";\n\n\tasync function update_components(): Promise<void> {\n\t\t_components = await load_components(\n\t\t\tget_components_from_messages(value),\n\t\t\t_components,\n\t\t\tload_component\n\t\t);\n\t}\n\n\t$: value, update_components();\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let pending_message = false;\n\texport let generating = false;\n\texport let selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let show_copy_all_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let sanitize_html = true;\n\texport let bubble_full_width = true;\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let autoscroll = true;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let i18n: I18nFormatter;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let placeholder: string | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let msg_format: \"tuples\" | \"messages\" = \"tuples\";\n\texport let examples: ExampleMessage[] | null = null;\n\texport let _retryable = false;\n\texport let _undoable = false;\n\texport let like_user_message = false;\n\texport let root: string;\n\n\tlet target: HTMLElement | null = null;\n\n\tonMount(() => {\n\t\ttarget = document.querySelector(\"div.gradio-container\");\n\t});\n\n\tlet div: HTMLDivElement;\n\n\tlet show_scroll_button = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t\tlike: LikeData;\n\t\tundo: UndoRetryData;\n\t\tretry: UndoRetryData;\n\t\tclear: undefined;\n\t\tshare: any;\n\t\terror: string;\n\t\texample_select: SelectData;\n\t}>();\n\n\tfunction is_at_bottom(): boolean {\n\t\treturn div && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\n\t}\n\n\tfunction scroll_to_bottom(): void {\n\t\tif (!div) return;\n\t\tdiv.scrollTo(0, div.scrollHeight);\n\t\tshow_scroll_button = false;\n\t}\n\n\tlet scroll_after_component_load = false;\n\tfunction on_child_component_load(): void {\n\t\tif (scroll_after_component_load) {\n\t\t\tscroll_to_bottom();\n\t\t\tscroll_after_component_load = false;\n\t\t}\n\t}\n\n\tasync function scroll_on_value_update(): Promise<void> {\n\t\tif (!autoscroll) return;\n\n\t\tif (is_at_bottom()) {\n\t\t\t// Child components may be loaded asynchronously,\n\t\t\t// so trigger the scroll again after they load.\n\t\t\tscroll_after_component_load = true;\n\n\t\t\tawait tick(); // Wait for the DOM to update so that the scrollHeight is correct\n\t\t\tscroll_to_bottom();\n\t\t} else {\n\t\t\tshow_scroll_button = true;\n\t\t}\n\t}\n\tonMount(() => {\n\t\tscroll_on_value_update();\n\t});\n\t$: if (value || pending_message || _components) {\n\t\tscroll_on_value_update();\n\t}\n\n\tonMount(() => {\n\t\tfunction handle_scroll(): void {\n\t\t\tif (is_at_bottom()) {\n\t\t\t\tshow_scroll_button = false;\n\t\t\t} else {\n\t\t\t\tscroll_after_component_load = false;\n\t\t\t}\n\t\t}\n\n\t\tdiv?.addEventListener(\"scroll\", handle_scroll);\n\t\treturn () => {\n\t\t\tdiv?.removeEventListener(\"scroll\", handle_scroll);\n\t\t};\n\t});\n\n\tlet image_preview_source: string;\n\tlet image_preview_source_alt: string;\n\tlet is_image_preview_open = false;\n\n\tafterUpdate(() => {\n\t\tif (!div) return;\n\t\tdiv.querySelectorAll(\"img\").forEach((n) => {\n\t\t\tn.addEventListener(\"click\", (e) => {\n\t\t\t\tconst target = e.target as HTMLImageElement;\n\t\t\t\tif (target) {\n\t\t\t\t\timage_preview_source = target.src;\n\t\t\t\t\timage_preview_source_alt = target.alt;\n\t\t\t\t\tis_image_preview_open = true;\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t});\n\n\t$: {\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\");\n\t\t}\n\t}\n\n\t$: groupedMessages = value && group_messages(value, msg_format);\n\n\tfunction handle_example_select(i: number, example: ExampleMessage): void {\n\t\tdispatch(\"example_select\", {\n\t\t\tindex: i,\n\t\t\tvalue: { text: example.text, files: example.files }\n\t\t});\n\t}\n\n\tfunction handle_like(\n\t\ti: number,\n\t\tmessage: NormalisedMessage,\n\t\tselected: string | null\n\t): void {\n\t\tif (selected === \"undo\" || selected === \"retry\") {\n\t\t\tconst val_ = value as NormalisedMessage[];\n\t\t\t// iterate through messages until we find the last user message\n\t\t\t// the index of this message is where the user needs to edit the chat history\n\t\t\tlet last_index = val_.length - 1;\n\t\t\twhile (val_[last_index].role === \"assistant\") {\n\t\t\t\tlast_index--;\n\t\t\t}\n\t\t\tdispatch(selected, {\n\t\t\t\tindex: val_[last_index].index,\n\t\t\t\tvalue: val_[last_index].content\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\n\t\tif (msg_format === \"tuples\") {\n\t\t\tdispatch(\"like\", {\n\t\t\t\tindex: message.index,\n\t\t\t\tvalue: message.content,\n\t\t\t\tliked: selected === \"like\"\n\t\t\t});\n\t\t} else {\n\t\t\tif (!groupedMessages) return;\n\n\t\t\tconst message_group = groupedMessages[i];\n\t\t\tconst [first, last] = [\n\t\t\t\tmessage_group[0],\n\t\t\t\tmessage_group[message_group.length - 1]\n\t\t\t];\n\n\t\t\tdispatch(\"like\", {\n\t\t\t\tindex: [first.index, last.index] as [number, number],\n\t\t\t\tvalue: message_group.map((m) => m.content),\n\t\t\t\tliked: selected === \"like\"\n\t\t\t});\n\t\t}\n\t}\n</script>\n\n{#if value !== null && value.length > 0}\n\t<IconButtonWrapper>\n\t\t{#if show_share_button}\n\t\t\t<IconButton\n\t\t\t\tIcon={Community}\n\t\t\t\ton:click={async () => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\tconst formatted = await format_chat_for_sharing(value);\n\t\t\t\t\t\tdispatch(\"share\", {\n\t\t\t\t\t\t\tdescription: formatted\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t\tlet message = e instanceof ShareError ? e.message : \"Share failed.\";\n\t\t\t\t\t\tdispatch(\"error\", message);\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<Community />\n\t\t\t</IconButton>\n\t\t{/if}\n\t\t<IconButton Icon={Trash} on:click={() => dispatch(\"clear\")} label={\"Clear\"}\n\t\t></IconButton>\n\t\t{#if show_copy_all_button}\n\t\t\t<CopyAll {value} />\n\t\t{/if}\n\t</IconButtonWrapper>\n{/if}\n\n<div\n\tclass={layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"}\n\tbind:this={div}\n\trole=\"log\"\n\taria-label=\"chatbot conversation\"\n\taria-live=\"polite\"\n>\n\t{#if value !== null && value.length > 0 && groupedMessages !== null}\n\t\t<div class=\"message-wrap\" use:copy>\n\t\t\t{#each groupedMessages as messages, i}\n\t\t\t\t{@const role = messages[0].role === \"user\" ? \"user\" : \"bot\"}\n\t\t\t\t{@const avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{@const opposite_avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{#if is_image_preview_open}\n\t\t\t\t\t<div class=\"image-preview\">\n\t\t\t\t\t\t<img src={image_preview_source} alt={image_preview_source_alt} />\n\t\t\t\t\t\t<IconButtonWrapper>\n\t\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\t\tIcon={Clear}\n\t\t\t\t\t\t\t\ton:click={() => (is_image_preview_open = false)}\n\t\t\t\t\t\t\t\tlabel={\"Clear\"}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</IconButtonWrapper>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t\t<Message\n\t\t\t\t\t{messages}\n\t\t\t\t\t{opposite_avatar_img}\n\t\t\t\t\t{avatar_img}\n\t\t\t\t\t{role}\n\t\t\t\t\t{layout}\n\t\t\t\t\t{dispatch}\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{_fetch}\n\t\t\t\t\t{line_breaks}\n\t\t\t\t\t{theme_mode}\n\t\t\t\t\t{target}\n\t\t\t\t\t{root}\n\t\t\t\t\t{upload}\n\t\t\t\t\t{selectable}\n\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t{bubble_full_width}\n\t\t\t\t\t{render_markdown}\n\t\t\t\t\t{rtl}\n\t\t\t\t\t{i}\n\t\t\t\t\t{value}\n\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t{_components}\n\t\t\t\t\t{generating}\n\t\t\t\t\t{msg_format}\n\t\t\t\t\tshow_like={role === \"user\" ? likeable && like_user_message : likeable}\n\t\t\t\t\tshow_retry={_retryable && is_last_bot_message(messages, value)}\n\t\t\t\t\tshow_undo={_undoable && is_last_bot_message(messages, value)}\n\t\t\t\t\t{show_copy_button}\n\t\t\t\t\thandle_action={(selected) => handle_like(i, messages[0], selected)}\n\t\t\t\t\tscroll={is_browser ? scroll : () => {}}\n\t\t\t\t/>\n\t\t\t{/each}\n\t\t\t{#if pending_message}\n\t\t\t\t<Pending {layout} />\n\t\t\t{/if}\n\t\t</div>\n\t{:else}\n\t\t<div class=\"placeholder-content\">\n\t\t\t{#if placeholder !== null}\n\t\t\t\t<div class=\"placeholder\">\n\t\t\t\t\t<Markdown message={placeholder} {latex_delimiters} {root} />\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t{#if examples !== null}\n\t\t\t\t<div class=\"examples\">\n\t\t\t\t\t{#each examples as example, i}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"example\"\n\t\t\t\t\t\t\ton:click={() => handle_example_select(i, example)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if example.icon !== undefined}\n\t\t\t\t\t\t\t\t<div class=\"example-icon-container\">\n\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\t\tsrc={example.icon.url}\n\t\t\t\t\t\t\t\t\t\talt=\"example-icon\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{#if example.display_text !== undefined}\n\t\t\t\t\t\t\t\t<span class=\"example-display-text\">{example.display_text}</span>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<span class=\"example-text\">{example.text}</span>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{#if example.files !== undefined && example.files.length > 1}\n\t\t\t\t\t\t\t\t<span class=\"example-file\"\n\t\t\t\t\t\t\t\t\t><em>{example.files.length} Files</em></span\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{:else if example.files !== undefined && example.files[0] !== undefined && example.files[0].mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={example.files[0].url}\n\t\t\t\t\t\t\t\t\t\talt=\"example-image\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files !== undefined && example.files[0] !== undefined}\n\t\t\t\t\t\t\t\t<span class=\"example-file\"\n\t\t\t\t\t\t\t\t\t><em>{example.files[0].orig_name}</em></span\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</div>\n\t{/if}\n</div>\n\n{#if show_scroll_button}\n\t<div class=\"scroll-down-button-container\">\n\t\t<IconButton\n\t\t\tIcon={ScrollDownArrow}\n\t\t\tlabel=\"Scroll down\"\n\t\t\tsize=\"large\"\n\t\t\ton:click={scroll_to_bottom}\n\t\t/>\n\t</div>\n{/if}\n\n<style>\n\t.placeholder-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\theight: 100%;\n\t}\n\n\t.placeholder {\n\t\talign-items: center;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\theight: 100%;\n\t\tflex-grow: 1;\n\t}\n\n\t.examples :global(img) {\n\t\tpointer-events: none;\n\t}\n\n\t.examples {\n\t\tmargin: auto;\n\t\tpadding: var(--spacing-xxl);\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n\t\tgap: var(--spacing-xxl);\n\t\tmax-width: calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));\n\t}\n\n\t.example {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: var(--spacing-xl);\n\t\tborder: 0.05px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-xl);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tcursor: pointer;\n\t\ttransition: var(--button-transition);\n\t\tmax-width: var(--size-56);\n\t\twidth: 100%;\n\t\tjustify-content: center;\n\t}\n\n\t.example:hover {\n\t\tbackground-color: var(--color-accent-soft);\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.example-icon-container {\n\t\tdisplay: flex;\n\t\talign-self: flex-start;\n\t\tmargin-left: var(--spacing-md);\n\t\twidth: var(--size-6);\n\t\theight: var(--size-6);\n\t}\n\n\t.example-display-text,\n\t.example-text,\n\t.example-file {\n\t\tfont-size: var(--text-md);\n\t\twidth: 100%;\n\t\ttext-align: center;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.example-display-text,\n\t.example-file {\n\t\tmargin-top: var(--spacing-md);\n\t}\n\n\t.example-image-container {\n\t\tflex-grow: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-top: var(--spacing-xl);\n\t}\n\n\t.example-image-container :global(img) {\n\t\tmax-height: 100%;\n\t\tmax-width: 100%;\n\t\theight: var(--size-32);\n\t\twidth: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: var(--radius-xl);\n\t}\n\n\t.panel-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.bubble-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t\theight: 100%;\n\t\tpadding-top: var(--spacing-xxl);\n\t}\n\n\t@media (prefers-color-scheme: dark) {\n\t\t.bubble-wrap {\n\t\t\tbackground: var(--background-fill-secondary);\n\t\t}\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.message-wrap :global(.prose.chatbot.md) {\n\t\topacity: 0.8;\n\t\toverflow-wrap: break-word;\n\t}\n\n\t.message-wrap :global(.message-row .md img) {\n\t\tborder-radius: var(--radius-xl);\n\t\tmargin: var(--size-2);\n\t\twidth: 400px;\n\t\tmax-width: 30vw;\n\t\tmax-height: 30vw;\n\t}\n\n\t/* link styles */\n\t.message-wrap :global(.message a) {\n\t\tcolor: var(--color-text-link);\n\t\ttext-decoration: underline;\n\t}\n\n\t/* table styles */\n\t.message-wrap :global(.bot table),\n\t.message-wrap :global(.bot tr),\n\t.message-wrap :global(.bot td),\n\t.message-wrap :global(.bot th) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.message-wrap :global(.user table),\n\t.message-wrap :global(.user tr),\n\t.message-wrap :global(.user td),\n\t.message-wrap :global(.user th) {\n\t\tborder: 1px solid var(--border-color-accent);\n\t}\n\n\t/* KaTeX */\n\t.message-wrap :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\t.message-wrap :global(span.katex-display) {\n\t\tmargin-top: 0;\n\t}\n\n\t.message-wrap :global(pre) {\n\t\tposition: relative;\n\t}\n\n\t.message-wrap :global(.grid-wrap) {\n\t\tmax-height: 80% !important;\n\t\tmax-width: 600px;\n\t\tobject-fit: contain;\n\t}\n\n\t.message-wrap > div :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.panel-wrap :global(.message-row:first-child) {\n\t\tpadding-top: calc(var(--spacing-xxl) * 2);\n\t}\n\n\t.scroll-down-button-container {\n\t\tposition: absolute;\n\t\tbottom: 10px;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\tz-index: var(--layer-top);\n\t}\n\t.scroll-down-button-container :global(button) {\n\t\tborder-radius: 50%;\n\t\tbox-shadow: var(--shadow-drop);\n\t\ttransition:\n\t\t\tbox-shadow 0.2s ease-in-out,\n\t\t\ttransform 0.2s ease-in-out;\n\t}\n\t.scroll-down-button-container :global(button:hover) {\n\t\tbox-shadow:\n\t\t\tvar(--shadow-drop),\n\t\t\t0 2px 2px rgba(0, 0, 0, 0.05);\n\t\ttransform: translateY(-2px);\n\t}\n\n\t.image-preview {\n\t\tposition: absolute;\n\t\tz-index: 999;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: auto;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseChatBot } from \"./shared/ChatBot.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, LikeData } from \"@gradio/utils\";\n\n\timport ChatBot from \"./shared/ChatBot.svelte\";\n\timport type { UndoRetryData } from \"./shared/utils\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Chat } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type {\n\t\tMessage,\n\t\tExampleMessage,\n\t\tTupleFormat,\n\t\tNormalisedMessage\n\t} from \"./types\";\n\n\timport { normalise_tuples, normalise_messages } from \"./shared/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: TupleFormat | Message[] = [];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string;\n\texport let show_label = true;\n\texport let root: string;\n\texport let _selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = true;\n\texport let show_copy_all_button = false;\n\texport let sanitize_html = true;\n\texport let bubble_full_width = true;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let type: \"tuples\" | \"messages\" = \"tuples\";\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let autoscroll = true;\n\texport let _retryable = false;\n\texport let _undoable = false;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tlike: LikeData;\n\t\tclear_status: LoadingStatus;\n\t\texample_select: SelectData;\n\t\tretry: UndoRetryData;\n\t\tundo: UndoRetryData;\n\t\tclear: null;\n\t}>;\n\n\tlet _value: NormalisedMessage[] | null = [];\n\n\t$: _value =\n\t\ttype === \"tuples\"\n\t\t\t? normalise_tuples(value as TupleFormat, root)\n\t\t\t: normalise_messages(value as Message[], root);\n\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let like_user_message = false;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height: number | string | undefined;\n\texport let min_height: number | string | undefined;\n\texport let max_height: number | string | undefined;\n\texport let placeholder: string | null = null;\n\texport let examples: ExampleMessage[] | null = null;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n</script>\n\n<Block\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\tpadding={false}\n\t{scale}\n\t{min_width}\n\t{height}\n\t{min_height}\n\t{max_height}\n\tallow_overflow={true}\n\tflex={true}\n\toverflow_behavior=\"auto\"\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tshow_progress={loading_status.show_progress === \"hidden\"\n\t\t\t\t? \"hidden\"\n\t\t\t\t: \"minimal\"}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t<div class=\"wrapper\">\n\t\t{#if show_label}\n\t\t\t<BlockLabel\n\t\t\t\t{show_label}\n\t\t\t\tIcon={Chat}\n\t\t\t\tfloat={true}\n\t\t\t\tlabel={label || \"Chatbot\"}\n\t\t\t/>\n\t\t{/if}\n\t\t<ChatBot\n\t\t\ti18n={gradio.i18n}\n\t\t\tselectable={_selectable}\n\t\t\t{likeable}\n\t\t\t{show_share_button}\n\t\t\t{show_copy_all_button}\n\t\t\tvalue={_value}\n\t\t\t{latex_delimiters}\n\t\t\t{render_markdown}\n\t\t\t{theme_mode}\n\t\t\tpending_message={loading_status?.status === \"pending\"}\n\t\t\tgenerating={loading_status?.status === \"generating\"}\n\t\t\t{rtl}\n\t\t\t{show_copy_button}\n\t\t\t{like_user_message}\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:like={(e) => gradio.dispatch(\"like\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\ton:example_select={(e) => gradio.dispatch(\"example_select\", e.detail)}\n\t\t\ton:retry={(e) => gradio.dispatch(\"retry\", e.detail)}\n\t\t\ton:undo={(e) => gradio.dispatch(\"undo\", e.detail)}\n\t\t\ton:clear={() => {\n\t\t\t\tvalue = [];\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\t{avatar_images}\n\t\t\t{sanitize_html}\n\t\t\t{bubble_full_width}\n\t\t\t{line_breaks}\n\t\t\t{autoscroll}\n\t\t\t{layout}\n\t\t\t{placeholder}\n\t\t\t{examples}\n\t\t\t{_retryable}\n\t\t\t{_undoable}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\t_fetch={(...args) => gradio.client.fetch(...args)}\n\t\t\tload_component={gradio.load_component}\n\t\t\tmsg_format={type}\n\t\t\troot={gradio.root}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\talign-items: start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tflex-grow: 1;\n\t}\n\n\t:global(.progress-text) {\n\t\tright: auto;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "path2", "path3", "path", "format_chat_for_sharing", "chat", "message_pair", "message", "speaker_emoji", "html_content", "regexPatterns", "_", "regex", "match", "fileUrl", "newUrl", "uploadToHuggingFace", "file_url", "redirect_src_url", "src", "root", "get_component_for_mime_type", "mime_type", "convert_file_message_to_component_message", "_file", "normalise_messages", "messages", "i", "normalise_tuples", "index", "role", "is_component_message", "is_last_bot_message", "all_messages", "is_bot", "last_index", "group_messages", "msg_format", "groupedMessages", "currentGroup", "currentRole", "load_components", "component_names", "_components", "load_component", "names", "components", "component_name", "name", "component", "get_components_from_messages", "switch_value", "ctx", "func", "dirty", "switch_instance_changes", "track", "type", "$$props", "value", "theme_mode", "props", "i18n", "upload", "_fetch", "div", "create_if_block", "button", "span0", "span1", "expanded", "title", "toggleExpanded", "ThumbDownActive", "ThumbDownDefault", "ThumbUpActive", "ThumbUpDefault", "iconbutton0_changes", "iconbutton1_changes", "handle_action", "selected", "$$invalidate", "Check", "Copy", "copied", "timer", "copy_feedback", "handle_copy", "textArea", "error", "onDestroy", "attr", "div_class_value", "current", "downloadlink_changes", "DownloadIcon", "Retry", "Undo", "create_if_block_5", "create_if_block_3", "create_if_block_2", "create_if_block_1", "is_all_text", "m", "all_text", "likeable", "show_retry", "show_undo", "show_copy_button", "show", "position", "avatar", "generating", "layout", "click_handler", "click_handler_1", "message_text", "show_copy", "show_download", "image_changes", "a", "a_href_value", "a_download_value", "set_data", "t", "t_value", "is_function", "markdown_changes", "if_block1", "button_aria_label_value", "get_message_label_data", "toggle_class", "if_block0", "create_if_block_6", "div0", "div1", "div1_class_value", "each_blocks", "avatar_img", "opposite_avatar_img", "bubble_full_width", "render_markdown", "latex_delimiters", "sanitize_html", "selectable", "rtl", "dispatch", "line_breaks", "show_like", "scroll", "handle_select", "button_panel_props", "e", "set_style", "div3", "copy_conversation", "conversation_value", "err", "constants_0", "child_ctx", "constants_1", "constants_2", "Community", "create_if_block_13", "iconbutton", "IconButton", "Trash", "create_if_block_12", "create_if_block_10", "create_if_block_4", "div_1", "span", "em", "t0", "t0_value", "create_if_block_9", "create_if_block_8", "img", "img_src_value", "Clear", "func_1", "message_changes", "ScrollDownArrow", "create_if_block_11", "div_1_class_value", "null_to_empty", "old_value", "is_browser", "update_components", "pending_message", "show_share_button", "show_copy_all_button", "avatar_images", "autoscroll", "placeholder", "examples", "_retryable", "_undoable", "like_user_message", "onMount", "show_scroll_button", "createEventDispatcher", "is_at_bottom", "scroll_to_bottom", "scroll_on_value_update", "tick", "handle_scroll", "image_preview_source", "image_preview_source_alt", "is_image_preview_open", "afterUpdate", "n", "handle_example_select", "example", "handle_like", "val_", "message_group", "first", "last", "formatted", "ShareError", "click_handler_2", "$$value", "dequal", "Cha<PERSON>", "blocklabel_changes", "chatbot_changes", "elem_id", "elem_classes", "visible", "scale", "min_width", "label", "show_label", "_selectable", "gradio", "_value", "loading_status", "height", "min_height", "max_height", "clear_status_handler", "args", "change_handler"], "mappings": "gpDAAAA,EAgBKC,EAAAC,EAAAC,CAAA,EALJC,EAGCF,EAAAG,CAAA,EACDD,EAAyDF,EAAAI,CAAA,2pCCf1DN,EAqCKC,EAAAC,EAAAC,CAAA,EA5BJC,EAMOF,EAAAG,CAAA,EACPD,EAMOF,EAAAI,CAAA,EACPF,EAMOF,EAAAK,CAAA,EACPH,EAMOF,EAAAM,CAAA,kcCpCRR,EAcKC,EAAAC,EAAAC,CAAA,EAPJC,EAMCF,EAAAO,CAAA,gGCCW,MAAAC,GAA0B,MACtCC,IAEe,MAAM,QAAQ,IAC5BA,EAAK,IAAI,MAAOC,GACR,MAAM,QAAQ,IACpBA,EAAa,IAAI,MAAOC,EAAS,IAAM,CACtC,GAAIA,IAAY,KAAa,MAAA,GACzB,IAAAC,EAAgB,IAAM,EAAI,KAAO,KACjCC,EAAe,GAEf,GAAA,OAAOF,GAAY,SAAU,CAChC,MAAMG,EAAgB,CACrB,MAAO,+BACP,MAAO,+BACP,MAAO,2DAAA,EAGOD,EAAAF,EAEf,OAAS,CAACI,EAAGC,CAAK,IAAK,OAAO,QAAQF,CAAa,EAAG,CACjD,IAAAG,EAEJ,MAAQA,EAAQD,EAAM,KAAKL,CAAO,KAAO,MAAM,CAC9C,MAAMO,EAAUD,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC7BE,EAAS,MAAMC,GAAoBF,CAAc,EACxCL,EAAAA,EAAa,QAAQK,EAASC,CAAM,CACpD,CACD,CAAA,KACM,CACN,GAAI,CAACR,GAAS,IAAY,MAAA,GAC1B,MAAMU,EAAW,MAAMD,GAAoBT,EAAQ,GAAU,EACzDA,EAAQ,WAAW,SAAS,OAAO,EACtCE,EAAe,wBAAwBQ,CAAQ,aACrCV,EAAQ,WAAW,SAAS,OAAO,EAC9BE,EAAAQ,EACLV,EAAQ,WAAW,SAAS,OAAO,IAC7CE,EAAe,aAAaQ,CAAQ,OAEtC,CAEO,MAAA,GAAGT,CAAa,KAAKC,CAAY,EAAA,CACxC,CAAA,CAEF,CAAA,GAGA,IAAKH,GACLA,EAAa,KACZA,EAAa,CAAC,IAAM,IAAMA,EAAa,CAAC,IAAM,GAAK;AAAA,EAAO,EAC3D,CAAA,EAEA,KAAK;AAAA,CAAI,EAQNY,GAAmB,CAACC,EAAaC,IACtCD,EAAI,QAAQ,aAAc,QAAQC,CAAI,MAAM,EAE7C,SAASC,GACRC,EACS,CACT,OAAKA,EACDA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACjC,OAJgB,MAKxB,CAEA,SAASC,GACRhB,EACgB,CACV,MAAAiB,EAAQ,MAAM,QAAQjB,EAAQ,IAAI,EAAIA,EAAQ,KAAK,CAAC,EAAIA,EAAQ,KAC/D,MAAA,CACN,UAAWc,GAA4BG,GAAO,SAAS,EACvD,MAAOjB,EAAQ,KACf,SAAUA,EAAQ,SAClB,iBAAkB,CAAC,EACnB,MAAO,CAAC,CAAA,CAEV,CAEgB,SAAAkB,GACfC,EACAN,EAC6B,CAC7B,OAAIM,IAAa,KAAaA,EACvBA,EAAS,IAAI,CAACnB,EAASoB,IACzB,OAAOpB,EAAQ,SAAY,SACvB,CACN,KAAMA,EAAQ,KACd,SAAUA,EAAQ,SAClB,QAASW,GAAiBX,EAAQ,QAASa,CAAI,EAC/C,KAAM,OACN,MAAOO,CAAA,EAEE,SAAUpB,EAAQ,QACrB,CACN,QAASgB,GAA0ChB,EAAQ,OAAO,EAClE,SAAUA,EAAQ,SAClB,KAAMA,EAAQ,KACd,KAAM,YACN,MAAOoB,CAAA,EAGF,CAAE,KAAM,YAAa,GAAGpB,CAAQ,CACvC,CACF,CAEgB,SAAAqB,GACfF,EACAN,EAC6B,CAC7B,OAAIM,IAAa,KAAaA,EAClBA,EAAS,QAAQ,CAACpB,EAAc,IACpCA,EAAa,IAAI,CAACC,EAASsB,IAAU,CAC3C,GAAItB,GAAW,KAAa,OAAA,KACtB,MAAAuB,EAAOD,GAAS,EAAI,OAAS,YAE/B,OAAA,OAAOtB,GAAY,SACf,CACN,KAAAuB,EACA,KAAM,OACN,QAASZ,GAAiBX,EAASa,CAAI,EACvC,SAAU,CAAE,MAAO,IAAK,EACxB,MAAO,CAAC,EAAGS,CAAK,CAAA,EAId,SAAUtB,EACN,CACN,QAASgB,GAA0ChB,CAAO,EAC1D,KAAAuB,EACA,KAAM,YACN,MAAO,CAAC,EAAGD,CAAK,CAAA,EAIX,CACN,KAAAC,EACA,QAASvB,EACT,KAAM,YACN,MAAO,CAAC,EAAGsB,CAAK,CAAA,CACjB,CACA,CACD,EACU,OAAQtB,GAAYA,GAAW,IAAI,CAC/C,CAEO,SAASwB,GACfxB,EAC8B,CAC9B,OAAOA,EAAQ,OAAS,WACzB,CAEgB,SAAAyB,GACfN,EACAO,EACU,CACV,MAAMC,EAASR,EAASA,EAAS,OAAS,CAAC,EAAE,OAAS,YAChDS,EAAaT,EAASA,EAAS,OAAS,CAAC,EAAE,MAMjD,OAFC,KAAK,UAAUS,CAAU,IACzB,KAAK,UAAUF,EAAaA,EAAa,OAAS,CAAC,EAAE,KAAK,GACzCC,CACnB,CAEgB,SAAAE,GACfV,EACAW,EACwB,CACxB,MAAMC,EAAyC,CAAA,EAC/C,IAAIC,EAAoC,CAAA,EACpCC,EAAkC,KAEtC,UAAWjC,KAAWmB,EACjBW,IAAe,WACJG,EAAA,OAGTjC,EAAQ,OAAS,aAAeA,EAAQ,OAAS,UAGnDA,EAAQ,OAASiC,EACpBD,EAAa,KAAKhC,CAAO,GAErBgC,EAAa,OAAS,GACzBD,EAAgB,KAAKC,CAAY,EAElCA,EAAe,CAAChC,CAAO,EACvBiC,EAAcjC,EAAQ,OAIpB,OAAAgC,EAAa,OAAS,GACzBD,EAAgB,KAAKC,CAAY,EAG3BD,CACR,CAEsB,eAAAG,GACrBC,EACAC,EACAC,EAC0D,CAC1D,IAAIC,EAAkB,CAAA,EAClBC,EAA+D,CAAA,EAEnD,OAAAJ,EAAA,QAASK,GAAmB,CAC3C,GAAIJ,EAAYI,CAAc,GAAKA,IAAmB,OACrD,OAGD,KAAM,CAAE,KAAAC,EAAM,UAAAC,CAAA,EAAcL,EAAeG,EAAgB,MAAM,EACjEF,EAAM,KAAKG,CAAI,EACfF,EAAW,KAAKG,CAAS,CACzB,CACA,GAE4C,MAAM,QAAQ,IAAIH,CAAU,GACvD,QAAQ,CAACG,EAAWtB,IAAM,CAC3CgB,EAAYE,EAAMlB,CAAC,CAAC,EAAIsB,EAAU,OAAA,CAClC,EAEMN,CACR,CAEO,SAASO,GACfxB,EACW,CACX,GAAI,CAACA,EAAU,MAAO,GAClB,IAAAoB,MAA8B,IACzB,OAAApB,EAAA,QAASnB,GAAY,CACzBA,EAAQ,OAAS,aACTuC,EAAA,IAAIvC,EAAQ,QAAQ,SAAS,CACzC,CACA,EACM,MAAM,KAAKuC,CAAU,CAC7B,0BCvLQ,IAAAK,EAAAC,KAAWA,EAAI,CAAA,CAAA,sDAET,2CAEO,qBAET,SAAQC,EAAA,yHANZ,GAAAC,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,iWAVf,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,sDAET,8CAEU,oIAJhB,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,iWAdf,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,yCACX,SACHA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,aACf,qBACO,8CAGG,iKAPhB,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,kMAEdA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,wNAfrB,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,sDAET,qBACO,oFAKG,0HARhB,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,iWAXf,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,uEAIN,cAAAA,KAAM,6CAEA,0HANf,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,uPAINE,EAAA,KAAAC,EAAA,cAAAH,KAAM,oJAlBf,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,sDAET,gDAIG,eACF,+BAEC,wHATR,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,0aAgDrB1D,EAAwBC,EAAA6D,EAAA3D,CAAA,kGAlDrB,OAAAuD,OAAS,UAAS,EAcbA,OAAS,OAAM,EAWfA,OAAS,QAAO,EAahBA,OAAS,QAAO,EAchBA,OAAS,QAAO,EAUhBA,OAAS,OAAM,iXAzEb,GAAA,CAAA,KAAAK,CAAA,EAAAC,EACA,CAAA,WAAAZ,CAAA,EAAAY,EACA,CAAA,MAAAC,CAAA,EAAAD,EACA,CAAA,OAAA/D,CAAA,EAAA+D,EACA,CAAA,WAAAE,CAAA,EAAAF,EACA,CAAA,MAAAG,CAAA,EAAAH,EACA,CAAA,KAAAI,CAAA,EAAAJ,EACA,CAAA,OAAAK,CAAA,EAAAL,EACA,CAAA,OAAAM,CAAA,EAAAN,y9CCWVhE,EAEKC,EAAAsE,EAAApE,CAAA,8MAHDuD,EAAQ,CAAA,GAAAc,GAAAd,CAAA,uDARcA,EAAK,CAAA,CAAA,mJAEbA,EAAQ,CAAA,EAAG,YAAc,eAAe,gFAJ5D1D,EAeQC,EAAAwE,EAAAtE,CAAA,EAdPC,EAQKqE,EAAAF,CAAA,EAPJnE,EAAsCmE,EAAAG,CAAA,gBACtCtE,EAKMmE,EAAAI,CAAA,gDARsBjB,EAAc,CAAA,CAAA,kCAEhBA,EAAK,CAAA,CAAA,wBAEbA,EAAQ,CAAA,EAAG,YAAc,eAAe,EAMtDA,EAAQ,CAAA,0NAlBF,SAAAkB,EAAW,EAAA,EAAAZ,EACX,CAAA,MAAAa,CAAA,EAAAb,EAEF,SAAAc,GAAA,KACRF,EAAY,CAAAA,CAAA,klCCLd5E,EAWKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAO,CAAA,qqCCVFT,EAWKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAO,CAAA,+zBCVFT,EAWKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAO,CAAA,iqCCVFT,EAWKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAO,CAAA,mJCGK,KAAAiD,OAAa,UAAYqB,GAAkBC,GAC1C,MAAAtB,OAAa,UAAY,kBAAoB,UAC7C,MAAAA,OAAa,UACjB,sBACA,wEAQG,KAAAA,OAAa,OAASuB,GAAgBC,GACrC,MAAAxB,EAAa,CAAA,IAAA,OAAS,eAAiB,OACvC,MAAAA,OAAa,OACjB,sBACA,iKAhBGE,EAAA,IAAAuB,EAAA,KAAAzB,OAAa,UAAYqB,GAAkBC,IAC1CpB,EAAA,IAAAuB,EAAA,MAAAzB,OAAa,UAAY,kBAAoB,WAC7CE,EAAA,IAAAuB,EAAA,MAAAzB,OAAa,UACjB,sBACA,sDAQGE,EAAA,IAAAwB,EAAA,KAAA1B,OAAa,OAASuB,GAAgBC,IACrCtB,EAAA,IAAAwB,EAAA,MAAA1B,EAAa,CAAA,IAAA,OAAS,eAAiB,QACvCE,EAAA,IAAAwB,EAAA,MAAA1B,OAAa,OACjB,sBACA,mMAtBQ,GAAA,CAAA,cAAA2B,CAAA,EAAArB,EAEPsB,EAAsC,kBAUzCC,EAAA,EAAAD,EAAW,SAAS,EACpBD,EAAcC,CAAQ,UAWtBC,EAAA,EAAAD,EAAW,MAAM,EACjBD,EAAcC,CAAQ,kTCiBhB5B,EAAM,CAAA,EAAG,iBAAmB,oBAC7BA,EAAM,CAAA,EAAG8B,GAAQC,oBAFb/B,EAAW,CAAA,CAAA,iFACdA,EAAM,CAAA,EAAG,iBAAmB,6BAC7BA,EAAM,CAAA,EAAG8B,GAAQC,qHA7CnBC,EAAS,GACF,CAAA,MAAAzB,CAAA,EAAAD,EACP2B,EAEK,SAAAC,GAAA,KACRF,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPD,EAAS,EAAA,GACP,KAGW,eAAAG,GAAA,IACV,cAAe,gBACZ,UAAU,UAAU,UAAU5B,CAAK,EACzC2B,eAEME,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQ7B,EAEjB6B,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEtB,SAAS,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAA,MAGR,SAAS,YAAY,MAAM,EAC3BF,GACQ,OAAAG,EAAA,CACR,QAAQ,MAAMA,CAAK,UAEnBD,EAAS,OAAA,IAKZ,OAAAE,GAAA,IAAA,CACKL,GAAO,aAAaA,CAAK,i3EC3C/B3F,EAeKC,EAAAC,EAAAC,CAAA,EARJC,EAGCF,EAAAG,CAAA,EACDD,EAGCF,EAAAI,CAAA,6JCyC8B,8EAHN2F,EAAA1B,EAAA,QAAA2B,EAAA,mBAAAxC,SAAWA,EAAM,CAAA,EAAA,qBAAmBA,EAC3D,CAAA,IAAA,MAAQ,eAAa,iBAAA,UAFvB1D,EAoCKC,EAAAsE,EAAApE,CAAA,sFAnCoB,CAAAgG,GAAAvC,EAAA,KAAAsC,KAAAA,EAAA,mBAAAxC,SAAWA,EAAM,CAAA,EAAA,qBAAmBA,EAC3D,CAAA,IAAA,MAAQ,eAAa,+KAIPA,EAAY,EAAA,CAAA,oFAAZA,EAAY,EAAA,4IAIlB,KAAAA,EAAS,CAAA,GAAA,SAAS,MAAM,IACpB,SAAAA,KAAQ,QAAQ,MAAM,WAAa,iHADvCE,EAAA,KAAAwC,EAAA,KAAA1C,EAAS,CAAA,GAAA,SAAS,MAAM,KACpBE,EAAA,KAAAwC,EAAA,SAAA1C,KAAQ,QAAQ,MAAM,WAAa,4LAE3B2C,EAAY,CAAA,CAAA,sLAKxBC,0BAGI5C,EAAU,CAAA,4GAAVA,EAAU,CAAA,8JAMd6C,YAEI7C,EAAU,CAAA,4GAAVA,EAAU,CAAA,gXArBjBA,EAAa,EAAA,GAAA,CAAK,MAAM,QAAQA,EAAO,CAAA,CAAA,GAAKrB,GAAqBqB,EAAO,CAAA,CAAA,cAHxEA,EAAS,EAAA,GAAA8C,GAAA9C,CAAA,eAWTA,EAAU,CAAA,GAAA+C,GAAA/C,CAAA,IAQVA,EAAS,CAAA,GAAAgD,GAAAhD,CAAA,IAQTA,EAAQ,CAAA,GAAAiD,GAAAjD,CAAA,sNA3BRA,EAAS,EAAA,uHAGTA,EAAa,EAAA,GAAA,CAAK,MAAM,QAAQA,EAAO,CAAA,CAAA,GAAKrB,GAAqBqB,EAAO,CAAA,CAAA,+GAQxEA,EAAU,CAAA,yGAQVA,EAAS,CAAA,yGAQTA,EAAQ,CAAA,uSAjCXA,EAAI,CAAA,GAAAc,GAAAd,CAAA,wEAAJA,EAAI,CAAA,iLA1BCkD,GACR/F,EAAAA,CAGE,OAAA,MAAM,QAAQA,CAAO,GACrBA,EAAQ,MAAOgG,GAAa,OAAAA,EAAE,SAAY,QAAQ,IACjD,MAAM,QAAQhG,CAAO,GAAA,OAAYA,EAAQ,SAAY,kBAIhDiG,GAASjG,EAAAA,CACb,OAAA,MAAM,QAAQA,CAAO,EACjBA,EAAQ,IAAKgG,GAAMA,EAAE,OAAO,EAAE,KAAK;AAAA,CAAI,EAExChG,EAAQ,qCA3BL,CAAA,SAAAkG,CAAA,EAAA/C,EACA,CAAA,WAAAgD,CAAA,EAAAhD,EACA,CAAA,UAAAiD,CAAA,EAAAjD,EACA,CAAA,iBAAAkD,CAAA,EAAAlD,EACA,CAAA,KAAAmD,CAAA,EAAAnD,EACA,CAAA,QAAAnD,CAAA,EAAAmD,EACA,CAAA,SAAAoD,CAAA,EAAApD,EACA,CAAA,OAAAqD,CAAA,EAAArD,EACA,CAAA,WAAAsD,CAAA,EAAAtD,EAEA,CAAA,cAAAqB,CAAA,EAAArB,EACA,CAAA,OAAAuD,CAAA,EAAAvD,EAiDS,MAAAwD,EAAA,IAAAnC,EAAc,OAAO,EAQrBoC,EAAA,IAAApC,EAAc,MAAM,+bAtCxCE,EAAA,GAAGmC,EAAed,GAAY/F,CAAO,EAAIiG,GAASjG,CAAO,EAAI,EAAA,mBAE7D0E,EAAA,GAAGoC,EAAYT,GAAoBrG,GAAW+F,GAAY/F,CAAO,CAAA,iBAC9D0E,EAAA,GAAAqC,EAAA,CACD,MAAM,QAAQ/G,CAAO,GACtBwB,GAAqBxB,CAAO,GAC5BA,EAAQ,QAAQ,OAAO,GAAA,gxCC8DW,IAAA6C,MAAY,QAAWA,EAAI,CAAA,EAAA,mGAD7D1D,EAEKC,EAAAsE,EAAApE,CAAA,sCAD6ByD,EAAA,CAAA,EAAA,IAAAiE,EAAA,IAAAnE,MAAY,oBAAWA,EAAI,CAAA,EAAA,mIA2FvDA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,QAAM,4GAVDuC,EAAA6B,EAAA,OAAAC,EAAArE,EAAQ,EAAA,EAAA,QAAQ,MAAM,GAAG,yBAErBuC,EAAA6B,EAAA,WAAAE,EAAA,OAAO,aACd,KACAtE,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,MAAM,UATT1D,EAcGC,EAAA6H,EAAA3H,CAAA,kCAHDuD,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,OACvC,QAAM,KAAAuE,GAAAC,EAAAC,CAAA,EAVDvE,EAAA,CAAA,EAAA,IAAAmE,KAAAA,EAAArE,EAAQ,EAAA,EAAA,QAAQ,MAAM,oBAElBE,EAAA,CAAA,EAAA,IAAAoE,KAAAA,EAAA,OAAO,aACd,KACAtE,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,sIAnBKA,EAAO,EAAA,EAAC,QAAQ,WACjBA,EAAO,EAAA,EAAC,QAAQ,qBACVA,EAAW,EAAA,QAChBA,EAAO,EAAA,EAAC,QAAQ,kNAHhBA,EAAO,EAAA,EAAC,QAAQ,wBACjBA,EAAO,EAAA,EAAC,QAAQ,sCACVA,EAAW,EAAA,qBAChBA,EAAO,EAAA,EAAC,QAAQ,2PAjCnBA,EAAO,EAAA,EAAC,SAAS,MAAK,qVAiBhB,QAAAA,MAAQ,8HAKR0E,GAAA1E,QAAAA,EAAM,EAAA,EAAA,MAAA,KAAA,SAAA,uEALNE,EAAA,CAAA,EAAA,KAAAyE,EAAA,QAAA3E,MAAQ,8TAfVA,EAAO,EAAA,EAAC,SAAS,eACdpB,GAAmB,CAAEoB,EAAO,EAAA,CAAA,EAAGA,EAAK,CAAA,CAAA,6HADvCA,EAAO,EAAA,EAAC,SAAS,4BACdpB,GAAmB,CAAEoB,EAAO,EAAA,CAAA,EAAGA,EAAK,CAAA,CAAA,+LAGpC,QAAAA,MAAQ,8HAKR0E,GAAA1E,QAAAA,EAAM,EAAA,EAAA,MAAA,KAAA,SAAA,uEALNE,EAAA,CAAA,EAAA,KAAAyE,EAAA,QAAA3E,MAAQ,ySAsDLA,EAAkB,EAAA,CAAA,iKAAlBA,EAAkB,EAAA,CAAA,CAAA,CAAA,yLA7D7BA,EAAO,EAAA,EAAC,OAAS,OAAM,EA2BlBA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,aAAaA,EAAW,EAAA,EAAA,EAaxEA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,YAAc,OAAM,4GAoB3E,IAAA4E,EAAA5E,OAAW,SAAOiD,GAAAjD,CAAA,sFA5ERA,EAAI,CAAA,CAAA,cAaZA,EAAG,EAAA,EAAG,MAAQ,KAAK,EACZuC,EAAAxB,EAAA,aAAA8D,EAAA7E,EAAO,CAAA,EAAA,eAAiB8E,GAAuB9E,EAAO,EAAA,CAAA,CAAA,8BAbpD+E,EAAAhE,EAAA,SAAAf,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mCACFA,EAAe,CAAA,CAAA,mEAGnCA,EAAU,EAAA,EAAG,UAAY,SAAS,oBAC9BA,EAAG,EAAA,EAAG,QAAU,MAAM,2BAnBzBA,EAAI,CAAA,EAAA,KAAGrB,GAAqBqB,EAAO,EAAA,CAAA,EAChDA,EAAO,EAAA,GAAE,QAAQ,UACjB,IAAE,eAAA,oBACcA,EAAM,CAAA,IAAK,UAAQ,CAAKA,EAAiB,CAAA,CAAA,yBACpC,EAAI,mCACMA,EAAe,CAAA,CAAA,kBAEhCA,EAAO,EAAA,EAAC,OAAS,WAAW,aACjCrB,GAAqBqB,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,EACtB+E,EAAAlE,EAAA,UAAAb,MAAgB,CAAC,oBAJdA,EAAG,EAAA,GAAIA,EAAI,CAAA,IAAK,OAAS,OAAS,OAAO,UAP5D1D,EAwFKC,EAAAsE,EAAApE,CAAA,EA3EJC,EA0EQmE,EAAAE,CAAA,4SAzEMf,EAAI,CAAA,CAAA,yBAaZA,EAAG,EAAA,EAAG,MAAQ,uBACP,CAAAyC,GAAAvC,EAAA,CAAA,EAAA,IAAA2E,KAAAA,EAAA7E,EAAO,CAAA,EAAA,eAAiB8E,GAAuB9E,EAAO,EAAA,CAAA,6CAbpD+E,EAAAhE,EAAA,SAAAf,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mDACFA,EAAe,CAAA,CAAA,oEAGnCA,EAAU,EAAA,EAAG,UAAY,SAAS,+BAC9BA,EAAG,EAAA,EAAG,QAAU,MAAM,kCAnBzBA,EAAI,CAAA,EAAA,KAAGrB,GAAqBqB,EAAO,EAAA,CAAA,EAChDA,EAAO,EAAA,GAAE,QAAQ,UACjB,IAAE,oEACcA,EAAM,CAAA,IAAK,UAAQ,CAAKA,EAAiB,CAAA,CAAA,wCACpC,EAAI,mDACMA,EAAe,CAAA,CAAA,iCAEhCA,EAAO,EAAA,EAAC,OAAS,WAAW,4BACjCrB,GAAqBqB,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,iBACtB+E,EAAAlE,EAAA,UAAAb,MAAgB,CAAC,+BAJdA,EAAG,EAAA,GAAIA,EAAI,CAAA,IAAK,OAAS,OAAS,OAAO,EAmFvDA,OAAW,gQAQDA,EAAkB,EAAA,CAAA,iKAAlBA,EAAkB,EAAA,CAAA,CAAA,CAAA,gIA7G9BgF,EAAAhF,OAAe,MAAIiF,GAAAjF,CAAA,OAUhBA,EAAQ,CAAA,CAAA,uBAAb,OAAIzB,GAAA,4DAkGH,IAAAqG,EAAA5E,OAAW,UAAQc,GAAAd,CAAA,mKApGA+E,EAAAG,EAAA,iBAAAlF,EAAS,CAAA,EAAA,CAAC,EAAE,OAAS,WAAW,EAZnCuC,EAAA4C,EAAA,QAAAC,EAAA,eAAApF,SAASA,EAAI,CAAA,EAAA,mBAAA,EACd+E,EAAAI,EAAA,cAAAnF,OAAe,IAAI,EACV+E,EAAAI,EAAA,uBAAAnF,OAAwB,IAAI,UAHzD1D,EA+GKC,EAAA4I,EAAA1I,CAAA,wBArGJC,EAoGKyI,EAAAD,CAAA,iGAzGAlF,OAAe,wHAUZA,EAAQ,CAAA,CAAA,oBAAb,OAAIzB,GAAA,EAAA,2GAAJ,OAAIA,EAAA8G,EAAA,OAAA9G,GAAA,yDAFgBwG,EAAAG,EAAA,iBAAAlF,EAAS,CAAA,EAAA,CAAC,EAAE,OAAS,WAAW,GAZnC,CAAAyC,GAAAvC,EAAA,CAAA,EAAA,IAAAkF,KAAAA,EAAA,eAAApF,SAASA,EAAI,CAAA,EAAA,qDACd+E,EAAAI,EAAA,cAAAnF,OAAe,IAAI,iBACV+E,EAAAI,EAAA,uBAAAnF,OAAwB,IAAI,EA8GpDA,OAAW,sJAlGZ,OAAIzB,GAAA,kKA/DEuG,GAAuB3H,EAAA,CAC3B,OAAAA,EAAQ,OAAS,OACbA,EAAQ,QAEfA,EAAQ,OAAS,aACjBA,EAAQ,QAAQ,YAAc,OAE1B,MAAM,QAAQA,EAAQ,QAAQ,KAAK,EACJ,2BAAAA,EAAQ,QAAQ,MAAM,CAAC,EAAE,WAAW,MAAM,GAAG,EAAE,IAAA,CAAA,8BAGtDA,EAAQ,QAAQ,OAAO,WAAW,MAAM,GAAG,EAAE,IACvE,CAAA,IAAAA,EAAQ,QAAQ,OAAO,WAAa,2BAGTA,EAAQ,QAAQ,WAAa,SAAS,sBAzD1D,GAAA,CAAA,MAAAoD,CAAA,EAAAD,EACA,CAAA,WAAAgF,CAAA,EAAAhF,GACA,oBAAAiF,EAAuC,IAAA,EAAAjF,GACvC,KAAA5B,EAAO,MAAA,EAAA4B,EACP,CAAA,SAAAhC,EAAA,EAAA,EAAAgC,EACA,CAAA,OAAAuD,CAAA,EAAAvD,EACA,CAAA,kBAAAkF,CAAA,EAAAlF,EACA,CAAA,gBAAAmF,CAAA,EAAAnF,EACA,CAAA,iBAAAoF,CAAA,EAAApF,EAKA,CAAA,cAAAqF,CAAA,EAAArF,EACA,CAAA,WAAAsF,CAAA,EAAAtF,EACA,CAAA,OAAAM,CAAA,EAAAN,EACA,CAAA,IAAAuF,CAAA,EAAAvF,EACA,CAAA,SAAAwF,CAAA,EAAAxF,EACA,CAAA,KAAAI,CAAA,EAAAJ,EACA,CAAA,YAAAyF,CAAA,EAAAzF,EACA,CAAA,OAAAK,CAAA,EAAAL,EACA,CAAA,OAAA/D,CAAA,EAAA+D,EACA,CAAA,KAAAtC,CAAA,EAAAsC,EACA,CAAA,WAAAE,EAAA,EAAAF,EACA,CAAA,YAAAf,EAAA,EAAAe,EACA,CAAA,EAAA/B,EAAA,EAAA+B,EACA,CAAA,iBAAAkD,CAAA,EAAAlD,EACA,CAAA,WAAAsD,EAAA,EAAAtD,EACA,CAAA,UAAA0F,EAAA,EAAA1F,EACA,CAAA,WAAAgD,CAAA,EAAAhD,EACA,CAAA,UAAAiD,EAAA,EAAAjD,EACA,CAAA,WAAArB,EAAA,EAAAqB,EACA,CAAA,cAAAqB,CAAA,EAAArB,EACA,CAAA,OAAA2F,EAAA,EAAA3F,EAEF,SAAA4F,GAAc3H,EAAWpB,EAAA,CACjC2I,EAAS,SAAA,CACR,MAAO3I,EAAQ,MACf,MAAOA,EAAQ,UAoCb,IAAAgJ,gBAoGiBF,WA/CDC,GAAc3H,GAAGpB,CAAO,QAC3BiJ,IAAC,CACTA,EAAE,MAAQ,SACbF,GAAc3H,GAAGpB,CAAO,krCAvD3B0E,EAAA,GAAAsE,GAAA,CACF,KAAMH,IAAa1C,GAAcC,IAAaC,EAC9C,cAAA7B,EACA,SAAUqE,GACV,WAAA1C,EACA,UAAAC,GACA,WAAAK,GACA,iBAAAJ,EACA,QAASvE,KAAe,SAAWX,EAAS,CAAC,EAAIA,EACjD,SAAUI,IAAS,OAAS,QAAU,OACtC,OAAQ4G,EACR,OAAAzB;;;;oLCzFoBwC,GAAAC,EAAA,gBAAAtG,OAAW,SAAW,oBAAsB,MAAM,UALxE1D,EAaKC,EAAA+J,EAAA7J,CAAA,iBARiB4J,GAAAC,EAAA,gBAAAtG,OAAW,SAAW,oBAAsB,MAAM,iDAR5D,OAAA6D,EAAS,QAAA,EAAAvD,gQCgDdN,EAAM,CAAA,EAAG8B,GAAQC,SAEhB/B,EAAM,CAAA,EAAG,sBAAwB,qCAD9BA,EAAW,CAAA,CAAA,gFADfA,EAAM,CAAA,EAAG8B,GAAQC,kBAEhB/B,EAAM,CAAA,EAAG,sBAAwB,sIA7CpCgC,EAAS,GACF,CAAA,MAAAzB,CAAA,EAAAD,EAEP2B,EAEK,SAAAC,GAAA,KACRF,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPD,EAAS,EAAA,GACP,KAGE,MAAAuE,EAAA,IAAA,CACD,GAAAhG,EAAA,CACG,MAAAiG,EAAqBjG,EACzB,IAAKpD,GACDA,EAAQ,OAAS,UACVA,EAAQ,IAAI,KAAKA,EAAQ,OAAO,GAEjC,GAAAA,EAAQ,IAAI,KAAKA,EAAQ,QAAQ,MAAM,GAAG,EAEpD,EAAA,KAAK;AAAA;AAAA,CAAM,EAEb,UAAU,UAAU,UAAUqJ,CAAkB,EAAE,MAAOC,GAAA,CACxD,QAAQ,MAAM,gCAAiCA,CAAG,MAKtC,eAAAtE,GAAA,CACV,cAAe,YAClBoE,IACArE,KAIF,OAAAI,GAAA,IAAA,CACKL,GAAO,aAAaA,CAAK,8TC8OZ,MAAAyE,EAAAC,EAAS,EAAA,EAAA,CAAC,EAAE,OAAS,OAAS,OAAS,cACjC,MAAAC,EAAAD,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,UAC5B,MAAAE,EAAAF,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,sWAtC7DG,krBAFH9G,EAAiB,CAAA,GAAA+G,GAAA/G,CAAA,EAoBJgH,EAAA,IAAAC,GAAA,CAAA,MAAA,CAAA,KAAAC,SAAiD,OAAO,CAAA,CAAA,6BAErElH,EAAoB,CAAA,GAAAmH,GAAAnH,CAAA,mJAtBpBA,EAAiB,CAAA,8GAsBjBA,EAAoB,CAAA,mRAsEnBgF,EAAAhF,QAAgB,MAAIoH,GAAApH,CAAA,EAKpB4E,EAAA5E,QAAa,MAAIqH,GAAArH,CAAA,yGANvB1D,EAgDKC,EAAA+K,EAAA7K,CAAA,oDA/CCuD,QAAgB,yGAKhBA,QAAa,gOA5DXA,EAAe,EAAA,CAAA,uBAApB,OAAIzB,GAAA,kEAiDDyB,EAAe,CAAA,GAAAgD,GAAAhD,CAAA,8HAlDrB1D,EAqDKC,EAAA+K,EAAA7K,CAAA,uJApDGuD,EAAe,EAAA,CAAA,oBAApB,OAAIzB,GAAA,EAAA,wGAAJ,OAAIA,EAAA8G,EAAA,OAAA9G,GAAA,WAiDDyB,EAAe,CAAA,iIAjDlB,OAAIzB,GAAA,gMAyDeyB,EAAW,EAAA,wHAD/B1D,EAEKC,EAAA+K,EAAA7K,CAAA,8DADeuD,EAAW,EAAA,+LAKvBA,EAAQ,EAAA,CAAA,uBAAb,OAAIzB,GAAA,qKADPjC,EAuCKC,EAAA+K,EAAA7K,CAAA,+FAtCGuD,EAAQ,EAAA,CAAA,oBAAb,OAAIzB,GAAA,EAAA,2GAAJ,OAAIA,EAAA8G,EAAA,OAAA9G,GAAA,yCAAJ,OAAIA,GAAA,qLASIyB,EAAO,EAAA,EAAC,KAAK,uHAHpB1D,EAMKC,EAAA+K,EAAA7K,CAAA,2DAHEuD,EAAO,EAAA,EAAC,KAAK,0HAQQyE,EAAAzE,MAAQ,KAAI,sFAAxC1D,EAA+CC,EAAAgL,EAAA9K,CAAA,iBAAnByD,EAAA,CAAA,EAAA,SAAAuE,KAAAA,EAAAzE,MAAQ,KAAI,KAAAuE,GAAAC,EAAAC,CAAA,uCAFJA,EAAAzE,MAAQ,aAAY,8FAAxD1D,EAA+DC,EAAAgL,EAAA9K,CAAA,iBAA3ByD,EAAA,CAAA,EAAA,SAAAuE,KAAAA,EAAAzE,MAAQ,aAAY,KAAAuE,GAAAC,EAAAC,CAAA,yCAkBjDA,EAAAzE,EAAQ,EAAA,EAAA,MAAM,CAAC,EAAE,UAAS,gGADjC1D,EAEAC,EAAAgL,EAAA9K,CAAA,EADEC,EAAqC6K,EAAAC,CAAA,iBAAhCtH,EAAA,CAAA,EAAA,SAAAuE,KAAAA,EAAAzE,EAAQ,EAAA,EAAA,MAAM,CAAC,EAAE,UAAS,KAAAuE,GAAAC,EAAAC,CAAA,iGAN1B,IAAAzE,EAAQ,EAAA,EAAA,MAAM,CAAC,EAAE,yHAHxB1D,EAMKC,EAAA+K,EAAA7K,CAAA,sCAHEyD,EAAA,CAAA,EAAA,UAAAiE,EAAA,IAAAnE,EAAQ,EAAA,EAAA,MAAM,CAAC,EAAE,8HANjBA,EAAO,EAAA,EAAC,MAAM,OAAM,qDAAC,QAAM,oDADlC1D,EAEAC,EAAAgL,EAAA9K,CAAA,EADEC,EAAqC6K,EAAAC,CAAA,6CAAhCxH,EAAO,EAAA,EAAC,MAAM,OAAM,KAAAuE,GAAAkD,EAAAC,CAAA,mEAhBvB1H,EAAO,EAAA,EAAC,OAAS,QAAS2H,GAAA3H,CAAA,yBAS1BA,EAAO,EAAA,EAAC,eAAiB,OAAS4H,8FAKlC5H,EAAO,EAAA,EAAC,QAAU,QAAaA,EAAO,EAAA,EAAC,MAAM,OAAS,EAAC,kBAIlDA,EAAO,EAAA,EAAC,QAAU,QAAaA,EAAO,EAAA,EAAC,MAAM,CAAC,IAAM,QAAaA,EAAQ,EAAA,EAAA,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,QAQ7GA,EAAO,EAAA,EAAC,QAAU,QAAaA,EAAO,EAAA,EAAC,MAAM,CAAC,IAAM,OAAS,oMA9BxE1D,EAmCQC,EAAAwE,EAAAtE,CAAA,kHA/BFuD,EAAO,EAAA,EAAC,OAAS,kkBA7DbA,EAAoB,EAAA,CAAA,GAAAuC,EAAAsF,EAAA,MAAAC,CAAA,YAAO9H,EAAwB,EAAA,CAAA,qDAD9D1D,EASKC,EAAA+K,EAAA7K,CAAA,EARJC,EAAgE4K,EAAAO,CAAA,2DAAtD7H,EAAoB,EAAA,CAAA,yCAAOA,EAAwB,EAAA,CAAA,+KAGrD,OAAAgH,EAAA,IAAAC,GAAA,CAAA,MAAA,CAAA,KAAAc,SAEC,OAAO,CAAA,CAAA,kLAPb/H,EAAqB,EAAA,GAAA+C,GAAA/C,CAAA,icAqCd,UAAAA,QAAS,OAASA,MAAYA,EAAiB,EAAA,EAAGA,EAAQ,CAAA,EACzD,WAAAA,EAAc,EAAA,GAAApB,GAAoBoB,MAAUA,EAAK,CAAA,CAAA,EAClD,UAAAA,EAAa,EAAA,GAAApB,GAAoBoB,MAAUA,EAAK,CAAA,CAAA,yCAGnD,OAAAA,MAAa,OAAMgI,kGA1CvBhI,EAAqB,EAAA,szBAqCdE,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,IAAA+H,EAAA,UAAAjI,QAAS,OAASA,MAAYA,EAAiB,EAAA,EAAGA,EAAQ,CAAA,GACzDE,EAAA,CAAA,EAAA,QAAAA,EAAA,CAAA,EAAA,IAAA+H,EAAA,WAAAjI,EAAc,EAAA,GAAApB,GAAoBoB,MAAUA,EAAK,CAAA,CAAA,GAClDE,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,IAAA+H,EAAA,UAAAjI,EAAa,EAAA,GAAApB,GAAoBoB,MAAUA,EAAK,CAAA,CAAA,ueAkEvDkI,qDAGIlI,EAAgB,EAAA,CAAA,qGAL5B1D,EAOKC,EAAA+K,EAAA7K,CAAA,gJAzJDuI,EAAAhF,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,GAACmI,GAAAnI,CAAA,8CAqCjCA,EAAK,CAAA,IAAK,MAAQA,EAAK,CAAA,EAAC,OAAS,GAAKA,EAAe,EAAA,IAAK,KAAI,gCA4G/DA,EAAkB,EAAA,GAAAc,GAAAd,CAAA,kEAlHfuC,EAAA+E,EAAA,QAAAc,EAAAC,GAAArI,QAAW,SAAW,cAAgB,YAAY,EAAA,gBAAA,oHAD1D1D,EAiHKC,EAAA+K,EAAA7K,CAAA,qEA/IAuD,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,gPA+B9B,CAAAyC,GAAAvC,EAAA,CAAA,EAAA,QAAAkI,KAAAA,EAAAC,GAAArI,QAAW,SAAW,cAAgB,YAAY,EAAA,mCAkHrDA,EAAkB,EAAA,iSAhWX,CAAA,MAAAO,EAAA,EAAA,EAAAD,EACPgI,EAAwC,KAIjC,CAAA,OAAA1H,CAAA,EAAAN,EACA,CAAA,eAAAd,CAAA,EAAAc,EAEPf,EAAA,CAAA,EAEE,MAAAgJ,EAAA,OAAoB,OAAW,IAEtB,eAAAC,GAAA,CACd3G,EAAA,GAAAtC,EAAA,MAAoBF,GACnBS,GAA6BS,CAAK,EAClChB,EACAC,CAAA,CAAA,EAMS,GAAA,CAAA,iBAAAkG,CAAA,EAAApF,GAKA,gBAAAmI,EAAkB,EAAA,EAAAnI,GAClB,WAAAsD,EAAa,EAAA,EAAAtD,GACb,WAAAsF,EAAa,EAAA,EAAAtF,GACb,SAAA+C,EAAW,EAAA,EAAA/C,GACX,kBAAAoI,EAAoB,EAAA,EAAApI,GACpB,qBAAAqI,EAAuB,EAAA,EAAArI,GACvB,IAAAuF,EAAM,EAAA,EAAAvF,GACN,iBAAAkD,EAAmB,EAAA,EAAAlD,EACnB,CAAA,cAAAsI,EAAA,CAAqD,KAAM,IAAI,CAAA,EAAAtI,GAC/D,cAAAqF,EAAgB,EAAA,EAAArF,GAChB,kBAAAkF,GAAoB,EAAA,EAAAlF,GACpB,gBAAAmF,GAAkB,EAAA,EAAAnF,GAClB,YAAAyF,GAAc,EAAA,EAAAzF,GACd,WAAAuI,EAAa,EAAA,EAAAvI,EACb,CAAA,WAAAE,EAAA,EAAAF,EACA,CAAA,KAAAI,EAAA,EAAAJ,GACA,OAAAuD,EAA6B,QAAA,EAAAvD,GAC7B,YAAAwI,GAA6B,IAAA,EAAAxI,EAC7B,CAAA,OAAAK,EAAA,EAAAL,GACA,WAAArB,EAAoC,QAAA,EAAAqB,GACpC,SAAAyI,GAAoC,IAAA,EAAAzI,GACpC,WAAA0I,GAAa,EAAA,EAAA1I,GACb,UAAA2I,GAAY,EAAA,EAAA3I,GACZ,kBAAA4I,GAAoB,EAAA,EAAA5I,EACpB,CAAA,KAAAtC,EAAA,EAAAsC,EAEP/D,GAA6B,KAEjC4M,GAAA,IAAA,MACC5M,GAAS,SAAS,cAAc,sBAAsB,CAAA,IAGnD,IAAAsE,EAEAuI,EAAqB,SAEnBtD,EAAWuD,KAYR,SAAAC,IAAA,CACD,OAAAzI,GAAOA,EAAI,aAAeA,EAAI,UAAYA,EAAI,aAAe,IAG5D,SAAA0I,IAAA,CACH1I,IACLA,EAAI,SAAS,EAAGA,EAAI,YAAY,OAChCuI,EAAqB,EAAA,GAWP,eAAAI,IAAA,CACTX,IAEDS,GAAA,GAKG,MAAAG,GAAA,EACNF,WAEAH,EAAqB,EAAA,GAGvBD,GAAA,IAAA,CACCK,OAMDL,GAAA,IAAA,CACU,SAAAO,GAAA,CACJJ,GAAA,QACHF,EAAqB,EAAA,EAMvB,OAAAvI,GAAK,iBAAiB,SAAU6I,CAAa,OAE5C7I,GAAK,oBAAoB,SAAU6I,CAAa,KAI9C,IAAAC,GACAC,GACAC,GAAwB,GAE5BC,GAAA,IAAA,CACMjJ,GACLA,EAAI,iBAAiB,KAAK,EAAE,QAASkJ,GAAA,CACpCA,EAAE,iBAAiB,QAAU3D,GAAA,CACtB7J,MAAAA,EAAS6J,EAAE,OACb7J,IACHsF,EAAA,GAAA8H,GAAuBpN,EAAO,GAAA,EAC9BsF,EAAA,GAAA+H,GAA2BrN,EAAO,GAAA,OAClCsN,GAAwB,EAAA,SAenB,SAAAG,GAAsBzL,EAAW0L,EAAA,CACzCnE,EAAS,iBAAA,CACR,MAAOvH,EACP,MAAA,CAAS,KAAM0L,EAAQ,KAAM,MAAOA,EAAQ,KAAA,IAIrC,SAAAC,GACR3L,EACApB,EACAyE,EAAA,CAEI,GAAAA,IAAa,QAAUA,IAAa,QAAA,OACjCuI,GAAO5J,EAGT,IAAAxB,GAAaoL,GAAK,OAAS,OACxBA,GAAKpL,EAAU,EAAE,OAAS,aAChCA,KAED+G,EAASlE,EAAA,CACR,MAAOuI,GAAKpL,EAAU,EAAE,MACxB,MAAOoL,GAAKpL,EAAU,EAAE,oBAKtBE,IAAe,SAClB6G,EAAS,OAAA,CACR,MAAO3I,EAAQ,MACf,MAAOA,EAAQ,QACf,MAAOyE,IAAa,cAGhB,GAAA,CAAA1C,EAAA,OAEC,MAAAkL,GAAgBlL,EAAgBX,CAAC,GAChC8L,GAAOC,EAAI,GACjBF,GAAc,CAAC,EACfA,GAAcA,GAAc,OAAS,CAAC,CAAA,EAGvCtE,EAAS,OAAA,CACR,MAAQ,CAAAuE,GAAM,MAAOC,GAAK,KAAK,EAC/B,MAAOF,GAAc,IAAKjH,IAAMA,GAAE,OAAO,EACzC,MAAOvB,IAAa,uCAcZ2I,EAAS,MAASvN,GAAwBuD,CAAK,EACrDuF,EAAS,QACR,CAAA,YAAayE,CAAA,CAAA,QAENnE,EAAC,CACT,QAAQ,MAAMA,CAAC,MACXjJ,EAAUiJ,aAAaoE,GAAapE,EAAE,QAAU,gBACpDN,EAAS,QAAS3I,CAAO,IAOY4G,GAAA,IAAA+B,EAAS,OAAO,EA2BlC2E,EAAA,IAAA5I,EAAA,GAAAgI,GAAwB,EAAK,UAmCjCjI,IAAasI,GAAY3L,EAAGD,EAAS,CAAC,EAAGsD,CAAQ,YAoB/CoI,GAAsBzL,EAAG0L,CAAO,6CAxE3CpJ,EAAG6J,+tCA3NJlC,EAAA,4BA0FHjI,GAASkI,GAAmBlJ,IAClCiK,0CAqCKmB,GAAOpK,EAAO+H,CAAS,SAC3BA,EAAY/H,CAAA,EACZuF,EAAS,QAAQ,2BAInBjE,EAAA,GAAG3C,EAAkBqB,GAASvB,GAAeuB,EAAOtB,CAAU,CAAA,kvGC1FhD,WAAAe,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,sNALS,WAAAA,MAAO,YACbE,EAAA,CAAA,EAAA,UAAA,CAAA,KAAAF,MAAO,IAAI,qBACbA,EAAc,EAAA,CAAA,iCACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,8KAQI4K,SACC,GACA,MAAA5K,MAAS,0GAATE,EAAA,CAAA,EAAA,KAAA2K,EAAA,MAAA7K,MAAS,oIAjBdA,EAAc,EAAA,GAAAiD,GAAAjD,CAAA,IAYbA,EAAU,CAAA,GAAAc,GAAAd,CAAA,0BASR,KAAAA,MAAO,gBACDA,EAAW,CAAA,yEAIhBA,EAAM,EAAA,gFAIIA,EAAc,EAAA,GAAE,SAAW,qBAChCA,EAAc,EAAA,GAAE,SAAW,kRA4BvB,eAAAA,MAAO,0BACXA,EAAI,EAAA,EACV,KAAAA,MAAO,+UAlDf1D,EAoDKC,EAAAsE,EAAApE,CAAA,iDA/DAuD,EAAc,EAAA,oHAYbA,EAAU,CAAA,8GASRE,EAAA,CAAA,EAAA,WAAA4K,EAAA,KAAA9K,MAAO,8BACDA,EAAW,CAAA,iIAIhBA,EAAM,EAAA,mJAIIA,EAAc,EAAA,GAAE,SAAW,yCAChCA,EAAc,EAAA,GAAE,SAAW,4gBA4BvBE,EAAA,CAAA,EAAA,WAAA4K,EAAA,eAAA9K,MAAO,2CACXA,EAAI,EAAA,GACVE,EAAA,CAAA,EAAA,WAAA4K,EAAA,KAAA9K,MAAO,uPAvEN,2FAMO,QACV,8hBAvEK,QAAA+K,EAAU,EAAA,EAAAzK,EACV,CAAA,aAAA0K,EAAA,EAAA,EAAA1K,GACA,QAAA2K,EAAU,EAAA,EAAA3K,EACV,CAAA,MAAAC,EAAA,EAAA,EAAAD,GACA,MAAA4K,EAAuB,IAAA,EAAA5K,GACvB,UAAA6K,EAAgC,MAAA,EAAA7K,EAChC,CAAA,MAAA8K,CAAA,EAAA9K,GACA,WAAA+K,EAAa,EAAA,EAAA/K,EACb,CAAA,KAAAtC,CAAA,EAAAsC,GACA,YAAAgL,EAAc,EAAA,EAAAhL,GACd,SAAA+C,EAAW,EAAA,EAAA/C,GACX,kBAAAoI,EAAoB,EAAA,EAAApI,GACpB,IAAAuF,EAAM,EAAA,EAAAvF,GACN,iBAAAkD,EAAmB,EAAA,EAAAlD,GACnB,qBAAAqI,EAAuB,EAAA,EAAArI,GACvB,cAAAqF,EAAgB,EAAA,EAAArF,GAChB,kBAAAkF,EAAoB,EAAA,EAAAlF,GACpB,OAAAuD,EAA6B,QAAA,EAAAvD,GAC7B,KAAAD,EAA8B,QAAA,EAAAC,GAC9B,gBAAAmF,GAAkB,EAAA,EAAAnF,GAClB,YAAAyF,GAAc,EAAA,EAAAzF,GACd,WAAAuI,GAAa,EAAA,EAAAvI,GACb,WAAA0I,EAAa,EAAA,EAAA1I,GACb,UAAA2I,GAAY,EAAA,EAAA3I,EACZ,CAAA,iBAAAoF,EAAA,EAAApF,EAKA,CAAA,OAAAiL,CAAA,EAAAjL,EAaPkL,GAAA,CAAA,EAOO,CAAA,cAAA5C,GAAA,CAAqD,KAAM,IAAI,CAAA,EAAAtI,GAC/D,kBAAA4I,EAAoB,EAAA,EAAA5I,GACpB,eAAAmL,GAA4C,MAAA,EAAAnL,EAC5C,CAAA,OAAAoL,EAAA,EAAApL,EACA,CAAA,WAAAqL,EAAA,EAAArL,EACA,CAAA,WAAAsL,EAAA,EAAAtL,GACA,YAAAwI,GAA6B,IAAA,EAAAxI,GAC7B,SAAAyI,GAAoC,IAAA,EAAAzI,EACpC,CAAA,WAAAE,CAAA,EAAAF,EAyBc,MAAAuL,EAAA,IAAAN,EAAO,SAAS,eAAgBE,EAAc,EAiDzDxL,EAAA,IAAA6L,IAASP,EAAO,OAAO,UAAUO,CAAI,EACrC9D,GAAA,IAAA8D,IAASP,EAAO,OAAO,SAASO,CAAI,EAvB/BC,GAAA,IAAAR,EAAO,SAAS,SAAUhL,CAAK,KACpC6F,GAAMmF,EAAO,SAAS,SAAUnF,EAAE,MAAM,KAC1CA,GAAMmF,EAAO,SAAS,OAAQnF,EAAE,MAAM,KACrCA,GAAMmF,EAAO,SAAS,QAASnF,EAAE,MAAM,KACvCA,GAAMmF,EAAO,SAAS,QAASnF,EAAE,MAAM,KAC9BA,GAAMmF,EAAO,SAAS,iBAAkBnF,EAAE,MAAM,KACzDA,GAAMmF,EAAO,SAAS,QAASnF,EAAE,MAAM,KACxCA,GAAMmF,EAAO,SAAS,OAAQnF,EAAE,MAAM,cAE/C7F,EAAK,CAAA,CAAA,EACLgL,EAAO,SAAS,OAAO,g7CA3E1B1J,EAAA,GAAG2J,GACFnL,IAAS,SACN7B,GAAiB+B,EAAsBvC,CAAI,EAC3CK,GAAmBkC,EAAoBvC,CAAI,CAAA"}