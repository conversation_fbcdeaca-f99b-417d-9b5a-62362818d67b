import{a as d,i as g,s as o,f as u,p as y,k as c,q as v,r,l as h,v as m,n as _,w as f,o as b}from"../lite.js";/* empty css                                              */function A(s){let e,l=(s[0]?Array.isArray(s[0])?s[0].join(", "):s[0]:"")+"",i;return{c(){e=y("div"),i=c(l),v(e,"class","svelte-rgtszb"),r(e,"table",s[1]==="table"),r(e,"gallery",s[1]==="gallery"),r(e,"selected",s[2])},m(t,a){h(t,e,a),m(e,i)},p(t,[a]){a&1&&l!==(l=(t[0]?Array.isArray(t[0])?t[0].join(", "):t[0]:"")+"")&&_(i,l),a&2&&r(e,"table",t[1]==="table"),a&2&&r(e,"gallery",t[1]==="gallery"),a&4&&r(e,"selected",t[2])},i:f,o:f,d(t){t&&b(e)}}}function p(s,e,l){let{value:i}=e,{type:t}=e,{selected:a=!1}=e;return s.$$set=n=>{"value"in n&&l(0,i=n.value),"type"in n&&l(1,t=n.type),"selected"in n&&l(2,a=n.selected)},[i,t,a]}class k extends d{constructor(e){super(),g(this,e,p,A,o,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),u()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),u()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),u()}}export{k as default};
//# sourceMappingURL=Example-Dp3_tQzH.js.map
