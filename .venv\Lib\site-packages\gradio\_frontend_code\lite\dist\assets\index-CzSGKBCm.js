const __vite__fileDeps=["./module-B6bz2o68.js","./module-C-VadMaF.js","../lite.js","../lite.css","./module-BA06XY8_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{a as ve,i as pe,s as ke,f as h,p as L,q as P,l as O,w as ge,o as W,A as Ye,ao as Ke,E as Ee,aq as yt,k as ie,M as Ve,v as D,n as ne,I as T,a5 as K,x as F,c as q,m as V,F as _e,a6 as Q,t as A,b as M,d as J,P as Rt,y as he,z as be,a3 as Ge,$ as Qe,R as Xe,a8 as Dt,ay as Ze,av as Et,H as me,e as St,u as At,h as Mt,j as Pt,D as zt,B as ut,Y as ft,S as _t,a0 as ct,a4 as dt}from"../lite.js";import{s as $e,W as Bt,p as xe,a as Je,A as Lt,S as It}from"./StaticAudio-Bzja1Z2P.js";import{U as Ct}from"./Upload-TGDabKXH.js";import{M as mt}from"./ModifyUpload-CWo4TCq1.js";import{B as Tt}from"./BlockLabel-B0HN-MOU.js";import{M as Ut}from"./Music-FnJcUklZ.js";import{a as jt,S as Ot}from"./SelectSource-CfNi7fcQ.js";import{S as Wt}from"./StreamingBar-C8nPcBp-.js";import{P as Nt}from"./Trim-CMIh4Ypu.js";import{f as ce}from"./utils-BsGrhMNe.js";import{U as Ft}from"./UploadText-CW3z6Ye_.js";import{default as en}from"./Example-CQIvA0m_.js";import"./Empty-C76eC2zW.js";import"./ShareButton-Bn_rnIUK.js";import"./Community-_qL8Iyvr.js";import"./Download-CgLP-Xl6.js";import"./IconButtonWrapper-Ck50MZwX.js";import"./Play-Cd5lLtoD.js";import"./Undo-DitIvwTU.js";import"./file-url-Co2ROWca.js";import"./hls-CnVhpNcu.js";import"./DownloadLink-B8hI46-W.js";/* empty css                                             */import"./Upload-D_TzP4YC.js";function qe(i,e,t,n){return new(t||(t=Promise))(function(s,l){function r(f){try{o(n.next(f))}catch(a){l(a)}}function u(f){try{o(n.throw(f))}catch(a){l(a)}}function o(f){var a;f.done?s(f.value):(a=f.value,a instanceof t?a:new t(function(_){_(a)})).then(r,u)}o((n=n.apply(i,[])).next())})}class Ht{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,n){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),n?.once){const s=()=>{this.removeEventListener(e,s),this.removeEventListener(e,t)};return this.addEventListener(e,s),s}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;(n=this.listeners[e])===null||n===void 0||n.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(n=>n(...t))}}class qt extends Ht{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}const Vt=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class ye extends qt{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:(t=e.audioBitsPerSecond)!==null&&t!==void 0?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new ye(e||{})}renderMicStream(e){const t=new AudioContext,n=t.createMediaStreamSource(e),s=t.createAnalyser();n.connect(s);const l=s.frequencyBinCount,r=new Float32Array(l),u=l/t.sampleRate;let o;const f=()=>{s.getFloatTimeDomainData(r),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[r],u)),o=requestAnimationFrame(f)};return f(),()=>{cancelAnimationFrame(o),n?.disconnect(),t?.close()}}startMic(e){return qe(this,void 0,void 0,function*(){let t;try{t=yield navigator.mediaDevices.getUserMedia({audio:!e?.deviceId||{deviceId:e.deviceId}})}catch(s){throw new Error("Error accessing the microphone: "+s.message)}const n=this.renderMicStream(t);return this.subscriptions.push(this.once("destroy",n)),this.stream=t,t})}stopMic(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(e){return qe(this,void 0,void 0,function*(){const t=this.stream||(yield this.startMic(e)),n=this.mediaRecorder||new MediaRecorder(t,{mimeType:this.options.mimeType||Vt.find(l=>MediaRecorder.isTypeSupported(l)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=n,this.stopRecording();const s=[];n.ondataavailable=l=>{l.data.size>0&&s.push(l.data)},n.onstop=()=>{var l;const r=new Blob(s,{type:n.mimeType});this.emit("record-end",r),this.options.renderRecordedAudio!==!1&&((l=this.wavesurfer)===null||l===void 0||l.load(URL.createObjectURL(r)))},n.start(),this.emit("record-start")})}isRecording(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="recording"}isPaused(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="paused"}stopRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.stop())}pauseRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.pause(),this.emit("record-pause"))}resumeRecording(){var e;this.isPaused()&&((e=this.mediaRecorder)===null||e===void 0||e.resume(),this.emit("record-resume"))}static getAvailableAudioDevices(){return qe(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(e=>e.filter(t=>t.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}function et(i,e,t){const n=i.slice();return n[3]=e[t],n}function Jt(i){let e,t=Ke(i[0]),n=[];for(let s=0;s<t.length;s+=1)n[s]=tt(et(i,t,s));return{c(){for(let s=0;s<n.length;s+=1)n[s].c();e=Ee()},m(s,l){for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(s,l);O(s,e,l)},p(s,l){if(l&1){t=Ke(s[0]);let r;for(r=0;r<t.length;r+=1){const u=et(s,t,r);n[r]?n[r].p(u,l):(n[r]=tt(u),n[r].c(),n[r].m(e.parentNode,e))}for(;r<n.length;r+=1)n[r].d(1);n.length=t.length}},d(s){s&&W(e),yt(n,s)}}}function Yt(i){let e,t=i[1]("audio.no_microphone")+"",n;return{c(){e=L("option"),n=ie(t),e.__value="",Ve(e,e.__value)},m(s,l){O(s,e,l),D(e,n)},p(s,l){l&2&&t!==(t=s[1]("audio.no_microphone")+"")&&ne(n,t)},d(s){s&&W(e)}}}function tt(i){let e,t=i[3].label+"",n,s;return{c(){e=L("option"),n=ie(t),e.__value=s=i[3].deviceId,Ve(e,e.__value)},m(l,r){O(l,e,r),D(e,n)},p(l,r){r&1&&t!==(t=l[3].label+"")&&ne(n,t),r&1&&s!==(s=l[3].deviceId)&&(e.__value=s,Ve(e,e.__value))},d(l){l&&W(e)}}}function Gt(i){let e,t;function n(r,u){return r[0].length===0?Yt:Jt}let s=n(i),l=s(i);return{c(){e=L("select"),l.c(),P(e,"class","mic-select svelte-1ya9x7a"),P(e,"aria-label","Select input device"),e.disabled=t=i[0].length===0},m(r,u){O(r,e,u),l.m(e,null)},p(r,[u]){s===(s=n(r))&&l?l.p(r,u):(l.d(1),l=s(r),l&&(l.c(),l.m(e,null))),u&1&&t!==(t=r[0].length===0)&&(e.disabled=t)},i:ge,o:ge,d(r){r&&W(e),l.d()}}}function Kt(i,e,t){let{i18n:n}=e,{micDevices:s=[]}=e;const l=Ye();return i.$$set=r=>{"i18n"in r&&t(1,n=r.i18n),"micDevices"in r&&t(0,s=r.micDevices)},i.$$.update=()=>{if(i.$$.dirty&2&&typeof window<"u")try{let r=[];ye.getAvailableAudioDevices().then(u=>{t(0,s=u),u.forEach(o=>{o.deviceId&&r.push(o)}),t(0,s=r)})}catch(r){throw r instanceof DOMException&&r.name=="NotAllowedError"&&l("error",n("audio.allow_recording_access")),r}},[s,n]}class gt extends ve{constructor(e){super(),pe(this,e,Kt,Gt,ke,{i18n:1,micDevices:0})}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),h()}get micDevices(){return this.$$.ctx[0]}set micDevices(e){this.$$set({micDevices:e}),h()}}function it(i){let e,t;return{c(){e=L("time"),t=ie(i[2]),P(e,"class","duration-button duration svelte-11fum4b")},m(n,s){O(n,e,s),D(e,t)},p(n,s){s&4&&ne(t,n[2])},d(n){n&&W(e)}}}function Qt(i){let e,t,n,s=i[1]("audio.record")+"",l,r,u,o=i[1]("audio.stop")+"",f,a,_,m,y=i[1]("audio.stop")+"",z,B,k,b,c,w,H=i[1]("audio.resume")+"",p,U,X,Y,Z,I,R,x;b=new Nt({});let j=i[4]&&!i[3]&&it(i);function se(E){i[23](E)}let fe={i18n:i[1]};return i[5]!==void 0&&(fe.micDevices=i[5]),Y=new gt({props:fe}),T.push(()=>K(Y,"micDevices",se)),{c(){e=L("div"),t=L("div"),n=L("button"),l=ie(s),r=F(),u=L("button"),f=ie(o),_=F(),m=L("button"),z=ie(y),B=F(),k=L("button"),q(b.$$.fragment),c=F(),w=L("button"),p=ie(H),U=F(),j&&j.c(),X=F(),q(Y.$$.fragment),P(n,"class","record record-button svelte-11fum4b"),P(u,"class",a="stop-button "+(i[0].isPaused()?"stop-button-paused":"")+" svelte-11fum4b"),P(m,"id","stop-paused"),P(m,"class","stop-button-paused svelte-11fum4b"),P(k,"aria-label","pause"),P(k,"class","pause-button svelte-11fum4b"),P(w,"class","resume-button svelte-11fum4b"),P(t,"class","wrapper svelte-11fum4b"),P(e,"class","controls svelte-11fum4b")},m(E,C){O(E,e,C),D(e,t),D(t,n),D(n,l),i[13](n),D(t,r),D(t,u),D(u,f),i[15](u),D(t,_),D(t,m),D(m,z),i[17](m),D(t,B),D(t,k),V(b,k,null),i[19](k),D(t,c),D(t,w),D(w,p),i[21](w),D(t,U),j&&j.m(t,null),D(e,X),V(Y,e,null),I=!0,R||(x=[_e(n,"click",i[14]),_e(u,"click",i[16]),_e(m,"click",i[18]),_e(k,"click",i[20]),_e(w,"click",i[22])],R=!0)},p(E,[C]){(!I||C&2)&&s!==(s=E[1]("audio.record")+"")&&ne(l,s),(!I||C&2)&&o!==(o=E[1]("audio.stop")+"")&&ne(f,o),(!I||C&1&&a!==(a="stop-button "+(E[0].isPaused()?"stop-button-paused":"")+" svelte-11fum4b"))&&P(u,"class",a),(!I||C&2)&&y!==(y=E[1]("audio.stop")+"")&&ne(z,y),(!I||C&2)&&H!==(H=E[1]("audio.resume")+"")&&ne(p,H),E[4]&&!E[3]?j?j.p(E,C):(j=it(E),j.c(),j.m(t,null)):j&&(j.d(1),j=null);const S={};C&2&&(S.i18n=E[1]),!Z&&C&32&&(Z=!0,S.micDevices=E[5],Q(()=>Z=!1)),Y.$set(S)},i(E){I||(A(b.$$.fragment,E),A(Y.$$.fragment,E),I=!0)},o(E){M(b.$$.fragment,E),M(Y.$$.fragment,E),I=!1},d(E){E&&W(e),i[13](null),i[15](null),i[17](null),J(b),i[19](null),i[21](null),j&&j.d(),J(Y),R=!1,Rt(x)}}}function Xt(i,e,t){let{record:n}=e,{i18n:s}=e,{recording:l=!1}=e,r=[],u,o,f,a,_,m=!1,{record_time:y}=e,{show_recording_waveform:z}=e,{timing:B=!1}=e;function k(R){T[R?"unshift":"push"](()=>{u=R,t(6,u),t(0,n)})}const b=()=>n.startRecording();function c(R){T[R?"unshift":"push"](()=>{a=R,t(9,a),t(0,n)})}const w=()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopRecording()};function H(R){T[R?"unshift":"push"](()=>{_=R,t(10,_),t(0,n)})}const p=()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopRecording()};function U(R){T[R?"unshift":"push"](()=>{o=R,t(7,o),t(0,n)})}const X=()=>n.pauseRecording();function Y(R){T[R?"unshift":"push"](()=>{f=R,t(8,f),t(0,n)})}const Z=()=>n.resumeRecording();function I(R){r=R,t(5,r)}return i.$$set=R=>{"record"in R&&t(0,n=R.record),"i18n"in R&&t(1,s=R.i18n),"recording"in R&&t(11,l=R.recording),"record_time"in R&&t(2,y=R.record_time),"show_recording_waveform"in R&&t(3,z=R.show_recording_waveform),"timing"in R&&t(4,B=R.timing)},i.$$.update=()=>{i.$$.dirty&1&&n.on("record-start",()=>{n.startMic(),t(6,u.style.display="none",u),t(9,a.style.display="flex",a),t(7,o.style.display="block",o)}),i.$$.dirty&1&&n.on("record-end",()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopMic(),t(6,u.style.display="flex",u),t(9,a.style.display="none",a),t(7,o.style.display="none",o),t(6,u.disabled=!1,u)}),i.$$.dirty&1&&n.on("record-pause",()=>{t(7,o.style.display="none",o),t(8,f.style.display="block",f),t(9,a.style.display="none",a),t(10,_.style.display="flex",_)}),i.$$.dirty&1&&n.on("record-resume",()=>{t(7,o.style.display="block",o),t(8,f.style.display="none",f),t(6,u.style.display="none",u),t(9,a.style.display="flex",a),t(10,_.style.display="none",_)}),i.$$.dirty&6145&&(l&&!m?(n.startRecording(),t(12,m=!0)):(n.stopRecording(),t(12,m=!1)))},[n,s,y,z,B,r,u,o,f,a,_,l,m,k,b,c,w,H,p,U,X,Y,Z,I]}class Zt extends ve{constructor(e){super(),pe(this,e,Xt,Qt,ke,{record:0,i18n:1,recording:11,record_time:2,show_recording_waveform:3,timing:4})}get record(){return this.$$.ctx[0]}set record(e){this.$$set({record:e}),h()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),h()}get recording(){return this.$$.ctx[11]}set recording(e){this.$$set({recording:e}),h()}get record_time(){return this.$$.ctx[2]}set record_time(e){this.$$set({record_time:e}),h()}get show_recording_waveform(){return this.$$.ctx[3]}set show_recording_waveform(e){this.$$set({show_recording_waveform:e}),h()}get timing(){return this.$$.ctx[4]}set timing(e){this.$$set({timing:e}),h()}}function nt(i){let e,t,n,s,l,r=i[0]==="edit"&&i[17]>0&&st(i);function u(a,_){return a[16]?xt:$t}let o=u(i),f=o(i);return{c(){e=L("div"),t=L("time"),t.textContent="0:00",n=F(),s=L("div"),r&&r.c(),l=F(),f.c(),P(t,"class","time svelte-9n45fh"),P(e,"class","timestamps svelte-9n45fh")},m(a,_){O(a,e,_),D(e,t),i[23](t),D(e,n),D(e,s),r&&r.m(s,null),D(s,l),f.m(s,null)},p(a,_){a[0]==="edit"&&a[17]>0?r?r.p(a,_):(r=st(a),r.c(),r.m(s,l)):r&&(r.d(1),r=null),o===(o=u(a))&&f?f.p(a,_):(f.d(1),f=o(a),f&&(f.c(),f.m(s,null)))},d(a){a&&W(e),i[23](null),r&&r.d(),f.d()}}}function st(i){let e,t=ce(i[17])+"",n;return{c(){e=L("time"),n=ie(t),P(e,"class","trim-duration svelte-9n45fh")},m(s,l){O(s,e,l),D(e,n)},p(s,l){l[0]&131072&&t!==(t=ce(s[17])+"")&&ne(n,t)},d(s){s&&W(e)}}}function $t(i){let e;return{c(){e=L("time"),e.textContent="0:00",P(e,"class","duration svelte-9n45fh")},m(t,n){O(t,e,n),i[24](e)},p:ge,d(t){t&&W(e),i[24](null)}}}function xt(i){let e,t=ce(i[15])+"",n;return{c(){e=L("time"),n=ie(t),P(e,"class","duration svelte-9n45fh")},m(s,l){O(s,e,l),D(e,n)},p(s,l){l[0]&32768&&t!==(t=ce(s[15])+"")&&ne(n,t)},d(s){s&&W(e)}}}function ot(i){let e,t,n;function s(r){i[25](r)}let l={i18n:i[1],timing:i[16],recording:i[5],show_recording_waveform:i[2].show_recording_waveform,record_time:ce(i[15])};return i[7]!==void 0&&(l.record=i[7]),e=new Zt({props:l}),T.push(()=>K(e,"record",s)),{c(){q(e.$$.fragment)},m(r,u){V(e,r,u),n=!0},p(r,u){const o={};u[0]&2&&(o.i18n=r[1]),u[0]&65536&&(o.timing=r[16]),u[0]&32&&(o.recording=r[5]),u[0]&4&&(o.show_recording_waveform=r[2].show_recording_waveform),u[0]&32768&&(o.record_time=ce(r[15])),!t&&u[0]&128&&(t=!0,o.record=r[7],Q(()=>t=!1)),e.$set(o)},i(r){n||(A(e.$$.fragment,r),n=!0)},o(r){M(e.$$.fragment,r),n=!1},d(r){J(e,r)}}}function rt(i){let e,t,n,s,l;function r(a){i[26](a)}function u(a){i[27](a)}function o(a){i[28](a)}let f={container:i[11],playing:i[10],audio_duration:i[14],i18n:i[1],editable:i[4],interactive:!0,handle_trim_audio:i[18],show_redo:!0,handle_reset_value:i[3],waveform_options:i[2]};return i[6]!==void 0&&(f.waveform=i[6]),i[17]!==void 0&&(f.trimDuration=i[17]),i[0]!==void 0&&(f.mode=i[0]),e=new Bt({props:f}),T.push(()=>K(e,"waveform",r)),T.push(()=>K(e,"trimDuration",u)),T.push(()=>K(e,"mode",o)),{c(){q(e.$$.fragment)},m(a,_){V(e,a,_),l=!0},p(a,_){const m={};_[0]&2048&&(m.container=a[11]),_[0]&1024&&(m.playing=a[10]),_[0]&16384&&(m.audio_duration=a[14]),_[0]&2&&(m.i18n=a[1]),_[0]&16&&(m.editable=a[4]),_[0]&8&&(m.handle_reset_value=a[3]),_[0]&4&&(m.waveform_options=a[2]),!t&&_[0]&64&&(t=!0,m.waveform=a[6],Q(()=>t=!1)),!n&&_[0]&131072&&(n=!0,m.trimDuration=a[17],Q(()=>n=!1)),!s&&_[0]&1&&(s=!0,m.mode=a[0],Q(()=>s=!1)),e.$set(m)},i(a){l||(A(e.$$.fragment,a),l=!0)},o(a){M(e.$$.fragment,a),l=!1},d(a){J(e,a)}}}function ei(i){let e,t,n,s,l,r,u,o,f=(i[16]||i[13])&&i[2].show_recording_waveform&&nt(i),a=i[12]&&!i[13]&&ot(i),_=i[6]&&i[13]&&rt(i);return{c(){e=L("div"),t=L("div"),n=F(),s=L("div"),l=F(),f&&f.c(),r=F(),a&&a.c(),u=F(),_&&_.c(),P(t,"class","microphone svelte-9n45fh"),P(t,"data-testid","microphone-waveform"),P(s,"data-testid","recording-waveform"),P(e,"class","component-wrapper svelte-9n45fh")},m(m,y){O(m,e,y),D(e,t),i[21](t),D(e,n),D(e,s),i[22](s),D(e,l),f&&f.m(e,null),D(e,r),a&&a.m(e,null),D(e,u),_&&_.m(e,null),o=!0},p(m,y){(m[16]||m[13])&&m[2].show_recording_waveform?f?f.p(m,y):(f=nt(m),f.c(),f.m(e,r)):f&&(f.d(1),f=null),m[12]&&!m[13]?a?(a.p(m,y),y[0]&12288&&A(a,1)):(a=ot(m),a.c(),A(a,1),a.m(e,u)):a&&(he(),M(a,1,1,()=>{a=null}),be()),m[6]&&m[13]?_?(_.p(m,y),y[0]&8256&&A(_,1)):(_=rt(m),_.c(),A(_,1),_.m(e,null)):_&&(he(),M(_,1,1,()=>{_=null}),be())},i(m){o||(A(a),A(_),o=!0)},o(m){M(a),M(_),o=!1},d(m){m&&W(e),i[21](null),i[22](null),f&&f.d(),a&&a.d(),_&&_.d()}}}function ti(i,e,t){let{mode:n}=e,{i18n:s}=e,{dispatch_blob:l}=e,{waveform_settings:r}=e,{waveform_options:u={show_recording_waveform:!0}}=e,{handle_reset_value:o}=e,{editable:f=!0}=e,{recording:a=!1}=e,_,m,y=!1,z,B,k,b=null,c,w,H,p=0,U,X=!1,Y=0;const Z=()=>{clearInterval(U),U=setInterval(()=>{t(15,p++,p)},1e3)},I=Ye();function R(){if(Z(),t(16,X=!0),I("start_recording"),u.show_recording_waveform){let v=B;v&&(v.style.display="block")}}async function x(v){t(15,p=0),t(16,X=!1),clearInterval(U);try{const te=await v.arrayBuffer(),$=await new AudioContext({sampleRate:r.sampleRate}).decodeAudioData(te);$&&await xe($).then(async we=>{await l([we],"change"),await l([we],"stop_recording")})}catch(te){console.error(te)}}const j=()=>{B&&t(12,B.innerHTML="",B),_!==void 0&&_.destroy(),B&&(_=Je.create({...r,normalize:!1,container:B}),t(7,k=_.registerPlugin(ye.create())),k.startMic(),k?.on("record-end",x),k?.on("record-start",R),k?.on("record-pause",()=>{I("pause_recording"),clearInterval(U)}),k?.on("record-end",v=>{t(13,b=URL.createObjectURL(v));const te=B,N=z;te&&(te.style.display="none"),N&&b&&(N.innerHTML="",se())}))},se=()=>{let v=z;!b||!v||t(6,m=Je.create({container:v,url:b,...r}))},fe=async(v,te)=>{t(0,n="edit");const N=m.getDecodedData();N&&await xe(N,v,te).then(async $=>{await l([$],"change"),await l([$],"stop_recording"),m.destroy(),se()}),I("edit")};Ge(()=>{j(),window.addEventListener("keydown",v=>{v.key==="ArrowRight"?$e(m,.1):v.key==="ArrowLeft"&&$e(m,-.1)})});function E(v){T[v?"unshift":"push"](()=>{B=v,t(12,B)})}function C(v){T[v?"unshift":"push"](()=>{z=v,t(11,z)})}function S(v){T[v?"unshift":"push"](()=>{c=v,t(8,c),t(6,m)})}function ee(v){T[v?"unshift":"push"](()=>{w=v,t(9,w),t(6,m)})}function re(v){k=v,t(7,k)}function oe(v){m=v,t(6,m)}function le(v){Y=v,t(17,Y)}function ae(v){n=v,t(0,n)}return i.$$set=v=>{"mode"in v&&t(0,n=v.mode),"i18n"in v&&t(1,s=v.i18n),"dispatch_blob"in v&&t(19,l=v.dispatch_blob),"waveform_settings"in v&&t(20,r=v.waveform_settings),"waveform_options"in v&&t(2,u=v.waveform_options),"handle_reset_value"in v&&t(3,o=v.handle_reset_value),"editable"in v&&t(4,f=v.editable),"recording"in v&&t(5,a=v.recording)},i.$$.update=()=>{i.$$.dirty[0]&128&&k?.on("record-resume",()=>{Z()}),i.$$.dirty[0]&576&&m?.on("decode",v=>{t(14,H=v),w&&t(9,w.textContent=ce(v),w)}),i.$$.dirty[0]&320&&m?.on("timeupdate",v=>c&&t(8,c.textContent=ce(v),c)),i.$$.dirty[0]&64&&m?.on("pause",()=>{I("pause"),t(10,y=!1)}),i.$$.dirty[0]&64&&m?.on("play",()=>{I("play"),t(10,y=!0)}),i.$$.dirty[0]&64&&m?.on("finish",()=>{I("stop"),t(10,y=!1)})},[n,s,u,o,f,a,m,k,c,w,y,z,B,b,H,p,X,Y,fe,l,r,E,C,S,ee,re,oe,le,ae]}class ii extends ve{constructor(e){super(),pe(this,e,ti,ei,ke,{mode:0,i18n:1,dispatch_blob:19,waveform_settings:20,waveform_options:2,handle_reset_value:3,editable:4,recording:5},null,[-1,-1])}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),h()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),h()}get dispatch_blob(){return this.$$.ctx[19]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),h()}get waveform_settings(){return this.$$.ctx[20]}set waveform_settings(e){this.$$set({waveform_settings:e}),h()}get waveform_options(){return this.$$.ctx[2]}set waveform_options(e){this.$$set({waveform_options:e}),h()}get handle_reset_value(){return this.$$.ctx[3]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),h()}get editable(){return this.$$.ctx[4]}set editable(e){this.$$set({editable:e}),h()}get recording(){return this.$$.ctx[5]}set recording(e){this.$$set({recording:e}),h()}}function lt(i){let e;return{c(){e=L("div"),Qe(e,"display",i[0]?"block":"none")},m(t,n){O(t,e,n),i[11](e)},p(t,n){n&1&&Qe(e,"display",t[0]?"block":"none")},d(t){t&&W(e),i[11](null)}}}function ni(i){let e,t,n,s=i[4]("audio.record")+"",l,r,u;return{c(){e=L("button"),t=L("span"),t.innerHTML='<span class="dot"></span>',n=F(),l=ie(s),P(t,"class","record-icon"),P(e,"class","record-button svelte-1fz19cj")},m(o,f){O(o,e,f),D(e,t),D(e,n),D(e,l),r||(u=_e(e,"click",i[14]),r=!0)},p(o,f){f&16&&s!==(s=o[4]("audio.record")+"")&&ne(l,s)},i:ge,o:ge,d(o){o&&W(e),r=!1,u()}}}function si(i){let e,t,n,s,l=i[4]("audio.waiting")+"",r,u,o,f;return n=new jt({}),{c(){e=L("button"),t=L("div"),q(n.$$.fragment),s=F(),r=ie(l),P(t,"class","icon svelte-1fz19cj"),P(e,"class","spinner-button svelte-1fz19cj")},m(a,_){O(a,e,_),D(e,t),V(n,t,null),D(e,s),D(e,r),u=!0,o||(f=_e(e,"click",i[13]),o=!0)},p(a,_){(!u||_&16)&&l!==(l=a[4]("audio.waiting")+"")&&ne(r,l)},i(a){u||(A(n.$$.fragment,a),u=!0)},o(a){M(n.$$.fragment,a),u=!1},d(a){a&&W(e),J(n),o=!1,f()}}}function oi(i){let e,t,n,s=(i[1]?i[4]("audio.pause"):i[4]("audio.stop"))+"",l,r,u,o;return{c(){e=L("button"),t=L("span"),t.innerHTML='<span class="pinger"></span> <span class="dot"></span>',n=F(),l=ie(s),P(t,"class","record-icon"),P(e,"class",r=Xe(i[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")},m(f,a){O(f,e,a),D(e,t),D(e,n),D(e,l),u||(o=_e(e,"click",i[12]),u=!0)},p(f,a){a&18&&s!==(s=(f[1]?f[4]("audio.pause"):f[4]("audio.stop"))+"")&&ne(l,s),a&2&&r!==(r=Xe(f[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")&&P(e,"class",r)},i:ge,o:ge,d(f){f&&W(e),u=!1,o()}}}function ri(i){let e,t,n,s,l,r,u,o,f,a=i[5].show_recording_waveform&&lt(i);const _=[oi,si,ni],m=[];function y(k,b){return k[0]&&!k[6]?0:k[0]&&k[6]?1:2}s=y(i),l=m[s]=_[s](i);function z(k){i[15](k)}let B={i18n:i[4]};return i[9]!==void 0&&(B.micDevices=i[9]),u=new gt({props:B}),T.push(()=>K(u,"micDevices",z)),{c(){e=L("div"),a&&a.c(),t=F(),n=L("div"),l.c(),r=F(),q(u.$$.fragment),P(n,"class","controls svelte-1fz19cj"),P(e,"class","mic-wrap svelte-1fz19cj")},m(k,b){O(k,e,b),a&&a.m(e,null),D(e,t),D(e,n),m[s].m(n,null),D(n,r),V(u,n,null),f=!0},p(k,[b]){k[5].show_recording_waveform?a?a.p(k,b):(a=lt(k),a.c(),a.m(e,t)):a&&(a.d(1),a=null);let c=s;s=y(k),s===c?m[s].p(k,b):(he(),M(m[c],1,1,()=>{m[c]=null}),be(),l=m[s],l?l.p(k,b):(l=m[s]=_[s](k),l.c()),A(l,1),l.m(n,r));const w={};b&16&&(w.i18n=k[4]),!o&&b&512&&(o=!0,w.micDevices=k[9],Q(()=>o=!1)),u.$set(w)},i(k){f||(A(l),A(u.$$.fragment,k),f=!0)},o(k){M(l),M(u.$$.fragment,k),f=!1},d(k){k&&W(e),a&&a.d(),m[s].d(),J(u)}}}function li(i,e,t){let{recording:n=!1}=e,{paused_recording:s=!1}=e,{stop:l}=e,{record:r}=e,{i18n:u}=e,{waveform_settings:o}=e,{waveform_options:f={show_recording_waveform:!0}}=e,{waiting:a=!1}=e,_,m,y,z=[];Ge(()=>{B()});const B=()=>{_!==void 0&&_.destroy(),y&&(_=Je.create({...o,height:100,container:y}),t(7,m=_.registerPlugin(ye.create())))};function k(p){T[p?"unshift":"push"](()=>{y=p,t(8,y)})}const b=()=>{m?.stopMic(),l()},c=()=>{l()},w=()=>{m?.startMic(),r()};function H(p){z=p,t(9,z)}return i.$$set=p=>{"recording"in p&&t(0,n=p.recording),"paused_recording"in p&&t(1,s=p.paused_recording),"stop"in p&&t(2,l=p.stop),"record"in p&&t(3,r=p.record),"i18n"in p&&t(4,u=p.i18n),"waveform_settings"in p&&t(10,o=p.waveform_settings),"waveform_options"in p&&t(5,f=p.waveform_options),"waiting"in p&&t(6,a=p.waiting)},[n,s,l,r,u,f,a,m,y,z,o,k,b,c,w,H]}class ai extends ve{constructor(e){super(),pe(this,e,li,ri,ke,{recording:0,paused_recording:1,stop:2,record:3,i18n:4,waveform_settings:10,waveform_options:5,waiting:6})}get recording(){return this.$$.ctx[0]}set recording(e){this.$$set({recording:e}),h()}get paused_recording(){return this.$$.ctx[1]}set paused_recording(e){this.$$set({paused_recording:e}),h()}get stop(){return this.$$.ctx[2]}set stop(e){this.$$set({stop:e}),h()}get record(){return this.$$.ctx[3]}set record(e){this.$$set({record:e}),h()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),h()}get waveform_settings(){return this.$$.ctx[10]}set waveform_settings(e){this.$$set({waveform_settings:e}),h()}get waveform_options(){return this.$$.ctx[5]}set waveform_options(e){this.$$set({waveform_options:e}),h()}get waiting(){return this.$$.ctx[6]}set waiting(e){this.$$set({waiting:e}),h()}}function ui(i){let e,t,n,s,l;e=new mt({props:{i18n:i[12],download:i[9]?i[2].url:null}}),e.$on("clear",i[27]),e.$on("edit",i[46]);function r(o){i[47](o)}let u={value:i[2],label:i[5],i18n:i[12],dispatch_blob:i[25],waveform_settings:i[13],waveform_options:i[15],trim_region_settings:i[14],handle_reset_value:i[16],editable:i[17],loop:i[7],interactive:!0};return i[23]!==void 0&&(u.mode=i[23]),n=new Lt({props:u}),T.push(()=>K(n,"mode",r)),n.$on("stop",i[48]),n.$on("play",i[49]),n.$on("pause",i[50]),n.$on("edit",i[51]),{c(){q(e.$$.fragment),t=F(),q(n.$$.fragment)},m(o,f){V(e,o,f),O(o,t,f),V(n,o,f),l=!0},p(o,f){const a={};f[0]&4096&&(a.i18n=o[12]),f[0]&516&&(a.download=o[9]?o[2].url:null),e.$set(a);const _={};f[0]&4&&(_.value=o[2]),f[0]&32&&(_.label=o[5]),f[0]&4096&&(_.i18n=o[12]),f[0]&8192&&(_.waveform_settings=o[13]),f[0]&32768&&(_.waveform_options=o[15]),f[0]&16384&&(_.trim_region_settings=o[14]),f[0]&65536&&(_.handle_reset_value=o[16]),f[0]&131072&&(_.editable=o[17]),f[0]&128&&(_.loop=o[7]),!s&&f[0]&8388608&&(s=!0,_.mode=o[23],Q(()=>s=!1)),n.$set(_)},i(o){l||(A(e.$$.fragment,o),A(n.$$.fragment,o),l=!0)},o(o){M(e.$$.fragment,o),M(n.$$.fragment,o),l=!1},d(o){o&&W(t),J(e,o),J(n,o)}}}function fi(i){let e,t,n,s;const l=[ci,_i],r=[];function u(o,f){return o[3]==="microphone"?0:o[3]==="upload"?1:-1}return~(e=u(i))&&(t=r[e]=l[e](i)),{c(){t&&t.c(),n=Ee()},m(o,f){~e&&r[e].m(o,f),O(o,n,f),s=!0},p(o,f){let a=e;e=u(o),e===a?~e&&r[e].p(o,f):(t&&(he(),M(r[a],1,1,()=>{r[a]=null}),be()),~e?(t=r[e],t?t.p(o,f):(t=r[e]=l[e](o),t.c()),A(t,1),t.m(n.parentNode,n)):t=null)},i(o){s||(A(t),s=!0)},o(o){M(t),s=!1},d(o){o&&W(n),~e&&r[e].d(o)}}}function _i(i){let e,t,n,s;function l(o){i[43](o)}function r(o){i[44](o)}let u={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:i[6],max_file_size:i[18],upload:i[19],stream_handler:i[20],$$slots:{default:[di]},$$scope:{ctx:i}};return i[0]!==void 0&&(u.dragging=i[0]),i[4]!==void 0&&(u.uploading=i[4]),e=new Ct({props:u}),T.push(()=>K(e,"dragging",l)),T.push(()=>K(e,"uploading",r)),e.$on("load",i[28]),e.$on("error",i[45]),{c(){q(e.$$.fragment)},m(o,f){V(e,o,f),s=!0},p(o,f){const a={};f[0]&64&&(a.root=o[6]),f[0]&262144&&(a.max_file_size=o[18]),f[0]&524288&&(a.upload=o[19]),f[0]&1048576&&(a.stream_handler=o[20]),f[1]&4194304&&(a.$$scope={dirty:f,ctx:o}),!t&&f[0]&1&&(t=!0,a.dragging=o[0],Q(()=>t=!1)),!n&&f[0]&16&&(n=!0,a.uploading=o[4],Q(()=>n=!1)),e.$set(a)},i(o){s||(A(e.$$.fragment,o),s=!0)},o(o){M(e.$$.fragment,o),s=!1},d(o){J(e,o)}}}function ci(i){let e,t,n,s,l,r;e=new mt({props:{i18n:i[12]}}),e.$on("clear",i[27]);const u=[gi,mi],o=[];function f(a,_){return a[11]?0:1}return n=f(i),s=o[n]=u[n](i),{c(){q(e.$$.fragment),t=F(),s.c(),l=Ee()},m(a,_){V(e,a,_),O(a,t,_),o[n].m(a,_),O(a,l,_),r=!0},p(a,_){const m={};_[0]&4096&&(m.i18n=a[12]),e.$set(m);let y=n;n=f(a),n===y?o[n].p(a,_):(he(),M(o[y],1,1,()=>{o[y]=null}),be(),s=o[n],s?s.p(a,_):(s=o[n]=u[n](a),s.c()),A(s,1),s.m(l.parentNode,l))},i(a){r||(A(e.$$.fragment,a),A(s),r=!0)},o(a){M(e.$$.fragment,a),M(s),r=!1},d(a){a&&(W(t),W(l)),J(e,a),o[n].d(a)}}}function di(i){let e;const t=i[38].default,n=St(t,i,i[53],null);return{c(){n&&n.c()},m(s,l){n&&n.m(s,l),e=!0},p(s,l){n&&n.p&&(!e||l[1]&4194304)&&At(n,t,s,s[53],e?Pt(t,s[53],l,null):Mt(s[53]),null)},i(s){e||(A(n,s),e=!0)},o(s){M(n,s),e=!1},d(s){n&&n.d(s)}}}function mi(i){let e,t,n;function s(r){i[39](r)}let l={i18n:i[12],editable:i[17],recording:i[1],dispatch_blob:i[25],waveform_settings:i[13],waveform_options:i[15],handle_reset_value:i[16]};return i[23]!==void 0&&(l.mode=i[23]),e=new ii({props:l}),T.push(()=>K(e,"mode",s)),e.$on("start_recording",i[40]),e.$on("pause_recording",i[41]),e.$on("stop_recording",i[42]),{c(){q(e.$$.fragment)},m(r,u){V(e,r,u),n=!0},p(r,u){const o={};u[0]&4096&&(o.i18n=r[12]),u[0]&131072&&(o.editable=r[17]),u[0]&2&&(o.recording=r[1]),u[0]&8192&&(o.waveform_settings=r[13]),u[0]&32768&&(o.waveform_options=r[15]),u[0]&65536&&(o.handle_reset_value=r[16]),!t&&u[0]&8388608&&(t=!0,o.mode=r[23],Q(()=>t=!1)),e.$set(o)},i(r){n||(A(e.$$.fragment,r),n=!0)},o(r){M(e.$$.fragment,r),n=!1},d(r){J(e,r)}}}function gi(i){let e,t;return e=new ai({props:{record:i[26],recording:i[1],stop:i[29],i18n:i[12],waveform_settings:i[13],waveform_options:i[15],waiting:i[22]==="waiting"}}),{c(){q(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const l={};s[0]&2&&(l.recording=n[1]),s[0]&4096&&(l.i18n=n[12]),s[0]&8192&&(l.waveform_settings=n[13]),s[0]&32768&&(l.waveform_options=n[15]),s[0]&4194304&&(l.waiting=n[22]==="waiting"),e.$set(l)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function hi(i){let e,t,n,s,l,r,u,o,f,a,_;e=new Tt({props:{show_label:i[8],Icon:Ut,float:i[3]==="upload"&&i[2]===null,label:i[5]||i[12]("audio.audio")}}),s=new Wt({props:{time_limit:i[21]}});const m=[fi,ui],y=[];function z(b,c){return b[2]===null||b[11]?0:1}r=z(i),u=y[r]=m[r](i);function B(b){i[52](b)}let k={sources:i[10],handle_clear:i[27]};return i[3]!==void 0&&(k.active_source=i[3]),f=new Ot({props:k}),T.push(()=>K(f,"active_source",B)),{c(){q(e.$$.fragment),t=F(),n=L("div"),q(s.$$.fragment),l=F(),u.c(),o=F(),q(f.$$.fragment),P(n,"class","audio-container svelte-cbyffp")},m(b,c){V(e,b,c),O(b,t,c),O(b,n,c),V(s,n,null),D(n,l),y[r].m(n,null),D(n,o),V(f,n,null),_=!0},p(b,c){const w={};c[0]&256&&(w.show_label=b[8]),c[0]&12&&(w.float=b[3]==="upload"&&b[2]===null),c[0]&4128&&(w.label=b[5]||b[12]("audio.audio")),e.$set(w);const H={};c[0]&2097152&&(H.time_limit=b[21]),s.$set(H);let p=r;r=z(b),r===p?y[r].p(b,c):(he(),M(y[p],1,1,()=>{y[p]=null}),be(),u=y[r],u?u.p(b,c):(u=y[r]=m[r](b),u.c()),A(u,1),u.m(n,o));const U={};c[0]&1024&&(U.sources=b[10]),!a&&c[0]&8&&(a=!0,U.active_source=b[3],Q(()=>a=!1)),f.$set(U)},i(b){_||(A(e.$$.fragment,b),A(s.$$.fragment,b),A(u),A(f.$$.fragment,b),_=!0)},o(b){M(e.$$.fragment,b),M(s.$$.fragment,b),M(u),M(f.$$.fragment,b),_=!1},d(b){b&&(W(t),W(n)),J(e,b),J(s),y[r].d(),J(f)}}}const at=44;function bi(i,e,t){let{$$slots:n={},$$scope:s}=e,{value:l=null}=e,{label:r}=e,{root:u}=e,{loop:o}=e,{show_label:f=!0}=e,{show_download_button:a=!1}=e,{sources:_=["microphone","upload"]}=e,{pending:m=!1}=e,{streaming:y=!1}=e,{i18n:z}=e,{waveform_settings:B}=e,{trim_region_settings:k={}}=e,{waveform_options:b={}}=e,{dragging:c}=e,{active_source:w}=e,{handle_reset_value:H=()=>{}}=e,{editable:p=!0}=e,{max_file_size:U=null}=e,{upload:X}=e,{stream_handler:Y}=e,{stream_every:Z}=e,{uploading:I=!1}=e,{recording:R=!1}=e,x=null,j="closed";const se=d=>{d==="closed"?(t(21,x=null),t(22,j="closed")):d==="waiting"?t(22,j="waiting"):t(22,j="open")},fe=d=>{R&&t(21,x=d)};let E,C="",S,ee=[],re=!1,oe=!1,le=[],ae;function v(){ae=[Ze(()=>import("./module-B6bz2o68.js"),__vite__mapDeps([0,1,2,3]),import.meta.url),Ze(()=>import("./module-BA06XY8_.js"),__vite__mapDeps([4,1]),import.meta.url)]}typeof window<"u"&&y&&v();const N=Ye(),$=async(d,G)=>{let ue=new File(d,"audio.wav");const de=await Et([ue],G==="stream");t(2,l=(await X(de,u,void 0,U||void 0))?.filter(Boolean)[0]),N(G,l)};Dt(()=>{y&&E&&E.state!=="inactive"&&E.stop()});async function we(){let d;try{d=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(G){if(!navigator.mediaDevices){N("error",z("audio.no_device_support"));return}if(G instanceof DOMException&&G.name=="NotAllowedError"){N("error",z("audio.allow_recording_access"));return}throw G}if(d!=null){if(y){const[{MediaRecorder:G,register:ue},{connect:de}]=await Promise.all(ae);await ue(await de()),t(34,E=new G(d,{mimeType:"audio/wav"})),E.addEventListener("dataavailable",Se)}else t(34,E=new MediaRecorder(d)),E.addEventListener("dataavailable",G=>{le.push(G.data)});E.addEventListener("stop",async()=>{t(1,R=!1),await $(le,"change"),await $(le,"stop_recording"),le=[]}),oe=!0}}async function Se(d){let G=await d.data.arrayBuffer(),ue=new Uint8Array(G);if(S||(t(35,S=new Uint8Array(G.slice(0,at))),ue=new Uint8Array(G.slice(at))),m)ee.push(ue);else{let de=[S].concat(ee,[ue]);if(!R||j==="waiting")return;$(de,"stream"),t(36,ee=[])}}async function Re(){t(1,R=!0),N("start_recording"),oe||await we(),t(35,S=void 0),y&&E.state!="recording"&&E.start(Z*1e3)}function Ae(){N("change",null),N("clear"),t(23,C=""),t(2,l=null)}function Me({detail:d}){t(2,l=d),N("change",d),N("upload",d)}async function De(){t(1,R=!1),y&&(N("close_stream"),N("stop_recording"),E.stop(),m&&t(37,re=!0),$(le,"stop_recording"),N("clear"),t(23,C=""))}function Pe(d){C=d,t(23,C)}function ze(d){me.call(this,i,d)}function Be(d){me.call(this,i,d)}function Le(d){me.call(this,i,d)}function Ie(d){c=d,t(0,c)}function Ce(d){I=d,t(4,I)}const Te=({detail:d})=>N("error",d),Ue=()=>t(23,C="edit");function je(d){C=d,t(23,C)}function Oe(d){me.call(this,i,d)}function We(d){me.call(this,i,d)}function Ne(d){me.call(this,i,d)}function Fe(d){me.call(this,i,d)}function He(d){w=d,t(3,w)}return i.$$set=d=>{"value"in d&&t(2,l=d.value),"label"in d&&t(5,r=d.label),"root"in d&&t(6,u=d.root),"loop"in d&&t(7,o=d.loop),"show_label"in d&&t(8,f=d.show_label),"show_download_button"in d&&t(9,a=d.show_download_button),"sources"in d&&t(10,_=d.sources),"pending"in d&&t(30,m=d.pending),"streaming"in d&&t(11,y=d.streaming),"i18n"in d&&t(12,z=d.i18n),"waveform_settings"in d&&t(13,B=d.waveform_settings),"trim_region_settings"in d&&t(14,k=d.trim_region_settings),"waveform_options"in d&&t(15,b=d.waveform_options),"dragging"in d&&t(0,c=d.dragging),"active_source"in d&&t(3,w=d.active_source),"handle_reset_value"in d&&t(16,H=d.handle_reset_value),"editable"in d&&t(17,p=d.editable),"max_file_size"in d&&t(18,U=d.max_file_size),"upload"in d&&t(19,X=d.upload),"stream_handler"in d&&t(20,Y=d.stream_handler),"stream_every"in d&&t(31,Z=d.stream_every),"uploading"in d&&t(4,I=d.uploading),"recording"in d&&t(1,R=d.recording),"$$scope"in d&&t(53,s=d.$$scope)},i.$$.update=()=>{if(i.$$.dirty[0]&1&&N("drag",c),i.$$.dirty[0]&1073741824|i.$$.dirty[1]&112&&re&&m===!1&&(t(37,re=!1),S&&ee)){let d=[S].concat(ee);t(36,ee=[]),$(d,"stream")}i.$$.dirty[0]&2|i.$$.dirty[1]&8&&!R&&E&&De(),i.$$.dirty[0]&2|i.$$.dirty[1]&8&&R&&E&&Re()},[c,R,l,w,I,r,u,o,f,a,_,y,z,B,k,b,H,p,U,X,Y,x,j,C,N,$,Re,Ae,Me,De,m,Z,se,fe,E,S,ee,re,n,Pe,ze,Be,Le,Ie,Ce,Te,Ue,je,Oe,We,Ne,Fe,He,s]}class wi extends ve{constructor(e){super(),pe(this,e,bi,hi,ke,{value:2,label:5,root:6,loop:7,show_label:8,show_download_button:9,sources:10,pending:30,streaming:11,i18n:12,waveform_settings:13,trim_region_settings:14,waveform_options:15,dragging:0,active_source:3,handle_reset_value:16,editable:17,max_file_size:18,upload:19,stream_handler:20,stream_every:31,uploading:4,recording:1,modify_stream:32,set_time_limit:33},null,[-1,-1])}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),h()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),h()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),h()}get loop(){return this.$$.ctx[7]}set loop(e){this.$$set({loop:e}),h()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),h()}get show_download_button(){return this.$$.ctx[9]}set show_download_button(e){this.$$set({show_download_button:e}),h()}get sources(){return this.$$.ctx[10]}set sources(e){this.$$set({sources:e}),h()}get pending(){return this.$$.ctx[30]}set pending(e){this.$$set({pending:e}),h()}get streaming(){return this.$$.ctx[11]}set streaming(e){this.$$set({streaming:e}),h()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),h()}get waveform_settings(){return this.$$.ctx[13]}set waveform_settings(e){this.$$set({waveform_settings:e}),h()}get trim_region_settings(){return this.$$.ctx[14]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),h()}get waveform_options(){return this.$$.ctx[15]}set waveform_options(e){this.$$set({waveform_options:e}),h()}get dragging(){return this.$$.ctx[0]}set dragging(e){this.$$set({dragging:e}),h()}get active_source(){return this.$$.ctx[3]}set active_source(e){this.$$set({active_source:e}),h()}get handle_reset_value(){return this.$$.ctx[16]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),h()}get editable(){return this.$$.ctx[17]}set editable(e){this.$$set({editable:e}),h()}get max_file_size(){return this.$$.ctx[18]}set max_file_size(e){this.$$set({max_file_size:e}),h()}get upload(){return this.$$.ctx[19]}set upload(e){this.$$set({upload:e}),h()}get stream_handler(){return this.$$.ctx[20]}set stream_handler(e){this.$$set({stream_handler:e}),h()}get stream_every(){return this.$$.ctx[31]}set stream_every(e){this.$$set({stream_every:e}),h()}get uploading(){return this.$$.ctx[4]}set uploading(e){this.$$set({uploading:e}),h()}get recording(){return this.$$.ctx[1]}set recording(e){this.$$set({recording:e}),h()}get modify_stream(){return this.$$.ctx[32]}get set_time_limit(){return this.$$.ctx[33]}}const vi=wi;function pi(i){let e,t;return e=new ut({props:{variant:i[0]===null&&i[25]==="upload"?"dashed":"solid",border_mode:i[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:i[4],elem_classes:i[5],visible:i[6],container:i[12],scale:i[13],min_width:i[14],$$slots:{default:[Ri]},$$scope:{ctx:i}}}),{c(){q(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const l={};s[0]&33554433&&(l.variant=n[0]===null&&n[25]==="upload"?"dashed":"solid"),s[0]&134217728&&(l.border_mode=n[27]?"focus":"base"),s[0]&16&&(l.elem_id=n[4]),s[0]&32&&(l.elem_classes=n[5]),s[0]&64&&(l.visible=n[6]),s[0]&4096&&(l.container=n[12]),s[0]&8192&&(l.scale=n[13]),s[0]&16384&&(l.min_width=n[14]),s[0]&536710927|s[2]&128&&(l.$$scope={dirty:s,ctx:n}),e.$set(l)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ki(i){let e,t;return e=new ut({props:{variant:"solid",border_mode:i[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:i[4],elem_classes:i[5],visible:i[6],container:i[12],scale:i[13],min_width:i[14],$$slots:{default:[Di]},$$scope:{ctx:i}}}),{c(){q(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const l={};s[0]&134217728&&(l.border_mode=n[27]?"focus":"base"),s[0]&16&&(l.elem_id=n[4]),s[0]&32&&(l.elem_classes=n[5]),s[0]&64&&(l.visible=n[6]),s[0]&4096&&(l.container=n[12]),s[0]&8192&&(l.scale=n[13]),s[0]&16384&&(l.min_width=n[14]),s[0]&277842435|s[2]&128&&(l.$$scope={dirty:s,ctx:n}),e.$set(l)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function yi(i){let e,t;return e=new Ft({props:{i18n:i[23].i18n,type:"audio"}}),{c(){q(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const l={};s[0]&8388608&&(l.i18n=n[23].i18n),e.$set(l)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Ri(i){let e,t,n,s,l,r,u,o,f;const a=[{autoscroll:i[23].autoscroll},{i18n:i[23].i18n},i[1]];let _={};for(let c=0;c<a.length;c+=1)_=ft(_,a[c]);e=new _t({props:_}),e.$on("clear_status",i[45]);function m(c){i[48](c)}function y(c){i[49](c)}function z(c){i[50](c)}function B(c){i[51](c)}function k(c){i[52](c)}let b={label:i[9],show_label:i[11],show_download_button:i[16],value:i[0],root:i[10],sources:i[8],active_source:i[25],pending:i[20],streaming:i[21],loop:i[15],max_file_size:i[23].max_file_size,handle_reset_value:i[29],editable:i[18],i18n:i[23].i18n,waveform_settings:i[28],waveform_options:i[19],trim_region_settings:i[30],stream_every:i[22],upload:i[46],stream_handler:i[47],$$slots:{default:[yi]},$$scope:{ctx:i}};return i[2]!==void 0&&(b.recording=i[2]),i[27]!==void 0&&(b.dragging=i[27]),i[24]!==void 0&&(b.uploading=i[24]),i[26]!==void 0&&(b.modify_stream=i[26]),i[3]!==void 0&&(b.set_time_limit=i[3]),n=new vi({props:b}),T.push(()=>K(n,"recording",m)),T.push(()=>K(n,"dragging",y)),T.push(()=>K(n,"uploading",z)),T.push(()=>K(n,"modify_stream",B)),T.push(()=>K(n,"set_time_limit",k)),n.$on("change",i[53]),n.$on("stream",i[54]),n.$on("drag",i[55]),n.$on("edit",i[56]),n.$on("play",i[57]),n.$on("pause",i[58]),n.$on("stop",i[59]),n.$on("start_recording",i[60]),n.$on("pause_recording",i[61]),n.$on("stop_recording",i[62]),n.$on("upload",i[63]),n.$on("clear",i[64]),n.$on("error",i[31]),n.$on("close_stream",i[65]),{c(){q(e.$$.fragment),t=F(),q(n.$$.fragment)},m(c,w){V(e,c,w),O(c,t,w),V(n,c,w),f=!0},p(c,w){const H=w[0]&8388610?ct(a,[w[0]&8388608&&{autoscroll:c[23].autoscroll},w[0]&8388608&&{i18n:c[23].i18n},w[0]&2&&dt(c[1])]):{};e.$set(H);const p={};w[0]&512&&(p.label=c[9]),w[0]&2048&&(p.show_label=c[11]),w[0]&65536&&(p.show_download_button=c[16]),w[0]&1&&(p.value=c[0]),w[0]&1024&&(p.root=c[10]),w[0]&256&&(p.sources=c[8]),w[0]&33554432&&(p.active_source=c[25]),w[0]&1048576&&(p.pending=c[20]),w[0]&2097152&&(p.streaming=c[21]),w[0]&32768&&(p.loop=c[15]),w[0]&8388608&&(p.max_file_size=c[23].max_file_size),w[0]&262144&&(p.editable=c[18]),w[0]&8388608&&(p.i18n=c[23].i18n),w[0]&268435456&&(p.waveform_settings=c[28]),w[0]&524288&&(p.waveform_options=c[19]),w[0]&4194304&&(p.stream_every=c[22]),w[0]&8388608&&(p.upload=c[46]),w[0]&8388608&&(p.stream_handler=c[47]),w[0]&8388608|w[2]&128&&(p.$$scope={dirty:w,ctx:c}),!s&&w[0]&4&&(s=!0,p.recording=c[2],Q(()=>s=!1)),!l&&w[0]&134217728&&(l=!0,p.dragging=c[27],Q(()=>l=!1)),!r&&w[0]&16777216&&(r=!0,p.uploading=c[24],Q(()=>r=!1)),!u&&w[0]&67108864&&(u=!0,p.modify_stream=c[26],Q(()=>u=!1)),!o&&w[0]&8&&(o=!0,p.set_time_limit=c[3],Q(()=>o=!1)),n.$set(p)},i(c){f||(A(e.$$.fragment,c),A(n.$$.fragment,c),f=!0)},o(c){M(e.$$.fragment,c),M(n.$$.fragment,c),f=!1},d(c){c&&W(t),J(e,c),J(n,c)}}}function Di(i){let e,t,n,s;const l=[{autoscroll:i[23].autoscroll},{i18n:i[23].i18n},i[1]];let r={};for(let u=0;u<l.length;u+=1)r=ft(r,l[u]);return e=new _t({props:r}),e.$on("clear_status",i[39]),n=new It({props:{i18n:i[23].i18n,show_label:i[11],show_download_button:i[16],show_share_button:i[17],value:i[0],label:i[9],loop:i[15],waveform_settings:i[28],waveform_options:i[19],editable:i[18]}}),n.$on("share",i[40]),n.$on("error",i[41]),n.$on("play",i[42]),n.$on("pause",i[43]),n.$on("stop",i[44]),{c(){q(e.$$.fragment),t=F(),q(n.$$.fragment)},m(u,o){V(e,u,o),O(u,t,o),V(n,u,o),s=!0},p(u,o){const f=o[0]&8388610?ct(l,[o[0]&8388608&&{autoscroll:u[23].autoscroll},o[0]&8388608&&{i18n:u[23].i18n},o[0]&2&&dt(u[1])]):{};e.$set(f);const a={};o[0]&8388608&&(a.i18n=u[23].i18n),o[0]&2048&&(a.show_label=u[11]),o[0]&65536&&(a.show_download_button=u[16]),o[0]&131072&&(a.show_share_button=u[17]),o[0]&1&&(a.value=u[0]),o[0]&512&&(a.label=u[9]),o[0]&32768&&(a.loop=u[15]),o[0]&268435456&&(a.waveform_settings=u[28]),o[0]&524288&&(a.waveform_options=u[19]),o[0]&262144&&(a.editable=u[18]),n.$set(a)},i(u){s||(A(e.$$.fragment,u),A(n.$$.fragment,u),s=!0)},o(u){M(e.$$.fragment,u),M(n.$$.fragment,u),s=!1},d(u){u&&W(t),J(e,u),J(n,u)}}}function Ei(i){let e,t,n,s;const l=[ki,pi],r=[];function u(o,f){return o[7]?1:0}return e=u(i),t=r[e]=l[e](i),{c(){t.c(),n=Ee()},m(o,f){r[e].m(o,f),O(o,n,f),s=!0},p(o,f){let a=e;e=u(o),e===a?r[e].p(o,f):(he(),M(r[a],1,1,()=>{r[a]=null}),be(),t=r[e],t?t.p(o,f):(t=r[e]=l[e](o),t.c()),A(t,1),t.m(n.parentNode,n))},i(o){s||(A(t),s=!0)},o(o){M(t),s=!1},d(o){o&&W(n),r[e].d(o)}}}function Si(i,e,t){let{value_is_output:n=!1}=e,{elem_id:s=""}=e,{elem_classes:l=[]}=e,{visible:r=!0}=e,{interactive:u}=e,{value:o=null}=e,{sources:f}=e,{label:a}=e,{root:_}=e,{show_label:m}=e,{container:y=!0}=e,{scale:z=null}=e,{min_width:B=void 0}=e,{loading_status:k}=e,{autoplay:b=!1}=e,{loop:c=!1}=e,{show_download_button:w}=e,{show_share_button:H=!1}=e,{editable:p=!0}=e,{waveform_options:U={}}=e,{pending:X}=e,{streaming:Y}=e,{stream_every:Z}=e,{input_ready:I}=e,{recording:R=!1}=e,x=!1,j="closed",se;function fe(g){j=g,se(g)}const E=()=>j;let{set_time_limit:C}=e,{gradio:S}=e,ee=null,re,oe=o;const le=()=>{oe===null||o===oe||t(0,o=oe)};let ae,v,te="darkorange";Ge(()=>{te=getComputedStyle(document?.documentElement).getPropertyValue("--color-accent"),$(),t(28,v.waveColor=U.waveform_color||"#9ca3af",v),t(28,v.progressColor=U.waveform_progress_color||te,v),t(28,v.mediaControls=U.show_controls,v),t(28,v.sampleRate=U.sample_rate||44100,v)});const N={color:U.trim_region_color,drag:!0,resize:!0};function $(){document.documentElement.style.setProperty("--trim-region-color",N.color||te)}function we({detail:g}){const[pt,kt]=g.includes("Invalid file type")?["warning","complete"]:["error","error"];t(1,k=k||{}),t(1,k.status=kt,k),t(1,k.message=g,k),S.dispatch(pt,g)}zt(()=>{t(32,n=!1)});const Se=()=>S.dispatch("clear_status",k),Re=g=>S.dispatch("share",g.detail),Ae=g=>S.dispatch("error",g.detail),Me=()=>S.dispatch("play"),De=()=>S.dispatch("pause"),Pe=()=>S.dispatch("stop"),ze=()=>S.dispatch("clear_status",k),Be=(...g)=>S.client.upload(...g),Le=(...g)=>S.client.stream(...g);function Ie(g){R=g,t(2,R)}function Ce(g){ae=g,t(27,ae)}function Te(g){x=g,t(24,x)}function Ue(g){se=g,t(26,se)}function je(g){C=g,t(3,C)}const Oe=({detail:g})=>t(0,o=g),We=({detail:g})=>{t(0,o=g),S.dispatch("stream",o)},Ne=({detail:g})=>t(27,ae=g),Fe=()=>S.dispatch("edit"),He=()=>S.dispatch("play"),d=()=>S.dispatch("pause"),G=()=>S.dispatch("stop"),ue=()=>S.dispatch("start_recording"),de=()=>S.dispatch("pause_recording"),ht=g=>S.dispatch("stop_recording"),bt=()=>S.dispatch("upload"),wt=()=>S.dispatch("clear"),vt=()=>S.dispatch("close_stream","stream");return i.$$set=g=>{"value_is_output"in g&&t(32,n=g.value_is_output),"elem_id"in g&&t(4,s=g.elem_id),"elem_classes"in g&&t(5,l=g.elem_classes),"visible"in g&&t(6,r=g.visible),"interactive"in g&&t(7,u=g.interactive),"value"in g&&t(0,o=g.value),"sources"in g&&t(8,f=g.sources),"label"in g&&t(9,a=g.label),"root"in g&&t(10,_=g.root),"show_label"in g&&t(11,m=g.show_label),"container"in g&&t(12,y=g.container),"scale"in g&&t(13,z=g.scale),"min_width"in g&&t(14,B=g.min_width),"loading_status"in g&&t(1,k=g.loading_status),"autoplay"in g&&t(34,b=g.autoplay),"loop"in g&&t(15,c=g.loop),"show_download_button"in g&&t(16,w=g.show_download_button),"show_share_button"in g&&t(17,H=g.show_share_button),"editable"in g&&t(18,p=g.editable),"waveform_options"in g&&t(19,U=g.waveform_options),"pending"in g&&t(20,X=g.pending),"streaming"in g&&t(21,Y=g.streaming),"stream_every"in g&&t(22,Z=g.stream_every),"input_ready"in g&&t(33,I=g.input_ready),"recording"in g&&t(2,R=g.recording),"set_time_limit"in g&&t(3,C=g.set_time_limit),"gradio"in g&&t(23,S=g.gradio)},i.$$.update=()=>{i.$$.dirty[0]&16777216&&t(33,I=!x),i.$$.dirty[0]&1|i.$$.dirty[1]&128&&o&&oe===null&&t(38,oe=o),i.$$.dirty[0]&8388609|i.$$.dirty[1]&66&&JSON.stringify(o)!==JSON.stringify(ee)&&(t(37,ee=o),S.dispatch("change"),n||S.dispatch("input")),i.$$.dirty[0]&33554688&&!re&&f&&t(25,re=f[0]),i.$$.dirty[1]&8&&t(28,v={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:b,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20})},[o,k,R,C,s,l,r,u,f,a,_,m,y,z,B,c,w,H,p,U,X,Y,Z,S,x,re,se,ae,v,le,N,we,n,I,b,fe,E,ee,oe,Se,Re,Ae,Me,De,Pe,ze,Be,Le,Ie,Ce,Te,Ue,je,Oe,We,Ne,Fe,He,d,G,ue,de,ht,bt,wt,vt]}class Ai extends ve{constructor(e){super(),pe(this,e,Si,Ei,ke,{value_is_output:32,elem_id:4,elem_classes:5,visible:6,interactive:7,value:0,sources:8,label:9,root:10,show_label:11,container:12,scale:13,min_width:14,loading_status:1,autoplay:34,loop:15,show_download_button:16,show_share_button:17,editable:18,waveform_options:19,pending:20,streaming:21,stream_every:22,input_ready:33,recording:2,modify_stream_state:35,get_stream_state:36,set_time_limit:3,gradio:23},null,[-1,-1,-1])}get value_is_output(){return this.$$.ctx[32]}set value_is_output(e){this.$$set({value_is_output:e}),h()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),h()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),h()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),h()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),h()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),h()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),h()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),h()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),h()}get autoplay(){return this.$$.ctx[34]}set autoplay(e){this.$$set({autoplay:e}),h()}get loop(){return this.$$.ctx[15]}set loop(e){this.$$set({loop:e}),h()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),h()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),h()}get editable(){return this.$$.ctx[18]}set editable(e){this.$$set({editable:e}),h()}get waveform_options(){return this.$$.ctx[19]}set waveform_options(e){this.$$set({waveform_options:e}),h()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),h()}get streaming(){return this.$$.ctx[21]}set streaming(e){this.$$set({streaming:e}),h()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),h()}get input_ready(){return this.$$.ctx[33]}set input_ready(e){this.$$set({input_ready:e}),h()}get recording(){return this.$$.ctx[2]}set recording(e){this.$$set({recording:e}),h()}get modify_stream_state(){return this.$$.ctx[35]}get get_stream_state(){return this.$$.ctx[36]}get set_time_limit(){return this.$$.ctx[3]}set set_time_limit(e){this.$$set({set_time_limit:e}),h()}get gradio(){return this.$$.ctx[23]}set gradio(e){this.$$set({gradio:e}),h()}}const Zi=Ai;export{en as BaseExample,vi as BaseInteractiveAudio,Lt as BasePlayer,It as BaseStaticAudio,Zi as default};
//# sourceMappingURL=index-CzSGKBCm.js.map
