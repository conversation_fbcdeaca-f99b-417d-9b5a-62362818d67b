import{a as ce,i as de,s as ue,f as c,I as M,a5 as Q,c as V,x as Y,p as R,q as F,m as B,l as I,v as oe,y as Z,b as p,z as y,t as v,a6 as X,o as j,d as S,A as De,H as A,k as re,n as _e,w as ae,E as me,e as He,u as Le,h as Te,j as Ye,B as ge,Y as be,S as we,a0 as pe,a4 as ve}from"../lite.js";import{U as Fe}from"./Upload-TGDabKXH.js";import{B as Ge}from"./BlockLabel-B0HN-MOU.js";import{V as Ke}from"./Video-BIoWYjY-.js";import{S as Me}from"./SelectSource-CfNi7fcQ.js";import"./Image-Bd-jnd8M.js";/* empty css                                                   */import{W as Qe}from"./ImageUploader-DjVwxXyF.js";/* empty css                                              */import{p as fe,a as Re}from"./Video-B2DSnbGg.js";import{l as $t}from"./Video-B2DSnbGg.js";import{P as Xe,V as Ze}from"./VideoPreview-CYXODlxd.js";import{default as el}from"./Example-DLsdnWMK.js";import{U as ye}from"./UploadText-CW3z6Ye_.js";/* empty css                                             */import"./Upload-D_TzP4YC.js";import"./file-url-Co2ROWca.js";import"./Image-Cvn5Jo_E.js";import"./utils-Gtzs_Zla.js";import"./DropdownArrow-B85Vabzi.js";import"./Square-7hK0fZD1.js";import"./index-DCygRGWm.js";import"./StreamingBar-C8nPcBp-.js";import"./IconButtonWrapper-Ck50MZwX.js";import"./hls-CnVhpNcu.js";import"./Empty-C76eC2zW.js";import"./ShareButton-Bn_rnIUK.js";import"./Community-_qL8Iyvr.js";import"./utils-BsGrhMNe.js";import"./Download-CgLP-Xl6.js";import"./DownloadLink-B8hI46-W.js";import"./Trim-CMIh4Ypu.js";import"./Play-Cd5lLtoD.js";import"./Undo-DitIvwTU.js";import"./ModifyUpload-CWo4TCq1.js";function $e(l){let e,s=(l[0].orig_name||l[0].url)+"",t,n,i,u=fe(l[0].size)+"",f;return{c(){e=R("div"),t=re(s),n=Y(),i=R("div"),f=re(u),F(e,"class","file-name svelte-14jis2k"),F(i,"class","file-size svelte-14jis2k")},m(o,h){I(o,e,h),oe(e,t),I(o,n,h),I(o,i,h),oe(i,f)},p(o,h){h[0]&1&&s!==(s=(o[0].orig_name||o[0].url)+"")&&_e(t,s),h[0]&1&&u!==(u=fe(o[0].size)+"")&&_e(f,u)},i:ae,o:ae,d(o){o&&(j(e),j(n),j(i))}}}function xe(l){let e=l[0]?.url,s,t,n=he(l);return{c(){n.c(),s=me()},m(i,u){n.m(i,u),I(i,s,u),t=!0},p(i,u){u[0]&1&&ue(e,e=i[0]?.url)?(Z(),p(n,1,1,ae),y(),n=he(i),n.c(),v(n,1),n.m(s.parentNode,s)):n.p(i,u)},i(i){t||(v(n),t=!0)},o(i){p(n),t=!1},d(i){i&&j(s),n.d(i)}}}function et(l){let e,s,t,n;const i=[lt,tt],u=[];function f(o,h){return o[1]==="upload"?0:o[1]==="webcam"?1:-1}return~(s=f(l))&&(t=u[s]=i[s](l)),{c(){e=R("div"),t&&t.c(),F(e,"class","upload-container svelte-14jis2k")},m(o,h){I(o,e,h),~s&&u[s].m(e,null),n=!0},p(o,h){let a=s;s=f(o),s===a?~s&&u[s].p(o,h):(t&&(Z(),p(u[a],1,1,()=>{u[a]=null}),y()),~s?(t=u[s],t?t.p(o,h):(t=u[s]=i[s](o),t.c()),v(t,1),t.m(e,null)):t=null)},i(o){n||(v(t),n=!0)},o(o){p(t),n=!1},d(o){o&&j(e),~s&&u[s].d()}}}function he(l){let e,s;return e=new Xe({props:{upload:l[15],root:l[11],interactive:!0,autoplay:l[10],src:l[0].url,subtitle:l[3]?.url,is_stream:!1,mirror:l[8]&&l[1]==="webcam",label:l[5],handle_change:l[23],handle_reset_value:l[13],loop:l[17],value:l[0],i18n:l[12],show_download_button:l[6],handle_clear:l[22],has_change_history:l[19]}}),e.$on("play",l[32]),e.$on("pause",l[33]),e.$on("stop",l[34]),e.$on("end",l[35]),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&32768&&(i.upload=t[15]),n[0]&2048&&(i.root=t[11]),n[0]&1024&&(i.autoplay=t[10]),n[0]&1&&(i.src=t[0].url),n[0]&8&&(i.subtitle=t[3]?.url),n[0]&258&&(i.mirror=t[8]&&t[1]==="webcam"),n[0]&32&&(i.label=t[5]),n[0]&8192&&(i.handle_reset_value=t[13]),n[0]&131072&&(i.loop=t[17]),n[0]&1&&(i.value=t[0]),n[0]&4096&&(i.i18n=t[12]),n[0]&64&&(i.show_download_button=t[6]),n[0]&524288&&(i.has_change_history=t[19]),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function tt(l){let e,s;return e=new Qe({props:{root:l[11],mirror_webcam:l[8],include_audio:l[9],mode:"video",i18n:l[12],upload:l[15],stream_every:1}}),e.$on("error",l[29]),e.$on("capture",l[24]),e.$on("start_recording",l[30]),e.$on("stop_recording",l[31]),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&2048&&(i.root=t[11]),n[0]&256&&(i.mirror_webcam=t[8]),n[0]&512&&(i.include_audio=t[9]),n[0]&4096&&(i.i18n=t[12]),n[0]&32768&&(i.upload=t[15]),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function lt(l){let e,s,t,n;function i(o){l[26](o)}function u(o){l[27](o)}let f={filetype:"video/x-m4v,video/*",max_file_size:l[14],root:l[11],upload:l[15],stream_handler:l[16],$$slots:{default:[st]},$$scope:{ctx:l}};return l[18]!==void 0&&(f.dragging=l[18]),l[2]!==void 0&&(f.uploading=l[2]),e=new Fe({props:f}),M.push(()=>Q(e,"dragging",i)),M.push(()=>Q(e,"uploading",u)),e.$on("load",l[21]),e.$on("error",l[28]),{c(){V(e.$$.fragment)},m(o,h){B(e,o,h),n=!0},p(o,h){const a={};h[0]&16384&&(a.max_file_size=o[14]),h[0]&2048&&(a.root=o[11]),h[0]&32768&&(a.upload=o[15]),h[0]&65536&&(a.stream_handler=o[16]),h[1]&64&&(a.$$scope={dirty:h,ctx:o}),!s&&h[0]&262144&&(s=!0,a.dragging=o[18],X(()=>s=!1)),!t&&h[0]&4&&(t=!0,a.uploading=o[2],X(()=>t=!1)),e.$set(a)},i(o){n||(v(e.$$.fragment,o),n=!0)},o(o){p(e.$$.fragment,o),n=!1},d(o){S(e,o)}}}function st(l){let e;const s=l[25].default,t=He(s,l,l[37],null);return{c(){t&&t.c()},m(n,i){t&&t.m(n,i),e=!0},p(n,i){t&&t.p&&(!e||i[1]&64)&&Le(t,s,n,n[37],e?Ye(s,n[37],i,null):Te(n[37]),null)},i(n){e||(v(t,n),e=!0)},o(n){p(t,n),e=!1},d(n){t&&t.d(n)}}}function nt(l){let e,s,t,n,i,u,f,o,h,a;e=new Ge({props:{show_label:l[7],Icon:Ke,label:l[5]||"Video"}});const d=[et,xe,$e],w=[];function g(m,k){return m[0]===null||m[0].url===void 0?0:(n==null&&(n=!!Re()),n?1:m[0].size?2:-1)}~(i=g(l))&&(u=w[i]=d[i](l));function O(m){l[36](m)}let E={sources:l[4],handle_clear:l[22]};return l[1]!==void 0&&(E.active_source=l[1]),o=new Me({props:E}),M.push(()=>Q(o,"active_source",O)),{c(){V(e.$$.fragment),s=Y(),t=R("div"),u&&u.c(),f=Y(),V(o.$$.fragment),F(t,"data-testid","video"),F(t,"class","video-container svelte-14jis2k")},m(m,k){B(e,m,k),I(m,s,k),I(m,t,k),~i&&w[i].m(t,null),oe(t,f),B(o,t,null),a=!0},p(m,k){const N={};k[0]&128&&(N.show_label=m[7]),k[0]&32&&(N.label=m[5]||"Video"),e.$set(N);let P=i;i=g(m),i===P?~i&&w[i].p(m,k):(u&&(Z(),p(w[P],1,1,()=>{w[P]=null}),y()),~i?(u=w[i],u?u.p(m,k):(u=w[i]=d[i](m),u.c()),v(u,1),u.m(t,f)):u=null);const U={};k[0]&16&&(U.sources=m[4]),!h&&k[0]&2&&(h=!0,U.active_source=m[1],X(()=>h=!1)),o.$set(U)},i(m){a||(v(e.$$.fragment,m),v(u),v(o.$$.fragment,m),a=!0)},o(m){p(e.$$.fragment,m),p(u),p(o.$$.fragment,m),a=!1},d(m){m&&(j(s),j(t)),S(e,m),~i&&w[i].d(),S(o)}}}function it(l,e,s){let{$$slots:t={},$$scope:n}=e,{value:i=null}=e,{subtitle:u=null}=e,{sources:f=["webcam","upload"]}=e,{label:o=void 0}=e,{show_download_button:h=!1}=e,{show_label:a=!0}=e,{mirror_webcam:d=!1}=e,{include_audio:w}=e,{autoplay:g}=e,{root:O}=e,{i18n:E}=e,{active_source:m="webcam"}=e,{handle_reset_value:k=()=>{}}=e,{max_file_size:N=null}=e,{upload:P}=e,{stream_handler:U}=e,{loop:b}=e,{uploading:W=!1}=e,D=!1;const z=De();function G({detail:r}){s(0,i=r),z("change",r),z("upload",r)}function H(){s(0,i=null),z("change",null),z("clear")}function C(r){s(19,D=!0),z("change",r)}function L({detail:r}){z("change",r)}let q=!1;function T(r){q=r,s(18,q)}function J(r){W=r,s(2,W)}const $=({detail:r})=>z("error",r);function K(r){A.call(this,l,r)}function x(r){A.call(this,l,r)}function ee(r){A.call(this,l,r)}function te(r){A.call(this,l,r)}function le(r){A.call(this,l,r)}function se(r){A.call(this,l,r)}function ne(r){A.call(this,l,r)}function ie(r){m=r,s(1,m)}return l.$$set=r=>{"value"in r&&s(0,i=r.value),"subtitle"in r&&s(3,u=r.subtitle),"sources"in r&&s(4,f=r.sources),"label"in r&&s(5,o=r.label),"show_download_button"in r&&s(6,h=r.show_download_button),"show_label"in r&&s(7,a=r.show_label),"mirror_webcam"in r&&s(8,d=r.mirror_webcam),"include_audio"in r&&s(9,w=r.include_audio),"autoplay"in r&&s(10,g=r.autoplay),"root"in r&&s(11,O=r.root),"i18n"in r&&s(12,E=r.i18n),"active_source"in r&&s(1,m=r.active_source),"handle_reset_value"in r&&s(13,k=r.handle_reset_value),"max_file_size"in r&&s(14,N=r.max_file_size),"upload"in r&&s(15,P=r.upload),"stream_handler"in r&&s(16,U=r.stream_handler),"loop"in r&&s(17,b=r.loop),"uploading"in r&&s(2,W=r.uploading),"$$scope"in r&&s(37,n=r.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&262144&&z("drag",q)},[i,m,W,u,f,o,h,a,d,w,g,O,E,k,N,P,U,b,q,D,z,G,H,C,L,t,T,J,$,K,x,ee,te,le,se,ne,ie,n]}class ot extends ce{constructor(e){super(),de(this,e,it,nt,ue,{value:0,subtitle:3,sources:4,label:5,show_download_button:6,show_label:7,mirror_webcam:8,include_audio:9,autoplay:10,root:11,i18n:12,active_source:1,handle_reset_value:13,max_file_size:14,upload:15,stream_handler:16,loop:17,uploading:2},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),c()}get subtitle(){return this.$$.ctx[3]}set subtitle(e){this.$$set({subtitle:e}),c()}get sources(){return this.$$.ctx[4]}set sources(e){this.$$set({sources:e}),c()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),c()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),c()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),c()}get mirror_webcam(){return this.$$.ctx[8]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),c()}get include_audio(){return this.$$.ctx[9]}set include_audio(e){this.$$set({include_audio:e}),c()}get autoplay(){return this.$$.ctx[10]}set autoplay(e){this.$$set({autoplay:e}),c()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),c()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),c()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),c()}get handle_reset_value(){return this.$$.ctx[13]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),c()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),c()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),c()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),c()}get loop(){return this.$$.ctx[17]}set loop(e){this.$$set({loop:e}),c()}get uploading(){return this.$$.ctx[2]}set uploading(e){this.$$set({uploading:e}),c()}}const at=ot;function ut(l){let e,s;return e=new ge({props:{visible:l[4],variant:l[0]===null&&l[23]==="upload"?"dashed":"solid",border_mode:l[26]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,$$slots:{default:[ft]},$$scope:{ctx:l}}}),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&16&&(i.visible=t[4]),n[0]&8388609&&(i.variant=t[0]===null&&t[23]==="upload"?"dashed":"solid"),n[0]&67108864&&(i.border_mode=t[26]?"focus":"base"),n[0]&4&&(i.elem_id=t[2]),n[0]&8&&(i.elem_classes=t[3]),n[0]&512&&(i.height=t[9]),n[0]&1024&&(i.width=t[10]),n[0]&2048&&(i.container=t[11]),n[0]&4096&&(i.scale=t[12]),n[0]&8192&&(i.min_width=t[13]),n[0]&133906914|n[1]&8388608&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function rt(l){let e,s;return e=new ge({props:{visible:l[4],variant:l[0]===null&&l[23]==="upload"?"dashed":"solid",border_mode:l[26]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,$$slots:{default:[ht]},$$scope:{ctx:l}}}),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&16&&(i.visible=t[4]),n[0]&8388609&&(i.variant=t[0]===null&&t[23]==="upload"?"dashed":"solid"),n[0]&67108864&&(i.border_mode=t[26]?"focus":"base"),n[0]&4&&(i.elem_id=t[2]),n[0]&8&&(i.elem_classes=t[3]),n[0]&512&&(i.height=t[9]),n[0]&1024&&(i.width=t[10]),n[0]&2048&&(i.container=t[11]),n[0]&4096&&(i.scale=t[12]),n[0]&8192&&(i.min_width=t[13]),n[0]&52674850|n[1]&8388608&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function _t(l){let e,s;return e=new ye({props:{i18n:l[17].i18n,type:"video"}}),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&131072&&(i.i18n=t[17].i18n),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function ft(l){let e,s,t,n,i;const u=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[1]];let f={};for(let a=0;a<u.length;a+=1)f=be(f,u[a]);e=new we({props:f}),e.$on("clear_status",l[41]);function o(a){l[44](a)}let h={value:l[24],subtitle:l[25],label:l[5],show_label:l[8],show_download_button:l[16],sources:l[6],active_source:l[23],mirror_webcam:l[19],include_audio:l[20],autoplay:l[14],root:l[7],loop:l[21],handle_reset_value:l[27],i18n:l[17].i18n,max_file_size:l[17].max_file_size,upload:l[42],stream_handler:l[43],$$slots:{default:[_t]},$$scope:{ctx:l}};return l[22]!==void 0&&(h.uploading=l[22]),t=new at({props:h}),M.push(()=>Q(t,"uploading",o)),t.$on("change",l[28]),t.$on("drag",l[45]),t.$on("error",l[29]),t.$on("clear",l[46]),t.$on("play",l[47]),t.$on("pause",l[48]),t.$on("upload",l[49]),t.$on("stop",l[50]),t.$on("end",l[51]),t.$on("start_recording",l[52]),t.$on("stop_recording",l[53]),{c(){V(e.$$.fragment),s=Y(),V(t.$$.fragment)},m(a,d){B(e,a,d),I(a,s,d),B(t,a,d),i=!0},p(a,d){const w=d[0]&131074?pe(u,[d[0]&131072&&{autoscroll:a[17].autoscroll},d[0]&131072&&{i18n:a[17].i18n},d[0]&2&&ve(a[1])]):{};e.$set(w);const g={};d[0]&16777216&&(g.value=a[24]),d[0]&33554432&&(g.subtitle=a[25]),d[0]&32&&(g.label=a[5]),d[0]&256&&(g.show_label=a[8]),d[0]&65536&&(g.show_download_button=a[16]),d[0]&64&&(g.sources=a[6]),d[0]&8388608&&(g.active_source=a[23]),d[0]&524288&&(g.mirror_webcam=a[19]),d[0]&1048576&&(g.include_audio=a[20]),d[0]&16384&&(g.autoplay=a[14]),d[0]&128&&(g.root=a[7]),d[0]&2097152&&(g.loop=a[21]),d[0]&131072&&(g.i18n=a[17].i18n),d[0]&131072&&(g.max_file_size=a[17].max_file_size),d[0]&131072&&(g.upload=a[42]),d[0]&131072&&(g.stream_handler=a[43]),d[0]&131072|d[1]&8388608&&(g.$$scope={dirty:d,ctx:a}),!n&&d[0]&4194304&&(n=!0,g.uploading=a[22],X(()=>n=!1)),t.$set(g)},i(a){i||(v(e.$$.fragment,a),v(t.$$.fragment,a),i=!0)},o(a){p(e.$$.fragment,a),p(t.$$.fragment,a),i=!1},d(a){a&&j(s),S(e,a),S(t,a)}}}function ht(l){let e,s,t,n;const i=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[1]];let u={};for(let f=0;f<i.length;f+=1)u=be(u,i[f]);return e=new we({props:u}),e.$on("clear_status",l[33]),t=new Ze({props:{value:l[24],subtitle:l[25],label:l[5],show_label:l[8],autoplay:l[14],loop:l[21],show_share_button:l[15],show_download_button:l[16],i18n:l[17].i18n,upload:l[34]}}),t.$on("play",l[35]),t.$on("pause",l[36]),t.$on("stop",l[37]),t.$on("end",l[38]),t.$on("share",l[39]),t.$on("error",l[40]),{c(){V(e.$$.fragment),s=Y(),V(t.$$.fragment)},m(f,o){B(e,f,o),I(f,s,o),B(t,f,o),n=!0},p(f,o){const h=o[0]&131074?pe(i,[o[0]&131072&&{autoscroll:f[17].autoscroll},o[0]&131072&&{i18n:f[17].i18n},o[0]&2&&ve(f[1])]):{};e.$set(h);const a={};o[0]&16777216&&(a.value=f[24]),o[0]&33554432&&(a.subtitle=f[25]),o[0]&32&&(a.label=f[5]),o[0]&256&&(a.show_label=f[8]),o[0]&16384&&(a.autoplay=f[14]),o[0]&2097152&&(a.loop=f[21]),o[0]&32768&&(a.show_share_button=f[15]),o[0]&65536&&(a.show_download_button=f[16]),o[0]&131072&&(a.i18n=f[17].i18n),o[0]&131072&&(a.upload=f[34]),t.$set(a)},i(f){n||(v(e.$$.fragment,f),v(t.$$.fragment,f),n=!0)},o(f){p(e.$$.fragment,f),p(t.$$.fragment,f),n=!1},d(f){f&&j(s),S(e,f),S(t,f)}}}function ct(l){let e,s,t,n;const i=[rt,ut],u=[];function f(o,h){return o[18]?1:0}return e=f(l),s=u[e]=i[e](l),{c(){s.c(),t=me()},m(o,h){u[e].m(o,h),I(o,t,h),n=!0},p(o,h){let a=e;e=f(o),e===a?u[e].p(o,h):(Z(),p(u[a],1,1,()=>{u[a]=null}),y(),s=u[e],s?s.p(o,h):(s=u[e]=i[e](o),s.c()),v(s,1),s.m(t.parentNode,t))},i(o){n||(v(s),n=!0)},o(o){p(s),n=!1},d(o){o&&j(t),u[e].d(o)}}}function dt(l,e,s){let{elem_id:t=""}=e,{elem_classes:n=[]}=e,{visible:i=!0}=e,{value:u=null}=e,f=null,{label:o}=e,{sources:h}=e,{root:a}=e,{show_label:d}=e,{loading_status:w}=e,{height:g}=e,{width:O}=e,{container:E=!1}=e,{scale:m=null}=e,{min_width:k=void 0}=e,{autoplay:N=!1}=e,{show_share_button:P=!0}=e,{show_download_button:U}=e,{gradio:b}=e,{interactive:W}=e,{mirror_webcam:D}=e,{include_audio:z}=e,{loop:G=!1}=e,{input_ready:H}=e,C=!1,L=null,q=null,T,J=u;const $=()=>{J===null||u===J||s(0,u=J)};let K=!1;function x({detail:_}){_!=null?s(0,u={video:_,subtitles:null}):s(0,u=null)}function ee({detail:_}){const[Ae,Ce]=_.includes("Invalid file type")?["warning","complete"]:["error","error"];s(1,w=w||{}),s(1,w.status=Ce,w),s(1,w.message=_,w),b.dispatch(Ae,_)}const te=()=>b.dispatch("clear_status",w),le=(..._)=>b.client.upload(..._),se=()=>b.dispatch("play"),ne=()=>b.dispatch("pause"),ie=()=>b.dispatch("stop"),r=()=>b.dispatch("end"),ke=({detail:_})=>b.dispatch("share",_),ze=({detail:_})=>b.dispatch("error",_),Ve=()=>b.dispatch("clear_status",w),Be=(..._)=>b.client.upload(..._),Se=(..._)=>b.client.stream(..._);function Ie(_){C=_,s(22,C)}const je=({detail:_})=>s(26,K=_),Ne=()=>b.dispatch("clear"),Pe=()=>b.dispatch("play"),Ue=()=>b.dispatch("pause"),Ee=()=>b.dispatch("upload"),qe=()=>b.dispatch("stop"),Je=()=>b.dispatch("end"),Oe=()=>b.dispatch("start_recording"),We=()=>b.dispatch("stop_recording");return l.$$set=_=>{"elem_id"in _&&s(2,t=_.elem_id),"elem_classes"in _&&s(3,n=_.elem_classes),"visible"in _&&s(4,i=_.visible),"value"in _&&s(0,u=_.value),"label"in _&&s(5,o=_.label),"sources"in _&&s(6,h=_.sources),"root"in _&&s(7,a=_.root),"show_label"in _&&s(8,d=_.show_label),"loading_status"in _&&s(1,w=_.loading_status),"height"in _&&s(9,g=_.height),"width"in _&&s(10,O=_.width),"container"in _&&s(11,E=_.container),"scale"in _&&s(12,m=_.scale),"min_width"in _&&s(13,k=_.min_width),"autoplay"in _&&s(14,N=_.autoplay),"show_share_button"in _&&s(15,P=_.show_share_button),"show_download_button"in _&&s(16,U=_.show_download_button),"gradio"in _&&s(17,b=_.gradio),"interactive"in _&&s(18,W=_.interactive),"mirror_webcam"in _&&s(19,D=_.mirror_webcam),"include_audio"in _&&s(20,z=_.include_audio),"loop"in _&&s(21,G=_.loop),"input_ready"in _&&s(30,H=_.input_ready)},l.$$.update=()=>{l.$$.dirty[0]&4194304&&s(30,H=!C),l.$$.dirty[0]&1|l.$$.dirty[1]&2&&u&&J===null&&s(32,J=u),l.$$.dirty[0]&8388672&&h&&!T&&s(23,T=h[0]),l.$$.dirty[0]&1&&(u!=null?(s(24,L=u.video),s(25,q=u.subtitles)):(s(24,L=null),s(25,q=null))),l.$$.dirty[0]&131073|l.$$.dirty[1]&1&&JSON.stringify(u)!==JSON.stringify(f)&&(s(31,f=u),b.dispatch("change"))},[u,w,t,n,i,o,h,a,d,g,O,E,m,k,N,P,U,b,W,D,z,G,C,T,L,q,K,$,x,ee,H,f,J,te,le,se,ne,ie,r,ke,ze,Ve,Be,Se,Ie,je,Ne,Pe,Ue,Ee,qe,Je,Oe,We]}class mt extends ce{constructor(e){super(),de(this,e,dt,ct,ue,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,sources:6,root:7,show_label:8,loading_status:1,height:9,width:10,container:11,scale:12,min_width:13,autoplay:14,show_share_button:15,show_download_button:16,gradio:17,interactive:18,mirror_webcam:19,include_audio:20,loop:21,input_ready:30},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),c()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),c()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),c()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),c()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),c()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),c()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),c()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),c()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),c()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),c()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),c()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),c()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),c()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),c()}get autoplay(){return this.$$.ctx[14]}set autoplay(e){this.$$set({autoplay:e}),c()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),c()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),c()}get gradio(){return this.$$.ctx[17]}set gradio(e){this.$$set({gradio:e}),c()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),c()}get mirror_webcam(){return this.$$.ctx[19]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),c()}get include_audio(){return this.$$.ctx[20]}set include_audio(e){this.$$set({include_audio:e}),c()}get loop(){return this.$$.ctx[21]}set loop(e){this.$$set({loop:e}),c()}get input_ready(){return this.$$.ctx[30]}set input_ready(e){this.$$set({input_ready:e}),c()}}const Xt=mt;export{el as BaseExample,at as BaseInteractiveVideo,Xe as BasePlayer,Ze as BaseStaticVideo,Xt as default,$t as loaded,Re as playable,fe as prettyBytes};
//# sourceMappingURL=index-DcpV9357.js.map
