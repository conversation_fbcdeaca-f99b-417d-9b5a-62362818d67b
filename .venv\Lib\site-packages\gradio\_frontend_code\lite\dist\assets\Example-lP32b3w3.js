import{a as r,i as d,s as g,f as u,p as h,k as v,q as o,r as i,l as y,v as m,n as _,w as c,o as b}from"../lite.js";function q(a){let e,l;return{c(){e=h("div"),l=v(a[0]),o(e,"class","svelte-1ayixqk"),i(e,"table",a[1]==="table"),i(e,"gallery",a[1]==="gallery"),i(e,"selected",a[2])},m(t,s){y(t,e,s),m(e,l)},p(t,[s]){s&1&&_(l,t[0]),s&2&&i(e,"table",t[1]==="table"),s&2&&i(e,"gallery",t[1]==="gallery"),s&4&&i(e,"selected",t[2])},i:c,o:c,d(t){t&&b(e)}}}function k(a,e,l){let{value:t}=e,{type:s}=e,{selected:f=!1}=e;return a.$$set=n=>{"value"in n&&l(0,t=n.value),"type"in n&&l(1,s=n.type),"selected"in n&&l(2,f=n.selected)},[t,s,f]}class x extends r{constructor(e){super(),d(this,e,k,q,g,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),u()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),u()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),u()}}export{x as default};
//# sourceMappingURL=Example-lP32b3w3.js.map
