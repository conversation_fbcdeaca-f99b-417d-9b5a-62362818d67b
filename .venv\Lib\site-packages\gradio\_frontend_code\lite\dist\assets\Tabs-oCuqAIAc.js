import{a as de,i as be,s as he,Q as J,q as _,l as E,v as p,w as W,o as M,f as G,e as ze,p as q,x as S,r as z,F as Q,t as P,y as Be,b as L,z as Te,u as je,h as qe,j as Ee,P as Me,b1 as ee,am as te,A as Ae,a3 as Ne,b2 as Oe,b3 as K,ao as F,c as Re,m as Se,aw as ye,b4 as le,b5 as ie,d as De,aq as Fe,E as U,k as Y,n as Z,I as X,G as Ge}from"../lite.js";function Pe(t){let e,l,s,i;return{c(){e=J("svg"),l=J("circle"),s=J("circle"),i=J("circle"),_(l,"cx","2.5"),_(l,"cy","8"),_(l,"r","1.5"),_(l,"fill","currentColor"),_(s,"cx","8"),_(s,"cy","8"),_(s,"r","1.5"),_(s,"fill","currentColor"),_(i,"cx","13.5"),_(i,"cy","8"),_(i,"r","1.5"),_(i,"fill","currentColor"),_(e,"width","16"),_(e,"height","16"),_(e,"viewBox","0 0 16 16"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(o,f){E(o,e,f),p(e,l),p(e,s),p(e,i)},p:W,i:W,o:W,d(o){o&&M(e)}}}class Qe extends de{constructor(e){super(),be(this,e,null,Pe,he,{})}}function se(t,e,l){const s=t.slice();return s[32]=e[l],s}function ne(t,e,l){const s=t.slice();return s[32]=e[l],s[36]=l,s}function ae(t,e,l){const s=t.slice();return s[32]=e[l],s[37]=e,s[36]=l,s}function oe(t){let e,l,s=[],i=new Map,o,f,c=[],v=new Map,d,r,h,I,m,C,g,j,R,B=F(t[3]);const A=a=>a[32].id;for(let a=0;a<B.length;a+=1){let b=ae(t,B,a),u=A(b);i.set(u,s[a]=ce(u,b))}let N=F(t[7]);const y=a=>a[32].id;for(let a=0;a<N.length;a+=1){let b=ne(t,N,a),u=y(b);v.set(u,c[a]=re(u,b))}I=new Qe({});let O=F(t[8]),w=[];for(let a=0;a<O.length;a+=1)w[a]=ue(se(t,O,a));return{c(){e=q("div"),l=q("div");for(let a=0;a<s.length;a+=1)s[a].c();o=S(),f=q("div");for(let a=0;a<c.length;a+=1)c[a].c();d=S(),r=q("span"),h=q("button"),Re(I.$$.fragment),m=S(),C=q("div");for(let a=0;a<w.length;a+=1)w[a].c();_(l,"class","tab-container visually-hidden svelte-1tcem6n"),_(l,"aria-hidden","true"),_(f,"class","tab-container svelte-1tcem6n"),_(f,"role","tablist"),_(h,"class","svelte-1tcem6n"),z(h,"overflow-item-selected",t[12]),_(C,"class","overflow-dropdown svelte-1tcem6n"),z(C,"hide",!t[9]),_(r,"class","overflow-menu svelte-1tcem6n"),z(r,"hide",!t[11]),_(e,"class","tab-wrapper svelte-1tcem6n")},m(a,b){E(a,e,b),p(e,l);for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(l,null);p(e,o),p(e,f);for(let u=0;u<c.length;u+=1)c[u]&&c[u].m(f,null);t[26](f),p(e,d),p(e,r),p(r,h),Se(I,h,null),p(r,m),p(r,C);for(let u=0;u<w.length;u+=1)w[u]&&w[u].m(C,null);t[29](r),g=!0,j||(R=Q(h,"click",ye(t[27])),j=!0)},p(a,b){if(b[0]&40&&(B=F(a[3]),s=le(s,b,A,1,a,B,i,l,ie,ce,null,ae)),b[0]&196800&&(N=F(a[7]),c=le(c,b,y,1,a,N,v,f,ie,re,null,ne)),(!g||b[0]&4096)&&z(h,"overflow-item-selected",a[12]),b[0]&131392){O=F(a[8]);let u;for(u=0;u<O.length;u+=1){const D=se(a,O,u);w[u]?w[u].p(D,b):(w[u]=ue(D),w[u].c(),w[u].m(C,null))}for(;u<w.length;u+=1)w[u].d(1);w.length=O.length}(!g||b[0]&512)&&z(C,"hide",!a[9]),(!g||b[0]&2048)&&z(r,"hide",!a[11])},i(a){g||(P(I.$$.fragment,a),g=!0)},o(a){L(I.$$.fragment,a),g=!1},d(a){a&&M(e);for(let b=0;b<s.length;b+=1)s[b].d();for(let b=0;b<c.length;b+=1)c[b].d();t[26](null),De(I),Fe(w,a),t[29](null),j=!1,R()}}}function _e(t){let e,l=t[32].label+"",s,i,o=t[32];const f=()=>t[24](e,o),c=()=>t[24](null,o);return{c(){e=q("button"),s=Y(l),i=S(),_(e,"class","svelte-1tcem6n")},m(v,d){E(v,e,d),p(e,s),p(e,i),f()},p(v,d){t=v,d[0]&8&&l!==(l=t[32].label+"")&&Z(s,l),o!==t[32]&&(c(),o=t[32],f())},d(v){v&&M(e),c()}}}function ce(t,e){let l,s,i=e[32].visible&&_e(e);return{key:t,first:null,c(){l=U(),i&&i.c(),s=U(),this.first=l},m(o,f){E(o,l,f),i&&i.m(o,f),E(o,s,f)},p(o,f){e=o,e[32].visible?i?i.p(e,f):(i=_e(e),i.c(),i.m(s.parentNode,s)):i&&(i.d(1),i=null)},d(o){o&&(M(l),M(s)),i&&i.d(o)}}}function fe(t){let e,l=t[32].label+"",s,i,o,f,c,v,d,r,h,I;function m(){return t[25](t[32],t[36])}return{c(){e=q("button"),s=Y(l),i=S(),_(e,"role","tab"),_(e,"aria-selected",o=t[32].id===t[6]),_(e,"aria-controls",f=t[32].elem_id),e.disabled=c=!t[32].interactive,_(e,"aria-disabled",v=!t[32].interactive),_(e,"id",d=t[32].elem_id?t[32].elem_id+"-button":null),_(e,"data-tab-id",r=t[32].id),_(e,"class","svelte-1tcem6n"),z(e,"selected",t[32].id===t[6])},m(C,g){E(C,e,g),p(e,s),p(e,i),h||(I=Q(e,"click",m),h=!0)},p(C,g){t=C,g[0]&128&&l!==(l=t[32].label+"")&&Z(s,l),g[0]&192&&o!==(o=t[32].id===t[6])&&_(e,"aria-selected",o),g[0]&128&&f!==(f=t[32].elem_id)&&_(e,"aria-controls",f),g[0]&128&&c!==(c=!t[32].interactive)&&(e.disabled=c),g[0]&128&&v!==(v=!t[32].interactive)&&_(e,"aria-disabled",v),g[0]&128&&d!==(d=t[32].elem_id?t[32].elem_id+"-button":null)&&_(e,"id",d),g[0]&128&&r!==(r=t[32].id)&&_(e,"data-tab-id",r),g[0]&192&&z(e,"selected",t[32].id===t[6])},d(C){C&&M(e),h=!1,I()}}}function re(t,e){let l,s,i=e[32].visible&&fe(e);return{key:t,first:null,c(){l=U(),i&&i.c(),s=U(),this.first=l},m(o,f){E(o,l,f),i&&i.m(o,f),E(o,s,f)},p(o,f){e=o,e[32].visible?i?i.p(e,f):(i=fe(e),i.c(),i.m(s.parentNode,s)):i&&(i.d(1),i=null)},d(o){o&&(M(l),M(s)),i&&i.d(o)}}}function ue(t){let e,l=t[32].label+"",s,i,o,f;function c(){return t[28](t[32])}return{c(){e=q("button"),s=Y(l),i=S(),_(e,"class","svelte-1tcem6n"),z(e,"selected",t[32].id===t[6])},m(v,d){E(v,e,d),p(e,s),p(e,i),o||(f=Q(e,"click",c),o=!0)},p(v,d){t=v,d[0]&256&&l!==(l=t[32].label+"")&&Z(s,l),d[0]&320&&z(e,"selected",t[32].id===t[6])},d(v){v&&M(e),o=!1,f()}}}function He(t){let e,l,s,i,o,f,c=t[13]&&oe(t);const v=t[23].default,d=ze(v,t,t[22],null);return{c(){e=q("div"),c&&c.c(),l=S(),d&&d.c(),_(e,"class",s="tabs "+t[2].join(" ")+" svelte-1tcem6n"),_(e,"id",t[1]),z(e,"hide",!t[0])},m(r,h){E(r,e,h),c&&c.m(e,null),p(e,l),d&&d.m(e,null),i=!0,o||(f=[Q(window,"resize",t[19]),Q(window,"click",t[18])],o=!0)},p(r,h){r[13]?c?(c.p(r,h),h[0]&8192&&P(c,1)):(c=oe(r),c.c(),P(c,1),c.m(e,l)):c&&(Be(),L(c,1,1,()=>{c=null}),Te()),d&&d.p&&(!i||h[0]&4194304)&&je(d,v,r,r[22],i?Ee(v,r[22],h,null):qe(r[22]),null),(!i||h[0]&4&&s!==(s="tabs "+r[2].join(" ")+" svelte-1tcem6n"))&&_(e,"class",s),(!i||h[0]&2)&&_(e,"id",r[1]),(!i||h[0]&5)&&z(e,"hide",!r[0])},i(r){i||(P(c),P(d,r),i=!0)},o(r){L(c),L(d,r),i=!1},d(r){r&&M(e),c&&c.d(),d&&d.d(r),o=!1,Me(f)}}}const Je={};function Ke(t,e){const l={};return t.forEach(s=>{l[s.id]=e[s.id]?.getBoundingClientRect()}),l}function Le(t,e,l){let s,i,o,{$$slots:f={},$$scope:c}=e,{visible:v=!0}=e,{elem_id:d=""}=e,{elem_classes:r=[]}=e,{selected:h}=e,{initial_tabs:I}=e,m=[...I],C=[...I],g=[],j=!1,R,B;const A=ee(h||m[0]?.id||!1);te(t,A,n=>l(6,i=n));const N=ee(m.findIndex(n=>n.id===h)||0);te(t,N,n=>l(30,o=n));const y=Ae();let O=!1,w=!1,a={};Ne(()=>{new IntersectionObserver(k=>{D()}).observe(B)}),Oe(Je,{register_tab:n=>{let k=m.findIndex(T=>T.id===n.id);return k!==-1?l(3,m[k]={...m[k],...n},m):(l(3,m=[...m,n]),k=m.length-1),i===!1&&n.visible&&n.interactive&&K(A,i=n.id,i),k},unregister_tab:n=>{m.findIndex(T=>T.id===n.id)!==-1&&(l(3,m=m.filter(T=>T.id!==n.id)),i===n.id&&K(A,i=m[0]?.id||!1,i))},selected_tab:A,selected_tab_index:N});function b(n){const k=m.find(T=>T.id===n);k&&k.interactive&&k.visible&&i!==k.id&&(l(20,h=n),K(A,i=n,i),K(N,o=m.findIndex(T=>T.id===n),o),y("change"),l(9,j=!1))}function u(n){j&&R&&!R.contains(n.target)&&l(9,j=!1)}async function D(){if(!B)return;await Ge();const n=B.getBoundingClientRect();let k=n.width;const T=Ke(m,a);let V=0;const Ce=n.left;for(let H=m.length-1;H>=0;H--){const Ie=m[H],x=T[Ie.id];if(x&&x.right-Ce<k){V=H;break}}l(8,g=m.slice(V+1)),l(7,C=m.slice(0,V+1)),l(12,w=$(i)),l(11,O=g.length>0)}function $(n){return n===!1?!1:g.some(k=>k.id===n)}function me(n,k){X[n?"unshift":"push"](()=>{a[k.id]=n,l(5,a)})}const ve=(n,k)=>{n.id!==i&&(b(n.id),y("select",{value:n.label,index:k}))};function ge(n){X[n?"unshift":"push"](()=>{B=n,l(4,B)})}const ke=()=>l(9,j=!j),we=n=>b(n.id);function pe(n){X[n?"unshift":"push"](()=>{R=n,l(10,R)})}return t.$$set=n=>{"visible"in n&&l(0,v=n.visible),"elem_id"in n&&l(1,d=n.elem_id),"elem_classes"in n&&l(2,r=n.elem_classes),"selected"in n&&l(20,h=n.selected),"initial_tabs"in n&&l(21,I=n.initial_tabs),"$$scope"in n&&l(22,c=n.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&8&&l(13,s=m.length>0),t.$$.dirty[0]&1048584&&h!==null&&b(h),t.$$.dirty[0]&56&&D(),t.$$.dirty[0]&64&&l(12,w=$(i))},[v,d,r,m,B,a,i,C,g,j,R,O,w,s,A,N,y,b,u,D,h,I,c,f,me,ve,ge,ke,we,pe]}class Ue extends de{constructor(e){super(),be(this,e,Le,He,he,{visible:0,elem_id:1,elem_classes:2,selected:20,initial_tabs:21},null,[-1,-1])}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),G()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),G()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),G()}get selected(){return this.$$.ctx[20]}set selected(e){this.$$set({selected:e}),G()}get initial_tabs(){return this.$$.ctx[21]}set initial_tabs(e){this.$$set({initial_tabs:e}),G()}}const We=Ue;export{We as T,Je as a};
//# sourceMappingURL=Tabs-oCuqAIAc.js.map
