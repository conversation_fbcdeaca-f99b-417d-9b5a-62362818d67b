import{a as C,i as E,s as N,f as d,p as h,q as w,r as u,l as o,w as g,o as _,ao as b,x as S,E as j,aq as z,k as p,v as m,n as B}from"../lite.js";function v(n,e,l){const s=n.slice();return s[3]=e[l],s}function k(n){let e,l=Array.isArray(n[0])&&n[0].length>3,s,f=b(Array.isArray(n[0])?n[0].slice(0,3):[n[0]]),i=[];for(let t=0;t<f.length;t+=1)i[t]=A(v(n,f,t));let a=l&&q();return{c(){for(let t=0;t<i.length;t+=1)i[t].c();e=S(),a&&a.c(),s=j()},m(t,c){for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(t,c);o(t,e,c),a&&a.m(t,c),o(t,s,c)},p(t,c){if(c&1){f=b(Array.isArray(t[0])?t[0].slice(0,3):[t[0]]);let r;for(r=0;r<f.length;r+=1){const y=v(t,f,r);i[r]?i[r].p(y,c):(i[r]=A(y),i[r].c(),i[r].m(e.parentNode,e))}for(;r<i.length;r+=1)i[r].d(1);i.length=f.length}c&1&&(l=Array.isArray(t[0])&&t[0].length>3),l?a||(a=q(),a.c(),a.m(s.parentNode,s)):a&&(a.d(1),a=null)},d(t){t&&(_(e),_(s)),z(i,t),a&&a.d(t)}}}function A(n){let e,l,s,f=n[3]+"",i;return{c(){e=h("li"),l=h("code"),s=p("./"),i=p(f)},m(a,t){o(a,e,t),m(e,l),m(l,s),m(l,i)},p(a,t){t&1&&f!==(f=a[3]+"")&&B(i,f)},d(a){a&&_(e)}}}function q(n){let e;return{c(){e=h("li"),e.textContent="...",w(e,"class","extra svelte-4tf8f")},m(l,s){o(l,e,s)},d(l){l&&_(e)}}}function D(n){let e,l=n[0]&&k(n);return{c(){e=h("ul"),l&&l.c(),w(e,"class","svelte-4tf8f"),u(e,"table",n[1]==="table"),u(e,"gallery",n[1]==="gallery"),u(e,"selected",n[2])},m(s,f){o(s,e,f),l&&l.m(e,null)},p(s,[f]){s[0]?l?l.p(s,f):(l=k(s),l.c(),l.m(e,null)):l&&(l.d(1),l=null),f&2&&u(e,"table",s[1]==="table"),f&2&&u(e,"gallery",s[1]==="gallery"),f&4&&u(e,"selected",s[2])},i:g,o:g,d(s){s&&_(e),l&&l.d()}}}function F(n,e,l){let{value:s}=e,{type:f}=e,{selected:i=!1}=e;return n.$$set=a=>{"value"in a&&l(0,s=a.value),"type"in a&&l(1,f=a.type),"selected"in a&&l(2,i=a.selected)},[s,f,i]}class H extends C{constructor(e){super(),E(this,e,F,D,N,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),d()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),d()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),d()}}export{H as default};
//# sourceMappingURL=Example-C5MYwHrq.js.map
