import{a as ue,i as oe,s as fe,Q as Oe,q as v,l as R,v as I,w as we,o as U,f as k,J as Me,p as L,x as F,E as Te,F as H,t as q,y as x,b as M,z as $,P as re,A as pe,ao as he,$ as V,a1 as ke,bk as Ae,aq as Ie,k as _e,r as T,n as ce,I as X,c as Q,m as Y,M as de,d as G,D as Le,B as Ke,Y as Ve,S as We,a0 as Xe,a4 as Ze,a5 as me,a6 as be}from"../lite.js";import{B as Re}from"./BlockTitle-BlPSRItZ.js";import{D as Ue}from"./DropdownArrow-B85Vabzi.js";import{a as Ee}from"./index-DCygRGWm.js";import{default as St}from"./Example-DCrHN0lL.js";import"./Info-2h-pcw07.js";import"./MarkdownCode-C6RHM0m6.js";function xe(l){let e,t;return{c(){e=Oe("svg"),t=Oe("path"),v(t,"d","M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"),v(e,"xmlns","http://www.w3.org/2000/svg"),v(e,"viewBox","0 0 24 24"),v(e,"width","100%"),v(e,"height","100%")},m(n,o){R(n,e,o),I(e,t)},p:we,i:we,o:we,d(n){n&&U(e)}}}class He extends ue{constructor(e){super(),oe(this,e,null,xe,fe,{})}}function Be(l,e,t){const n=l.slice();return n[24]=e[t],n}function De(l){let e,t,n,o,u,_=he(l[1]),i=[];for(let s=0;s<_.length;s+=1)i[s]=Ne(Be(l,_,s));return{c(){e=L("ul");for(let s=0;s<i.length;s+=1)i[s].c();v(e,"class","options svelte-y6qw75"),v(e,"role","listbox"),V(e,"top",l[9]),V(e,"bottom",l[10]),V(e,"max-height",`calc(${l[11]}px - var(--window-padding))`),V(e,"width",l[8]+"px")},m(s,r){R(s,e,r);for(let f=0;f<i.length;f+=1)i[f]&&i[f].m(e,null);l[21](e),n=!0,o||(u=H(e,"mousedown",ke(l[20])),o=!0)},p(s,r){if(r&307){_=he(s[1]);let f;for(f=0;f<_.length;f+=1){const O=Be(s,_,f);i[f]?i[f].p(O,r):(i[f]=Ne(O),i[f].c(),i[f].m(e,null))}for(;f<i.length;f+=1)i[f].d(1);i.length=_.length}r&512&&V(e,"top",s[9]),r&1024&&V(e,"bottom",s[10]),r&2048&&V(e,"max-height",`calc(${s[11]}px - var(--window-padding))`),r&256&&V(e,"width",s[8]+"px")},i(s){n||(s&&Me(()=>{n&&(t||(t=Ae(e,Ee,{duration:200,y:5},!0)),t.run(1))}),n=!0)},o(s){s&&(t||(t=Ae(e,Ee,{duration:200,y:5},!1)),t.run(0)),n=!1},d(s){s&&U(e),Ie(i,s),l[21](null),s&&t&&t.end(),o=!1,u()}}}function Ne(l){let e,t,n,o=l[0][l[24]][0]+"",u,_,i,s,r;return{c(){e=L("li"),t=L("span"),t.textContent="✓",n=F(),u=_e(o),_=F(),v(t,"class","inner-item svelte-y6qw75"),T(t,"hide",!l[4].includes(l[24])),v(e,"class","item svelte-y6qw75"),v(e,"data-index",i=l[24]),v(e,"aria-label",s=l[0][l[24]][0]),v(e,"data-testid","dropdown-option"),v(e,"role","option"),v(e,"aria-selected",r=l[4].includes(l[24])),T(e,"selected",l[4].includes(l[24])),T(e,"active",l[24]===l[5]),T(e,"bg-gray-100",l[24]===l[5]),T(e,"dark:bg-gray-600",l[24]===l[5]),V(e,"width",l[8]+"px")},m(f,O){R(f,e,O),I(e,t),I(e,n),I(e,u),I(e,_)},p(f,O){O&18&&T(t,"hide",!f[4].includes(f[24])),O&3&&o!==(o=f[0][f[24]][0]+"")&&ce(u,o),O&2&&i!==(i=f[24])&&v(e,"data-index",i),O&3&&s!==(s=f[0][f[24]][0])&&v(e,"aria-label",s),O&18&&r!==(r=f[4].includes(f[24]))&&v(e,"aria-selected",r),O&18&&T(e,"selected",f[4].includes(f[24])),O&34&&T(e,"active",f[24]===f[5]),O&34&&T(e,"bg-gray-100",f[24]===f[5]),O&34&&T(e,"dark:bg-gray-600",f[24]===f[5]),O&256&&V(e,"width",f[8]+"px")},d(f){f&&U(e)}}}function $e(l){let e,t,n,o,u;Me(l[18]);let _=l[2]&&!l[3]&&De(l);return{c(){e=L("div"),t=F(),_&&_.c(),n=Te(),v(e,"class","reference")},m(i,s){R(i,e,s),l[19](e),R(i,t,s),_&&_.m(i,s),R(i,n,s),o||(u=[H(window,"scroll",l[13]),H(window,"resize",l[18])],o=!0)},p(i,[s]){i[2]&&!i[3]?_?(_.p(i,s),s&12&&q(_,1)):(_=De(i),_.c(),q(_,1),_.m(n.parentNode,n)):_&&(x(),M(_,1,1,()=>{_=null}),$())},i(i){q(_)},o(i){M(_)},d(i){i&&(U(e),U(t),U(n)),l[19](null),_&&_.d(i),o=!1,re(u)}}}function et(l,e,t){let{choices:n}=e,{filtered_indices:o}=e,{show_options:u=!1}=e,{disabled:_=!1}=e,{selected_indices:i=[]}=e,{active_index:s=null}=e,r,f,O,g,p,y,N,m,h,C;function b(){const{top:D,bottom:j}=p.getBoundingClientRect();t(15,r=D),t(16,f=C-j)}let c=null;function d(){u&&(c!==null&&clearTimeout(c),c=setTimeout(()=>{b(),c=null},10))}const z=pe();function J(){t(12,C=window.innerHeight)}function B(D){X[D?"unshift":"push"](()=>{p=D,t(6,p)})}const A=D=>z("change",D);function S(D){X[D?"unshift":"push"](()=>{y=D,t(7,y)})}return l.$$set=D=>{"choices"in D&&t(0,n=D.choices),"filtered_indices"in D&&t(1,o=D.filtered_indices),"show_options"in D&&t(2,u=D.show_options),"disabled"in D&&t(3,_=D.disabled),"selected_indices"in D&&t(4,i=D.selected_indices),"active_index"in D&&t(5,s=D.active_index)},l.$$.update=()=>{if(l.$$.dirty&229588){if(u&&p){if(y&&i.length>0){let j=y.querySelectorAll("li");for(const W of Array.from(j))if(W.getAttribute("data-index")===i[0].toString()){y?.scrollTo?.(0,W.offsetTop);break}}b();const D=p.parentElement?.getBoundingClientRect();t(17,O=D?.height||0),t(8,g=D?.width||0)}f>r?(t(9,N=`${r}px`),t(11,h=f),t(10,m=null)):(t(10,m=`${f+O}px`),t(11,h=r-O),t(9,N=null))}},[n,o,u,_,i,s,p,y,g,N,m,h,C,d,z,r,f,O,J,B,A,S]}class je extends ue{constructor(e){super(),oe(this,e,et,$e,fe,{choices:0,filtered_indices:1,show_options:2,disabled:3,selected_indices:4,active_index:5})}get choices(){return this.$$.ctx[0]}set choices(e){this.$$set({choices:e}),k()}get filtered_indices(){return this.$$.ctx[1]}set filtered_indices(e){this.$$set({filtered_indices:e}),k()}get show_options(){return this.$$.ctx[2]}set show_options(e){this.$$set({show_options:e}),k()}get disabled(){return this.$$.ctx[3]}set disabled(e){this.$$set({disabled:e}),k()}get selected_indices(){return this.$$.ctx[4]}set selected_indices(e){this.$$set({selected_indices:e}),k()}get active_index(){return this.$$.ctx[5]}set active_index(e){this.$$set({active_index:e}),k()}}function tt(l,e){return(l%e+e)%e}function ve(l,e){return l.reduce((t,n,o)=>((!e||n[0].toLowerCase().includes(e.toLowerCase()))&&t.push(o),t),[])}function Fe(l,e,t){l("change",e),t||l("input")}function Pe(l,e,t){if(l.key==="Escape")return[!1,e];if((l.key==="ArrowDown"||l.key==="ArrowUp")&&t.length>=0)if(e===null)e=l.key==="ArrowDown"?t[0]:t[t.length-1];else{const n=t.indexOf(e),o=l.key==="ArrowUp"?-1:1;e=t[tt(n+o,t.length)]}return[!0,e]}function Se(l,e,t){const n=l.slice();return n[41]=e[t],n}function lt(l){let e;return{c(){e=_e(l[0])},m(t,n){R(t,e,n)},p(t,n){n[0]&1&&ce(e,t[0])},d(t){t&&U(e)}}}function st(l){let e=l[41]+"",t;return{c(){t=_e(e)},m(n,o){R(n,t,o)},p(n,o){o[0]&8192&&e!==(e=n[41]+"")&&ce(t,e)},d(n){n&&U(t)}}}function nt(l){let e=l[16][l[41]]+"",t;return{c(){t=_e(e)},m(n,o){R(n,t,o)},p(n,o){o[0]&73728&&e!==(e=n[16][n[41]]+"")&&ce(t,e)},d(n){n&&U(t)}}}function qe(l){let e,t,n,o,u,_;t=new He({});function i(){return l[32](l[41])}function s(...r){return l[33](l[41],...r)}return{c(){e=L("div"),Q(t.$$.fragment),v(e,"class","token-remove svelte-1scun43"),v(e,"role","button"),v(e,"tabindex","0"),v(e,"title",n=l[9]("common.remove")+" "+l[41])},m(r,f){R(r,e,f),Y(t,e,null),o=!0,u||(_=[H(e,"click",ke(i)),H(e,"keydown",ke(s))],u=!0)},p(r,f){l=r,(!o||f[0]&8704&&n!==(n=l[9]("common.remove")+" "+l[41]))&&v(e,"title",n)},i(r){o||(q(t.$$.fragment,r),o=!0)},o(r){M(t.$$.fragment,r),o=!1},d(r){r&&U(e),G(t),u=!1,re(_)}}}function ye(l){let e,t,n,o;function u(r,f){return typeof r[41]=="number"?nt:st}let _=u(l),i=_(l),s=!l[4]&&qe(l);return{c(){e=L("div"),t=L("span"),i.c(),n=F(),s&&s.c(),v(t,"class","svelte-1scun43"),v(e,"class","token svelte-1scun43")},m(r,f){R(r,e,f),I(e,t),i.m(t,null),I(e,n),s&&s.m(e,null),o=!0},p(r,f){_===(_=u(r))&&i?i.p(r,f):(i.d(1),i=_(r),i&&(i.c(),i.m(t,null))),r[4]?s&&(x(),M(s,1,1,()=>{s=null}),$()):s?(s.p(r,f),f[0]&16&&q(s,1)):(s=qe(r),s.c(),q(s,1),s.m(e,null))},i(r){o||(q(s),o=!0)},o(r){M(s),o=!1},d(r){r&&U(e),i.d(),s&&s.d()}}}function Je(l){let e,t,n,o,u=l[13].length>0&&Ce(l);return n=new Ue({}),{c(){u&&u.c(),e=F(),t=L("span"),Q(n.$$.fragment),v(t,"class","icon-wrap svelte-1scun43")},m(_,i){u&&u.m(_,i),R(_,e,i),R(_,t,i),Y(n,t,null),o=!0},p(_,i){_[13].length>0?u?(u.p(_,i),i[0]&8192&&q(u,1)):(u=Ce(_),u.c(),q(u,1),u.m(e.parentNode,e)):u&&(x(),M(u,1,1,()=>{u=null}),$())},i(_){o||(q(u),q(n.$$.fragment,_),o=!0)},o(_){M(u),M(n.$$.fragment,_),o=!1},d(_){_&&(U(e),U(t)),u&&u.d(_),G(n)}}}function Ce(l){let e,t,n,o,u,_;return t=new He({}),{c(){e=L("div"),Q(t.$$.fragment),v(e,"role","button"),v(e,"tabindex","0"),v(e,"class","token-remove remove-all svelte-1scun43"),v(e,"title",n=l[9]("common.clear"))},m(i,s){R(i,e,s),Y(t,e,null),o=!0,u||(_=[H(e,"click",l[22]),H(e,"keydown",l[37])],u=!0)},p(i,s){(!o||s[0]&512&&n!==(n=i[9]("common.clear")))&&v(e,"title",n)},i(i){o||(q(t.$$.fragment,i),o=!0)},o(i){M(t.$$.fragment,i),o=!1},d(i){i&&U(e),G(t),u=!1,re(_)}}}function it(l){let e,t,n,o,u,_,i,s,r,f,O,g,p,y,N;t=new Re({props:{root:l[10],show_label:l[5],info:l[1],$$slots:{default:[lt]},$$scope:{ctx:l}}});let m=he(l[13]),h=[];for(let c=0;c<m.length;c+=1)h[c]=ye(Se(l,m,c));const C=c=>M(h[c],1,1,()=>{h[c]=null});let b=!l[4]&&Je(l);return g=new je({props:{show_options:l[15],choices:l[3],filtered_indices:l[12],disabled:l[4],selected_indices:l[13],active_index:l[17]}}),g.$on("change",l[21]),{c(){e=L("label"),Q(t.$$.fragment),n=F(),o=L("div"),u=L("div");for(let c=0;c<h.length;c+=1)h[c].c();_=F(),i=L("div"),s=L("input"),f=F(),b&&b.c(),O=F(),Q(g.$$.fragment),v(s,"class","border-none svelte-1scun43"),s.disabled=l[4],v(s,"autocomplete","off"),s.readOnly=r=!l[8],T(s,"subdued",!l[16].includes(l[11])&&!l[7]||l[13].length===l[2]),v(i,"class","secondary-wrap svelte-1scun43"),v(u,"class","wrap-inner svelte-1scun43"),T(u,"show_options",l[15]),v(o,"class","wrap svelte-1scun43"),v(e,"class","svelte-1scun43"),T(e,"container",l[6])},m(c,d){R(c,e,d),Y(t,e,null),I(e,n),I(e,o),I(o,u);for(let z=0;z<h.length;z+=1)h[z]&&h[z].m(u,null);I(u,_),I(u,i),I(i,s),de(s,l[11]),l[35](s),I(i,f),b&&b.m(i,null),I(o,O),Y(g,o,null),p=!0,y||(N=[H(s,"input",l[34]),H(s,"keydown",l[24]),H(s,"keyup",l[36]),H(s,"blur",l[19]),H(s,"focus",l[23])],y=!0)},p(c,d){const z={};if(d[0]&1024&&(z.root=c[10]),d[0]&32&&(z.show_label=c[5]),d[0]&2&&(z.info=c[1]),d[0]&1|d[1]&8192&&(z.$$scope={dirty:d,ctx:c}),t.$set(z),d[0]&1122832){m=he(c[13]);let B;for(B=0;B<m.length;B+=1){const A=Se(c,m,B);h[B]?(h[B].p(A,d),q(h[B],1)):(h[B]=ye(A),h[B].c(),q(h[B],1),h[B].m(u,_))}for(x(),B=m.length;B<h.length;B+=1)C(B);$()}(!p||d[0]&16)&&(s.disabled=c[4]),(!p||d[0]&256&&r!==(r=!c[8]))&&(s.readOnly=r),d[0]&2048&&s.value!==c[11]&&de(s,c[11]),(!p||d[0]&75908)&&T(s,"subdued",!c[16].includes(c[11])&&!c[7]||c[13].length===c[2]),c[4]?b&&(x(),M(b,1,1,()=>{b=null}),$()):b?(b.p(c,d),d[0]&16&&q(b,1)):(b=Je(c),b.c(),q(b,1),b.m(i,null)),(!p||d[0]&32768)&&T(u,"show_options",c[15]);const J={};d[0]&32768&&(J.show_options=c[15]),d[0]&8&&(J.choices=c[3]),d[0]&4096&&(J.filtered_indices=c[12]),d[0]&16&&(J.disabled=c[4]),d[0]&8192&&(J.selected_indices=c[13]),d[0]&131072&&(J.active_index=c[17]),g.$set(J),(!p||d[0]&64)&&T(e,"container",c[6])},i(c){if(!p){q(t.$$.fragment,c);for(let d=0;d<m.length;d+=1)q(h[d]);q(b),q(g.$$.fragment,c),p=!0}},o(c){M(t.$$.fragment,c),h=h.filter(Boolean);for(let d=0;d<h.length;d+=1)M(h[d]);M(b),M(g.$$.fragment,c),p=!1},d(c){c&&U(e),G(t),Ie(h,c),l[35](null),b&&b.d(),G(g),y=!1,re(N)}}}function ut(l,e,t){let{label:n}=e,{info:o=void 0}=e,{value:u=[]}=e,_=[],{value_is_output:i=!1}=e,{max_choices:s=null}=e,{choices:r}=e,f,{disabled:O=!1}=e,{show_label:g}=e,{container:p=!0}=e,{allow_custom_value:y=!1}=e,{filterable:N=!0}=e,{i18n:m}=e,{root:h}=e,C,b="",c="",d=!1,z,J,B=[],A=null,S=[],D=[];const j=pe();Array.isArray(u)&&u.forEach(a=>{const P=r.map(ge=>ge[1]).indexOf(a);P!==-1?S.push(P):S.push(a)});function W(){y||t(11,b=""),y&&b!==""&&(Z(b),t(11,b="")),t(15,d=!1),t(17,A=null),j("blur")}function K(a){t(13,S=S.filter(P=>P!==a)),j("select",{index:typeof a=="number"?a:-1,value:typeof a=="number"?J[a]:a,selected:!1})}function Z(a){(s===null||S.length<s)&&(t(13,S=[...S,a]),j("select",{index:typeof a=="number"?a:-1,value:typeof a=="number"?J[a]:a,selected:!0})),S.length===s&&(t(15,d=!1),t(17,A=null),C.blur())}function le(a){const P=parseInt(a.detail.target.dataset.index);ee(P)}function ee(a){S.includes(a)?K(a):Z(a),t(11,b="")}function te(a){t(13,S=[]),t(11,b=""),a.preventDefault()}function se(a){t(12,B=r.map((P,ge)=>ge)),(s===null||S.length<s)&&t(15,d=!0),j("focus")}function ne(a){t(15,[d,A]=Pe(a,A,B),d,(t(17,A),t(3,r),t(28,f),t(11,b),t(29,c),t(7,y),t(12,B))),a.key==="Enter"&&(A!==null?ee(A):y&&(Z(b),t(11,b=""))),a.key==="Backspace"&&b===""&&t(13,S=[...S.slice(0,-1)]),S.length===s&&(t(15,d=!1),t(17,A=null))}function ie(){u===void 0?t(13,S=[]):Array.isArray(u)&&t(13,S=u.map(a=>{const P=J.indexOf(a);if(P!==-1)return P;if(y)return a}).filter(a=>a!==void 0))}Le(()=>{t(26,i=!1)});const E=a=>K(a),ae=(a,P)=>{P.key==="Enter"&&K(a)};function w(){b=this.value,t(11,b)}function Qe(a){X[a?"unshift":"push"](()=>{C=a,t(14,C)})}const Ye=a=>j("key_up",{key:a.key,input_value:b}),Ge=a=>{a.key==="Enter"&&te(a)};return l.$$set=a=>{"label"in a&&t(0,n=a.label),"info"in a&&t(1,o=a.info),"value"in a&&t(25,u=a.value),"value_is_output"in a&&t(26,i=a.value_is_output),"max_choices"in a&&t(2,s=a.max_choices),"choices"in a&&t(3,r=a.choices),"disabled"in a&&t(4,O=a.disabled),"show_label"in a&&t(5,g=a.show_label),"container"in a&&t(6,p=a.container),"allow_custom_value"in a&&t(7,y=a.allow_custom_value),"filterable"in a&&t(8,N=a.filterable),"i18n"in a&&t(9,m=a.i18n),"root"in a&&t(10,h=a.root)},l.$$.update=()=>{l.$$.dirty[0]&8&&(t(16,z=r.map(a=>a[0])),t(30,J=r.map(a=>a[1]))),l.$$.dirty[0]&805312648&&(r!==f||b!==c)&&(t(12,B=ve(r,b)),t(28,f=r),t(29,c=b),y||t(17,A=B[0])),l.$$.dirty[0]&1073750016|l.$$.dirty[1]&1&&JSON.stringify(S)!=JSON.stringify(D)&&(t(25,u=S.map(a=>typeof a=="number"?J[a]:a)),t(31,D=S.slice())),l.$$.dirty[0]&234881024&&JSON.stringify(u)!=JSON.stringify(_)&&(Fe(j,u,i),t(27,_=Array.isArray(u)?u.slice():u)),l.$$.dirty[0]&33554432&&ie()},[n,o,s,r,O,g,p,y,N,m,h,b,B,S,C,d,z,A,j,W,K,le,te,se,ne,u,i,_,f,c,J,D,E,ae,w,Qe,Ye,Ge]}class ot extends ue{constructor(e){super(),oe(this,e,ut,it,fe,{label:0,info:1,value:25,value_is_output:26,max_choices:2,choices:3,disabled:4,show_label:5,container:6,allow_custom_value:7,filterable:8,i18n:9,root:10},null,[-1,-1])}get label(){return this.$$.ctx[0]}set label(e){this.$$set({label:e}),k()}get info(){return this.$$.ctx[1]}set info(e){this.$$set({info:e}),k()}get value(){return this.$$.ctx[25]}set value(e){this.$$set({value:e}),k()}get value_is_output(){return this.$$.ctx[26]}set value_is_output(e){this.$$set({value_is_output:e}),k()}get max_choices(){return this.$$.ctx[2]}set max_choices(e){this.$$set({max_choices:e}),k()}get choices(){return this.$$.ctx[3]}set choices(e){this.$$set({choices:e}),k()}get disabled(){return this.$$.ctx[4]}set disabled(e){this.$$set({disabled:e}),k()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),k()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),k()}get allow_custom_value(){return this.$$.ctx[7]}set allow_custom_value(e){this.$$set({allow_custom_value:e}),k()}get filterable(){return this.$$.ctx[8]}set filterable(e){this.$$set({filterable:e}),k()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),k()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),k()}}const ft=ot;function rt(l){let e;return{c(){e=_e(l[0])},m(t,n){R(t,e,n)},p(t,n){n[0]&1&&ce(e,t[0])},d(t){t&&U(e)}}}function ze(l){let e,t,n;return t=new Ue({}),{c(){e=L("div"),Q(t.$$.fragment),v(e,"class","icon-wrap svelte-1hfxrpf")},m(o,u){R(o,e,u),Y(t,e,null),n=!0},i(o){n||(q(t.$$.fragment,o),n=!0)},o(o){M(t.$$.fragment,o),n=!1},d(o){o&&U(e),G(t)}}}function _t(l){let e,t,n,o,u,_,i,s,r,f,O,g,p,y;t=new Re({props:{root:l[8],show_label:l[4],info:l[1],$$slots:{default:[rt]},$$scope:{ctx:l}}});let N=!l[3]&&ze();return O=new je({props:{show_options:l[13],choices:l[2],filtered_indices:l[11],disabled:l[3],selected_indices:l[12]===null?[]:[l[12]],active_index:l[15]}}),O.$on("change",l[17]),{c(){e=L("div"),Q(t.$$.fragment),n=F(),o=L("div"),u=L("div"),_=L("div"),i=L("input"),r=F(),N&&N.c(),f=F(),Q(O.$$.fragment),v(i,"role","listbox"),v(i,"aria-controls","dropdown-options"),v(i,"aria-expanded",l[13]),v(i,"aria-label",l[0]),v(i,"class","border-none svelte-1hfxrpf"),i.disabled=l[3],v(i,"autocomplete","off"),i.readOnly=s=!l[7],T(i,"subdued",!l[14].includes(l[10])&&!l[6]),v(_,"class","secondary-wrap svelte-1hfxrpf"),v(u,"class","wrap-inner svelte-1hfxrpf"),T(u,"show_options",l[13]),v(o,"class","wrap svelte-1hfxrpf"),v(e,"class","svelte-1hfxrpf"),T(e,"container",l[5])},m(m,h){R(m,e,h),Y(t,e,null),I(e,n),I(e,o),I(o,u),I(u,_),I(_,i),de(i,l[10]),l[30](i),I(_,r),N&&N.m(_,null),I(o,f),Y(O,o,null),g=!0,p||(y=[H(i,"input",l[29]),H(i,"keydown",l[20]),H(i,"keyup",l[31]),H(i,"blur",l[19]),H(i,"focus",l[18])],p=!0)},p(m,h){const C={};h[0]&256&&(C.root=m[8]),h[0]&16&&(C.show_label=m[4]),h[0]&2&&(C.info=m[1]),h[0]&1|h[1]&16&&(C.$$scope={dirty:h,ctx:m}),t.$set(C),(!g||h[0]&8192)&&v(i,"aria-expanded",m[13]),(!g||h[0]&1)&&v(i,"aria-label",m[0]),(!g||h[0]&8)&&(i.disabled=m[3]),(!g||h[0]&128&&s!==(s=!m[7]))&&(i.readOnly=s),h[0]&1024&&i.value!==m[10]&&de(i,m[10]),(!g||h[0]&17472)&&T(i,"subdued",!m[14].includes(m[10])&&!m[6]),m[3]?N&&(x(),M(N,1,1,()=>{N=null}),$()):N?h[0]&8&&q(N,1):(N=ze(),N.c(),q(N,1),N.m(_,null)),(!g||h[0]&8192)&&T(u,"show_options",m[13]);const b={};h[0]&8192&&(b.show_options=m[13]),h[0]&4&&(b.choices=m[2]),h[0]&2048&&(b.filtered_indices=m[11]),h[0]&8&&(b.disabled=m[3]),h[0]&4096&&(b.selected_indices=m[12]===null?[]:[m[12]]),h[0]&32768&&(b.active_index=m[15]),O.$set(b),(!g||h[0]&32)&&T(e,"container",m[5])},i(m){g||(q(t.$$.fragment,m),q(N),q(O.$$.fragment,m),g=!0)},o(m){M(t.$$.fragment,m),M(N),M(O.$$.fragment,m),g=!1},d(m){m&&U(e),G(t),l[30](null),N&&N.d(),G(O),p=!1,re(y)}}}function ct(l,e,t){let{label:n}=e,{info:o=void 0}=e,{value:u=void 0}=e,_,{value_is_output:i=!1}=e,{choices:s}=e,r,{disabled:f=!1}=e,{show_label:O}=e,{container:g=!0}=e,{allow_custom_value:p=!1}=e,{filterable:y=!0}=e,{root:N}=e,m,h=!1,C,b,c="",d="",z=!1,J=[],B=null,A=null,S;const D=pe();u&&(S=s.map(E=>E[1]).indexOf(u),A=S,A===-1?(_=u,A=null):([c,_]=s[A],d=c),K());function j(){t(14,C=s.map(E=>E[0])),t(25,b=s.map(E=>E[1]))}const W=typeof window<"u";function K(){j(),u===void 0||Array.isArray(u)&&u.length===0?(t(10,c=""),t(12,A=null)):b.includes(u)?(t(10,c=C[b.indexOf(u)]),t(12,A=b.indexOf(u))):p?(t(10,c=u),t(12,A=null)):(t(10,c=""),t(12,A=null)),t(28,S=A)}function Z(E){if(t(12,A=parseInt(E.detail.target.dataset.index)),isNaN(A)){t(12,A=null);return}t(13,h=!1),t(15,B=null),m.blur()}function le(E){t(11,J=s.map((ae,w)=>w)),t(13,h=!0),D("focus")}function ee(){p?t(21,u=c):t(10,c=C[b.indexOf(u)]),t(13,h=!1),t(15,B=null),D("blur")}function te(E){t(13,[h,B]=Pe(E,B,J),h,(t(15,B),t(2,s),t(24,r),t(6,p),t(10,c),t(11,J),t(9,m),t(26,d),t(12,A),t(28,S),t(27,z),t(25,b))),E.key==="Enter"&&(B!==null?(t(12,A=B),t(13,h=!1),m.blur(),t(15,B=null)):C.includes(c)?(t(12,A=C.indexOf(c)),t(13,h=!1),t(15,B=null),m.blur()):p&&(t(21,u=c),t(12,A=null),t(13,h=!1),t(15,B=null),m.blur()))}Le(()=>{t(22,i=!1),t(27,z=!0)});function se(){c=this.value,t(10,c),t(12,A),t(28,S),t(27,z),t(2,s),t(25,b)}function ne(E){X[E?"unshift":"push"](()=>{m=E,t(9,m)})}const ie=E=>D("key_up",{key:E.key,input_value:c});return l.$$set=E=>{"label"in E&&t(0,n=E.label),"info"in E&&t(1,o=E.info),"value"in E&&t(21,u=E.value),"value_is_output"in E&&t(22,i=E.value_is_output),"choices"in E&&t(2,s=E.choices),"disabled"in E&&t(3,f=E.disabled),"show_label"in E&&t(4,O=E.show_label),"container"in E&&t(5,g=E.container),"allow_custom_value"in E&&t(6,p=E.allow_custom_value),"filterable"in E&&t(7,y=E.filterable),"root"in E&&t(8,N=E.root)},l.$$.update=()=>{l.$$.dirty[0]&436211716&&A!==S&&A!==null&&z&&(t(10,[c,u]=s[A],c,(t(21,u),t(12,A),t(28,S),t(27,z),t(2,s),t(25,b))),t(28,S=A),D("select",{index:A,value:b[A],selected:!0})),l.$$.dirty[0]&14680064&&JSON.stringify(_)!==JSON.stringify(u)&&(K(),Fe(D,u,i),t(23,_=u)),l.$$.dirty[0]&4&&j(),l.$$.dirty[0]&16780868&&s!==r&&(p||K(),t(24,r=s),t(11,J=ve(s,c)),!p&&J.length>0&&t(15,B=J[0]),W&&m===document.activeElement&&t(13,h=!0)),l.$$.dirty[0]&67112004&&c!==d&&(t(11,J=ve(s,c)),t(26,d=c),!p&&J.length>0&&t(15,B=J[0]))},[n,o,s,f,O,g,p,y,N,m,c,J,A,h,C,B,D,Z,le,ee,te,u,i,_,r,b,d,z,S,se,ne,ie]}class at extends ue{constructor(e){super(),oe(this,e,ct,_t,fe,{label:0,info:1,value:21,value_is_output:22,choices:2,disabled:3,show_label:4,container:5,allow_custom_value:6,filterable:7,root:8},null,[-1,-1])}get label(){return this.$$.ctx[0]}set label(e){this.$$set({label:e}),k()}get info(){return this.$$.ctx[1]}set info(e){this.$$set({info:e}),k()}get value(){return this.$$.ctx[21]}set value(e){this.$$set({value:e}),k()}get value_is_output(){return this.$$.ctx[22]}set value_is_output(e){this.$$set({value_is_output:e}),k()}get choices(){return this.$$.ctx[2]}set choices(e){this.$$set({choices:e}),k()}get disabled(){return this.$$.ctx[3]}set disabled(e){this.$$set({disabled:e}),k()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),k()}get container(){return this.$$.ctx[5]}set container(e){this.$$set({container:e}),k()}get allow_custom_value(){return this.$$.ctx[6]}set allow_custom_value(e){this.$$set({allow_custom_value:e}),k()}get filterable(){return this.$$.ctx[7]}set filterable(e){this.$$set({filterable:e}),k()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),k()}}const ht=at;function dt(l){let e,t,n,o;function u(s){l[29](s)}function _(s){l[30](s)}let i={choices:l[9],label:l[2],root:l[17],info:l[3],show_label:l[10],filterable:l[11],allow_custom_value:l[16],container:l[12],disabled:!l[19]};return l[0]!==void 0&&(i.value=l[0]),l[1]!==void 0&&(i.value_is_output=l[1]),e=new ht({props:i}),X.push(()=>me(e,"value",u)),X.push(()=>me(e,"value_is_output",_)),e.$on("change",l[31]),e.$on("input",l[32]),e.$on("select",l[33]),e.$on("blur",l[34]),e.$on("focus",l[35]),e.$on("key_up",l[36]),{c(){Q(e.$$.fragment)},m(s,r){Y(e,s,r),o=!0},p(s,r){const f={};r[0]&512&&(f.choices=s[9]),r[0]&4&&(f.label=s[2]),r[0]&131072&&(f.root=s[17]),r[0]&8&&(f.info=s[3]),r[0]&1024&&(f.show_label=s[10]),r[0]&2048&&(f.filterable=s[11]),r[0]&65536&&(f.allow_custom_value=s[16]),r[0]&4096&&(f.container=s[12]),r[0]&524288&&(f.disabled=!s[19]),!t&&r[0]&1&&(t=!0,f.value=s[0],be(()=>t=!1)),!n&&r[0]&2&&(n=!0,f.value_is_output=s[1],be(()=>n=!1)),e.$set(f)},i(s){o||(q(e.$$.fragment,s),o=!0)},o(s){M(e.$$.fragment,s),o=!1},d(s){G(e,s)}}}function mt(l){let e,t,n,o;function u(s){l[21](s)}function _(s){l[22](s)}let i={choices:l[9],max_choices:l[8],root:l[17],label:l[2],info:l[3],show_label:l[10],allow_custom_value:l[16],filterable:l[11],container:l[12],i18n:l[18].i18n,disabled:!l[19]};return l[0]!==void 0&&(i.value=l[0]),l[1]!==void 0&&(i.value_is_output=l[1]),e=new ft({props:i}),X.push(()=>me(e,"value",u)),X.push(()=>me(e,"value_is_output",_)),e.$on("change",l[23]),e.$on("input",l[24]),e.$on("select",l[25]),e.$on("blur",l[26]),e.$on("focus",l[27]),e.$on("key_up",l[28]),{c(){Q(e.$$.fragment)},m(s,r){Y(e,s,r),o=!0},p(s,r){const f={};r[0]&512&&(f.choices=s[9]),r[0]&256&&(f.max_choices=s[8]),r[0]&131072&&(f.root=s[17]),r[0]&4&&(f.label=s[2]),r[0]&8&&(f.info=s[3]),r[0]&1024&&(f.show_label=s[10]),r[0]&65536&&(f.allow_custom_value=s[16]),r[0]&2048&&(f.filterable=s[11]),r[0]&4096&&(f.container=s[12]),r[0]&262144&&(f.i18n=s[18].i18n),r[0]&524288&&(f.disabled=!s[19]),!t&&r[0]&1&&(t=!0,f.value=s[0],be(()=>t=!1)),!n&&r[0]&2&&(n=!0,f.value_is_output=s[1],be(()=>n=!1)),e.$set(f)},i(s){o||(q(e.$$.fragment,s),o=!0)},o(s){M(e.$$.fragment,s),o=!1},d(s){G(e,s)}}}function bt(l){let e,t,n,o,u,_;const i=[{autoscroll:l[18].autoscroll},{i18n:l[18].i18n},l[15]];let s={};for(let g=0;g<i.length;g+=1)s=Ve(s,i[g]);e=new We({props:s}),e.$on("clear_status",l[20]);const r=[mt,dt],f=[];function O(g,p){return g[7]?0:1}return n=O(l),o=f[n]=r[n](l),{c(){Q(e.$$.fragment),t=F(),o.c(),u=Te()},m(g,p){Y(e,g,p),R(g,t,p),f[n].m(g,p),R(g,u,p),_=!0},p(g,p){const y=p[0]&294912?Xe(i,[p[0]&262144&&{autoscroll:g[18].autoscroll},p[0]&262144&&{i18n:g[18].i18n},p[0]&32768&&Ze(g[15])]):{};e.$set(y);let N=n;n=O(g),n===N?f[n].p(g,p):(x(),M(f[N],1,1,()=>{f[N]=null}),$(),o=f[n],o?o.p(g,p):(o=f[n]=r[n](g),o.c()),q(o,1),o.m(u.parentNode,u))},i(g){_||(q(e.$$.fragment,g),q(o),_=!0)},o(g){M(e.$$.fragment,g),M(o),_=!1},d(g){g&&(U(t),U(u)),G(e,g),f[n].d(g)}}}function gt(l){let e,t;return e=new Ke({props:{visible:l[6],elem_id:l[4],elem_classes:l[5],padding:l[12],allow_overflow:!1,scale:l[13],min_width:l[14],$$slots:{default:[bt]},$$scope:{ctx:l}}}),{c(){Q(e.$$.fragment)},m(n,o){Y(e,n,o),t=!0},p(n,o){const u={};o[0]&64&&(u.visible=n[6]),o[0]&16&&(u.elem_id=n[4]),o[0]&32&&(u.elem_classes=n[5]),o[0]&4096&&(u.padding=n[12]),o[0]&8192&&(u.scale=n[13]),o[0]&16384&&(u.min_width=n[14]),o[0]&1023887|o[1]&64&&(u.$$scope={dirty:o,ctx:n}),e.$set(u)},i(n){t||(q(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function wt(l,e,t){let{label:n="Dropdown"}=e,{info:o=void 0}=e,{elem_id:u=""}=e,{elem_classes:_=[]}=e,{visible:i=!0}=e,{multiselect:s=!1}=e,{value:r=s?[]:void 0}=e,{value_is_output:f=!1}=e,{max_choices:O=null}=e,{choices:g}=e,{show_label:p}=e,{filterable:y}=e,{container:N=!0}=e,{scale:m=null}=e,{min_width:h=void 0}=e,{loading_status:C}=e,{allow_custom_value:b=!1}=e,{root:c}=e,{gradio:d}=e,{interactive:z}=e;const J=()=>d.dispatch("clear_status",C);function B(w){r=w,t(0,r)}function A(w){f=w,t(1,f)}const S=()=>d.dispatch("change"),D=()=>d.dispatch("input"),j=w=>d.dispatch("select",w.detail),W=()=>d.dispatch("blur"),K=()=>d.dispatch("focus"),Z=()=>d.dispatch("key_up");function le(w){r=w,t(0,r)}function ee(w){f=w,t(1,f)}const te=()=>d.dispatch("change"),se=()=>d.dispatch("input"),ne=w=>d.dispatch("select",w.detail),ie=()=>d.dispatch("blur"),E=()=>d.dispatch("focus"),ae=w=>d.dispatch("key_up",w.detail);return l.$$set=w=>{"label"in w&&t(2,n=w.label),"info"in w&&t(3,o=w.info),"elem_id"in w&&t(4,u=w.elem_id),"elem_classes"in w&&t(5,_=w.elem_classes),"visible"in w&&t(6,i=w.visible),"multiselect"in w&&t(7,s=w.multiselect),"value"in w&&t(0,r=w.value),"value_is_output"in w&&t(1,f=w.value_is_output),"max_choices"in w&&t(8,O=w.max_choices),"choices"in w&&t(9,g=w.choices),"show_label"in w&&t(10,p=w.show_label),"filterable"in w&&t(11,y=w.filterable),"container"in w&&t(12,N=w.container),"scale"in w&&t(13,m=w.scale),"min_width"in w&&t(14,h=w.min_width),"loading_status"in w&&t(15,C=w.loading_status),"allow_custom_value"in w&&t(16,b=w.allow_custom_value),"root"in w&&t(17,c=w.root),"gradio"in w&&t(18,d=w.gradio),"interactive"in w&&t(19,z=w.interactive)},[r,f,n,o,u,_,i,s,O,g,p,y,N,m,h,C,b,c,d,z,J,B,A,S,D,j,W,K,Z,le,ee,te,se,ne,ie,E,ae]}class Bt extends ue{constructor(e){super(),oe(this,e,wt,gt,fe,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,multiselect:7,value:0,value_is_output:1,max_choices:8,choices:9,show_label:10,filterable:11,container:12,scale:13,min_width:14,loading_status:15,allow_custom_value:16,root:17,gradio:18,interactive:19},null,[-1,-1])}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),k()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),k()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),k()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),k()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),k()}get multiselect(){return this.$$.ctx[7]}set multiselect(e){this.$$set({multiselect:e}),k()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),k()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),k()}get max_choices(){return this.$$.ctx[8]}set max_choices(e){this.$$set({max_choices:e}),k()}get choices(){return this.$$.ctx[9]}set choices(e){this.$$set({choices:e}),k()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),k()}get filterable(){return this.$$.ctx[11]}set filterable(e){this.$$set({filterable:e}),k()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),k()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),k()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),k()}get loading_status(){return this.$$.ctx[15]}set loading_status(e){this.$$set({loading_status:e}),k()}get allow_custom_value(){return this.$$.ctx[16]}set allow_custom_value(e){this.$$set({allow_custom_value:e}),k()}get root(){return this.$$.ctx[17]}set root(e){this.$$set({root:e}),k()}get gradio(){return this.$$.ctx[18]}set gradio(e){this.$$set({gradio:e}),k()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),k()}}export{ht as BaseDropdown,St as BaseExample,ft as BaseMultiselect,Bt as default};
//# sourceMappingURL=Index-CxnExXGa.js.map
