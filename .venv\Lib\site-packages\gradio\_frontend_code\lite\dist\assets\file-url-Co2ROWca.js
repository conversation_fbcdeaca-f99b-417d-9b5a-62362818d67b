import{T as a,U as u,V as i}from"../lite.js";function f(e){const n=typeof window<"u";if(e==null||!n)return!1;const t=new URL(e,window.location.href);return!(!i(t)||t.protocol!=="http:"&&t.protocol!=="https:")}let o;async function c(e){const n=typeof window<"u";if(e==null||!n||!f(e))return e;if(o==null)try{o=a()}catch{return e}if(o==null)return e;const l=new URL(e,window.location.href).pathname;return o.httpRequest({method:"GET",path:l,headers:{},query_string:""}).then(r=>{if(r.status!==200)throw new Error(`Failed to get file ${l} from the Wasm worker.`);const s=new Blob([r.body],{type:u(r.headers,"content-type")});return URL.createObjectURL(s)})}export{c as r,f as s};
//# sourceMappingURL=file-url-Co2ROWca.js.map
