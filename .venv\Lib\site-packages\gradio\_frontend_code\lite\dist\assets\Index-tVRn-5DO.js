import{a as B,i as D,s as N,f as v,p as b,x as A,q as h,l as y,v as c,w as E,o as F,k as z,F as K,n as C,ao as S,E as R,b4 as Q,b5 as U,I as W,$ as X,r as Z,bc as I,bb as V,c as Y,m as P,t as x,b as ee,d as te}from"../lite.js";(function(i){i.languages.typescript=i.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),i.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete i.languages.typescript.parameter,delete i.languages.typescript["literal-property"];var e=i.languages.extend("typescript",{});delete e["class-name"],i.languages.typescript["class-name"].inside=e,i.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e}}}}),i.languages.ts=i.languages.typescript})(Prism);function L(i,e,l){const t=i.slice();return t[11]=e[l].type,t[12]=e[l].description,t[13]=e[l].default,t[14]=e[l].name,t}function O(i){let e,l,t,a,n,s,f,u,g;return{c(){e=b("div"),l=b("span"),t=z(i[1]),a=A(),n=b("button"),s=z("▼"),h(l,"class","title svelte-1k7zb06"),h(n,"class","toggle-all svelte-1k7zb06"),h(n,"title",f=i[4]?"Close All":"Open All"),h(e,"class","header svelte-1k7zb06")},m(p,k){y(p,e,k),c(e,l),c(l,t),c(e,a),c(e,n),c(n,s),u||(g=K(n,"click",i[5]),u=!0)},p(p,k){k&2&&C(t,p[1]),k&16&&f!==(f=p[4]?"Close All":"Open All")&&h(n,"title",f)},d(p){p&&F(e),u=!1,g()}}}function $(i){let e=[],l=new Map,t,a=S(i[3]);const n=s=>s[14];for(let s=0;s<a.length;s+=1){let f=L(i,a,s),u=n(f);l.set(u,e[s]=T(u,f))}return{c(){for(let s=0;s<e.length;s+=1)e[s].c();t=R()},m(s,f){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(s,f);y(s,t,f)},p(s,f){f&9&&(a=S(s[3]),e=Q(e,f,n,1,s,a,l,t.parentNode,U,T,t,L))},d(s){s&&F(t);for(let f=0;f<e.length;f+=1)e[f].d(s)}}}function j(i){let e,l,t=i[11]+"",a;return{c(){e=z(": "),l=new V(!1),a=R(),l.a=a},m(n,s){y(n,e,s),l.m(t,n,s),y(n,a,s)},p(n,s){s&8&&t!==(t=n[11]+"")&&l.p(t)},d(n){n&&(F(e),F(a),l.d())}}}function H(i){let e,l,t,a,n,s,f=i[13]+"";return{c(){e=b("div"),l=b("span"),l.textContent="default",t=A(),a=b("code"),n=z("= "),s=new V(!1),h(l,"class","svelte-1k7zb06"),X(l,"padding-right","4px"),s.a=null,h(a,"class","svelte-1k7zb06"),h(e,"class","default svelte-1k7zb06"),Z(e,"last",!i[12])},m(u,g){y(u,e,g),c(e,l),c(e,t),c(e,a),c(a,n),s.m(f,a)},p(u,g){g&8&&f!==(f=u[13]+"")&&s.p(f),g&8&&Z(e,"last",!u[12])},d(u){u&&F(e)}}}function M(i){let e,l,t=i[12]+"",a;return{c(){e=b("div"),l=b("p"),a=z(t),h(e,"class","description svelte-1k7zb06")},m(n,s){y(n,e,s),c(e,l),c(l,a)},p(n,s){s&8&&t!==(t=n[12]+"")&&C(a,t)},d(n){n&&F(e)}}}function T(i,e){let l,t,a,n,s=e[14]+"",f,u,g,p,k,o=e[11]&&j(e),d=e[13]&&H(e),r=e[12]&&M(e);return{key:i,first:null,c(){l=b("details"),t=b("summary"),a=b("pre"),n=b("code"),f=z(s),o&&o.c(),g=A(),d&&d.c(),p=A(),r&&r.c(),k=A(),h(n,"class","svelte-1k7zb06"),h(a,"class",u="language-"+e[0]+" svelte-1k7zb06"),h(t,"class","type svelte-1k7zb06"),h(l,"class","param md svelte-1k7zb06"),this.first=l},m(_,m){y(_,l,m),c(l,t),c(t,a),c(a,n),c(n,f),o&&o.m(n,null),c(l,g),d&&d.m(l,null),c(l,p),r&&r.m(l,null),c(l,k)},p(_,m){e=_,m&8&&s!==(s=e[14]+"")&&C(f,s),e[11]?o?o.p(e,m):(o=j(e),o.c(),o.m(n,null)):o&&(o.d(1),o=null),m&1&&u!==(u="language-"+e[0]+" svelte-1k7zb06")&&h(a,"class",u),e[13]?d?d.p(e,m):(d=H(e),d.c(),d.m(l,p)):d&&(d.d(1),d=null),e[12]?r?r.p(e,m):(r=M(e),r.c(),r.m(l,k)):r&&(r.d(1),r=null)},d(_){_&&F(l),o&&o.d(),d&&d.d(),r&&r.d()}}}function le(i){let e,l,t=i[1]!==null&&O(i),a=i[3]&&$(i);return{c(){e=b("div"),t&&t.c(),l=A(),a&&a.c(),h(e,"class","wrap svelte-1k7zb06")},m(n,s){y(n,e,s),t&&t.m(e,null),c(e,l),a&&a.m(e,null),i[8](e)},p(n,[s]){n[1]!==null?t?t.p(n,s):(t=O(n),t.c(),t.m(e,l)):t&&(t.d(1),t=null),n[3]?a?a.p(n,s):(a=$(n),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i:E,o:E,d(n){n&&F(e),t&&t.d(),a&&a.d(),i[8](null)}}}function ne(i,e,l){let{docs:t}=e,{lang:a="python"}=e,{linkify:n=[]}=e,{header:s}=e,f,u,g=!1;function p(r,_){let m=I.highlight(r,I.languages[_],_);for(const w of n)m=m.replace(new RegExp(w,"g"),`<a href="#h-${w.toLocaleLowerCase()}">${w}</a>`);return m}function k(r,_){return r?Object.entries(r).map(([m,{type:w,description:G,default:q}])=>{let J=w?p(w,_):null;return{name:m,type:J,description:G,default:q?p(q,_):null}}):[]}function o(){l(4,g=!g),f.querySelectorAll(".param").forEach(_=>{_ instanceof HTMLDetailsElement&&(_.open=g)})}function d(r){W[r?"unshift":"push"](()=>{f=r,l(2,f)})}return i.$$set=r=>{"docs"in r&&l(6,t=r.docs),"lang"in r&&l(0,a=r.lang),"linkify"in r&&l(7,n=r.linkify),"header"in r&&l(1,s=r.header)},i.$$.update=()=>{i.$$.dirty&65&&l(3,u=k(t,a))},[a,s,f,u,g,o,t,n,d]}class se extends B{constructor(e){super(),D(this,e,ne,le,N,{docs:6,lang:0,linkify:7,header:1})}get docs(){return this.$$.ctx[6]}set docs(e){this.$$set({docs:e}),v()}get lang(){return this.$$.ctx[0]}set lang(e){this.$$set({lang:e}),v()}get linkify(){return this.$$.ctx[7]}set linkify(e){this.$$set({linkify:e}),v()}get header(){return this.$$.ctx[1]}set header(e){this.$$set({header:e}),v()}}function ae(i){let e,l;return e=new se({props:{docs:i[0],linkify:i[1],header:i[2]}}),{c(){Y(e.$$.fragment)},m(t,a){P(e,t,a),l=!0},p(t,[a]){const n={};a&1&&(n.docs=t[0]),a&2&&(n.linkify=t[1]),a&4&&(n.header=t[2]),e.$set(n)},i(t){l||(x(e.$$.fragment,t),l=!0)},o(t){ee(e.$$.fragment,t),l=!1},d(t){te(e,t)}}}function ie(i,e,l){let{value:t}=e,{linkify:a=[]}=e,{header:n=null}=e;return i.$$set=s=>{"value"in s&&l(0,t=s.value),"linkify"in s&&l(1,a=s.linkify),"header"in s&&l(2,n=s.header)},[t,a,n]}class fe extends B{constructor(e){super(),D(this,e,ie,ae,N,{value:0,linkify:1,header:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),v()}get linkify(){return this.$$.ctx[1]}set linkify(e){this.$$set({linkify:e}),v()}get header(){return this.$$.ctx[2]}set header(e){this.$$set({header:e}),v()}}export{fe as default};
//# sourceMappingURL=Index-tVRn-5DO.js.map
