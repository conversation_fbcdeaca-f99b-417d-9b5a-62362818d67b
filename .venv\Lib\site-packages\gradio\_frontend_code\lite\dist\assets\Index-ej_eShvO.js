import{a as W,i as X,ar as Z,f,B as y,c as P,m as Y,t as z,b as A,d as D,Y as x,S as $,ao as G,x as T,p as O,q as v,l as B,a0 as p,a4 as ee,o as q,aq as te,k as L,n as M,r as N,v as j,F as I,P as se}from"../lite.js";import{B as le}from"./BlockTitle-BlPSRItZ.js";import"./Info-2h-pcw07.js";import"./MarkdownCode-C6RHM0m6.js";function H(t,e,s){const n=t.slice();return n[22]=e[s][0],n[23]=e[s][1],n[25]=s,n}function ie(t){let e;return{c(){e=L(t[9])},m(s,n){B(s,e,n)},p(s,n){n&512&&M(e,s[9])},d(s){s&&q(e)}}}function K(t){let e,s,n,a,u,k,m,r=t[22]+"",d,c,l,o;function b(){return t[19](t[23])}function w(...h){return t[20](t[25],t[23],...h)}function _(...h){return t[21](t[23],t[25],...h)}return{c(){e=O("label"),s=O("input"),k=T(),m=O("span"),d=L(r),c=T(),s.disabled=t[14],s.checked=n=t[0].includes(t[23]),v(s,"type","checkbox"),v(s,"name",a=t[23]?.toString()),v(s,"title",u=t[23]?.toString()),v(s,"class","svelte-kujow2"),v(m,"class","ml-2 svelte-kujow2"),v(e,"class","svelte-kujow2"),N(e,"disabled",t[14]),N(e,"selected",t[0].includes(t[23]))},m(h,g){B(h,e,g),j(e,s),j(e,k),j(e,m),j(m,d),j(e,c),l||(o=[I(s,"change",b),I(s,"input",w),I(s,"keydown",_)],l=!0)},p(h,g){t=h,g&16384&&(s.disabled=t[14]),g&33&&n!==(n=t[0].includes(t[23]))&&(s.checked=n),g&32&&a!==(a=t[23]?.toString())&&v(s,"name",a),g&32&&u!==(u=t[23]?.toString())&&v(s,"title",u),g&32&&r!==(r=t[22]+"")&&M(d,r),g&16384&&N(e,"disabled",t[14]),g&33&&N(e,"selected",t[0].includes(t[23]))},d(h){h&&q(e),l=!1,se(o)}}}function ne(t){let e,s,n,a,u,k;const m=[{autoscroll:t[1].autoscroll},{i18n:t[1].i18n},t[13]];let r={};for(let l=0;l<m.length;l+=1)r=x(r,m[l]);e=new $({props:r}),e.$on("clear_status",t[18]),n=new le({props:{root:t[12],show_label:t[11],info:t[10],$$slots:{default:[ie]},$$scope:{ctx:t}}});let d=G(t[5]),c=[];for(let l=0;l<d.length;l+=1)c[l]=K(H(t,d,l));return{c(){P(e.$$.fragment),s=T(),P(n.$$.fragment),a=T(),u=O("div");for(let l=0;l<c.length;l+=1)c[l].c();v(u,"class","wrap svelte-kujow2"),v(u,"data-testid","checkbox-group")},m(l,o){Y(e,l,o),B(l,s,o),Y(n,l,o),B(l,a,o),B(l,u,o);for(let b=0;b<c.length;b+=1)c[b]&&c[b].m(u,null);k=!0},p(l,o){const b=o&8194?p(m,[o&2&&{autoscroll:l[1].autoscroll},o&2&&{i18n:l[1].i18n},o&8192&&ee(l[13])]):{};e.$set(b);const w={};if(o&4096&&(w.root=l[12]),o&2048&&(w.show_label=l[11]),o&1024&&(w.info=l[10]),o&67109376&&(w.$$scope={dirty:o,ctx:l}),n.$set(w),o&49187){d=G(l[5]);let _;for(_=0;_<d.length;_+=1){const h=H(l,d,_);c[_]?c[_].p(h,o):(c[_]=K(h),c[_].c(),c[_].m(u,null))}for(;_<c.length;_+=1)c[_].d(1);c.length=d.length}},i(l){k||(z(e.$$.fragment,l),z(n.$$.fragment,l),k=!0)},o(l){A(e.$$.fragment,l),A(n.$$.fragment,l),k=!1},d(l){l&&(q(s),q(a),q(u)),D(e,l),D(n,l),te(c,l)}}}function ae(t){let e,s;return e=new y({props:{visible:t[4],elem_id:t[2],elem_classes:t[3],type:"fieldset",container:t[6],scale:t[7],min_width:t[8],$$slots:{default:[ne]},$$scope:{ctx:t}}}),{c(){P(e.$$.fragment)},m(n,a){Y(e,n,a),s=!0},p(n,[a]){const u={};a&16&&(u.visible=n[4]),a&4&&(u.elem_id=n[2]),a&8&&(u.elem_classes=n[3]),a&64&&(u.container=n[6]),a&128&&(u.scale=n[7]),a&256&&(u.min_width=n[8]),a&67141155&&(u.$$scope={dirty:a,ctx:n}),e.$set(u)},i(n){s||(z(e.$$.fragment,n),s=!0)},o(n){A(e.$$.fragment,n),s=!1},d(n){D(e,n)}}}function ue(t,e,s){let n,{gradio:a}=e,{elem_id:u=""}=e,{elem_classes:k=[]}=e,{visible:m=!0}=e,{value:r=[]}=e,{choices:d}=e,{container:c=!0}=e,{scale:l=null}=e,{min_width:o=void 0}=e,{label:b=a.i18n("checkbox.checkbox_group")}=e,{info:w=void 0}=e,{show_label:_=!0}=e,{root:h}=e,{loading_status:g}=e,{interactive:C=!0}=e,{old_value:J=r.slice()}=e;function E(i){r.includes(i)?s(0,r=r.filter(S=>S!==i)):s(0,r=[...r,i]),a.dispatch("input")}const Q=()=>a.dispatch("clear_status",g),R=i=>E(i),U=(i,S,F)=>a.dispatch("select",{index:i,value:S,selected:F.currentTarget.checked}),V=(i,S,F)=>{F.key==="Enter"&&(E(i),a.dispatch("select",{index:S,value:i,selected:!r.includes(i)}))};return t.$$set=i=>{"gradio"in i&&s(1,a=i.gradio),"elem_id"in i&&s(2,u=i.elem_id),"elem_classes"in i&&s(3,k=i.elem_classes),"visible"in i&&s(4,m=i.visible),"value"in i&&s(0,r=i.value),"choices"in i&&s(5,d=i.choices),"container"in i&&s(6,c=i.container),"scale"in i&&s(7,l=i.scale),"min_width"in i&&s(8,o=i.min_width),"label"in i&&s(9,b=i.label),"info"in i&&s(10,w=i.info),"show_label"in i&&s(11,_=i.show_label),"root"in i&&s(12,h=i.root),"loading_status"in i&&s(13,g=i.loading_status),"interactive"in i&&s(17,C=i.interactive),"old_value"in i&&s(16,J=i.old_value)},t.$$.update=()=>{t.$$.dirty&131072&&s(14,n=!C),t.$$.dirty&65539&&JSON.stringify(J)!==JSON.stringify(r)&&(s(16,J=r),a.dispatch("change"))},[r,a,u,k,m,d,c,l,o,b,w,_,h,g,n,E,J,C,Q,R,U,V]}class fe extends W{constructor(e){super(),X(this,e,ue,ae,Z,{gradio:1,elem_id:2,elem_classes:3,visible:4,value:0,choices:5,container:6,scale:7,min_width:8,label:9,info:10,show_label:11,root:12,loading_status:13,interactive:17,old_value:16})}get gradio(){return this.$$.ctx[1]}set gradio(e){this.$$set({gradio:e}),f()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get choices(){return this.$$.ctx[5]}set choices(e){this.$$set({choices:e}),f()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[7]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[8]}set min_width(e){this.$$set({min_width:e}),f()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),f()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),f()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),f()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),f()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),f()}get interactive(){return this.$$.ctx[17]}set interactive(e){this.$$set({interactive:e}),f()}get old_value(){return this.$$.ctx[16]}set old_value(e){this.$$set({old_value:e}),f()}}export{fe as default};
//# sourceMappingURL=Index-ej_eShvO.js.map
