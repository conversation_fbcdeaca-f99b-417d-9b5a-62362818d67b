{"version": 3, "file": "IconButtonWrapper-Ck50MZwX.js", "sources": ["../../../atoms/src/IconButtonWrapper.svelte"], "sourcesContent": ["<script>\n\texport let top_panel = true;\n</script>\n\n<div class={`icon-button-wrapper ${top_panel ? \"top-panel\" : \"\"}`}>\n\t<slot></slot>\n</div>\n\n<style>\n\t.icon-button-wrapper {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: var(--layer-2);\n\t\tgap: var(--spacing-sm);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: var(--spacing-xxs);\n\t}\n\n\t.icon-button-wrapper:not(.top-panel) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t}\n\n\t.top-panel {\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tright: var(--block-label-margin);\n\t\tmargin: 0;\n\t}\n\n\t.icon-button-wrapper :global(button) {\n\t\tmargin: var(--spacing-xxs);\n\t\tborder-radius: var(--radius-xs);\n\t\tposition: relative;\n\t}\n\n\t.icon-button-wrapper :global(a.download-link:not(:last-child)),\n\t.icon-button-wrapper :global(button:not(:last-child)) {\n\t\tmargin-right: var(--spacing-xxs);\n\t}\n\n\t.icon-button-wrapper :global(a.download-link:not(:last-child)::after),\n\t.icon-button-wrapper :global(button:not(:last-child)::after) {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tright: -4.5px;\n\t\ttop: 15%;\n\t\theight: 70%;\n\t\twidth: 1px;\n\t\tbackground-color: var(--border-color-primary);\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "top_panel", "$$props"], "mappings": "uQAImCA,EAAS,CAAA,EAAG,YAAc,EAAE,EAAA,EAAA,gBAAA,UAA/DC,EAEKC,EAAAC,EAAAC,CAAA,2IAF8BJ,EAAS,CAAA,EAAG,YAAc,EAAE,EAAA,EAAA,sJAHnD,CAAA,UAAAK,EAAY,EAAI,EAAAC"}