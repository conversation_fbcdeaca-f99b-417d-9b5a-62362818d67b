import{a as H,i as J,s as K,f as _,p as L,x as O,c as B,q as k,r as E,$ as N,l as A,v as P,m as I,N as Q,t as b,y as R,b as w,z as U,o as D,d as M,A as V,W as X,B as Z,Y as y,S as x,a0 as $,a4 as p}from"../lite.js";import{c as q,a as ee}from"./utils-BsGrhMNe.js";import{C as Y}from"./Check-ChD9RrF6.js";import{C as F}from"./Copy-DOOc0VFX.js";import{M as te}from"./MarkdownCode-C6RHM0m6.js";import{I as se}from"./IconButtonWrapper-Ck50MZwX.js";import{default as ve}from"./Example-CKq8IFW3.js";function G(t){let e,i;return e=new se({props:{$$slots:{default:[ie]},$$scope:{ctx:t}}}),{c(){B(e.$$.fragment)},m(s,a){I(e,s,a),i=!0},p(s,a){const h={};a&270336&&(h.$$scope={dirty:a,ctx:s}),e.$set(h)},i(s){i||(b(e.$$.fragment,s),i=!0)},o(s){w(e.$$.fragment,s),i=!1},d(s){M(e,s)}}}function ie(t){let e,i;return e=new X({props:{Icon:t[13]?Y:F,label:t[13]?"Copied conversation":"Copy conversation"}}),e.$on("click",t[14]),{c(){B(e.$$.fragment)},m(s,a){I(e,s,a),i=!0},p(s,a){const h={};a&8192&&(h.Icon=s[13]?Y:F),a&8192&&(h.label=s[13]?"Copied conversation":"Copy conversation"),e.$set(h)},i(s){i||(b(e.$$.fragment,s),i=!0)},o(s){w(e.$$.fragment,s),i=!1},d(s){M(e,s)}}}function ne(t){let e,i,s,a,h,c,g,l,o,f=t[10]&&G(t);return s=new te({props:{message:t[2],latex_delimiters:t[7],sanitize_html:t[5],line_breaks:t[6],chatbot:!1,header_links:t[8],root:t[11]}}),{c(){e=L("div"),f&&f.c(),i=O(),B(s.$$.fragment),k(e,"class",a="prose "+t[0].join(" ")+" svelte-lag733"),k(e,"data-testid","markdown"),k(e,"dir",h=t[4]?"rtl":"ltr"),k(e,"style",c=t[9]?`max-height: ${q(t[9])}; overflow-y: auto;`:""),E(e,"hide",!t[1]),N(e,"min-height",t[3]&&t[12]?.status!=="pending"?q(t[3]):void 0)},m(n,m){A(n,e,m),f&&f.m(e,null),P(e,i),I(s,e,null),g=!0,l||(o=Q(ee.call(null,e)),l=!0)},p(n,[m]){n[10]?f?(f.p(n,m),m&1024&&b(f,1)):(f=G(n),f.c(),b(f,1),f.m(e,i)):f&&(R(),w(f,1,1,()=>{f=null}),U());const d={};m&4&&(d.message=n[2]),m&128&&(d.latex_delimiters=n[7]),m&32&&(d.sanitize_html=n[5]),m&64&&(d.line_breaks=n[6]),m&256&&(d.header_links=n[8]),m&2048&&(d.root=n[11]),s.$set(d),(!g||m&1&&a!==(a="prose "+n[0].join(" ")+" svelte-lag733"))&&k(e,"class",a),(!g||m&16&&h!==(h=n[4]?"rtl":"ltr"))&&k(e,"dir",h),(!g||m&512&&c!==(c=n[9]?`max-height: ${q(n[9])}; overflow-y: auto;`:""))&&k(e,"style",c),(!g||m&3)&&E(e,"hide",!n[1]);const v=m&512;(m&4616||v)&&N(e,"min-height",n[3]&&n[12]?.status!=="pending"?q(n[3]):void 0)},i(n){g||(b(f),b(s.$$.fragment,n),g=!0)},o(n){w(f),w(s.$$.fragment,n),g=!1},d(n){n&&D(e),f&&f.d(),M(s),l=!1,o()}}}function le(t,e,i){let{elem_classes:s=[]}=e,{visible:a=!0}=e,{value:h}=e,{min_height:c=void 0}=e,{rtl:g=!1}=e,{sanitize_html:l=!0}=e,{line_breaks:o=!1}=e,{latex_delimiters:f}=e,{header_links:n=!1}=e,{height:m=void 0}=e,{show_copy_button:d=!1}=e,{root:v}=e,{loading_status:j=void 0}=e,z=!1,C;const S=V();async function T(){"clipboard"in navigator&&(await navigator.clipboard.writeText(h),W())}function W(){i(13,z=!0),C&&clearTimeout(C),C=setTimeout(()=>{i(13,z=!1)},1e3)}return t.$$set=u=>{"elem_classes"in u&&i(0,s=u.elem_classes),"visible"in u&&i(1,a=u.visible),"value"in u&&i(2,h=u.value),"min_height"in u&&i(3,c=u.min_height),"rtl"in u&&i(4,g=u.rtl),"sanitize_html"in u&&i(5,l=u.sanitize_html),"line_breaks"in u&&i(6,o=u.line_breaks),"latex_delimiters"in u&&i(7,f=u.latex_delimiters),"header_links"in u&&i(8,n=u.header_links),"height"in u&&i(9,m=u.height),"show_copy_button"in u&&i(10,d=u.show_copy_button),"root"in u&&i(11,v=u.root),"loading_status"in u&&i(12,j=u.loading_status)},t.$$.update=()=>{t.$$.dirty&4&&S("change")},[s,a,h,c,g,l,o,f,n,m,d,v,j,z,T]}class ae extends H{constructor(e){super(),J(this,e,le,ne,K,{elem_classes:0,visible:1,value:2,min_height:3,rtl:4,sanitize_html:5,line_breaks:6,latex_delimiters:7,header_links:8,height:9,show_copy_button:10,root:11,loading_status:12})}get elem_classes(){return this.$$.ctx[0]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),_()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),_()}get min_height(){return this.$$.ctx[3]}set min_height(e){this.$$set({min_height:e}),_()}get rtl(){return this.$$.ctx[4]}set rtl(e){this.$$set({rtl:e}),_()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),_()}get line_breaks(){return this.$$.ctx[6]}set line_breaks(e){this.$$set({line_breaks:e}),_()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),_()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),_()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),_()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),_()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),_()}get loading_status(){return this.$$.ctx[12]}set loading_status(e){this.$$set({loading_status:e}),_()}}const he=ae;function re(t){let e,i,s,a,h;const c=[{autoscroll:t[8].autoscroll},{i18n:t[8].i18n},t[4],{variant:"center"}];let g={};for(let l=0;l<c.length;l+=1)g=y(g,c[l]);return e=new x({props:g}),e.$on("clear_status",t[17]),a=new he({props:{value:t[3],elem_classes:t[1],visible:t[2],rtl:t[5],latex_delimiters:t[9],sanitize_html:t[6],line_breaks:t[7],header_links:t[10],show_copy_button:t[14],root:t[8].root,loading_status:t[4]}}),a.$on("change",t[18]),{c(){B(e.$$.fragment),i=O(),s=L("div"),B(a.$$.fragment),k(s,"class","svelte-1ed2p3z"),E(s,"pending",t[4]?.status==="pending")},m(l,o){I(e,l,o),A(l,i,o),A(l,s,o),I(a,s,null),h=!0},p(l,o){const f=o&272?$(c,[o&256&&{autoscroll:l[8].autoscroll},o&256&&{i18n:l[8].i18n},o&16&&p(l[4]),c[3]]):{};e.$set(f);const n={};o&8&&(n.value=l[3]),o&2&&(n.elem_classes=l[1]),o&4&&(n.visible=l[2]),o&32&&(n.rtl=l[5]),o&512&&(n.latex_delimiters=l[9]),o&64&&(n.sanitize_html=l[6]),o&128&&(n.line_breaks=l[7]),o&1024&&(n.header_links=l[10]),o&16384&&(n.show_copy_button=l[14]),o&256&&(n.root=l[8].root),o&16&&(n.loading_status=l[4]),a.$set(n),(!h||o&16)&&E(s,"pending",l[4]?.status==="pending")},i(l){h||(b(e.$$.fragment,l),b(a.$$.fragment,l),h=!0)},o(l){w(e.$$.fragment,l),w(a.$$.fragment,l),h=!1},d(l){l&&(D(i),D(s)),M(e,l),M(a)}}}function _e(t){let e,i;return e=new Z({props:{visible:t[2],elem_id:t[0],elem_classes:t[1],container:t[15],allow_overflow:!0,overflow_behavior:"auto",height:t[11],min_height:t[12],max_height:t[13],$$slots:{default:[re]},$$scope:{ctx:t}}}),{c(){B(e.$$.fragment)},m(s,a){I(e,s,a),i=!0},p(s,[a]){const h={};a&4&&(h.visible=s[2]),a&1&&(h.elem_id=s[0]),a&2&&(h.elem_classes=s[1]),a&32768&&(h.container=s[15]),a&2048&&(h.height=s[11]),a&4096&&(h.min_height=s[12]),a&8192&&(h.max_height=s[13]),a&542718&&(h.$$scope={dirty:a,ctx:s}),e.$set(h)},i(s){i||(b(e.$$.fragment,s),i=!0)},o(s){w(e.$$.fragment,s),i=!1},d(s){M(e,s)}}}function oe(t,e,i){let{label:s}=e,{elem_id:a=""}=e,{elem_classes:h=[]}=e,{visible:c=!0}=e,{value:g=""}=e,{loading_status:l}=e,{rtl:o=!1}=e,{sanitize_html:f=!0}=e,{line_breaks:n=!1}=e,{gradio:m}=e,{latex_delimiters:d}=e,{header_links:v=!1}=e,{height:j}=e,{min_height:z}=e,{max_height:C}=e,{show_copy_button:S=!1}=e,{container:T=!1}=e;const W=()=>m.dispatch("clear_status",l),u=()=>m.dispatch("change");return t.$$set=r=>{"label"in r&&i(16,s=r.label),"elem_id"in r&&i(0,a=r.elem_id),"elem_classes"in r&&i(1,h=r.elem_classes),"visible"in r&&i(2,c=r.visible),"value"in r&&i(3,g=r.value),"loading_status"in r&&i(4,l=r.loading_status),"rtl"in r&&i(5,o=r.rtl),"sanitize_html"in r&&i(6,f=r.sanitize_html),"line_breaks"in r&&i(7,n=r.line_breaks),"gradio"in r&&i(8,m=r.gradio),"latex_delimiters"in r&&i(9,d=r.latex_delimiters),"header_links"in r&&i(10,v=r.header_links),"height"in r&&i(11,j=r.height),"min_height"in r&&i(12,z=r.min_height),"max_height"in r&&i(13,C=r.max_height),"show_copy_button"in r&&i(14,S=r.show_copy_button),"container"in r&&i(15,T=r.container)},t.$$.update=()=>{t.$$.dirty&65792&&m.dispatch("change")},[a,h,c,g,l,o,f,n,m,d,v,j,z,C,S,T,s,W,u]}class be extends H{constructor(e){super(),J(this,e,oe,_e,K,{label:16,elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,rtl:5,sanitize_html:6,line_breaks:7,gradio:8,latex_delimiters:9,header_links:10,height:11,min_height:12,max_height:13,show_copy_button:14,container:15})}get label(){return this.$$.ctx[16]}set label(e){this.$$set({label:e}),_()}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),_()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),_()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),_()}get loading_status(){return this.$$.ctx[4]}set loading_status(e){this.$$set({loading_status:e}),_()}get rtl(){return this.$$.ctx[5]}set rtl(e){this.$$set({rtl:e}),_()}get sanitize_html(){return this.$$.ctx[6]}set sanitize_html(e){this.$$set({sanitize_html:e}),_()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),_()}get gradio(){return this.$$.ctx[8]}set gradio(e){this.$$set({gradio:e}),_()}get latex_delimiters(){return this.$$.ctx[9]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),_()}get header_links(){return this.$$.ctx[10]}set header_links(e){this.$$set({header_links:e}),_()}get height(){return this.$$.ctx[11]}set height(e){this.$$set({height:e}),_()}get min_height(){return this.$$.ctx[12]}set min_height(e){this.$$set({min_height:e}),_()}get max_height(){return this.$$.ctx[13]}set max_height(e){this.$$set({max_height:e}),_()}get show_copy_button(){return this.$$.ctx[14]}set show_copy_button(e){this.$$set({show_copy_button:e}),_()}get container(){return this.$$.ctx[15]}set container(e){this.$$set({container:e}),_()}}export{ve as BaseExample,he as BaseMarkdown,be as default};
//# sourceMappingURL=Index-Oe-qVXKW.js.map
