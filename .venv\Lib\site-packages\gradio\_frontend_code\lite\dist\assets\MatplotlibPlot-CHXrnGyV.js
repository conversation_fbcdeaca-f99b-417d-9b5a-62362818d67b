import{a as v,i as f,s as m,f as _,p as c,ap as d,q as u,l as h,v as g,F as b,w as p,o as j,H as q}from"../lite.js";function $(e){let a,t,s,l,r,i;return{c(){a=c("div"),t=c("img"),d(t.src,s=e[1])||u(t,"src",s),u(t,"alt",l=`${e[0].chart} plot visualising provided data`),u(t,"class","svelte-j1jcu3"),u(a,"data-testid","matplotlib"),u(a,"class","matplotlib layout svelte-j1jcu3")},m(o,n){h(o,a,n),g(a,t),r||(i=b(t,"load",e[2]),r=!0)},p(o,[n]){n&2&&!d(t.src,s=o[1])&&u(t,"src",s),n&1&&l!==(l=`${o[0].chart} plot visualising provided data`)&&u(t,"alt",l)},i:p,o:p,d(o){o&&j(a),r=!1,i()}}}function w(e,a,t){let s,{value:l}=a;function r(i){q.call(this,e,i)}return e.$$set=i=>{"value"in i&&t(0,l=i.value)},e.$$.update=()=>{e.$$.dirty&1&&t(1,s=l?.plot)},[l,s,r]}class C extends v{constructor(a){super(),f(this,a,w,$,m,{value:0})}get value(){return this.$$.ctx[0]}set value(a){this.$$set({value:a}),_()}}export{C as default};
//# sourceMappingURL=MatplotlibPlot-CHXrnGyV.js.map
