import{a as P,i as $,s as R,Q as q,q as _,l as H,v as B,w as I,o as z,f as m,E as G,y as T,b,z as j,t as c,H as ke,as as W,c as L,m as A,d as N,p as S,k as be,x as D,$ as ee,F as Be,n as Ce,e as Zt,u as Dt,h as Ut,j as qt,W as te,a8 as Vt,ao as pe,r as Z,aq as Ze,P as Pt,Y as De,a0 as Ue,a4 as qe,O as Mt,R as Fe,a3 as Se,A as $t,D as Rt,I as Ft,N as Ot,G as Gt,ap as Oe,ax as Jt,B as Wt,S as Yt}from"../lite.js";import{u as Ge,S as Qt,a as Kt}from"./utils-BsGrhMNe.js";import{I as Pe}from"./Image-Bd-jnd8M.js";import{M as $e}from"./MarkdownCode-C6RHM0m6.js";/* empty css                                                   */import{C as Ie}from"./Check-ChD9RrF6.js";import{C as Te}from"./Copy-DOOc0VFX.js";import{D as Xt}from"./DownloadLink-B8hI46-W.js";import{U as xt}from"./Undo-DitIvwTU.js";import{I as Re}from"./IconButtonWrapper-Ck50MZwX.js";import{d as el}from"./index-CnqicUFC.js";import{C as Lt}from"./Community-_qL8Iyvr.js";import{T as tl}from"./Trash-CRoy_ZGY.js";import{B as ll}from"./BlockLabel-B0HN-MOU.js";import"./file-url-Co2ROWca.js";function nl(l){let e,t,n;return{c(){e=q("svg"),t=q("path"),n=q("path"),_(t,"fill","currentColor"),_(t,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),_(n,"fill","currentColor"),_(n,"d","M8 10h16v2H8zm0 6h10v2H8z"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),_(e,"aria-hidden","true"),_(e,"role","img"),_(e,"class","iconify iconify--carbon"),_(e,"width","100%"),_(e,"height","100%"),_(e,"preserveAspectRatio","xMidYMid meet"),_(e,"viewBox","0 0 32 32")},m(i,o){H(i,e,o),B(e,t),B(e,n)},p:I,i:I,o:I,d(i){i&&z(e)}}}class il extends P{constructor(e){super(),$(this,e,null,nl,R,{})}}function sl(l){let e,t,n,i,o;return{c(){e=q("svg"),t=q("path"),n=q("path"),i=q("path"),o=q("path"),_(t,"d","M19.1679 9C18.0247 6.46819 15.3006 4.5 11.9999 4.5C8.31459 4.5 5.05104 7.44668 4.54932 11"),_(t,"stroke","currentColor"),_(t,"stroke-width","1.5"),_(t,"stroke-linecap","round"),_(t,"stroke-linejoin","round"),_(n,"d","M16 9H19.4C19.7314 9 20 8.73137 20 8.4V5"),_(n,"stroke","currentColor"),_(n,"stroke-width","1.5"),_(n,"stroke-linecap","round"),_(n,"stroke-linejoin","round"),_(i,"d","M4.88146 15C5.92458 17.5318 8.64874 19.5 12.0494 19.5C15.7347 19.5 18.9983 16.5533 19.5 13"),_(i,"stroke","currentColor"),_(i,"stroke-width","1.5"),_(i,"stroke-linecap","round"),_(i,"stroke-linejoin","round"),_(o,"d","M8.04932 15H4.64932C4.31795 15 4.04932 15.2686 4.04932 15.6V19"),_(o,"stroke","currentColor"),_(o,"stroke-width","1.5"),_(o,"stroke-linecap","round"),_(o,"stroke-linejoin","round"),_(e,"width","100%"),_(e,"height","100%"),_(e,"stroke-width","1.5"),_(e,"viewBox","0 0 24 24"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"color","currentColor")},m(s,a){H(s,e,a),B(e,t),B(e,n),B(e,i),B(e,o)},p:I,i:I,o:I,d(s){s&&z(e)}}}class ol extends P{constructor(e){super(),$(this,e,null,sl,R,{})}}function rl(l){let e,t;return{c(){e=q("svg"),t=q("path"),_(t,"d","M12 20L12 4M12 20L7 15M12 20L17 15"),_(t,"stroke","currentColor"),_(t,"stroke-width","2"),_(t,"stroke-linecap","round"),_(t,"stroke-linejoin","round"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 24 24"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){H(n,e,i),B(e,t)},p:I,i:I,o:I,d(n){n&&z(e)}}}class al extends P{constructor(e){super(),$(this,e,null,rl,R,{})}}const ul=async l=>(await Promise.all(l.map(async t=>await Promise.all(t.map(async(n,i)=>{if(n===null)return"";let o=i===0?"😃":"🤖",s="";if(typeof n=="string"){const a={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};s=n;for(let[r,u]of Object.entries(a)){let f;for(;(f=u.exec(n))!==null;){const g=f[1]||f[2],w=await Ge(g);s=s.replace(g,w)}}}else{if(!n?.url)return"";const a=await Ge(n.url);n.mime_type?.includes("audio")?s=`<audio controls src="${a}"></audio>`:n.mime_type?.includes("video")?s=a:n.mime_type?.includes("image")&&(s=`<img src="${a}" />`)}return`${o}: ${s}`}))))).map(t=>t.join(t[0]!==""&&t[1]!==""?`
`:"")).join(`
`),At=(l,e)=>l.replace('src="/file',`src="${e}file`);function fl(l){return l?l.includes("audio")?"audio":l.includes("video")?"video":l.includes("image")?"image":"file":"file"}function Nt(l){const e=Array.isArray(l.file)?l.file[0]:l.file;return{component:fl(e?.mime_type),value:l.file,alt_text:l.alt_text,constructor_args:{},props:{}}}function _l(l,e){return l===null?l:l.map((t,n)=>typeof t.content=="string"?{role:t.role,metadata:t.metadata,content:At(t.content,e),type:"text",index:n}:"file"in t.content?{content:Nt(t.content),metadata:t.metadata,role:t.role,type:"component",index:n}:{type:"component",...t})}function cl(l,e){return l===null?l:l.flatMap((n,i)=>n.map((o,s)=>{if(o==null)return null;const a=s==0?"user":"assistant";return typeof o=="string"?{role:a,type:"text",content:At(o,e),metadata:{title:null},index:[i,s]}:"file"in o?{content:Nt(o),role:a,type:"component",index:[i,s]}:{role:a,content:o,type:"component",index:[i,s]}})).filter(n=>n!=null)}function de(l){return l.type==="component"}function ve(l,e){const t=l[l.length-1].role==="assistant",n=l[l.length-1].index;return JSON.stringify(n)===JSON.stringify(e[e.length-1].index)&&t}function hl(l,e){const t=[];let n=[],i=null;for(const o of l)e==="tuples"&&(i=null),(o.role==="assistant"||o.role==="user")&&(o.role===i?n.push(o):(n.length>0&&t.push(n),n=[o],i=o.role));return n.length>0&&t.push(n),t}async function ml(l,e,t){let n=[],i=[];return l.forEach(s=>{if(e[s]||s==="file")return;const{name:a,component:r}=t(s,"base");n.push(a),i.push(r)}),(await Promise.all(i)).forEach((s,a)=>{e[n[a]]=s.default}),e}function gl(l){if(!l)return[];let e=new Set;return l.forEach(t=>{t.type==="component"&&e.add(t.content.component)}),Array.from(e)}function dl(l){let e,t,n;var i=l[1][l[0]];function o(s,a){return{props:{value:s[2],show_label:!1,label:"chatbot-image",show_share_button:!0,i18n:s[6],gradio:{dispatch:Hl}}}}return i&&(e=W(i,o(l)),e.$on("load",l[14])),{c(){e&&L(e.$$.fragment),t=G()},m(s,a){e&&A(e,s,a),H(s,t,a),n=!0},p(s,a){if(a&3&&i!==(i=s[1][s[0]])){if(e){T();const r=e;b(r.$$.fragment,1,0,()=>{N(r,1)}),j()}i?(e=W(i,o(s)),e.$on("load",s[14]),L(e.$$.fragment),c(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else if(i){const r={};a&4&&(r.value=s[2]),a&64&&(r.i18n=s[6]),e.$set(r)}},i(s){n||(e&&c(e.$$.fragment,s),n=!0)},o(s){e&&b(e.$$.fragment,s),n=!1},d(s){s&&z(t),e&&N(e,s)}}}function bl(l){let e,t,n;var i=l[1][l[0]];function o(s,a){return{props:{value:s[2],show_label:!1,label:"chatbot-image",show_download_button:!1,i18n:s[6]}}}return i&&(e=W(i,o(l)),e.$on("load",l[13])),{c(){e&&L(e.$$.fragment),t=G()},m(s,a){e&&A(e,s,a),H(s,t,a),n=!0},p(s,a){if(a&3&&i!==(i=s[1][s[0]])){if(e){T();const r=e;b(r.$$.fragment,1,0,()=>{N(r,1)}),j()}i?(e=W(i,o(s)),e.$on("load",s[13]),L(e.$$.fragment),c(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else if(i){const r={};a&4&&(r.value=s[2]),a&64&&(r.i18n=s[6]),e.$set(r)}},i(s){n||(e&&c(e.$$.fragment,s),n=!0)},o(s){e&&b(e.$$.fragment,s),n=!1},d(s){s&&z(t),e&&N(e,s)}}}function wl(l){let e,t,n;var i=l[1][l[0]];function o(s,a){return{props:{autoplay:!0,value:s[2].video||s[2],show_label:!1,show_share_button:!0,i18n:s[6],upload:s[7],show_download_button:!1,$$slots:{default:[Cl]},$$scope:{ctx:s}}}}return i&&(e=W(i,o(l)),e.$on("load",l[12])),{c(){e&&L(e.$$.fragment),t=G()},m(s,a){e&&A(e,s,a),H(s,t,a),n=!0},p(s,a){if(a&3&&i!==(i=s[1][s[0]])){if(e){T();const r=e;b(r.$$.fragment,1,0,()=>{N(r,1)}),j()}i?(e=W(i,o(s)),e.$on("load",s[12]),L(e.$$.fragment),c(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else if(i){const r={};a&4&&(r.value=s[2].video||s[2]),a&64&&(r.i18n=s[6]),a&128&&(r.upload=s[7]),a&32768&&(r.$$scope={dirty:a,ctx:s}),e.$set(r)}},i(s){n||(e&&c(e.$$.fragment,s),n=!0)},o(s){e&&b(e.$$.fragment,s),n=!1},d(s){s&&z(t),e&&N(e,s)}}}function kl(l){let e,t,n;var i=l[1][l[0]];function o(s,a){return{props:{value:s[2],show_label:!1,show_share_button:!0,i18n:s[6],label:"",waveform_settings:{},waveform_options:{},show_download_button:!1}}}return i&&(e=W(i,o(l)),e.$on("load",l[11])),{c(){e&&L(e.$$.fragment),t=G()},m(s,a){e&&A(e,s,a),H(s,t,a),n=!0},p(s,a){if(a&3&&i!==(i=s[1][s[0]])){if(e){T();const r=e;b(r.$$.fragment,1,0,()=>{N(r,1)}),j()}i?(e=W(i,o(s)),e.$on("load",s[11]),L(e.$$.fragment),c(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else if(i){const r={};a&4&&(r.value=s[2]),a&64&&(r.i18n=s[6]),e.$set(r)}},i(s){n||(e&&c(e.$$.fragment,s),n=!0)},o(s){e&&b(e.$$.fragment,s),n=!1},d(s){s&&z(t),e&&N(e,s)}}}function vl(l){let e,t,n;var i=l[1][l[0]];function o(s,a){return{props:{value:s[2],target:s[3],theme_mode:s[4],bokeh_version:s[5].bokeh_version,caption:"",show_actions_button:!0}}}return i&&(e=W(i,o(l)),e.$on("load",l[10])),{c(){e&&L(e.$$.fragment),t=G()},m(s,a){e&&A(e,s,a),H(s,t,a),n=!0},p(s,a){if(a&3&&i!==(i=s[1][s[0]])){if(e){T();const r=e;b(r.$$.fragment,1,0,()=>{N(r,1)}),j()}i?(e=W(i,o(s)),e.$on("load",s[10]),L(e.$$.fragment),c(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else if(i){const r={};a&4&&(r.value=s[2]),a&8&&(r.target=s[3]),a&16&&(r.theme_mode=s[4]),a&32&&(r.bokeh_version=s[5].bokeh_version),e.$set(r)}},i(s){n||(e&&c(e.$$.fragment,s),n=!0)},o(s){e&&b(e.$$.fragment,s),n=!1},d(s){s&&z(t),e&&N(e,s)}}}function pl(l){let e,t,n;var i=l[1][l[0]];function o(s,a){return{props:{value:s[2],show_label:!1,i18n:s[6],label:"",_fetch:s[8],allow_preview:!1,interactive:!1,mode:"minimal",fixed_height:1}}}return i&&(e=W(i,o(l)),e.$on("load",l[9])),{c(){e&&L(e.$$.fragment),t=G()},m(s,a){e&&A(e,s,a),H(s,t,a),n=!0},p(s,a){if(a&3&&i!==(i=s[1][s[0]])){if(e){T();const r=e;b(r.$$.fragment,1,0,()=>{N(r,1)}),j()}i?(e=W(i,o(s)),e.$on("load",s[9]),L(e.$$.fragment),c(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else if(i){const r={};a&4&&(r.value=s[2]),a&64&&(r.i18n=s[6]),a&256&&(r._fetch=s[8]),e.$set(r)}},i(s){n||(e&&c(e.$$.fragment,s),n=!0)},o(s){e&&b(e.$$.fragment,s),n=!1},d(s){s&&z(t),e&&N(e,s)}}}function Cl(l){let e;return{c(){e=S("track"),_(e,"kind","captions")},m(t,n){H(t,e,n)},p:I,d(t){t&&z(e)}}}function yl(l){let e,t,n,i;const o=[pl,vl,kl,wl,bl,dl],s=[];function a(r,u){return r[0]==="gallery"?0:r[0]==="plot"?1:r[0]==="audio"?2:r[0]==="video"?3:r[0]==="image"?4:r[0]==="html"?5:-1}return~(e=a(l))&&(t=s[e]=o[e](l)),{c(){t&&t.c(),n=G()},m(r,u){~e&&s[e].m(r,u),H(r,n,u),i=!0},p(r,[u]){let f=e;e=a(r),e===f?~e&&s[e].p(r,u):(t&&(T(),b(s[f],1,1,()=>{s[f]=null}),j()),~e?(t=s[e],t?t.p(r,u):(t=s[e]=o[e](r),t.c()),c(t,1),t.m(n.parentNode,n)):t=null)},i(r){i||(c(t),i=!0)},o(r){b(t),i=!1},d(r){r&&z(n),~e&&s[e].d(r)}}}const Hl=()=>{};function zl(l,e,t){let{type:n}=e,{components:i}=e,{value:o}=e,{target:s}=e,{theme_mode:a}=e,{props:r}=e,{i18n:u}=e,{upload:f}=e,{_fetch:g}=e;function w(y){ke.call(this,l,y)}function h(y){ke.call(this,l,y)}function d(y){ke.call(this,l,y)}function V(y){ke.call(this,l,y)}function E(y){ke.call(this,l,y)}function F(y){ke.call(this,l,y)}return l.$$set=y=>{"type"in y&&t(0,n=y.type),"components"in y&&t(1,i=y.components),"value"in y&&t(2,o=y.value),"target"in y&&t(3,s=y.target),"theme_mode"in y&&t(4,a=y.theme_mode),"props"in y&&t(5,r=y.props),"i18n"in y&&t(6,u=y.i18n),"upload"in y&&t(7,f=y.upload),"_fetch"in y&&t(8,g=y._fetch)},[n,i,o,s,a,r,u,f,g,w,h,d,V,E,F]}class Vl extends P{constructor(e){super(),$(this,e,zl,yl,R,{type:0,components:1,value:2,target:3,theme_mode:4,props:5,i18n:6,upload:7,_fetch:8})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),m()}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),m()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),m()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),m()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),m()}get props(){return this.$$.ctx[5]}set props(e){this.$$set({props:e}),m()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),m()}get upload(){return this.$$.ctx[7]}set upload(e){this.$$set({upload:e}),m()}get _fetch(){return this.$$.ctx[8]}set _fetch(e){this.$$set({_fetch:e}),m()}}function Je(l){let e,t;const n=l[4].default,i=Zt(n,l,l[3],null);return{c(){e=S("div"),i&&i.c(),_(e,"class","content svelte-1e60bn1")},m(o,s){H(o,e,s),i&&i.m(e,null),t=!0},p(o,s){i&&i.p&&(!t||s&8)&&Dt(i,n,o,o[3],t?qt(n,o[3],s,null):Ut(o[3]),null)},i(o){t||(c(i,o),t=!0)},o(o){b(i,o),t=!1},d(o){o&&z(e),i&&i.d(o)}}}function Ml(l){let e,t,n,i,o,s,a,r,u,f,g=l[0]&&Je(l);return{c(){e=S("button"),t=S("div"),n=S("span"),i=be(l[1]),o=D(),s=S("span"),s.textContent="▼",a=D(),g&&g.c(),_(n,"class","title-text svelte-1e60bn1"),_(s,"class","arrow svelte-1e60bn1"),ee(s,"transform",l[0]?"rotate(0)":"rotate(90deg)"),_(t,"class","title svelte-1e60bn1"),_(e,"class","box svelte-1e60bn1")},m(w,h){H(w,e,h),B(e,t),B(t,n),B(n,i),B(t,o),B(t,s),B(e,a),g&&g.m(e,null),r=!0,u||(f=Be(e,"click",l[2]),u=!0)},p(w,[h]){(!r||h&2)&&Ce(i,w[1]),h&1&&ee(s,"transform",w[0]?"rotate(0)":"rotate(90deg)"),w[0]?g?(g.p(w,h),h&1&&c(g,1)):(g=Je(w),g.c(),c(g,1),g.m(e,null)):g&&(T(),b(g,1,1,()=>{g=null}),j())},i(w){r||(c(g),r=!0)},o(w){b(g),r=!1},d(w){w&&z(e),g&&g.d(),u=!1,f()}}}function Ll(l,e,t){let{$$slots:n={},$$scope:i}=e,{expanded:o=!1}=e,{title:s}=e;function a(){t(0,o=!o)}return l.$$set=r=>{"expanded"in r&&t(0,o=r.expanded),"title"in r&&t(1,s=r.title),"$$scope"in r&&t(3,i=r.$$scope)},[o,s,a,i,n]}class Al extends P{constructor(e){super(),$(this,e,Ll,Ml,R,{expanded:0,title:1})}get expanded(){return this.$$.ctx[0]}set expanded(e){this.$$set({expanded:e}),m()}get title(){return this.$$.ctx[1]}set title(e){this.$$set({title:e}),m()}}function Nl(l){let e,t;return{c(){e=q("svg"),t=q("path"),_(t,"d","M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z"),_(t,"fill","currentColor"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 12 12"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){H(n,e,i),B(e,t)},p:I,i:I,o:I,d(n){n&&z(e)}}}class We extends P{constructor(e){super(),$(this,e,null,Nl,R,{})}}function Bl(l){let e,t;return{c(){e=q("svg"),t=q("path"),_(t,"d","M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z"),_(t,"fill","currentColor"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 12 12"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){H(n,e,i),B(e,t)},p:I,i:I,o:I,d(n){n&&z(e)}}}class Ye extends P{constructor(e){super(),$(this,e,null,Bl,R,{})}}function Il(l){let e,t;return{c(){e=q("svg"),t=q("path"),_(t,"d","M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z"),_(t,"fill","currentColor"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 12 12"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){H(n,e,i),B(e,t)},p:I,i:I,o:I,d(n){n&&z(e)}}}class Qe extends P{constructor(e){super(),$(this,e,null,Il,R,{})}}function Tl(l){let e,t;return{c(){e=q("svg"),t=q("path"),_(t,"d","M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z"),_(t,"fill","currentColor"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 12 12"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){H(n,e,i),B(e,t)},p:I,i:I,o:I,d(n){n&&z(e)}}}class Ke extends P{constructor(e){super(),$(this,e,null,Tl,R,{})}}function jl(l){let e,t,n,i;return e=new te({props:{Icon:l[1]==="dislike"?We:Ye,label:l[1]==="dislike"?"clicked dislike":"dislike",color:l[1]==="dislike"?"var(--color-accent)":"var(--block-label-text-color)"}}),e.$on("click",l[2]),n=new te({props:{Icon:l[1]==="like"?Qe:Ke,label:l[1]==="like"?"clicked like":"like",color:l[1]==="like"?"var(--color-accent)":"var(--block-label-text-color)"}}),n.$on("click",l[3]),{c(){L(e.$$.fragment),t=D(),L(n.$$.fragment)},m(o,s){A(e,o,s),H(o,t,s),A(n,o,s),i=!0},p(o,[s]){const a={};s&2&&(a.Icon=o[1]==="dislike"?We:Ye),s&2&&(a.label=o[1]==="dislike"?"clicked dislike":"dislike"),s&2&&(a.color=o[1]==="dislike"?"var(--color-accent)":"var(--block-label-text-color)"),e.$set(a);const r={};s&2&&(r.Icon=o[1]==="like"?Qe:Ke),s&2&&(r.label=o[1]==="like"?"clicked like":"like"),s&2&&(r.color=o[1]==="like"?"var(--color-accent)":"var(--block-label-text-color)"),n.$set(r)},i(o){i||(c(e.$$.fragment,o),c(n.$$.fragment,o),i=!0)},o(o){b(e.$$.fragment,o),b(n.$$.fragment,o),i=!1},d(o){o&&z(t),N(e,o),N(n,o)}}}function El(l,e,t){let{handle_action:n}=e,i=null;const o=()=>{t(1,i="dislike"),n(i)},s=()=>{t(1,i="like"),n(i)};return l.$$set=a=>{"handle_action"in a&&t(0,n=a.handle_action)},[n,i,o,s]}class Sl extends P{constructor(e){super(),$(this,e,El,jl,R,{handle_action:0})}get handle_action(){return this.$$.ctx[0]}set handle_action(e){this.$$set({handle_action:e}),m()}}function Zl(l){let e,t;return e=new te({props:{label:l[0]?"Copied message":"Copy message",Icon:l[0]?Ie:Te}}),e.$on("click",l[1]),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.label=n[0]?"Copied message":"Copy message"),i&1&&(o.Icon=n[0]?Ie:Te),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function Dl(l,e,t){let n=!1,{value:i}=e,o;function s(){t(0,n=!0),o&&clearTimeout(o),o=setTimeout(()=>{t(0,n=!1)},2e3)}async function a(){if("clipboard"in navigator)await navigator.clipboard.writeText(i),s();else{const r=document.createElement("textarea");r.value=i,r.style.position="absolute",r.style.left="-999999px",document.body.prepend(r),r.select();try{document.execCommand("copy"),s()}catch(u){console.error(u)}finally{r.remove()}}}return Vt(()=>{o&&clearTimeout(o)}),l.$$set=r=>{"value"in r&&t(2,i=r.value)},[n,a,i]}class Ul extends P{constructor(e){super(),$(this,e,Dl,Zl,R,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),m()}}function ql(l){let e,t,n;return{c(){e=q("svg"),t=q("path"),n=q("path"),_(t,"d","M6.27701 8.253C6.24187 8.29143 6.19912 8.32212 6.15147 8.34311C6.10383 8.36411 6.05233 8.37495 6.00026 8.37495C5.94819 8.37495 5.89669 8.36411 5.84905 8.34311C5.8014 8.32212 5.75865 8.29143 5.72351 8.253L3.72351 6.0655C3.65798 5.99185 3.62408 5.89536 3.62916 5.79691C3.63424 5.69846 3.67788 5.60596 3.75064 5.53945C3.8234 5.47293 3.91943 5.43774 4.01794 5.44149C4.11645 5.44525 4.20952 5.48764 4.27701 5.5595L5.62501 7.0345V1.5C5.62501 1.40054 5.66452 1.30516 5.73485 1.23483C5.80517 1.16451 5.90055 1.125 6.00001 1.125C6.09947 1.125 6.19485 1.16451 6.26517 1.23483C6.3355 1.30516 6.37501 1.40054 6.37501 1.5V7.034L7.72351 5.559C7.79068 5.4856 7.88425 5.44189 7.98364 5.43748C8.08304 5.43308 8.18011 5.46833 8.25351 5.5355C8.32691 5.60267 8.37062 5.69624 8.37503 5.79563C8.37943 5.89503 8.34418 5.9921 8.27701 6.0655L6.27701 8.253Z"),_(t,"fill","currentColor"),_(n,"d","M1.875 7.39258C1.875 7.29312 1.83549 7.19774 1.76517 7.12741C1.69484 7.05709 1.59946 7.01758 1.5 7.01758C1.40054 7.01758 1.30516 7.05709 1.23483 7.12741C1.16451 7.19774 1.125 7.29312 1.125 7.39258V7.42008C1.125 8.10358 1.125 8.65508 1.1835 9.08858C1.2435 9.53858 1.3735 9.91758 1.674 10.2186C1.975 10.5196 2.354 10.6486 2.804 10.7096C3.2375 10.7676 3.789 10.7676 4.4725 10.7676H7.5275C8.211 10.7676 8.7625 10.7676 9.196 10.7096C9.646 10.6486 10.025 10.5196 10.326 10.2186C10.627 9.91758 10.756 9.53858 10.817 9.08858C10.875 8.65508 10.875 8.10358 10.875 7.42008V7.39258C10.875 7.29312 10.8355 7.19774 10.7652 7.12741C10.6948 7.05709 10.5995 7.01758 10.5 7.01758C10.4005 7.01758 10.3052 7.05709 10.2348 7.12741C10.1645 7.19774 10.125 7.29312 10.125 7.39258C10.125 8.11008 10.124 8.61058 10.0735 8.98858C10.024 9.35558 9.9335 9.54958 9.7955 9.68808C9.657 9.82658 9.463 9.91658 9.0955 9.96608C8.718 10.0166 8.2175 10.0176 7.5 10.0176H4.5C3.7825 10.0176 3.2815 10.0166 2.904 9.96608C2.537 9.91658 2.343 9.82608 2.2045 9.68808C2.066 9.54958 1.976 9.35558 1.9265 8.98808C1.876 8.61058 1.875 8.11008 1.875 7.39258Z"),_(n,"fill","currentColor"),_(e,"width","16"),_(e,"height","16"),_(e,"viewBox","0 0 12 12"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(i,o){H(i,e,o),B(e,t),B(e,n)},p:I,i:I,o:I,d(i){i&&z(e)}}}class Pl extends P{constructor(e){super(),$(this,e,null,ql,R,{})}}function Xe(l){let e,t,n,i;return t=new Re({props:{top_panel:!1,$$slots:{default:[Rl]},$$scope:{ctx:l}}}),{c(){e=S("div"),L(t.$$.fragment),_(e,"class",n="message-buttons-"+l[5]+" "+l[9]+" message-buttons "+(l[6]!==null&&"with-avatar")+" svelte-1ibfe7l")},m(o,s){H(o,e,s),A(t,e,null),i=!0},p(o,s){const a={};s&73111&&(a.$$scope={dirty:s,ctx:o}),t.$set(a),(!i||s&608&&n!==(n="message-buttons-"+o[5]+" "+o[9]+" message-buttons "+(o[6]!==null&&"with-avatar")+" svelte-1ibfe7l"))&&_(e,"class",n)},i(o){i||(c(t.$$.fragment,o),i=!0)},o(o){b(t.$$.fragment,o),i=!1},d(o){o&&z(e),N(t)}}}function xe(l){let e,t;return e=new Ul({props:{value:l[12]}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i&4096&&(o.value=n[12]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function et(l){let e,t;return e=new Xt({props:{href:l[4]?.content?.value.url,download:l[4].content.value.orig_name||"image",$$slots:{default:[$l]},$$scope:{ctx:l}}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i&16&&(o.href=n[4]?.content?.value.url),i&16&&(o.download=n[4].content.value.orig_name||"image"),i&65536&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function $l(l){let e,t;return e=new te({props:{Icon:Pl}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p:I,i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function tt(l){let e,t;return e=new te({props:{Icon:ol,label:"Retry",disabled:l[7]}}),e.$on("click",l[14]),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i&128&&(o.disabled=n[7]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function lt(l){let e,t;return e=new te({props:{label:"Undo",Icon:xt,disabled:l[7]}}),e.$on("click",l[15]),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i&128&&(o.disabled=n[7]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function nt(l){let e,t;return e=new Sl({props:{handle_action:l[8]}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i&256&&(o.handle_action=n[8]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function Rl(l){let e,t=l[10]&&!Array.isArray(l[4])&&de(l[4]),n,i,o,s,a,r=l[11]&&xe(l),u=t&&et(l),f=l[1]&&tt(l),g=l[2]&&lt(l),w=l[0]&&nt(l);return{c(){r&&r.c(),e=D(),u&&u.c(),n=D(),f&&f.c(),i=D(),g&&g.c(),o=D(),w&&w.c(),s=G()},m(h,d){r&&r.m(h,d),H(h,e,d),u&&u.m(h,d),H(h,n,d),f&&f.m(h,d),H(h,i,d),g&&g.m(h,d),H(h,o,d),w&&w.m(h,d),H(h,s,d),a=!0},p(h,d){h[11]?r?(r.p(h,d),d&2048&&c(r,1)):(r=xe(h),r.c(),c(r,1),r.m(e.parentNode,e)):r&&(T(),b(r,1,1,()=>{r=null}),j()),d&1040&&(t=h[10]&&!Array.isArray(h[4])&&de(h[4])),t?u?(u.p(h,d),d&1040&&c(u,1)):(u=et(h),u.c(),c(u,1),u.m(n.parentNode,n)):u&&(T(),b(u,1,1,()=>{u=null}),j()),h[1]?f?(f.p(h,d),d&2&&c(f,1)):(f=tt(h),f.c(),c(f,1),f.m(i.parentNode,i)):f&&(T(),b(f,1,1,()=>{f=null}),j()),h[2]?g?(g.p(h,d),d&4&&c(g,1)):(g=lt(h),g.c(),c(g,1),g.m(o.parentNode,o)):g&&(T(),b(g,1,1,()=>{g=null}),j()),h[0]?w?(w.p(h,d),d&1&&c(w,1)):(w=nt(h),w.c(),c(w,1),w.m(s.parentNode,s)):w&&(T(),b(w,1,1,()=>{w=null}),j())},i(h){a||(c(r),c(u),c(f),c(g),c(w),a=!0)},o(h){b(r),b(u),b(f),b(g),b(w),a=!1},d(h){h&&(z(e),z(n),z(i),z(o),z(s)),r&&r.d(h),u&&u.d(h),f&&f.d(h),g&&g.d(h),w&&w.d(h)}}}function Fl(l){let e,t,n=l[3]&&Xe(l);return{c(){n&&n.c(),e=G()},m(i,o){n&&n.m(i,o),H(i,e,o),t=!0},p(i,[o]){i[3]?n?(n.p(i,o),o&8&&c(n,1)):(n=Xe(i),n.c(),c(n,1),n.m(e.parentNode,e)):n&&(T(),b(n,1,1,()=>{n=null}),j())},i(i){t||(c(n),t=!0)},o(i){b(n),t=!1},d(i){i&&z(e),n&&n.d(i)}}}function it(l){return Array.isArray(l)&&l.every(e=>typeof e.content=="string")||!Array.isArray(l)&&typeof l.content=="string"}function Ol(l){return Array.isArray(l)?l.map(e=>e.content).join(`
`):l.content}function Gl(l,e,t){let n,i,o,{likeable:s}=e,{show_retry:a}=e,{show_undo:r}=e,{show_copy_button:u}=e,{show:f}=e,{message:g}=e,{position:w}=e,{avatar:h}=e,{generating:d}=e,{handle_action:V}=e,{layout:E}=e;const F=()=>V("retry"),y=()=>V("undo");return l.$$set=C=>{"likeable"in C&&t(0,s=C.likeable),"show_retry"in C&&t(1,a=C.show_retry),"show_undo"in C&&t(2,r=C.show_undo),"show_copy_button"in C&&t(13,u=C.show_copy_button),"show"in C&&t(3,f=C.show),"message"in C&&t(4,g=C.message),"position"in C&&t(5,w=C.position),"avatar"in C&&t(6,h=C.avatar),"generating"in C&&t(7,d=C.generating),"handle_action"in C&&t(8,V=C.handle_action),"layout"in C&&t(9,E=C.layout)},l.$$.update=()=>{l.$$.dirty&16&&t(12,n=it(g)?Ol(g):""),l.$$.dirty&8208&&t(11,i=u&&g&&it(g)),l.$$.dirty&16&&t(10,o=!Array.isArray(g)&&de(g)&&g.content.value?.url)},[s,a,r,f,g,w,h,d,V,E,o,i,n,u,F,y]}class Bt extends P{constructor(e){super(),$(this,e,Gl,Fl,R,{likeable:0,show_retry:1,show_undo:2,show_copy_button:13,show:3,message:4,position:5,avatar:6,generating:7,handle_action:8,layout:9})}get likeable(){return this.$$.ctx[0]}set likeable(e){this.$$set({likeable:e}),m()}get show_retry(){return this.$$.ctx[1]}set show_retry(e){this.$$set({show_retry:e}),m()}get show_undo(){return this.$$.ctx[2]}set show_undo(e){this.$$set({show_undo:e}),m()}get show_copy_button(){return this.$$.ctx[13]}set show_copy_button(e){this.$$set({show_copy_button:e}),m()}get show(){return this.$$.ctx[3]}set show(e){this.$$set({show:e}),m()}get message(){return this.$$.ctx[4]}set message(e){this.$$set({message:e}),m()}get position(){return this.$$.ctx[5]}set position(e){this.$$set({position:e}),m()}get avatar(){return this.$$.ctx[6]}set avatar(e){this.$$set({avatar:e}),m()}get generating(){return this.$$.ctx[7]}set generating(e){this.$$set({generating:e}),m()}get handle_action(){return this.$$.ctx[8]}set handle_action(e){this.$$set({handle_action:e}),m()}get layout(){return this.$$.ctx[9]}set layout(e){this.$$set({layout:e}),m()}}function st(l,e,t){const n=l.slice();return n[35]=e[t],n[37]=t,n}function ot(l){let e,t,n;return t=new Pe({props:{class:"avatar-image",src:l[1]?.url,alt:l[3]+" avatar"}}),{c(){e=S("div"),L(t.$$.fragment),_(e,"class","avatar-container svelte-5ng3n")},m(i,o){H(i,e,o),A(t,e,null),n=!0},p(i,o){const s={};o[0]&2&&(s.src=i[1]?.url),o[0]&8&&(s.alt=i[3]+" avatar"),t.$set(s)},i(i){n||(c(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&z(e),N(t)}}}function Jl(l){let e,t=(l[35].content.value?.orig_name||l[35].content.value?.path.split("/").pop()||"file")+"",n,i,o;return{c(){e=S("a"),n=be(t),_(e,"data-testid","chatbot-file"),_(e,"class","file-pil svelte-5ng3n"),_(e,"href",i=l[35].content.value.url),_(e,"target","_blank"),_(e,"download",o=window.__is_colab__?null:l[35].content.value?.orig_name||l[35].content.value?.path.split("/").pop()||"file")},m(s,a){H(s,e,a),B(e,n)},p(s,a){a[0]&16&&t!==(t=(s[35].content.value?.orig_name||s[35].content.value?.path.split("/").pop()||"file")+"")&&Ce(n,t),a[0]&16&&i!==(i=s[35].content.value.url)&&_(e,"href",i),a[0]&16&&o!==(o=window.__is_colab__?null:s[35].content.value?.orig_name||s[35].content.value?.path.split("/").pop()||"file")&&_(e,"download",o)},i:I,o:I,d(s){s&&z(e)}}}function Wl(l){let e,t;return e=new Vl({props:{target:l[16],theme_mode:l[18],props:l[35].content.props,type:l[35].content.component,components:l[19],value:l[35].content.value,i18n:l[13],upload:l[15],_fetch:l[11]}}),e.$on("load",l[32]),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i[0]&65536&&(o.target=n[16]),i[0]&262144&&(o.theme_mode=n[18]),i[0]&16&&(o.props=n[35].content.props),i[0]&16&&(o.type=n[35].content.component),i[0]&524288&&(o.components=n[19]),i[0]&16&&(o.value=n[35].content.value),i[0]&8192&&(o.i18n=n[13]),i[0]&32768&&(o.upload=n[15]),i[0]&2048&&(o._fetch=n[11]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function Yl(l){let e,t,n,i;const o=[Kl,Ql],s=[];function a(r,u){return r[35].metadata.title?0:1}return e=a(l),t=s[e]=o[e](l),{c(){t.c(),n=G()},m(r,u){s[e].m(r,u),H(r,n,u),i=!0},p(r,u){let f=e;e=a(r),e===f?s[e].p(r,u):(T(),b(s[f],1,1,()=>{s[f]=null}),j(),t=s[e],t?t.p(r,u):(t=s[e]=o[e](r),t.c()),c(t,1),t.m(n.parentNode,n))},i(r){i||(c(t),i=!0)},o(r){b(t),i=!1},d(r){r&&z(n),s[e].d(r)}}}function Ql(l){let e,t;return e=new $e({props:{message:l[35].content,latex_delimiters:l[8],sanitize_html:l[9],render_markdown:l[7],line_breaks:l[14],root:l[17]}}),e.$on("load",function(){Mt(l[21])&&l[21].apply(this,arguments)}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){l=n;const o={};i[0]&16&&(o.message=l[35].content),i[0]&256&&(o.latex_delimiters=l[8]),i[0]&512&&(o.sanitize_html=l[9]),i[0]&128&&(o.render_markdown=l[7]),i[0]&16384&&(o.line_breaks=l[14]),i[0]&131072&&(o.root=l[17]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function Kl(l){let e,t;return e=new Al({props:{title:l[35].metadata.title,expanded:ve([l[35]],l[0]),$$slots:{default:[Xl]},$$scope:{ctx:l}}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i[0]&16&&(o.title=n[35].metadata.title),i[0]&17&&(o.expanded=ve([n[35]],n[0])),i[0]&2245520|i[1]&128&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function Xl(l){let e,t;return e=new $e({props:{message:l[35].content,latex_delimiters:l[8],sanitize_html:l[9],render_markdown:l[7],line_breaks:l[14],root:l[17]}}),e.$on("load",function(){Mt(l[21])&&l[21].apply(this,arguments)}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){l=n;const o={};i[0]&16&&(o.message=l[35].content),i[0]&256&&(o.latex_delimiters=l[8]),i[0]&512&&(o.sanitize_html=l[9]),i[0]&128&&(o.render_markdown=l[7]),i[0]&16384&&(o.line_breaks=l[14]),i[0]&131072&&(o.root=l[17]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function rt(l){let e,t;const n=[l[22]];let i={};for(let o=0;o<n.length;o+=1)i=De(i,n[o]);return e=new Bt({props:i}),{c(){L(e.$$.fragment)},m(o,s){A(e,o,s),t=!0},p(o,s){const a=s[0]&4194304?Ue(n,[qe(o[22])]):{};e.$set(a)},i(o){t||(c(e.$$.fragment,o),t=!0)},o(o){b(e.$$.fragment,o),t=!1},d(o){N(e,o)}}}function at(l){let e,t,n,i,o,s,a,r,u,f,g,w;const h=[Yl,Wl,Jl],d=[];function V(C,M){return C[35].type==="text"?0:C[35].type==="component"&&C[35].content.component in C[19]?1:C[35].type==="component"&&C[35].content.component==="file"?2:-1}~(n=V(l))&&(i=d[n]=h[n](l));function E(){return l[33](l[35])}function F(...C){return l[34](l[35],...C)}let y=l[5]==="panel"&&rt(l);return{c(){e=S("div"),t=S("button"),i&&i.c(),r=D(),y&&y.c(),u=G(),_(t,"data-testid",l[3]),_(t,"dir",o=l[12]?"rtl":"ltr"),_(t,"aria-label",s=l[3]+"'s message: "+ft(l[35])),_(t,"class","svelte-5ng3n"),Z(t,"latest",l[20]===l[0].length-1),Z(t,"message-markdown-disabled",!l[7]),Z(t,"selectable",l[10]),ee(t,"user-select","text"),ee(t,"cursor",l[10]?"pointer":"default"),ee(t,"text-align",l[12]?"right":"left"),_(e,"class",a="message "+l[3]+" "+(de(l[35])?l[35]?.content.component:"")+" svelte-5ng3n"),Z(e,"message-fit",l[5]==="bubble"&&!l[6]),Z(e,"panel-full-width",!0),Z(e,"message-markdown-disabled",!l[7]),Z(e,"component",l[35].type==="component"),Z(e,"html",de(l[35])&&l[35].content.component==="html"),Z(e,"thought",l[37]>0),ee(e,"text-align",l[12]&&l[3]==="user"?"left":"right")},m(C,M){H(C,e,M),B(e,t),~n&&d[n].m(t,null),H(C,r,M),y&&y.m(C,M),H(C,u,M),f=!0,g||(w=[Be(t,"click",E),Be(t,"keydown",F)],g=!0)},p(C,M){l=C;let O=n;n=V(l),n===O?~n&&d[n].p(l,M):(i&&(T(),b(d[O],1,1,()=>{d[O]=null}),j()),~n?(i=d[n],i?i.p(l,M):(i=d[n]=h[n](l),i.c()),c(i,1),i.m(t,null)):i=null),(!f||M[0]&8)&&_(t,"data-testid",l[3]),(!f||M[0]&4096&&o!==(o=l[12]?"rtl":"ltr"))&&_(t,"dir",o),(!f||M[0]&24&&s!==(s=l[3]+"'s message: "+ft(l[35])))&&_(t,"aria-label",s),(!f||M[0]&1048577)&&Z(t,"latest",l[20]===l[0].length-1),(!f||M[0]&128)&&Z(t,"message-markdown-disabled",!l[7]),(!f||M[0]&1024)&&Z(t,"selectable",l[10]),M[0]&1024&&ee(t,"cursor",l[10]?"pointer":"default"),M[0]&4096&&ee(t,"text-align",l[12]?"right":"left"),(!f||M[0]&24&&a!==(a="message "+l[3]+" "+(de(l[35])?l[35]?.content.component:"")+" svelte-5ng3n"))&&_(e,"class",a),(!f||M[0]&120)&&Z(e,"message-fit",l[5]==="bubble"&&!l[6]),(!f||M[0]&24)&&Z(e,"panel-full-width",!0),(!f||M[0]&152)&&Z(e,"message-markdown-disabled",!l[7]),(!f||M[0]&24)&&Z(e,"component",l[35].type==="component"),(!f||M[0]&24)&&Z(e,"html",de(l[35])&&l[35].content.component==="html"),(!f||M[0]&24)&&Z(e,"thought",l[37]>0),M[0]&4104&&ee(e,"text-align",l[12]&&l[3]==="user"?"left":"right"),l[5]==="panel"?y?(y.p(l,M),M[0]&32&&c(y,1)):(y=rt(l),y.c(),c(y,1),y.m(u.parentNode,u)):y&&(T(),b(y,1,1,()=>{y=null}),j())},i(C){f||(c(i),c(y),f=!0)},o(C){b(i),b(y),f=!1},d(C){C&&(z(e),z(r),z(u)),~n&&d[n].d(),y&&y.d(C),g=!1,Pt(w)}}}function ut(l){let e,t;const n=[l[22]];let i={};for(let o=0;o<n.length;o+=1)i=De(i,n[o]);return e=new Bt({props:i}),{c(){L(e.$$.fragment)},m(o,s){A(e,o,s),t=!0},p(o,s){const a=s[0]&4194304?Ue(n,[qe(o[22])]):{};e.$set(a)},i(o){t||(c(e.$$.fragment,o),t=!0)},o(o){b(e.$$.fragment,o),t=!1},d(o){N(e,o)}}}function xl(l){let e,t,n,i,o,s,a,r=l[1]!==null&&ot(l),u=pe(l[4]),f=[];for(let h=0;h<u.length;h+=1)f[h]=at(st(l,u,h));const g=h=>b(f[h],1,1,()=>{f[h]=null});let w=l[5]==="bubble"&&ut(l);return{c(){e=S("div"),r&&r.c(),t=D(),n=S("div");for(let h=0;h<f.length;h+=1)f[h].c();o=D(),w&&w.c(),s=G(),_(n,"class","flex-wrap svelte-5ng3n"),Z(n,"role",l[3]),Z(n,"component-wrap",l[4][0].type==="component"),_(e,"class",i="message-row "+l[5]+" "+l[3]+"-row svelte-5ng3n"),Z(e,"with_avatar",l[1]!==null),Z(e,"with_opposite_avatar",l[2]!==null)},m(h,d){H(h,e,d),r&&r.m(e,null),B(e,t),B(e,n);for(let V=0;V<f.length;V+=1)f[V]&&f[V].m(n,null);H(h,o,d),w&&w.m(h,d),H(h,s,d),a=!0},p(h,d){if(h[1]!==null?r?(r.p(h,d),d[0]&2&&c(r,1)):(r=ot(h),r.c(),c(r,1),r.m(e,t)):r&&(T(),b(r,1,1,()=>{r=null}),j()),d[0]&16777209){u=pe(h[4]);let V;for(V=0;V<u.length;V+=1){const E=st(h,u,V);f[V]?(f[V].p(E,d),c(f[V],1)):(f[V]=at(E),f[V].c(),c(f[V],1),f[V].m(n,null))}for(T(),V=u.length;V<f.length;V+=1)g(V);j()}(!a||d[0]&8)&&Z(n,"role",h[3]),(!a||d[0]&16)&&Z(n,"component-wrap",h[4][0].type==="component"),(!a||d[0]&40&&i!==(i="message-row "+h[5]+" "+h[3]+"-row svelte-5ng3n"))&&_(e,"class",i),(!a||d[0]&42)&&Z(e,"with_avatar",h[1]!==null),(!a||d[0]&44)&&Z(e,"with_opposite_avatar",h[2]!==null),h[5]==="bubble"?w?(w.p(h,d),d[0]&32&&c(w,1)):(w=ut(h),w.c(),c(w,1),w.m(s.parentNode,s)):w&&(T(),b(w,1,1,()=>{w=null}),j())},i(h){if(!a){c(r);for(let d=0;d<u.length;d+=1)c(f[d]);c(w),a=!0}},o(h){b(r),f=f.filter(Boolean);for(let d=0;d<f.length;d+=1)b(f[d]);b(w),a=!1},d(h){h&&(z(e),z(o),z(s)),r&&r.d(),Ze(f,h),w&&w.d(h)}}}function ft(l){return l.type==="text"?l.content:l.type==="component"&&l.content.component==="file"?Array.isArray(l.content.value)?`file of extension type: ${l.content.value[0].orig_name?.split(".").pop()}`:`file of extension type: ${l.content.value?.orig_name?.split(".").pop()}`+(l.content.value?.orig_name??""):`a component of type ${l.content.component??"unknown"}`}function en(l,e,t){let{value:n}=e,{avatar_img:i}=e,{opposite_avatar_img:o=null}=e,{role:s="user"}=e,{messages:a=[]}=e,{layout:r}=e,{bubble_full_width:u}=e,{render_markdown:f}=e,{latex_delimiters:g}=e,{sanitize_html:w}=e,{selectable:h}=e,{_fetch:d}=e,{rtl:V}=e,{dispatch:E}=e,{i18n:F}=e,{line_breaks:y}=e,{upload:C}=e,{target:M}=e,{root:O}=e,{theme_mode:fe}=e,{_components:_e}=e,{i:le}=e,{show_copy_button:Y}=e,{generating:oe}=e,{show_like:ne}=e,{show_retry:U}=e,{show_undo:ie}=e,{msg_format:re}=e,{handle_action:Q}=e,{scroll:se}=e;function ae(v,K){E("select",{index:K.index,value:K.content})}let ce;const he=()=>se(),me=v=>ae(le,v),ge=(v,K)=>{K.key==="Enter"&&ae(le,v)};return l.$$set=v=>{"value"in v&&t(0,n=v.value),"avatar_img"in v&&t(1,i=v.avatar_img),"opposite_avatar_img"in v&&t(2,o=v.opposite_avatar_img),"role"in v&&t(3,s=v.role),"messages"in v&&t(4,a=v.messages),"layout"in v&&t(5,r=v.layout),"bubble_full_width"in v&&t(6,u=v.bubble_full_width),"render_markdown"in v&&t(7,f=v.render_markdown),"latex_delimiters"in v&&t(8,g=v.latex_delimiters),"sanitize_html"in v&&t(9,w=v.sanitize_html),"selectable"in v&&t(10,h=v.selectable),"_fetch"in v&&t(11,d=v._fetch),"rtl"in v&&t(12,V=v.rtl),"dispatch"in v&&t(24,E=v.dispatch),"i18n"in v&&t(13,F=v.i18n),"line_breaks"in v&&t(14,y=v.line_breaks),"upload"in v&&t(15,C=v.upload),"target"in v&&t(16,M=v.target),"root"in v&&t(17,O=v.root),"theme_mode"in v&&t(18,fe=v.theme_mode),"_components"in v&&t(19,_e=v._components),"i"in v&&t(20,le=v.i),"show_copy_button"in v&&t(25,Y=v.show_copy_button),"generating"in v&&t(26,oe=v.generating),"show_like"in v&&t(27,ne=v.show_like),"show_retry"in v&&t(28,U=v.show_retry),"show_undo"in v&&t(29,ie=v.show_undo),"msg_format"in v&&t(30,re=v.msg_format),"handle_action"in v&&t(31,Q=v.handle_action),"scroll"in v&&t(21,se=v.scroll)},l.$$.update=()=>{l.$$.dirty[0]&2113929274|l.$$.dirty[1]&1&&t(22,ce={show:ne||U||ie||Y,handle_action:Q,likeable:ne,show_retry:U,show_undo:ie,generating:oe,show_copy_button:Y,message:re==="tuples"?a[0]:a,position:s==="user"?"right":"left",avatar:i,layout:r})},[n,i,o,s,a,r,u,f,g,w,h,d,V,F,y,C,M,O,fe,_e,le,se,ce,ae,E,Y,oe,ne,U,ie,re,Q,he,me,ge]}class tn extends P{constructor(e){super(),$(this,e,en,xl,R,{value:0,avatar_img:1,opposite_avatar_img:2,role:3,messages:4,layout:5,bubble_full_width:6,render_markdown:7,latex_delimiters:8,sanitize_html:9,selectable:10,_fetch:11,rtl:12,dispatch:24,i18n:13,line_breaks:14,upload:15,target:16,root:17,theme_mode:18,_components:19,i:20,show_copy_button:25,generating:26,show_like:27,show_retry:28,show_undo:29,msg_format:30,handle_action:31,scroll:21},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get avatar_img(){return this.$$.ctx[1]}set avatar_img(e){this.$$set({avatar_img:e}),m()}get opposite_avatar_img(){return this.$$.ctx[2]}set opposite_avatar_img(e){this.$$set({opposite_avatar_img:e}),m()}get role(){return this.$$.ctx[3]}set role(e){this.$$set({role:e}),m()}get messages(){return this.$$.ctx[4]}set messages(e){this.$$set({messages:e}),m()}get layout(){return this.$$.ctx[5]}set layout(e){this.$$set({layout:e}),m()}get bubble_full_width(){return this.$$.ctx[6]}set bubble_full_width(e){this.$$set({bubble_full_width:e}),m()}get render_markdown(){return this.$$.ctx[7]}set render_markdown(e){this.$$set({render_markdown:e}),m()}get latex_delimiters(){return this.$$.ctx[8]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),m()}get sanitize_html(){return this.$$.ctx[9]}set sanitize_html(e){this.$$set({sanitize_html:e}),m()}get selectable(){return this.$$.ctx[10]}set selectable(e){this.$$set({selectable:e}),m()}get _fetch(){return this.$$.ctx[11]}set _fetch(e){this.$$set({_fetch:e}),m()}get rtl(){return this.$$.ctx[12]}set rtl(e){this.$$set({rtl:e}),m()}get dispatch(){return this.$$.ctx[24]}set dispatch(e){this.$$set({dispatch:e}),m()}get i18n(){return this.$$.ctx[13]}set i18n(e){this.$$set({i18n:e}),m()}get line_breaks(){return this.$$.ctx[14]}set line_breaks(e){this.$$set({line_breaks:e}),m()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),m()}get target(){return this.$$.ctx[16]}set target(e){this.$$set({target:e}),m()}get root(){return this.$$.ctx[17]}set root(e){this.$$set({root:e}),m()}get theme_mode(){return this.$$.ctx[18]}set theme_mode(e){this.$$set({theme_mode:e}),m()}get _components(){return this.$$.ctx[19]}set _components(e){this.$$set({_components:e}),m()}get i(){return this.$$.ctx[20]}set i(e){this.$$set({i:e}),m()}get show_copy_button(){return this.$$.ctx[25]}set show_copy_button(e){this.$$set({show_copy_button:e}),m()}get generating(){return this.$$.ctx[26]}set generating(e){this.$$set({generating:e}),m()}get show_like(){return this.$$.ctx[27]}set show_like(e){this.$$set({show_like:e}),m()}get show_retry(){return this.$$.ctx[28]}set show_retry(e){this.$$set({show_retry:e}),m()}get show_undo(){return this.$$.ctx[29]}set show_undo(e){this.$$set({show_undo:e}),m()}get msg_format(){return this.$$.ctx[30]}set msg_format(e){this.$$set({msg_format:e}),m()}get handle_action(){return this.$$.ctx[31]}set handle_action(e){this.$$set({handle_action:e}),m()}get scroll(){return this.$$.ctx[21]}set scroll(e){this.$$set({scroll:e}),m()}}function ln(l){let e;return{c(){e=S("div"),e.innerHTML=`<span class="sr-only">Loading content</span> <div class="dot-flashing svelte-1gpwetz"></div>
	 
	<div class="dot-flashing svelte-1gpwetz"></div>
	 
	<div class="dot-flashing svelte-1gpwetz"></div>`,_(e,"class","message pending svelte-1gpwetz"),_(e,"role","status"),_(e,"aria-label","Loading response"),_(e,"aria-live","polite"),ee(e,"border-radius",l[0]==="bubble"?"var(--radius-xxl)":"none")},m(t,n){H(t,e,n)},p(t,[n]){n&1&&ee(e,"border-radius",t[0]==="bubble"?"var(--radius-xxl)":"none")},i:I,o:I,d(t){t&&z(e)}}}function nn(l,e,t){let{layout:n="bubble"}=e;return l.$$set=i=>{"layout"in i&&t(0,n=i.layout)},[n]}class sn extends P{constructor(e){super(),$(this,e,nn,ln,R,{layout:0})}get layout(){return this.$$.ctx[0]}set layout(e){this.$$set({layout:e}),m()}}function on(l){let e,t;return e=new te({props:{Icon:l[0]?Ie:Te,label:l[0]?"Copied conversation":"Copy conversation"}}),e.$on("click",l[1]),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.Icon=n[0]?Ie:Te),i&1&&(o.label=n[0]?"Copied conversation":"Copy conversation"),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function rn(l,e,t){let n=!1,{value:i}=e,o;function s(){t(0,n=!0),o&&clearTimeout(o),o=setTimeout(()=>{t(0,n=!1)},1e3)}const a=()=>{if(i){const u=i.map(f=>f.type==="text"?`${f.role}: ${f.content}`:`${f.role}: ${f.content.value.url}`).join(`

`);navigator.clipboard.writeText(u).catch(f=>{console.error("Failed to copy conversation: ",f)})}};async function r(){"clipboard"in navigator&&(a(),s())}return Vt(()=>{o&&clearTimeout(o)}),l.$$set=u=>{"value"in u&&t(2,i=u.value)},[n,r,i]}class an extends P{constructor(e){super(),$(this,e,rn,on,R,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),m()}}function _t(l,e,t){const n=l.slice();return n[60]=e[t],n[59]=t,n}function ct(l,e,t){const n=l.slice();n[54]=e[t],n[59]=t;const i=n[54][0].role==="user"?"user":"bot";n[55]=i;const o=n[11][n[55]==="user"?0:1];n[56]=o;const s=n[11][n[55]==="user"?0:1];return n[57]=s,n}function ht(l){let e,t;return e=new Re({props:{$$slots:{default:[fn]},$$scope:{ctx:l}}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i[0]&385|i[2]&1&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function mt(l){let e,t;return e=new te({props:{Icon:Lt,$$slots:{default:[un]},$$scope:{ctx:l}}}),e.$on("click",l[43]),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i[2]&1&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function un(l){let e,t;return e=new Lt({}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function gt(l){let e,t;return e=new an({props:{value:l[0]}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i[0]&1&&(o.value=n[0]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function fn(l){let e,t,n,i,o,s=l[7]&&mt(l);t=new te({props:{Icon:tl,label:"Clear"}}),t.$on("click",l[44]);let a=l[8]&&gt(l);return{c(){s&&s.c(),e=D(),L(t.$$.fragment),n=D(),a&&a.c(),i=G()},m(r,u){s&&s.m(r,u),H(r,e,u),A(t,r,u),H(r,n,u),a&&a.m(r,u),H(r,i,u),o=!0},p(r,u){r[7]?s?(s.p(r,u),u[0]&128&&c(s,1)):(s=mt(r),s.c(),c(s,1),s.m(e.parentNode,e)):s&&(T(),b(s,1,1,()=>{s=null}),j()),r[8]?a?(a.p(r,u),u[0]&256&&c(a,1)):(a=gt(r),a.c(),c(a,1),a.m(i.parentNode,i)):a&&(T(),b(a,1,1,()=>{a=null}),j())},i(r){o||(c(s),c(t.$$.fragment,r),c(a),o=!0)},o(r){b(s),b(t.$$.fragment,r),b(a),o=!1},d(r){r&&(z(e),z(n),z(i)),s&&s.d(r),N(t,r),a&&a.d(r)}}}function _n(l){let e,t,n,i=l[19]!==null&&dt(l),o=l[22]!==null&&bt(l);return{c(){e=S("div"),i&&i.c(),t=D(),o&&o.c(),_(e,"class","placeholder-content svelte-vxn3uw")},m(s,a){H(s,e,a),i&&i.m(e,null),B(e,t),o&&o.m(e,null),n=!0},p(s,a){s[19]!==null?i?(i.p(s,a),a[0]&524288&&c(i,1)):(i=dt(s),i.c(),c(i,1),i.m(e,t)):i&&(T(),b(i,1,1,()=>{i=null}),j()),s[22]!==null?o?(o.p(s,a),a[0]&4194304&&c(o,1)):(o=bt(s),o.c(),c(o,1),o.m(e,null)):o&&(T(),b(o,1,1,()=>{o=null}),j())},i(s){n||(c(i),c(o),n=!0)},o(s){b(i),b(o),n=!1},d(s){s&&z(e),i&&i.d(),o&&o.d()}}}function cn(l){let e,t,n,i,o,s=pe(l[34]),a=[];for(let f=0;f<s.length;f+=1)a[f]=pt(ct(l,s,f));const r=f=>b(a[f],1,1,()=>{a[f]=null});let u=l[3]&&Ct(l);return{c(){e=S("div");for(let f=0;f<a.length;f+=1)a[f].c();t=D(),u&&u.c(),_(e,"class","message-wrap svelte-vxn3uw")},m(f,g){H(f,e,g);for(let w=0;w<a.length;w+=1)a[w]&&a[w].m(e,null);B(e,t),u&&u.m(e,null),n=!0,i||(o=Ot(Kt.call(null,e)),i=!0)},p(f,g){if(g[0]&532151927|g[1]&319){s=pe(f[34]);let w;for(w=0;w<s.length;w+=1){const h=ct(f,s,w);a[w]?(a[w].p(h,g),c(a[w],1)):(a[w]=pt(h),a[w].c(),c(a[w],1),a[w].m(e,t))}for(T(),w=s.length;w<a.length;w+=1)r(w);j()}f[3]?u?(u.p(f,g),g[0]&8&&c(u,1)):(u=Ct(f),u.c(),c(u,1),u.m(e,null)):u&&(T(),b(u,1,1,()=>{u=null}),j())},i(f){if(!n){for(let g=0;g<s.length;g+=1)c(a[g]);c(u),n=!0}},o(f){a=a.filter(Boolean);for(let g=0;g<a.length;g+=1)b(a[g]);b(u),n=!1},d(f){f&&z(e),Ze(a,f),u&&u.d(),i=!1,o()}}}function dt(l){let e,t,n;return t=new $e({props:{message:l[19],latex_delimiters:l[2],root:l[26]}}),{c(){e=S("div"),L(t.$$.fragment),_(e,"class","placeholder svelte-vxn3uw")},m(i,o){H(i,e,o),A(t,e,null),n=!0},p(i,o){const s={};o[0]&524288&&(s.message=i[19]),o[0]&4&&(s.latex_delimiters=i[2]),o[0]&67108864&&(s.root=i[26]),t.$set(s)},i(i){n||(c(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&z(e),N(t)}}}function bt(l){let e,t,n=pe(l[22]),i=[];for(let s=0;s<n.length;s+=1)i[s]=kt(_t(l,n,s));const o=s=>b(i[s],1,1,()=>{i[s]=null});return{c(){e=S("div");for(let s=0;s<i.length;s+=1)i[s].c();_(e,"class","examples svelte-vxn3uw")},m(s,a){H(s,e,a);for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(e,null);t=!0},p(s,a){if(a[0]&4194304|a[1]&128){n=pe(s[22]);let r;for(r=0;r<n.length;r+=1){const u=_t(s,n,r);i[r]?(i[r].p(u,a),c(i[r],1)):(i[r]=kt(u),i[r].c(),c(i[r],1),i[r].m(e,null))}for(T(),r=n.length;r<i.length;r+=1)o(r);j()}},i(s){if(!t){for(let a=0;a<n.length;a+=1)c(i[a]);t=!0}},o(s){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)b(i[a]);t=!1},d(s){s&&z(e),Ze(i,s)}}}function wt(l){let e,t,n;return t=new Pe({props:{class:"example-icon",src:l[60].icon.url,alt:"example-icon"}}),{c(){e=S("div"),L(t.$$.fragment),_(e,"class","example-icon-container svelte-vxn3uw")},m(i,o){H(i,e,o),A(t,e,null),n=!0},p(i,o){const s={};o[0]&4194304&&(s.src=i[60].icon.url),t.$set(s)},i(i){n||(c(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&z(e),N(t)}}}function hn(l){let e,t=l[60].text+"",n;return{c(){e=S("span"),n=be(t),_(e,"class","example-text svelte-vxn3uw")},m(i,o){H(i,e,o),B(e,n)},p(i,o){o[0]&4194304&&t!==(t=i[60].text+"")&&Ce(n,t)},d(i){i&&z(e)}}}function mn(l){let e,t=l[60].display_text+"",n;return{c(){e=S("span"),n=be(t),_(e,"class","example-display-text svelte-vxn3uw")},m(i,o){H(i,e,o),B(e,n)},p(i,o){o[0]&4194304&&t!==(t=i[60].display_text+"")&&Ce(n,t)},d(i){i&&z(e)}}}function gn(l){let e,t,n=l[60].files[0].orig_name+"",i;return{c(){e=S("span"),t=S("em"),i=be(n),_(e,"class","example-file svelte-vxn3uw")},m(o,s){H(o,e,s),B(e,t),B(t,i)},p(o,s){s[0]&4194304&&n!==(n=o[60].files[0].orig_name+"")&&Ce(i,n)},i:I,o:I,d(o){o&&z(e)}}}function dn(l){let e,t,n;return t=new Pe({props:{class:"example-image",src:l[60].files[0].url,alt:"example-image"}}),{c(){e=S("div"),L(t.$$.fragment),_(e,"class","example-image-container svelte-vxn3uw")},m(i,o){H(i,e,o),A(t,e,null),n=!0},p(i,o){const s={};o[0]&4194304&&(s.src=i[60].files[0].url),t.$set(s)},i(i){n||(c(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&z(e),N(t)}}}function bn(l){let e,t,n=l[60].files.length+"",i,o;return{c(){e=S("span"),t=S("em"),i=be(n),o=be(" Files"),_(e,"class","example-file svelte-vxn3uw")},m(s,a){H(s,e,a),B(e,t),B(t,i),B(t,o)},p(s,a){a[0]&4194304&&n!==(n=s[60].files.length+"")&&Ce(i,n)},i:I,o:I,d(s){s&&z(e)}}}function kt(l){let e,t,n,i,o,s,a,r,u,f,g=l[60].icon!==void 0&&wt(l);function w(C,M){return C[60].display_text!==void 0?mn:hn}let h=w(l),d=h(l);const V=[bn,dn,gn],E=[];function F(C,M){return M[0]&4194304&&(i=null),C[60].files!==void 0&&C[60].files.length>1?0:(i==null&&(i=!!(C[60].files!==void 0&&C[60].files[0]!==void 0&&C[60].files[0].mime_type?.includes("image"))),i?1:C[60].files!==void 0&&C[60].files[0]!==void 0?2:-1)}~(o=F(l,[-1,-1,-1]))&&(s=E[o]=V[o](l));function y(){return l[47](l[59],l[60])}return{c(){e=S("button"),g&&g.c(),t=D(),d.c(),n=D(),s&&s.c(),a=D(),_(e,"class","example svelte-vxn3uw")},m(C,M){H(C,e,M),g&&g.m(e,null),B(e,t),d.m(e,null),B(e,n),~o&&E[o].m(e,null),B(e,a),r=!0,u||(f=Be(e,"click",y),u=!0)},p(C,M){l=C,l[60].icon!==void 0?g?(g.p(l,M),M[0]&4194304&&c(g,1)):(g=wt(l),g.c(),c(g,1),g.m(e,t)):g&&(T(),b(g,1,1,()=>{g=null}),j()),h===(h=w(l))&&d?d.p(l,M):(d.d(1),d=h(l),d&&(d.c(),d.m(e,n)));let O=o;o=F(l,M),o===O?~o&&E[o].p(l,M):(s&&(T(),b(E[O],1,1,()=>{E[O]=null}),j()),~o?(s=E[o],s?s.p(l,M):(s=E[o]=V[o](l),s.c()),c(s,1),s.m(e,a)):s=null)},i(C){r||(c(g),c(s),r=!0)},o(C){b(g),b(s),r=!1},d(C){C&&z(e),g&&g.d(),d.d(),~o&&E[o].d(),u=!1,f()}}}function vt(l){let e,t,n,i,o,s;return o=new Re({props:{$$slots:{default:[wn]},$$scope:{ctx:l}}}),{c(){e=S("div"),t=S("img"),i=D(),L(o.$$.fragment),Oe(t.src,n=l[31])||_(t,"src",n),_(t,"alt",l[32]),_(e,"class","image-preview svelte-vxn3uw")},m(a,r){H(a,e,r),B(e,t),B(e,i),A(o,e,null),s=!0},p(a,r){(!s||r[1]&1&&!Oe(t.src,n=a[31]))&&_(t,"src",n),(!s||r[1]&2)&&_(t,"alt",a[32]);const u={};r[1]&4|r[2]&1&&(u.$$scope={dirty:r,ctx:a}),o.$set(u)},i(a){s||(c(o.$$.fragment,a),s=!0)},o(a){b(o.$$.fragment,a),s=!1},d(a){a&&z(e),N(o)}}}function wn(l){let e,t;return e=new te({props:{Icon:Jt,label:"Clear"}}),e.$on("click",l[45]),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p:I,i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function pt(l){let e,t,n,i=l[33]&&vt(l);function o(...s){return l[46](l[59],l[54],...s)}return t=new tn({props:{messages:l[54],opposite_avatar_img:l[57],avatar_img:l[56],role:l[55],layout:l[18],dispatch:l[36],i18n:l[17],_fetch:l[1],line_breaks:l[15],theme_mode:l[16],target:l[28],root:l[26],upload:l[20],selectable:l[5],sanitize_html:l[12],bubble_full_width:l[13],render_markdown:l[14],rtl:l[9],i:l[59],value:l[0],latex_delimiters:l[2],_components:l[27],generating:l[4],msg_format:l[21],show_like:l[55]==="user"?l[6]&&l[25]:l[6],show_retry:l[23]&&ve(l[54],l[0]),show_undo:l[24]&&ve(l[54],l[0]),show_copy_button:l[10],handle_action:o,scroll:l[35]?scroll:vn}}),{c(){i&&i.c(),e=D(),L(t.$$.fragment)},m(s,a){i&&i.m(s,a),H(s,e,a),A(t,s,a),n=!0},p(s,a){l=s,l[33]?i?(i.p(l,a),a[1]&4&&c(i,1)):(i=vt(l),i.c(),c(i,1),i.m(e.parentNode,e)):i&&(T(),b(i,1,1,()=>{i=null}),j());const r={};a[1]&8&&(r.messages=l[54]),a[0]&2048|a[1]&8&&(r.opposite_avatar_img=l[57]),a[0]&2048|a[1]&8&&(r.avatar_img=l[56]),a[1]&8&&(r.role=l[55]),a[0]&262144&&(r.layout=l[18]),a[0]&131072&&(r.i18n=l[17]),a[0]&2&&(r._fetch=l[1]),a[0]&32768&&(r.line_breaks=l[15]),a[0]&65536&&(r.theme_mode=l[16]),a[0]&268435456&&(r.target=l[28]),a[0]&67108864&&(r.root=l[26]),a[0]&1048576&&(r.upload=l[20]),a[0]&32&&(r.selectable=l[5]),a[0]&4096&&(r.sanitize_html=l[12]),a[0]&8192&&(r.bubble_full_width=l[13]),a[0]&16384&&(r.render_markdown=l[14]),a[0]&512&&(r.rtl=l[9]),a[0]&1&&(r.value=l[0]),a[0]&4&&(r.latex_delimiters=l[2]),a[0]&134217728&&(r._components=l[27]),a[0]&16&&(r.generating=l[4]),a[0]&2097152&&(r.msg_format=l[21]),a[0]&33554496|a[1]&8&&(r.show_like=l[55]==="user"?l[6]&&l[25]:l[6]),a[0]&8388609|a[1]&8&&(r.show_retry=l[23]&&ve(l[54],l[0])),a[0]&16777217|a[1]&8&&(r.show_undo=l[24]&&ve(l[54],l[0])),a[0]&1024&&(r.show_copy_button=l[10]),a[1]&8&&(r.handle_action=o),t.$set(r)},i(s){n||(c(i),c(t.$$.fragment,s),n=!0)},o(s){b(i),b(t.$$.fragment,s),n=!1},d(s){s&&z(e),i&&i.d(s),N(t,s)}}}function Ct(l){let e,t;return e=new sn({props:{layout:l[18]}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i[0]&262144&&(o.layout=n[18]),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function yt(l){let e,t,n;return t=new te({props:{Icon:al,label:"Scroll down",size:"large"}}),t.$on("click",l[37]),{c(){e=S("div"),L(t.$$.fragment),_(e,"class","scroll-down-button-container svelte-vxn3uw")},m(i,o){H(i,e,o),A(t,e,null),n=!0},p:I,i(i){n||(c(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&z(e),N(t)}}}function kn(l){let e,t,n,i,o,s,a,r,u=l[0]!==null&&l[0].length>0&&ht(l);const f=[cn,_n],g=[];function w(d,V){return d[0]!==null&&d[0].length>0&&d[34]!==null?0:1}n=w(l),i=g[n]=f[n](l);let h=l[30]&&yt(l);return{c(){u&&u.c(),e=D(),t=S("div"),i.c(),s=D(),h&&h.c(),a=G(),_(t,"class",o=Fe(l[18]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-vxn3uw"),_(t,"role","log"),_(t,"aria-label","chatbot conversation"),_(t,"aria-live","polite")},m(d,V){u&&u.m(d,V),H(d,e,V),H(d,t,V),g[n].m(t,null),l[48](t),H(d,s,V),h&&h.m(d,V),H(d,a,V),r=!0},p(d,V){d[0]!==null&&d[0].length>0?u?(u.p(d,V),V[0]&1&&c(u,1)):(u=ht(d),u.c(),c(u,1),u.m(e.parentNode,e)):u&&(T(),b(u,1,1,()=>{u=null}),j());let E=n;n=w(d),n===E?g[n].p(d,V):(T(),b(g[E],1,1,()=>{g[E]=null}),j(),i=g[n],i?i.p(d,V):(i=g[n]=f[n](d),i.c()),c(i,1),i.m(t,null)),(!r||V[0]&262144&&o!==(o=Fe(d[18]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-vxn3uw"))&&_(t,"class",o),d[30]?h?(h.p(d,V),V[0]&1073741824&&c(h,1)):(h=yt(d),h.c(),c(h,1),h.m(a.parentNode,a)):h&&(T(),b(h,1,1,()=>{h=null}),j())},i(d){r||(c(u),c(i),c(h),r=!0)},o(d){b(u),b(i),b(h),r=!1},d(d){d&&(z(e),z(t),z(s),z(a)),u&&u.d(d),g[n].d(),l[48](null),h&&h.d(d)}}}const vn=()=>{};function pn(l,e,t){let n,{value:i=[]}=e,o=null,{_fetch:s}=e,{load_component:a}=e,r={};const u=typeof window<"u";async function f(){t(27,r=await ml(gl(i),r,a))}let{latex_delimiters:g}=e,{pending_message:w=!1}=e,{generating:h=!1}=e,{selectable:d=!1}=e,{likeable:V=!1}=e,{show_share_button:E=!1}=e,{show_copy_all_button:F=!1}=e,{rtl:y=!1}=e,{show_copy_button:C=!1}=e,{avatar_images:M=[null,null]}=e,{sanitize_html:O=!0}=e,{bubble_full_width:fe=!0}=e,{render_markdown:_e=!0}=e,{line_breaks:le=!0}=e,{autoscroll:Y=!0}=e,{theme_mode:oe}=e,{i18n:ne}=e,{layout:U="bubble"}=e,{placeholder:ie=null}=e,{upload:re}=e,{msg_format:Q="tuples"}=e,{examples:se=null}=e,{_retryable:ae=!1}=e,{_undoable:ce=!1}=e,{like_user_message:he=!1}=e,{root:me}=e,ge=null;Se(()=>{t(28,ge=document.querySelector("div.gradio-container"))});let v,K=!1;const X=$t();function He(){return v&&v.offsetHeight+v.scrollTop>v.scrollHeight-100}function ze(){v&&(v.scrollTo(0,v.scrollHeight),t(30,K=!1))}async function Ve(){Y&&(He()?(await Gt(),ze()):t(30,K=!0))}Se(()=>{Ve()}),Se(()=>{function p(){He()&&t(30,K=!1)}return v?.addEventListener("scroll",p),()=>{v?.removeEventListener("scroll",p)}});let Me,Le,ye=!1;Rt(()=>{v&&v.querySelectorAll("img").forEach(p=>{p.addEventListener("click",J=>{const x=J.target;x&&(t(31,Me=x.src),t(32,Le=x.alt),t(33,ye=!0))})})});function Ae(p,J){X("example_select",{index:p,value:{text:J.text,files:J.files}})}function Ne(p,J,x){if(x==="undo"||x==="retry"){const ue=i;let we=ue.length-1;for(;ue[we].role==="assistant";)we--;X(x,{index:ue[we].index,value:ue[we].content});return}if(Q==="tuples")X("like",{index:J.index,value:J.content,liked:x==="like"});else{if(!n)return;const ue=n[p],[we,Et]=[ue[0],ue[ue.length-1]];X("like",{index:[we.index,Et.index],value:ue.map(St=>St.content),liked:x==="like"})}}const je=async()=>{try{const p=await ul(i);X("share",{description:p})}catch(p){console.error(p);let J=p instanceof Qt?p.message:"Share failed.";X("error",J)}},Ee=()=>X("clear"),k=()=>t(33,ye=!1),It=(p,J,x)=>Ne(p,J[0],x),Tt=(p,J)=>Ae(p,J);function jt(p){Ft[p?"unshift":"push"](()=>{v=p,t(29,v)})}return l.$$set=p=>{"value"in p&&t(0,i=p.value),"_fetch"in p&&t(1,s=p._fetch),"load_component"in p&&t(40,a=p.load_component),"latex_delimiters"in p&&t(2,g=p.latex_delimiters),"pending_message"in p&&t(3,w=p.pending_message),"generating"in p&&t(4,h=p.generating),"selectable"in p&&t(5,d=p.selectable),"likeable"in p&&t(6,V=p.likeable),"show_share_button"in p&&t(7,E=p.show_share_button),"show_copy_all_button"in p&&t(8,F=p.show_copy_all_button),"rtl"in p&&t(9,y=p.rtl),"show_copy_button"in p&&t(10,C=p.show_copy_button),"avatar_images"in p&&t(11,M=p.avatar_images),"sanitize_html"in p&&t(12,O=p.sanitize_html),"bubble_full_width"in p&&t(13,fe=p.bubble_full_width),"render_markdown"in p&&t(14,_e=p.render_markdown),"line_breaks"in p&&t(15,le=p.line_breaks),"autoscroll"in p&&t(41,Y=p.autoscroll),"theme_mode"in p&&t(16,oe=p.theme_mode),"i18n"in p&&t(17,ne=p.i18n),"layout"in p&&t(18,U=p.layout),"placeholder"in p&&t(19,ie=p.placeholder),"upload"in p&&t(20,re=p.upload),"msg_format"in p&&t(21,Q=p.msg_format),"examples"in p&&t(22,se=p.examples),"_retryable"in p&&t(23,ae=p._retryable),"_undoable"in p&&t(24,ce=p._undoable),"like_user_message"in p&&t(25,he=p.like_user_message),"root"in p&&t(26,me=p.root)},l.$$.update=()=>{l.$$.dirty[0]&1&&f(),l.$$.dirty[0]&134217737&&(i||w||r)&&Ve(),l.$$.dirty[0]&1|l.$$.dirty[1]&2048&&(el(i,o)||(t(42,o=i),X("change"))),l.$$.dirty[0]&2097153&&t(34,n=i&&hl(i,Q))},[i,s,g,w,h,d,V,E,F,y,C,M,O,fe,_e,le,oe,ne,U,ie,re,Q,se,ae,ce,he,me,r,ge,v,K,Me,Le,ye,n,u,X,ze,Ae,Ne,a,Y,o,je,Ee,k,It,Tt,jt]}class Cn extends P{constructor(e){super(),$(this,e,pn,kn,R,{value:0,_fetch:1,load_component:40,latex_delimiters:2,pending_message:3,generating:4,selectable:5,likeable:6,show_share_button:7,show_copy_all_button:8,rtl:9,show_copy_button:10,avatar_images:11,sanitize_html:12,bubble_full_width:13,render_markdown:14,line_breaks:15,autoscroll:41,theme_mode:16,i18n:17,layout:18,placeholder:19,upload:20,msg_format:21,examples:22,_retryable:23,_undoable:24,like_user_message:25,root:26},null,[-1,-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get _fetch(){return this.$$.ctx[1]}set _fetch(e){this.$$set({_fetch:e}),m()}get load_component(){return this.$$.ctx[40]}set load_component(e){this.$$set({load_component:e}),m()}get latex_delimiters(){return this.$$.ctx[2]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),m()}get pending_message(){return this.$$.ctx[3]}set pending_message(e){this.$$set({pending_message:e}),m()}get generating(){return this.$$.ctx[4]}set generating(e){this.$$set({generating:e}),m()}get selectable(){return this.$$.ctx[5]}set selectable(e){this.$$set({selectable:e}),m()}get likeable(){return this.$$.ctx[6]}set likeable(e){this.$$set({likeable:e}),m()}get show_share_button(){return this.$$.ctx[7]}set show_share_button(e){this.$$set({show_share_button:e}),m()}get show_copy_all_button(){return this.$$.ctx[8]}set show_copy_all_button(e){this.$$set({show_copy_all_button:e}),m()}get rtl(){return this.$$.ctx[9]}set rtl(e){this.$$set({rtl:e}),m()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),m()}get avatar_images(){return this.$$.ctx[11]}set avatar_images(e){this.$$set({avatar_images:e}),m()}get sanitize_html(){return this.$$.ctx[12]}set sanitize_html(e){this.$$set({sanitize_html:e}),m()}get bubble_full_width(){return this.$$.ctx[13]}set bubble_full_width(e){this.$$set({bubble_full_width:e}),m()}get render_markdown(){return this.$$.ctx[14]}set render_markdown(e){this.$$set({render_markdown:e}),m()}get line_breaks(){return this.$$.ctx[15]}set line_breaks(e){this.$$set({line_breaks:e}),m()}get autoscroll(){return this.$$.ctx[41]}set autoscroll(e){this.$$set({autoscroll:e}),m()}get theme_mode(){return this.$$.ctx[16]}set theme_mode(e){this.$$set({theme_mode:e}),m()}get i18n(){return this.$$.ctx[17]}set i18n(e){this.$$set({i18n:e}),m()}get layout(){return this.$$.ctx[18]}set layout(e){this.$$set({layout:e}),m()}get placeholder(){return this.$$.ctx[19]}set placeholder(e){this.$$set({placeholder:e}),m()}get upload(){return this.$$.ctx[20]}set upload(e){this.$$set({upload:e}),m()}get msg_format(){return this.$$.ctx[21]}set msg_format(e){this.$$set({msg_format:e}),m()}get examples(){return this.$$.ctx[22]}set examples(e){this.$$set({examples:e}),m()}get _retryable(){return this.$$.ctx[23]}set _retryable(e){this.$$set({_retryable:e}),m()}get _undoable(){return this.$$.ctx[24]}set _undoable(e){this.$$set({_undoable:e}),m()}get like_user_message(){return this.$$.ctx[25]}set like_user_message(e){this.$$set({like_user_message:e}),m()}get root(){return this.$$.ctx[26]}set root(e){this.$$set({root:e}),m()}}const yn=Cn;function Ht(l){let e,t;const n=[{autoscroll:l[24].autoscroll},{i18n:l[24].i18n},l[27],{show_progress:l[27].show_progress==="hidden"?"hidden":"minimal"}];let i={};for(let o=0;o<n.length;o+=1)i=De(i,n[o]);return e=new Yt({props:i}),e.$on("clear_status",l[36]),{c(){L(e.$$.fragment)},m(o,s){A(e,o,s),t=!0},p(o,s){const a=s[0]&150994944?Ue(n,[s[0]&16777216&&{autoscroll:o[24].autoscroll},s[0]&16777216&&{i18n:o[24].i18n},s[0]&134217728&&qe(o[27]),s[0]&134217728&&{show_progress:o[27].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(a)},i(o){t||(c(e.$$.fragment,o),t=!0)},o(o){b(e.$$.fragment,o),t=!1},d(o){N(e,o)}}}function zt(l){let e,t;return e=new ll({props:{show_label:l[7],Icon:il,float:!0,label:l[6]||"Chatbot"}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i[0]&128&&(o.show_label=n[7]),i[0]&64&&(o.label=n[6]||"Chatbot"),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function Hn(l){let e,t,n,i,o,s=l[27]&&Ht(l),a=l[7]&&zt(l);return i=new yn({props:{i18n:l[24].i18n,selectable:l[8],likeable:l[9],show_share_button:l[10],show_copy_all_button:l[13],value:l[34],latex_delimiters:l[23],render_markdown:l[18],theme_mode:l[33],pending_message:l[27]?.status==="pending",generating:l[27]?.status==="generating",rtl:l[11],show_copy_button:l[12],like_user_message:l[26],avatar_images:l[25],sanitize_html:l[14],bubble_full_width:l[15],line_breaks:l[19],autoscroll:l[20],layout:l[16],placeholder:l[31],examples:l[32],_retryable:l[21],_undoable:l[22],upload:l[37],_fetch:l[38],load_component:l[24].load_component,msg_format:l[17],root:l[24].root}}),i.$on("change",l[39]),i.$on("select",l[40]),i.$on("like",l[41]),i.$on("share",l[42]),i.$on("error",l[43]),i.$on("example_select",l[44]),i.$on("retry",l[45]),i.$on("undo",l[46]),i.$on("clear",l[47]),{c(){s&&s.c(),e=D(),t=S("div"),a&&a.c(),n=D(),L(i.$$.fragment),_(t,"class","wrapper svelte-g3p8na")},m(r,u){s&&s.m(r,u),H(r,e,u),H(r,t,u),a&&a.m(t,null),B(t,n),A(i,t,null),o=!0},p(r,u){r[27]?s?(s.p(r,u),u[0]&134217728&&c(s,1)):(s=Ht(r),s.c(),c(s,1),s.m(e.parentNode,e)):s&&(T(),b(s,1,1,()=>{s=null}),j()),r[7]?a?(a.p(r,u),u[0]&128&&c(a,1)):(a=zt(r),a.c(),c(a,1),a.m(t,n)):a&&(T(),b(a,1,1,()=>{a=null}),j());const f={};u[0]&16777216&&(f.i18n=r[24].i18n),u[0]&256&&(f.selectable=r[8]),u[0]&512&&(f.likeable=r[9]),u[0]&1024&&(f.show_share_button=r[10]),u[0]&8192&&(f.show_copy_all_button=r[13]),u[1]&8&&(f.value=r[34]),u[0]&8388608&&(f.latex_delimiters=r[23]),u[0]&262144&&(f.render_markdown=r[18]),u[1]&4&&(f.theme_mode=r[33]),u[0]&134217728&&(f.pending_message=r[27]?.status==="pending"),u[0]&134217728&&(f.generating=r[27]?.status==="generating"),u[0]&2048&&(f.rtl=r[11]),u[0]&4096&&(f.show_copy_button=r[12]),u[0]&67108864&&(f.like_user_message=r[26]),u[0]&33554432&&(f.avatar_images=r[25]),u[0]&16384&&(f.sanitize_html=r[14]),u[0]&32768&&(f.bubble_full_width=r[15]),u[0]&524288&&(f.line_breaks=r[19]),u[0]&1048576&&(f.autoscroll=r[20]),u[0]&65536&&(f.layout=r[16]),u[1]&1&&(f.placeholder=r[31]),u[1]&2&&(f.examples=r[32]),u[0]&2097152&&(f._retryable=r[21]),u[0]&4194304&&(f._undoable=r[22]),u[0]&16777216&&(f.upload=r[37]),u[0]&16777216&&(f._fetch=r[38]),u[0]&16777216&&(f.load_component=r[24].load_component),u[0]&131072&&(f.msg_format=r[17]),u[0]&16777216&&(f.root=r[24].root),i.$set(f)},i(r){o||(c(s),c(a),c(i.$$.fragment,r),o=!0)},o(r){b(s),b(a),b(i.$$.fragment,r),o=!1},d(r){r&&(z(e),z(t)),s&&s.d(r),a&&a.d(),N(i)}}}function zn(l){let e,t;return e=new Wt({props:{elem_id:l[1],elem_classes:l[2],visible:l[3],padding:!1,scale:l[4],min_width:l[5],height:l[28],min_height:l[29],max_height:l[30],allow_overflow:!0,flex:!0,overflow_behavior:"auto",$$slots:{default:[Hn]},$$scope:{ctx:l}}}),{c(){L(e.$$.fragment)},m(n,i){A(e,n,i),t=!0},p(n,i){const o={};i[0]&2&&(o.elem_id=n[1]),i[0]&4&&(o.elem_classes=n[2]),i[0]&8&&(o.visible=n[3]),i[0]&16&&(o.scale=n[4]),i[0]&32&&(o.min_width=n[5]),i[0]&268435456&&(o.height=n[28]),i[0]&536870912&&(o.min_height=n[29]),i[0]&1073741824&&(o.max_height=n[30]),i[0]&268435393|i[1]&131087&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(c(e.$$.fragment,n),t=!0)},o(n){b(e.$$.fragment,n),t=!1},d(n){N(e,n)}}}function Vn(l,e,t){let{elem_id:n=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:s=[]}=e,{scale:a=null}=e,{min_width:r=void 0}=e,{label:u}=e,{show_label:f=!0}=e,{root:g}=e,{_selectable:w=!1}=e,{likeable:h=!1}=e,{show_share_button:d=!1}=e,{rtl:V=!1}=e,{show_copy_button:E=!0}=e,{show_copy_all_button:F=!1}=e,{sanitize_html:y=!0}=e,{bubble_full_width:C=!0}=e,{layout:M="bubble"}=e,{type:O="tuples"}=e,{render_markdown:fe=!0}=e,{line_breaks:_e=!0}=e,{autoscroll:le=!0}=e,{_retryable:Y=!1}=e,{_undoable:oe=!1}=e,{latex_delimiters:ne}=e,{gradio:U}=e,ie=[],{avatar_images:re=[null,null]}=e,{like_user_message:Q=!1}=e,{loading_status:se=void 0}=e,{height:ae}=e,{min_height:ce}=e,{max_height:he}=e,{placeholder:me=null}=e,{examples:ge=null}=e,{theme_mode:v}=e;const K=()=>U.dispatch("clear_status",se),X=(...k)=>U.client.upload(...k),He=(...k)=>U.client.fetch(...k),ze=()=>U.dispatch("change",s),Ve=k=>U.dispatch("select",k.detail),Me=k=>U.dispatch("like",k.detail),Le=k=>U.dispatch("share",k.detail),ye=k=>U.dispatch("error",k.detail),Ae=k=>U.dispatch("example_select",k.detail),Ne=k=>U.dispatch("retry",k.detail),je=k=>U.dispatch("undo",k.detail),Ee=()=>{t(0,s=[]),U.dispatch("clear")};return l.$$set=k=>{"elem_id"in k&&t(1,n=k.elem_id),"elem_classes"in k&&t(2,i=k.elem_classes),"visible"in k&&t(3,o=k.visible),"value"in k&&t(0,s=k.value),"scale"in k&&t(4,a=k.scale),"min_width"in k&&t(5,r=k.min_width),"label"in k&&t(6,u=k.label),"show_label"in k&&t(7,f=k.show_label),"root"in k&&t(35,g=k.root),"_selectable"in k&&t(8,w=k._selectable),"likeable"in k&&t(9,h=k.likeable),"show_share_button"in k&&t(10,d=k.show_share_button),"rtl"in k&&t(11,V=k.rtl),"show_copy_button"in k&&t(12,E=k.show_copy_button),"show_copy_all_button"in k&&t(13,F=k.show_copy_all_button),"sanitize_html"in k&&t(14,y=k.sanitize_html),"bubble_full_width"in k&&t(15,C=k.bubble_full_width),"layout"in k&&t(16,M=k.layout),"type"in k&&t(17,O=k.type),"render_markdown"in k&&t(18,fe=k.render_markdown),"line_breaks"in k&&t(19,_e=k.line_breaks),"autoscroll"in k&&t(20,le=k.autoscroll),"_retryable"in k&&t(21,Y=k._retryable),"_undoable"in k&&t(22,oe=k._undoable),"latex_delimiters"in k&&t(23,ne=k.latex_delimiters),"gradio"in k&&t(24,U=k.gradio),"avatar_images"in k&&t(25,re=k.avatar_images),"like_user_message"in k&&t(26,Q=k.like_user_message),"loading_status"in k&&t(27,se=k.loading_status),"height"in k&&t(28,ae=k.height),"min_height"in k&&t(29,ce=k.min_height),"max_height"in k&&t(30,he=k.max_height),"placeholder"in k&&t(31,me=k.placeholder),"examples"in k&&t(32,ge=k.examples),"theme_mode"in k&&t(33,v=k.theme_mode)},l.$$.update=()=>{l.$$.dirty[0]&131073|l.$$.dirty[1]&16&&t(34,ie=O==="tuples"?cl(s,g):_l(s,g))},[s,n,i,o,a,r,u,f,w,h,d,V,E,F,y,C,M,O,fe,_e,le,Y,oe,ne,U,re,Q,se,ae,ce,he,me,ge,v,ie,g,K,X,He,ze,Ve,Me,Le,ye,Ae,Ne,je,Ee]}class $n extends P{constructor(e){super(),$(this,e,Vn,zn,R,{elem_id:1,elem_classes:2,visible:3,value:0,scale:4,min_width:5,label:6,show_label:7,root:35,_selectable:8,likeable:9,show_share_button:10,rtl:11,show_copy_button:12,show_copy_all_button:13,sanitize_html:14,bubble_full_width:15,layout:16,type:17,render_markdown:18,line_breaks:19,autoscroll:20,_retryable:21,_undoable:22,latex_delimiters:23,gradio:24,avatar_images:25,like_user_message:26,loading_status:27,height:28,min_height:29,max_height:30,placeholder:31,examples:32,theme_mode:33},null,[-1,-1])}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),m()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),m()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),m()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get scale(){return this.$$.ctx[4]}set scale(e){this.$$set({scale:e}),m()}get min_width(){return this.$$.ctx[5]}set min_width(e){this.$$set({min_width:e}),m()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),m()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),m()}get root(){return this.$$.ctx[35]}set root(e){this.$$set({root:e}),m()}get _selectable(){return this.$$.ctx[8]}set _selectable(e){this.$$set({_selectable:e}),m()}get likeable(){return this.$$.ctx[9]}set likeable(e){this.$$set({likeable:e}),m()}get show_share_button(){return this.$$.ctx[10]}set show_share_button(e){this.$$set({show_share_button:e}),m()}get rtl(){return this.$$.ctx[11]}set rtl(e){this.$$set({rtl:e}),m()}get show_copy_button(){return this.$$.ctx[12]}set show_copy_button(e){this.$$set({show_copy_button:e}),m()}get show_copy_all_button(){return this.$$.ctx[13]}set show_copy_all_button(e){this.$$set({show_copy_all_button:e}),m()}get sanitize_html(){return this.$$.ctx[14]}set sanitize_html(e){this.$$set({sanitize_html:e}),m()}get bubble_full_width(){return this.$$.ctx[15]}set bubble_full_width(e){this.$$set({bubble_full_width:e}),m()}get layout(){return this.$$.ctx[16]}set layout(e){this.$$set({layout:e}),m()}get type(){return this.$$.ctx[17]}set type(e){this.$$set({type:e}),m()}get render_markdown(){return this.$$.ctx[18]}set render_markdown(e){this.$$set({render_markdown:e}),m()}get line_breaks(){return this.$$.ctx[19]}set line_breaks(e){this.$$set({line_breaks:e}),m()}get autoscroll(){return this.$$.ctx[20]}set autoscroll(e){this.$$set({autoscroll:e}),m()}get _retryable(){return this.$$.ctx[21]}set _retryable(e){this.$$set({_retryable:e}),m()}get _undoable(){return this.$$.ctx[22]}set _undoable(e){this.$$set({_undoable:e}),m()}get latex_delimiters(){return this.$$.ctx[23]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),m()}get gradio(){return this.$$.ctx[24]}set gradio(e){this.$$set({gradio:e}),m()}get avatar_images(){return this.$$.ctx[25]}set avatar_images(e){this.$$set({avatar_images:e}),m()}get like_user_message(){return this.$$.ctx[26]}set like_user_message(e){this.$$set({like_user_message:e}),m()}get loading_status(){return this.$$.ctx[27]}set loading_status(e){this.$$set({loading_status:e}),m()}get height(){return this.$$.ctx[28]}set height(e){this.$$set({height:e}),m()}get min_height(){return this.$$.ctx[29]}set min_height(e){this.$$set({min_height:e}),m()}get max_height(){return this.$$.ctx[30]}set max_height(e){this.$$set({max_height:e}),m()}get placeholder(){return this.$$.ctx[31]}set placeholder(e){this.$$set({placeholder:e}),m()}get examples(){return this.$$.ctx[32]}set examples(e){this.$$set({examples:e}),m()}get theme_mode(){return this.$$.ctx[33]}set theme_mode(e){this.$$set({theme_mode:e}),m()}}export{yn as BaseChatBot,$n as default};
//# sourceMappingURL=Index-CukgftOj.js.map
